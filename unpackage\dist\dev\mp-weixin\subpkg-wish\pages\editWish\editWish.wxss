
/* 页面容器 */
.page-container {
	min-height: 100vh;
	background-color: #f9fafc;
	padding: 30rpx;
	padding-bottom: 180rpx;
}

/* 表单标签 */
.form-label {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin: 30rpx 0 16rpx;
}

/* 表单输入框 */
.form-input {
	width: 100%;
	height: 88rpx;
	padding: 0 24rpx;
	background-color: #fff;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	border: 2rpx solid #e6e8eb;
	box-sizing: border-box;
}

/* 多行文本输入框 */
.form-textarea {
	width: 100%;
	height: 120rpx; /* 减小高度从160rpx到120rpx */
	padding: 20rpx 24rpx;
	background-color: #fff;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	border: 2rpx solid #e6e8eb;
	box-sizing: border-box;
}

/* 冲突警告样式 */
.input-conflict {
	border-color: #ff6b6b !important;
	background-color: #fff5f5 !important;
}
.conflict-warning {
	font-size: 24rpx;
	color: #ff6b6b;
	margin-top: 8rpx;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
}

/* 媒体类型选项卡 */
.media-type-tabs {
	display: flex;
	margin-bottom: 20rpx;
}
.media-tab {
	padding: 10rpx 30rpx;
	font-size: 26rpx;
	color: #666;
	background-color: #f0f2f5;
	margin-right: 20rpx;
	border-radius: 30rpx;
}
.media-tab.active {
	background-color: #8a2be2;
	color: #fff;
}

/* 图片上传网格 */
.image-grid {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -10rpx;
}
.image-item, .image-uploader {
	width: calc(33.33% - 20rpx);
	height: 200rpx;
	margin: 10rpx;
	border-radius: 8rpx;
	overflow: hidden;
	position: relative;
}
.thumbnail {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.delete-icon {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 40rpx;
	height: 40rpx;
	background: rgba(0, 0, 0, 0.5);
	color: #fff;
	border-radius: 50%;
	text-align: center;
	line-height: 36rpx;
	font-size: 32rpx;
	z-index: 2;
}
.image-uploader, .video-uploader, .audio-uploader {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	border: 2rpx dashed #ddd;
}
.upload-icon {
	font-size: 60rpx;
	color: #bbb;
}
.upload-text {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

/* 视频上传网格 */
.video-grid {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -10rpx;
}
.video-item {
	width: calc(50% - 20rpx);
	height: 320rpx;
	margin: 10rpx;
	border-radius: 8rpx;
	overflow: hidden;
	position: relative;
}
.video-thumbnail {
	width: 100%;
	height: 100%;
	object-fit: cover;
	background-color: #000;
}

/* 音频上传列表 */
.audio-list {
	margin-top: 20rpx;
}
.audio-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background-color: #fff;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
	border: 2rpx solid #e6e8eb;
}
.audio-info {
	display: flex;
	align-items: center;
}
.audio-icon {
	font-size: 40rpx;
	margin-right: 20rpx;
}
.audio-name {
	font-size: 28rpx;
	color: #333;
}
.audio-controls {
	display: flex;
}
.audio-play, .audio-delete {
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	border-radius: 30rpx;
	margin-left: 20rpx;
}
.audio-play {
	background-color: #f0f2f5;
	color: #666;
}
.audio-delete {
	background-color: #fff5f5;
	color: #f56c6c;
}
.audio-uploader {
	padding: 30rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	border: 2rpx dashed #ddd;
}

/* 日期选择区域 */
.date-section {
	display: flex;
	justify-content: space-between;
}
.date-item {
	width: 48%;
}
.date-picker {
	width: 100%;
}
.picker-value {
	height: 88rpx;
	padding: 0 20rpx;
	background-color: #fff;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	border: 2rpx solid #e6e8eb;
	display: flex;
	align-items: center;
	justify-content: center;
}
.picker-placeholder {
	color: #999;
}

/* 加载中提示 */
.no-groups {
	padding: 20rpx;
	text-align: center;
	font-size: 28rpx;
	color: #999;
}

/* 分组选择器 */
.group-container {
	margin-bottom: 30rpx;
	background-color: #fff;
	border-radius: 8rpx;
	padding: 20rpx 0;
}
.group-list-wrap {
	padding: 0 20rpx;
	display: flex;
	flex-wrap: wrap;
}
.group-item {
	height: 60rpx;
	padding: 0 30rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	background-color: #f0f0f0;
	border-radius: 30rpx;
	font-size: 28rpx;
	color: #666;
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
	box-sizing: border-box;
	position: relative;
}
.group-item:not(.add-group):active {
	opacity: 0.7;
	background-color: #e0e0e0;
}
.group-item.active {
	background-color: #8a2be2;
	color: #fff;
}
.group-item.add-group {
	background-color: #f0e6ff;
	border: 1px dashed #8a2be2;
	color: #8a2be2;
}
.add-icon-wrapper {
	display: flex;
	align-items: center;
	justify-content: center;
}
.add-icon {
	font-size: 28rpx;
	margin-right: 6rpx;
}
.add-text {
	font-size: 28rpx;
	line-height: 1;
}

/* 底部操作按钮 */
.action-footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-between;
	padding: 20rpx 30rpx 50rpx;
	background-color: #fff;
	border-top: 2rpx solid #f0f0f0;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.btn {
	width: 46%;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	font-weight: 500;
}
.btn-cancel {
	background-color: #f5f7fa;
	color: #666;
}
.btn-save {
	background-color: #8a2be2;
	color: #fff;
}

/* 权限选项 */
.permission-options {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}
.permission-option {
	flex: 1;
	margin: 0 10rpx;
	padding: 20rpx 10rpx;
	background-color: #f0f2f5;
	border-radius: 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.permission-option:first-child {
	margin-left: 0;
}
.permission-option:last-child {
	margin-right: 0;
}
.permission-option.active {
	background-color: #8a2be2;
	color: #fff;
}
.permission-icon {
	font-size: 40rpx;
	margin-bottom: 10rpx;
}
.permission-name {
	font-size: 24rpx;
}

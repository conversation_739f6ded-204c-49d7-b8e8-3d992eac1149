"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "HistoryWishCard",
  props: {
    wish: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    },
    activeCardId: {
      type: String,
      default: ""
    }
  },
  setup(props, { emit }) {
    const swiping = common_vendor.ref(false);
    const startX = common_vendor.ref(0);
    const moveX = common_vendor.ref(0);
    const startY = common_vendor.ref(0);
    const swipeRef = common_vendor.ref(null);
    const isOpen = common_vendor.ref(false);
    const buttonsWidth = 120;
    const hasValidImage = (image) => {
      if (!image)
        return false;
      if (typeof image === "string") {
        return image.trim() !== "";
      }
      if (Array.isArray(image)) {
        return image.length > 0 && image.some((img) => {
          if (typeof img === "string") {
            return img.trim() !== "";
          } else if (img && typeof img === "object" && img.url) {
            return img.url.trim() !== "";
          }
          return false;
        });
      }
      if (image && typeof image === "object" && image.url) {
        return image.url.trim() !== "";
      }
      return false;
    };
    const getImageUrl = (image) => {
      if (!image)
        return "";
      if (typeof image === "string") {
        return image;
      }
      if (Array.isArray(image) && image.length > 0) {
        const firstImage = image[0];
        if (typeof firstImage === "string") {
          return firstImage;
        } else if (firstImage && typeof firstImage === "object" && firstImage.url) {
          return firstImage.url;
        }
      }
      if (image && typeof image === "object" && image.url) {
        return image.url;
      }
      common_vendor.index.__f__("warn", "at components/HistoryWishCard/HistoryWishCard.vue:131", "[HistoryWishCard] 无法解析图片URL:", image);
      return "";
    };
    const getImageCount = (image) => {
      if (!image)
        return 0;
      if (typeof image === "string" && image.trim() !== "") {
        return 1;
      }
      if (Array.isArray(image)) {
        return image.filter((img) => {
          if (typeof img === "string") {
            return img.trim() !== "";
          } else if (img && typeof img === "object" && img.url) {
            return img.url.trim() !== "";
          }
          return false;
        }).length;
      }
      if (image && typeof image === "object" && image.url && image.url.trim() !== "") {
        return 1;
      }
      return 0;
    };
    const onImageLoad = (e) => {
      var _a, _b;
      const src = ((_a = e == null ? void 0 : e.target) == null ? void 0 : _a.src) || ((_b = e == null ? void 0 : e.detail) == null ? void 0 : _b.src) || "未知图片源";
      common_vendor.index.__f__("log", "at components/HistoryWishCard/HistoryWishCard.vue:164", "[HistoryWishCard] 图片加载成功:", src);
    };
    const onImageError = (e) => {
      var _a, _b;
      const src = ((_a = e == null ? void 0 : e.target) == null ? void 0 : _a.src) || ((_b = e == null ? void 0 : e.detail) == null ? void 0 : _b.src) || "未知图片源";
      common_vendor.index.__f__("error", "at components/HistoryWishCard/HistoryWishCard.vue:170", "[HistoryWishCard] 图片加载失败:", {
        src,
        error: e
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.$on("page-scroll-event", (data) => {
        if (isOpen.value && data && data.shouldClose) {
          resetSwipeWithClass();
        }
      });
      common_vendor.index.$on("onPageScroll", () => {
        if (isOpen.value) {
          resetSwipeWithClass();
        }
      });
      common_vendor.index.$on("force-close-cards", (data) => {
        if (isOpen.value) {
          resetSwipeWithClass();
        }
      });
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.$off("onPageScroll");
      common_vendor.index.$off("page-scroll-event");
      common_vendor.index.$off("force-close-cards");
    });
    const contentStyle = common_vendor.computed(() => {
      let x = moveX.value - startX.value;
      if (x > 0) {
        x = 0;
      }
      if (x < 0) {
        if (x < -buttonsWidth) {
          x = -buttonsWidth + (x + buttonsWidth) * 0.2;
        }
      }
      return {
        // 使用 translate3d 启用硬件加速
        transform: `translate3d(${x}px, 0, 0)`,
        transition: swiping.value ? "none" : "transform 0.25s cubic-bezier(0.3, 0.9, 0.3, 1)",
        // 性能优化
        willChange: swiping.value ? "transform" : "auto",
        backfaceVisibility: "hidden"
      };
    });
    const touchStart = (e) => {
      swiping.value = true;
      startX.value = e.touches[0].clientX;
      moveX.value = startX.value;
      startY.value = e.touches[0].clientY;
      if (props.activeCardId && props.activeCardId !== props.wish.id) {
        emit("card-swipe-start", props.wish.id);
      }
    };
    const touchMove = (e) => {
      if (!swiping.value)
        return;
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;
      const deltaY = Math.abs(currentY - startY.value);
      const deltaX = Math.abs(currentX - startX.value);
      if (deltaY > deltaX * 2 && deltaY > 15) {
        swiping.value = false;
        moveX.value = startX.value;
        if (isOpen.value) {
          resetSwipeWithClass();
          emit("card-scroll-detected", {
            cardId: props.wish.id,
            deltaY
          });
        }
        return;
      }
      if (currentX === moveX.value)
        return;
      moveX.value = currentX;
    };
    const touchEnd = (e) => {
      swiping.value = false;
      const distance = moveX.value - startX.value;
      if (distance < -buttonsWidth / 5) {
        moveX.value = startX.value - buttonsWidth;
        isOpen.value = true;
        emit("card-open", props.wish.id);
      } else {
        moveX.value = startX.value;
        isOpen.value = false;
        if (props.activeCardId === props.wish.id) {
          emit("card-close");
        }
      }
    };
    const resetSwipe = () => {
      moveX.value = startX.value;
      isOpen.value = false;
    };
    const resetSwipeWithClass = () => {
      moveX.value = startX.value;
      isOpen.value = false;
    };
    const handleButtonClick = (index) => {
      if (index === 0) {
        emit("restore", props.wish.id);
      } else if (index === 1) {
        emit("delete", props.wish.id);
      }
      moveX.value = startX.value;
      isOpen.value = false;
    };
    const formatDate = (date) => {
      if (!date)
        return "未知";
      try {
        let cleanDate = date;
        if (typeof cleanDate === "string" && cleanDate.includes("(已还原)")) {
          cleanDate = cleanDate.replace(/\s*\(已还原\)\s*/g, "").trim();
        }
        const d = new Date(cleanDate);
        if (isNaN(d.getTime())) {
          return typeof cleanDate === "string" ? cleanDate.replace(/\s*\(已还原\)\s*/g, "") : "未知";
        }
        const now = /* @__PURE__ */ new Date();
        const diffTime = now.getTime() - d.getTime();
        const diffDays = Math.floor(diffTime / (1e3 * 60 * 60 * 24));
        if (diffDays === 0) {
          return "今天";
        } else if (diffDays === 1) {
          return "昨天";
        } else if (diffDays < 7) {
          return `${diffDays}天前`;
        } else {
          return `${d.getMonth() + 1}月${d.getDate()}日`;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/HistoryWishCard/HistoryWishCard.vue:343", "日期格式化错误:", error);
        return typeof date === "string" ? date.replace(/\s*\(已还原\)\s*/g, "") : "未知";
      }
    };
    return {
      swipeRef,
      contentStyle,
      touchStart,
      touchMove,
      touchEnd,
      handleButtonClick,
      resetSwipe,
      isOpen,
      formatDate,
      hasValidImage,
      getImageUrl,
      getImageCount,
      onImageError,
      onImageLoad
    };
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($props.index + 1),
    b: common_vendor.t($props.wish.title),
    c: common_vendor.p({
      type: "checkmarkempty",
      size: "16",
      color: "#fff"
    }),
    d: $props.wish.description
  }, $props.wish.description ? {
    e: common_vendor.t($props.wish.description)
  } : {}, {
    f: $props.wish.image && $setup.hasValidImage($props.wish.image)
  }, $props.wish.image && $setup.hasValidImage($props.wish.image) ? common_vendor.e({
    g: $setup.getImageUrl($props.wish.image),
    h: common_vendor.o((...args) => $setup.onImageError && $setup.onImageError(...args)),
    i: common_vendor.o((...args) => $setup.onImageLoad && $setup.onImageLoad(...args)),
    j: $setup.getImageCount($props.wish.image) > 1
  }, $setup.getImageCount($props.wish.image) > 1 ? {
    k: common_vendor.t($setup.getImageCount($props.wish.image))
  } : {}) : {}, {
    l: common_vendor.t($setup.formatDate($props.wish.completeDate || $props.wish.lastCompleteDate)),
    m: common_vendor.s($setup.contentStyle),
    n: common_vendor.o(($event) => $setup.handleButtonClick(0)),
    o: common_vendor.o(($event) => $setup.handleButtonClick(1)),
    p: common_vendor.o((...args) => $setup.touchStart && $setup.touchStart(...args)),
    q: common_vendor.o((...args) => $setup.touchMove && $setup.touchMove(...args)),
    r: common_vendor.o((...args) => $setup.touchEnd && $setup.touchEnd(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/HistoryWishCard/HistoryWishCard.js.map

{"version": 3, "file": "editWish.js", "sources": ["subpkg-wish/pages/editWish/editWish.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGtnLXdpc2hccGFnZXNcZWRpdFdpc2hcZWRpdFdpc2gudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"page-container\">\r\n\t\t<!-- 标题输入 -->\r\n\t\t<view class=\"form-label\">标题</view>\r\n\t\t<input \r\n\t\t\tclass=\"form-input\" \r\n\t\t\t:class=\"{ 'input-conflict': titleConflictWarning }\"\r\n\t\t\ttype=\"text\" \r\n\t\t\tv-model=\"wishForm.title\" \r\n\t\t\tplaceholder=\"请输入心愿标题\" \r\n\t\t\t@input=\"onTitleInput\"\r\n\t\t/>\r\n\t\t<view v-if=\"titleConflictWarning\" class=\"conflict-warning\">\r\n\t\t\t⚠️ 已存在相同标题的心愿，请使用不同的标题\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 描述输入 -->\r\n\t\t<view class=\"form-label\">描述</view>\r\n\t\t<textarea class=\"form-textarea\" v-model=\"wishForm.description\" placeholder=\"请输入心愿描述\"></textarea>\r\n\t\t\r\n\t\t<!-- 多媒体上传区域 -->\r\n\t\t<view class=\"form-label\">图片/视频/音频</view>\r\n\t\t<view class=\"media-type-tabs\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"media-tab\" \r\n\t\t\t\t:class=\"{ active: currentMediaType === 'image' }\" \r\n\t\t\t\t@click=\"changeMediaType('image')\"\r\n\t\t\t>图片</view>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"media-tab\" \r\n\t\t\t\t:class=\"{ active: currentMediaType === 'video' }\" \r\n\t\t\t\t@click=\"changeMediaType('video')\"\r\n\t\t\t>视频</view>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"media-tab\" \r\n\t\t\t\t:class=\"{ active: currentMediaType === 'audio' }\" \r\n\t\t\t\t@click=\"changeMediaType('audio')\"\r\n\t\t\t>音频</view>\r\n\t\t</view>\r\n\t\t<!-- 图片上传 -->\r\n\t\t<view class=\"image-grid\" v-if=\"currentMediaType === 'image'\">\r\n\t\t\t<!-- 已上传图片展示区 -->\r\n\t\t\t<view class=\"image-item\" v-for=\"(img, index) in uploadedImages\" :key=\"index\" @click=\"previewImage(index)\">\r\n\t\t\t\t<image class=\"thumbnail\" :src=\"img\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"delete-icon\" @click.stop=\"deleteImage(index)\">×</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 添加图片按钮 -->\r\n\t\t\t<view class=\"image-uploader\" @click=\"chooseImage\" v-if=\"uploadedImages.length < 9\">\r\n\t\t\t\t<text class=\"upload-icon\">+</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 视频上传 -->\r\n\t\t<view class=\"video-grid\" v-if=\"currentMediaType === 'video'\">\r\n\t\t\t<!-- 已上传视频展示区 -->\r\n\t\t\t<view class=\"video-item\" v-for=\"(video, index) in uploadedVideos\" :key=\"index\">\r\n\t\t\t\t<video \r\n\t\t\t\t\tclass=\"video-thumbnail\" \r\n\t\t\t\t\t:src=\"video\" \r\n\t\t\t\t\tcontrols\r\n\t\t\t\t\tposter=\"/static/video-poster.png\"\r\n\t\t\t\t></video>\r\n\t\t\t\t<view class=\"delete-icon\" @click.stop=\"deleteVideo(index)\">×</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 添加视频按钮 -->\r\n\t\t\t<view class=\"video-uploader\" @click=\"chooseVideo\" v-if=\"uploadedVideos.length < 2\">\r\n\t\t\t\t<text class=\"upload-icon\">+</text>\r\n\t\t\t\t<text class=\"upload-text\">添加视频</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 音频上传 -->\r\n\t\t<view class=\"audio-list\" v-if=\"currentMediaType === 'audio'\">\r\n\t\t\t<!-- 已上传音频展示区 -->\r\n\t\t\t<view class=\"audio-item\" v-for=\"(audio, index) in uploadedAudios\" :key=\"index\">\r\n\t\t\t\t<view class=\"audio-info\">\r\n\t\t\t\t\t<view class=\"audio-icon\">🎵</view>\r\n\t\t\t\t\t<view class=\"audio-name\">音频 {{index + 1}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"audio-controls\">\r\n\t\t\t\t\t<view class=\"audio-play\" @click=\"playAudio(audio)\">播放</view>\r\n\t\t\t\t\t<view class=\"audio-delete\" @click=\"deleteAudio(index)\">删除</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 添加音频按钮 -->\r\n\t\t\t<view class=\"audio-uploader\" @click=\"chooseAudio\" v-if=\"uploadedAudios.length < 3\">\r\n\t\t\t\t<text class=\"upload-icon\">+</text>\r\n\t\t\t\t<text class=\"upload-text\">添加音频</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 心愿起止时间 -->\r\n\t\t<view class=\"form-label\">心愿起止时间</view>\r\n\t\t<view class=\"date-section\">\r\n\t\t\t<view class=\"date-item\">\r\n\t\t\t\t<picker mode=\"date\" :value=\"formatPickerDate(wishForm.startDate)\" @change=\"onStartDateChange\" class=\"date-picker\">\r\n\t\t\t\t\t<view class=\"picker-value\">\r\n\t\t\t\t\t\t<text class=\"picker-placeholder\" v-if=\"!wishForm.startDate\">开始时间</text>\r\n\t\t\t\t\t\t<text v-else>{{formatDate(wishForm.startDate)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"date-item\">\r\n\t\t\t\t<picker mode=\"date\" :value=\"formatPickerDate(wishForm.completeDate)\" @change=\"onCompleteDateChange\" class=\"date-picker\">\r\n\t\t\t\t\t<view class=\"picker-value\">\r\n\t\t\t\t\t\t<text class=\"picker-placeholder\" v-if=\"!wishForm.completeDate\">完成时间</text>\r\n\t\t\t\t\t\t<text v-else>{{formatDate(wishForm.completeDate)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 权限设置 -->\r\n\t\t<view class=\"form-label\">权限设置</view>\r\n\t\t<view class=\"permission-options\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"permission-option\" \r\n\t\t\t\t:class=\"{ active: wishForm.permission === 'private' }\"\r\n\t\t\t\t@click=\"setPermission('private')\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"permission-icon\">🔒</view>\r\n\t\t\t\t<view class=\"permission-name\">私密</view>\r\n\t\t\t</view>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"permission-option\" \r\n\t\t\t\t:class=\"{ active: wishForm.permission === 'friends' }\"\r\n\t\t\t\t@click=\"setPermission('friends')\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"permission-icon\">👥</view>\r\n\t\t\t\t<view class=\"permission-name\">朋友可见</view>\r\n\t\t\t</view>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"permission-option\" \r\n\t\t\t\t:class=\"{ active: wishForm.permission === 'public' }\"\r\n\t\t\t\t@click=\"setPermission('public')\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"permission-icon\">🌍</view>\r\n\t\t\t\t<view class=\"permission-name\">公开</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 分组选择 - 纵向布局 -->\r\n\t\t<view class=\"form-label\">分组</view>\r\n\t\t<view class=\"group-container\">\r\n\t\t\t<view v-if=\"!availableGroups || availableGroups.length === 0\" class=\"no-groups\">\r\n\t\t\t\t<text>加载分组数据中...</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"group-list-wrap\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"(group, index) in availableGroups\" \r\n\t\t\t\t\t:key=\"group.id\"\r\n\t\t\t\t\tclass=\"group-item\"\r\n\t\t\t\t\t:class=\"{ active: isGroupSelected(group.id) }\"\r\n\t\t\t\t\t@click=\"toggleGroup(group.id)\"\r\n\t\t\t\t\t@longpress=\"showGroupActionSheet(group)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{ group.name }}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"group-item add-group\" @click=\"showAddGroupDialog\">\r\n\t\t\t\t\t<view class=\"add-icon-wrapper\">\r\n\t\t\t\t\t\t<text class=\"add-icon\">+</text>\r\n\t\t\t\t\t\t<text class=\"add-text\">添加</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"action-footer\">\r\n\t\t\t<button class=\"btn btn-cancel\" @click=\"goBack\">取消</button>\r\n\t\t\t<button class=\"btn btn-save\" @click=\"saveWish\">保存</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { useWishStore } from '@/store/wish.js'\r\n\timport { useGroupStore } from '@/store/group.js'\r\n\timport { useUserStore } from '@/store/user.js'\r\n\timport { authService } from '@/services/authService.js'\r\n\timport groupTagOperations from '@/mixins/groupTagOperations.js'\r\n\t\r\n\texport default {\r\n\t\tmixins: [groupTagOperations],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\twishId: null,\r\n\t\t\t\tisEdit: false,\r\n\t\t\t\twishForm: {\r\n\t\t\t\t\ttitle: '',\r\n\t\t\t\t\tdescription: '',\r\n\t\t\t\t\timage: [],\r\n\t\t\t\t\tvideo: [],\r\n\t\t\t\t\taudio: [],\r\n\t\t\t\t\tisCompleted: false,\r\n\t\t\t\t\tstartDate: '',\r\n\t\t\t\t\tcompleteDate: null,\r\n\t\t\t\t\tgroupIds: ['all'],\r\n\t\t\t\t\tpermission: 'private'\r\n\t\t\t\t},\r\n\t\t\t\tcurrentMediaType: 'image', // image, video, audio\r\n\t\t\t\tuploadedImages: [],\r\n\t\t\t\tuploadedVideos: [],\r\n\t\t\t\tuploadedAudios: [],\r\n\t\t\t\tgroupStore: null,\r\n\t\t\t\twishStore: null,\r\n\t\t\t\tuserStore: null,\r\n\t\t\t\tnewGroupName: '',\r\n\t\t\t\t// 标题冲突警告\r\n\t\t\t\ttitleConflictWarning: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tavailableGroups() {\r\n\t\t\t\t// 获取所有分组\r\n\t\t\t\treturn this.groupStore ? this.groupStore.getAllGroups : []\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tconsole.log('Component created, initializing stores');\r\n\t\t\t// 尝试初始化store\r\n\t\t\tthis.initStores();\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// 初始化store\r\n\t\t\tconsole.log('onLoad triggered, initializing stores');\r\n\t\t\tthis.initStores()\r\n\t\t\t\r\n\t\t\t// 如果有ID参数，表示编辑现有心愿\r\n\t\t\tif (options && options.id) {\r\n\t\t\t\tthis.wishId = options.id\r\n\t\t\t\tthis.isEdit = true\r\n\t\t\t\tthis.loadWishData()\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查是否有备份的编辑数据需要恢复\r\n\t\t\tthis.restoreTempDataIfExists();\r\n\t\t\t\r\n\t\t\t// 处理传入的分组ID，自动将心愿添加到指定分组\r\n\t\t\tif (options && options.groupId && options.groupId !== 'all') {\r\n\t\t\t\tconsole.log('接收到分组ID:', options.groupId);\r\n\t\t\t\t\r\n\t\t\t\t// 确保groupIds数组已初始化\r\n\t\t\t\tif (!this.wishForm.groupIds) {\r\n\t\t\t\t\tthis.wishForm.groupIds = ['all'];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果传入的分组ID不是'all'且不在当前分组列表中，则添加\r\n\t\t\t\tif (!this.wishForm.groupIds.includes(options.groupId)) {\r\n\t\t\t\t\tthis.wishForm.groupIds.push(options.groupId);\r\n\t\t\t\t\tconsole.log('已将心愿分配到分组:', options.groupId);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 特殊处理：如果是朋友可见分组，自动设置权限为朋友可见\r\n\t\t\t\tif (options.groupId === 'friend-visible') {\r\n\t\t\t\t\tthis.wishForm.permission = 'friends';\r\n\t\t\t\t\tconsole.log('自动设置权限为朋友可见');\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// 页面显示时检查登录状态变化\r\n\t\t\tif (this.userStore && this.userStore.isLogin) {\r\n\t\t\t\t// 如果用户已登录，重新初始化需要认证的store\r\n\t\t\t\tif (this.groupStore && (!this.groupStore.getAllGroups || this.groupStore.getAllGroups.length === 0)) {\r\n\t\t\t\t\tconsole.log('[editWish] User logged in, reinitializing group store...');\r\n\t\t\t\t\tthis.groupStore.initGroups();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否有备份的编辑数据需要恢复\r\n\t\t\t\tthis.restoreTempDataIfExists();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t// 确保group store被初始化，如果没有则再次尝试\r\n\t\t\tif (!this.groupStore) {\r\n\t\t\t\tconsole.log('Group store not initialized, trying again...')\r\n\t\t\t\tthis.initStores()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 切换媒体类型\r\n\t\t\tchangeMediaType(type) {\r\n\t\t\t\tthis.currentMediaType = type\r\n\t\t\t},\r\n\r\n\t\t\t// 🔧 新增：上传文件到云存储\r\n\t\t\tasync uploadFilesToCloud(files, fileType = 'image') {\r\n\t\t\t\tif (!files || files.length === 0) {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst uploadedUrls = [];\r\n\t\t\t\tconst userId = this.userStore.userId;\r\n\r\n\t\t\t\tif (!userId) {\r\n\t\t\t\t\tthrow new Error('用户未登录，无法上传文件');\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfor (let i = 0; i < files.length; i++) {\r\n\t\t\t\t\tconst filePath = files[i];\r\n\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t// 生成云存储路径\r\n\t\t\t\t\t\tconst timestamp = Date.now();\r\n\t\t\t\t\t\tconst randomStr = Math.random().toString(36).substring(2, 8);\r\n\t\t\t\t\t\tconst fileExtension = filePath.split('.').pop() || (fileType === 'image' ? 'jpg' : 'mp4');\r\n\t\t\t\t\t\tconst cloudPath = `wish_files/${userId}/${fileType}s/${timestamp}_${randomStr}.${fileExtension}`;\r\n\r\n\t\t\t\t\t\tconsole.log(`[editWish.vue] 开始上传文件 ${i + 1}/${files.length}:`, {\r\n\t\t\t\t\t\t\tfilePath,\r\n\t\t\t\t\t\t\tcloudPath,\r\n\t\t\t\t\t\t\tfileType\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 上传到云存储\r\n\t\t\t\t\t\tconst uploadResult = await uniCloud.uploadFile({\r\n\t\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\t\tcloudPath: cloudPath,\r\n\t\t\t\t\t\t\tonUploadProgress: (progressEvent) => {\r\n\t\t\t\t\t\t\t\tconst progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);\r\n\t\t\t\t\t\t\t\tconsole.log(`[editWish.vue] 文件上传进度: ${progress}%`);\r\n\r\n\t\t\t\t\t\t\t\t// 更新上传进度显示\r\n\t\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\t\ttitle: `上传中 ${i + 1}/${files.length} (${progress}%)`\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tif (uploadResult.fileID) {\r\n\t\t\t\t\t\t\tuploadedUrls.push(uploadResult.fileID);\r\n\t\t\t\t\t\t\tconsole.log(`[editWish.vue] 文件上传成功:`, uploadResult.fileID);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.error(`[editWish.vue] 文件上传失败，未返回fileID:`, uploadResult);\r\n\t\t\t\t\t\t\tthrow new Error(`第${i + 1}个文件上传失败`);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error(`[editWish.vue] 文件上传失败:`, error);\r\n\t\t\t\t\t\tthrow new Error(`第${i + 1}个文件上传失败: ${error.message}`);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log(`[editWish.vue] 所有文件上传完成:`, uploadedUrls);\r\n\t\t\t\treturn uploadedUrls;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 初始化stores\r\n\t\t\tinitStores() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.wishStore = useWishStore()\r\n\t\t\t\t\tthis.groupStore = useGroupStore()\r\n\t\t\t\t\tthis.userStore = useUserStore()\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保分组数据被加载\r\n\t\t\t\t\tif (this.groupStore && !this.groupStore.getAllGroups.length && this.userStore.isLogin) {\r\n\t\t\t\t\t\tthis.groupStore.initGroups()\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('Failed to initialize stores:', e)\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '初始化失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 恢复临时保存的编辑数据\r\n\t\t\trestoreTempDataIfExists() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst tempData = uni.getStorageSync('editWish_tempData');\r\n\t\t\t\t\tif (tempData) {\r\n\t\t\t\t\t\tconsole.log('[editWish] 发现备份的编辑数据:', tempData);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查备份数据的时效性（24小时内有效）\r\n\t\t\t\t\t\tconst timeDiff = Date.now() - (tempData.timestamp || 0);\r\n\t\t\t\t\t\tif (timeDiff > 24 * 60 * 60 * 1000) {\r\n\t\t\t\t\t\t\tconsole.log('[editWish] 备份数据已过期，清除');\r\n\t\t\t\t\t\t\tuni.removeStorageSync('editWish_tempData');\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 恢复编辑数据\r\n\t\t\t\t\t\tthis.wishForm = tempData.wishForm;\r\n\t\t\t\t\t\tthis.uploadedImages = tempData.uploadedImages || [];\r\n\t\t\t\t\t\tthis.uploadedVideos = tempData.uploadedVideos || [];\r\n\t\t\t\t\t\tthis.uploadedAudios = tempData.uploadedAudios || [];\r\n\t\t\t\t\t\tthis.currentMediaType = tempData.currentMediaType || 'image';\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 确保朋友可见分组和权限设置的一致性\r\n\t\t\t\t\t\tthis.ensurePermissionGroupConsistency();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果是编辑模式，确保ID匹配\r\n\t\t\t\t\t\tif (tempData.isEdit && tempData.wishId) {\r\n\t\t\t\t\t\t\tthis.isEdit = tempData.isEdit;\r\n\t\t\t\t\t\t\tthis.wishId = tempData.wishId;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 清除备份数据\r\n\t\t\t\t\t\tuni.removeStorageSync('editWish_tempData');\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log('[editWish] 编辑数据恢复成功');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('[editWish] 恢复编辑数据失败:', error);\r\n\t\t\t\t\t// 清除可能损坏的数据\r\n\t\t\t\t\tuni.removeStorageSync('editWish_tempData');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 标题输入事件处理\r\n\t\t\tonTitleInput(e) {\r\n\t\t\t\t// 实时检测标题冲突（仅显示警告，不阻止输入）\r\n\t\t\t\tthis.titleConflictWarning = this.checkTitleConflictQuiet();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 检测标题冲突的核心逻辑（提取公共部分）\r\n\t\t\t_findConflictWish(title) {\r\n\t\t\t\tif (!this.wishStore || !title) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst currentTitle = title.trim();\r\n\t\t\t\tif (!currentTitle) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 获取所有现有的心愿（包括已完成和未完成的）\r\n\t\t\t\tconst allWishes = this.wishStore.wishList || [];\r\n\r\n\t\t\t\t// 查找标题完全一致的心愿\r\n\t\t\t\treturn allWishes.find(wish => {\r\n\t\t\t\t\t// 排除被删除的心愿\r\n\t\t\t\t\tif (wish._deleted) {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 如果是编辑模式，排除当前正在编辑的心愿\r\n\t\t\t\t\tif (this.isEdit && (wish._id === this.wishId || wish.id === this.wishId)) {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 比较标题（忽略大小写和前后空格）\r\n\t\t\t\t\tconst wishTitle = (wish.title || '').trim();\r\n\t\t\t\t\treturn wishTitle.toLowerCase() === currentTitle.toLowerCase();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 静默检测标题冲突（不显示弹窗）\r\n\t\t\tcheckTitleConflictQuiet() {\r\n\t\t\t\tconst conflictWish = this._findConflictWish(this.wishForm.title);\r\n\t\t\t\treturn !!conflictWish;\r\n\t\t\t},\r\n\r\n\t\t\t// 检测标题冲突（显示弹窗提示）\r\n\t\t\tcheckTitleConflict() {\r\n\t\t\t\tconst conflictWish = this._findConflictWish(this.wishForm.title);\r\n\r\n\t\t\t\tif (conflictWish) {\r\n\t\t\t\t\t// 显示冲突提示\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '标题冲突',\r\n\t\t\t\t\t\tcontent: `已存在相同标题的心愿：\"${conflictWish.title}\"，请使用不同的标题。`,\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '知道了',\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t// 可以选择聚焦到标题输入框\r\n\t\t\t\t\t\t\tconsole.log('[editWish] 标题冲突，用户已确认');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log('[editWish] 检测到标题冲突:', {\r\n\t\t\t\t\t\tnewTitle: this.wishForm.title.trim(),\r\n\t\t\t\t\t\tconflictWish: conflictWish\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\treturn true; // 有冲突\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn false; // 无冲突\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载心愿数据\r\n\t\t\tloadWishData() {\r\n\t\t\t\tif (!this.wishStore) return\r\n\t\t\t\t\r\n\t\t\t\tconst wish = this.wishStore.getWishById(this.wishId)\r\n\t\t\t\tif (wish) {\r\n\t\t\t\t\tthis.wishForm = {\r\n\t\t\t\t\t\ttitle: wish.title || '',\r\n\t\t\t\t\t\tdescription: wish.description || '',\r\n\t\t\t\t\t\timage: Array.isArray(wish.image) ? [...wish.image] : (wish.image ? [wish.image] : []),\r\n\t\t\t\t\t\tvideo: Array.isArray(wish.video) ? [...wish.video] : (wish.video ? [wish.video] : []),\r\n\t\t\t\t\t\taudio: Array.isArray(wish.audio) ? [...wish.audio] : (wish.audio ? [wish.audio] : []),\r\n\t\t\t\t\t\tisCompleted: !!wish.isCompleted,\r\n\t\t\t\t\t\tstartDate: wish.startDate || '',\r\n\t\t\t\t\t\tcompleteDate: wish.completeDate || null,\r\n\t\t\t\t\t\tgroupIds: wish.groupIds ? [...wish.groupIds] : ['all'],\r\n\t\t\t\t\t\tpermission: wish.permission || 'private'\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保朋友可见分组和权限设置的一致性\r\n\t\t\t\t\tthis.ensurePermissionGroupConsistency();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 同步多媒体展示数据\r\n\t\t\t\t\tthis.uploadedImages = [...this.wishForm.image]\r\n\t\t\t\t\tthis.uploadedVideos = [...this.wishForm.video] \r\n\t\t\t\t\tthis.uploadedAudios = [...this.wishForm.audio]\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '心愿不存在',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.safeNavigateBack()\r\n\t\t\t\t\t}, 1500)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 验证文件\r\n\t\t\tvalidateFile(file, type) {\r\n\t\t\t\t// 文件大小限制 (MB)\r\n\t\t\t\tconst maxSizes = {\r\n\t\t\t\t\timage: 5, // 5MB\r\n\t\t\t\t\tvideo: 50, // 50MB  \r\n\t\t\t\t\taudio: 20 // 20MB\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 支持的文件格式\r\n\t\t\t\tconst allowedTypes = {\r\n\t\t\t\t\timage: ['jpg', 'jpeg', 'png', 'webp', 'gif'],\r\n\t\t\t\t\tvideo: ['mp4', 'avi', 'mov', '3gp'],\r\n\t\t\t\t\taudio: ['mp3', 'wav', 'aac', 'm4a']\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取文件扩展名\r\n\t\t\t\tconst getFileExt = (path) => {\r\n\t\t\t\t\tconst parts = path.split('.')\r\n\t\t\t\t\treturn parts.length > 1 ? parts.pop().toLowerCase() : ''\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查文件大小\r\n\t\t\t\tif (file.size && file.size > maxSizes[type] * 1024 * 1024) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: `${type === 'image' ? '图片' : type === 'video' ? '视频' : '音频'}大小不能超过${maxSizes[type]}MB`,\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查文件格式\r\n\t\t\t\tconst ext = getFileExt(file.path || file.tempFilePath || '')\r\n\t\t\t\tif (ext && !allowedTypes[type].includes(ext)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: `不支持的${type === 'image' ? '图片' : type === 'video' ? '视频' : '音频'}格式`,\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\treturn true\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择图片\r\n\t\t\tchooseImage() {\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 9 - this.uploadedImages.length,\r\n\t\t\t\t\tsizeType: ['compressed'],\r\n\t\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res && res.tempFilePaths && res.tempFilePaths.length > 0) {\r\n\t\t\t\t\t\t\t// 验证每个文件\r\n\t\t\t\t\t\t\tconst validFiles = res.tempFilePaths.filter((path, index) => {\r\n\t\t\t\t\t\t\t\tconst file = res.tempFiles ? res.tempFiles[index] : { path, tempFilePath: path }\r\n\t\t\t\t\t\t\t\treturn this.validateFile(file, 'image')\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (validFiles.length > 0) {\r\n\t\t\t\t\t\t\t\tthis.uploadedImages = [...this.uploadedImages, ...validFiles]\r\n\t\t\t\t\t\t\t\t// 同步到wishForm\r\n\t\t\t\t\t\t\t\tthis.wishForm.image = [...this.uploadedImages]\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tif (validFiles.length < res.tempFilePaths.length) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: `已添加${validFiles.length}张图片，${res.tempFilePaths.length - validFiles.length}张图片验证失败`,\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tconsole.error('选择图片失败:', error)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '选择图片失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择视频\r\n\t\t\tchooseVideo() {\r\n\t\t\t\tuni.chooseVideo({\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\t\tmaxDuration: 60,\r\n\t\t\t\t\tcamera: 'back',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res && res.tempFilePath) {\r\n\t\t\t\t\t\t\t// 验证文件\r\n\t\t\t\t\t\t\tconst file = { \r\n\t\t\t\t\t\t\t\tpath: res.tempFilePath, \r\n\t\t\t\t\t\t\t\ttempFilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\t\tsize: res.size \r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (this.validateFile(file, 'video')) {\r\n\t\t\t\t\t\t\t\tthis.uploadedVideos = [...this.uploadedVideos, res.tempFilePath]\r\n\t\t\t\t\t\t\t\t// 同步到wishForm\r\n\t\t\t\t\t\t\t\tthis.wishForm.video = [...this.uploadedVideos]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tconsole.error('选择视频失败:', error)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '选择视频失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择音频\r\n\t\t\tchooseAudio() {\r\n\t\t\t\t// 微信小程序暂不支持直接选择音频，这里使用文件选择器\r\n\t\t\t\tuni.chooseFile({\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\ttype: 'all',\r\n\t\t\t\t\textension: ['.mp3', '.wav', '.aac'],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res && res.tempFilePaths && res.tempFilePaths.length > 0) {\r\n\t\t\t\t\t\t\t// 验证文件\r\n\t\t\t\t\t\t\tconst file = { \r\n\t\t\t\t\t\t\t\tpath: res.tempFilePaths[0], \r\n\t\t\t\t\t\t\t\ttempFilePath: res.tempFilePaths[0],\r\n\t\t\t\t\t\t\t\tsize: res.tempFiles ? res.tempFiles[0].size : 0\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (this.validateFile(file, 'audio')) {\r\n\t\t\t\t\t\t\t\tthis.uploadedAudios = [...this.uploadedAudios, res.tempFilePaths[0]]\r\n\t\t\t\t\t\t\t\t// 同步到wishForm\r\n\t\t\t\t\t\t\t\tthis.wishForm.audio = [...this.uploadedAudios]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tconsole.error('选择音频失败:', error)\r\n\t\t\t\t\t\t// 如果不支持chooseFile，提供反馈\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '当前平台不支持音频选择',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 播放音频\r\n\t\t\tplayAudio(src) {\r\n\t\t\t\tconst audioContext = uni.createInnerAudioContext()\r\n\t\t\t\taudioContext.src = src\r\n\t\t\t\taudioContext.play()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 预览图片\r\n\t\t\tpreviewImage(index) {\r\n\t\t\t\tif (this.uploadedImages.length > 0) {\r\n\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\tcurrent: this.uploadedImages[index],\r\n\t\t\t\t\t\turls: this.uploadedImages\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除图片\r\n\t\t\tdeleteImage(index) {\r\n\t\t\t\tthis.uploadedImages.splice(index, 1)\r\n\t\t\t\t// 同步到wishForm\r\n\t\t\t\tthis.wishForm.image = [...this.uploadedImages]\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除视频\r\n\t\t\tdeleteVideo(index) {\r\n\t\t\t\tthis.uploadedVideos.splice(index, 1)\r\n\t\t\t\t// 同步到wishForm\r\n\t\t\t\tthis.wishForm.video = [...this.uploadedVideos]\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除音频\r\n\t\t\tdeleteAudio(index) {\r\n\t\t\t\tthis.uploadedAudios.splice(index, 1)\r\n\t\t\t\t// 同步到wishForm\r\n\t\t\t\tthis.wishForm.audio = [...this.uploadedAudios]\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 检查分组是否被选中\r\n\t\t\tisGroupSelected(groupId) {\r\n\t\t\t\tif (!this.wishForm.groupIds) {\r\n\t\t\t\t\treturn groupId === 'all';\r\n\t\t\t\t}\r\n\t\t\t\treturn this.wishForm.groupIds.indexOf(groupId) > -1;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确保朋友可见权限和分组标签的一致性\r\n\t\t\tensurePermissionGroupConsistency() {\r\n\t\t\t\t// 确保groupIds数组已初始化\r\n\t\t\t\tif (!this.wishForm.groupIds) {\r\n\t\t\t\t\tthis.wishForm.groupIds = ['all'];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst hasFriendVisibleGroup = this.wishForm.groupIds.includes('friend-visible');\r\n\t\t\t\tconst hasFriendPermission = this.wishForm.permission === 'friends';\r\n\t\t\t\t\r\n\t\t\t\t// 如果权限是朋友可见但不在朋友可见分组中，自动添加到分组\r\n\t\t\t\tif (hasFriendPermission && !hasFriendVisibleGroup) {\r\n\t\t\t\t\tthis.wishForm.groupIds.push('friend-visible');\r\n\t\t\t\t\tconsole.log('自动添加到朋友可见分组（一致性修复）');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果在朋友可见分组中但权限不是朋友可见，自动设置权限\r\n\t\t\t\tif (hasFriendVisibleGroup && !hasFriendPermission) {\r\n\t\t\t\t\tthis.wishForm.permission = 'friends';\r\n\t\t\t\t\tconsole.log('自动设置权限为朋友可见（一致性修复）');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 切换分组选择状态\r\n\t\t\ttoggleGroup(groupId) {\r\n\t\t\t\t// 确保groupIds数组已初始化\r\n\t\t\t\tif (!this.wishForm.groupIds) {\r\n\t\t\t\t\tthis.wishForm.groupIds = ['all']\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// \"全部\"分组是默认分组，不能取消选择\r\n\t\t\t\tif (groupId === 'all') {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst index = this.wishForm.groupIds.indexOf(groupId)\r\n\t\t\t\tif (index > -1) {\r\n\t\t\t\t\t// 已选中，需要取消选择\r\n\t\t\t\t\tthis.wishForm.groupIds.splice(index, 1)\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 特殊处理：如果取消选择朋友可见分组，且当前权限是朋友可见，则重置权限为私密\r\n\t\t\t\t\tif (groupId === 'friend-visible' && this.wishForm.permission === 'friends') {\r\n\t\t\t\t\t\tthis.wishForm.permission = 'private';\r\n\t\t\t\t\t\tconsole.log('取消朋友可见分组，权限自动设置为私密');\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 未选中，添加到选中列表\r\n\t\t\t\t\tthis.wishForm.groupIds.push(groupId)\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 特殊处理：如果选择朋友可见分组，自动设置权限为朋友可见\r\n\t\t\t\t\tif (groupId === 'friend-visible') {\r\n\t\t\t\t\t\tthis.wishForm.permission = 'friends';\r\n\t\t\t\t\t\tconsole.log('选择朋友可见分组，权限自动设置为朋友可见');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('当前选中的分组:', this.wishForm.groupIds, '，当前权限:', this.wishForm.permission)\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 表单验证\r\n\t\t\tvalidateForm() {\r\n\t\t\t\tif (!this.wishForm.title || !this.wishForm.title.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入心愿标题',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\treturn true\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理开始日期选择\r\n\t\t\tonStartDateChange(e) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst dateStr = e.detail.value;\r\n\t\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\t\tif (!isNaN(date.getTime())) {\r\n\t\t\t\t\t\tthis.wishForm.startDate = date.toISOString();\r\n\t\t\t\t\t\tconsole.log('开始时间设置为:', this.wishForm.startDate);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('无法解析日期:', dateStr);\r\n\t\t\t\t\t\tthis.wishForm.startDate = dateStr;\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('日期处理错误:', error);\r\n\t\t\t\t\tthis.wishForm.startDate = e.detail.value;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理完成日期选择\r\n\t\t\tonCompleteDateChange(e) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst dateStr = e.detail.value;\r\n\t\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\t\tif (!isNaN(date.getTime())) {\r\n\t\t\t\t\t\tthis.wishForm.completeDate = date.toISOString();\r\n\t\t\t\t\t\tconsole.log('完成时间设置为:', this.wishForm.completeDate);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('无法解析日期:', dateStr);\r\n\t\t\t\t\t\tthis.wishForm.completeDate = dateStr;\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('日期处理错误:', error);\r\n\t\t\t\t\tthis.wishForm.completeDate = e.detail.value;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 保存心愿\r\n\t\t\tasync saveWish() {\r\n\t\t\t\t// 如果已经登录，直接保存\r\n\t\t\t\tif (this.userStore.isLogin) {\r\n\t\t\t\t\tawait this._actualSaveWish();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 未登录时，先备份当前编辑的数据到本地存储\r\n\t\t\t\tconst tempFormData = {\r\n\t\t\t\t\twishForm: JSON.parse(JSON.stringify(this.wishForm)),\r\n\t\t\t\t\tuploadedImages: [...this.uploadedImages],\r\n\t\t\t\t\tuploadedVideos: [...this.uploadedVideos],\r\n\t\t\t\t\tuploadedAudios: [...this.uploadedAudios],\r\n\t\t\t\t\tcurrentMediaType: this.currentMediaType,\r\n\t\t\t\t\tisEdit: this.isEdit,\r\n\t\t\t\t\twishId: this.wishId,\r\n\t\t\t\t\ttimestamp: Date.now()\r\n\t\t\t\t};\r\n\t\t\t\tuni.setStorageSync('editWish_tempData', tempFormData);\r\n\t\t\t\tconsole.log('[editWish] 备份编辑数据到本地存储:', tempFormData);\r\n\t\t\t\t\r\n\t\t\t\tawait authService.ensureAuthenticated({\r\n\t\t\t\t\t// 不传递 onAuthenticated，让登录后回到当前页面\r\n\t\t\t\t\tpromptMessage: '保存心愿需要登录，是否继续？',\r\n\t\t\t\t\tonCancelled: () => {\r\n\t\t\t\t\t\tconsole.log('[editWish.vue saveWish] 用户取消登录，未保存。');\r\n\t\t\t\t\t\t// 取消时清除备份数据\r\n\t\t\t\t\t\tuni.removeStorageSync('editWish_tempData');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tasync _actualSaveWish() {\r\n\t\t\t\t// 校验标题是否为空\r\n\t\t\t\tif (!this.wishForm.title.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '标题不能为空',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检测标题冲突\r\n\t\t\t\tif (this.checkTitleConflict()) {\r\n\t\t\t\t\treturn; // 如果有冲突，直接返回，不继续保存\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 🔧 修复图片数据结构问题 - 确保保存的是字符串数组而不是对象数组\r\n\t\t\t\tlet processedImages = [];\r\n\t\t\t\tlet processedVideos = [];\r\n\t\t\t\tlet processedAudios = [];\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.showLoading({ title: '正在保存...' });\r\n\r\n\t\t\t\t\t// 🔧 处理图片：上传到云存储\r\n\t\t\t\t\tif (this.uploadedImages.length > 0) {\r\n\t\t\t\t\t\tconsole.log(\"[editWish.vue] 开始上传图片到云存储，数量:\", this.uploadedImages.length);\r\n\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t// 过滤出需要上传的本地文件（临时路径）\r\n\t\t\t\t\t\t\tconst localImages = this.uploadedImages.filter(path => {\r\n\t\t\t\t\t\t\t\treturn typeof path === 'string' && (\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('wxfile://') ||\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('file://') ||\r\n\t\t\t\t\t\t\t\t\tpath.includes('tmp_') ||\r\n\t\t\t\t\t\t\t\t\tpath.includes('temp')\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t// 过滤出已经是云存储URL的文件（编辑模式下可能存在）\r\n\t\t\t\t\t\t\tconst cloudImages = this.uploadedImages.filter(path => {\r\n\t\t\t\t\t\t\t\treturn typeof path === 'string' && (\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('cloud://') ||\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('https://')\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\tconsole.log(\"[editWish.vue] 本地图片:\", localImages);\r\n\t\t\t\t\t\t\tconsole.log(\"[editWish.vue] 云端图片:\", cloudImages);\r\n\r\n\t\t\t\t\t\t\t// 上传本地图片到云存储\r\n\t\t\t\t\t\t\tif (localImages.length > 0) {\r\n\t\t\t\t\t\t\t\tconst uploadedUrls = await this.uploadFilesToCloud(localImages, 'image');\r\n\t\t\t\t\t\t\t\tprocessedImages = [...cloudImages, ...uploadedUrls];\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tprocessedImages = cloudImages;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tconsole.log(\"[editWish.vue] 图片处理完成:\", processedImages);\r\n\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error(\"[editWish.vue] 图片上传失败:\", error);\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: `图片上传失败: ${error.message}`,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\treturn; // 上传失败则不继续保存\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 🔧 处理视频：上传到云存储\r\n\t\t\t\t\tif (this.uploadedVideos.length > 0) {\r\n\t\t\t\t\t\tconsole.log(\"[editWish.vue] 开始上传视频到云存储，数量:\", this.uploadedVideos.length);\r\n\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst localVideos = this.uploadedVideos.filter(path => {\r\n\t\t\t\t\t\t\t\treturn typeof path === 'string' && (\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('wxfile://') ||\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('file://') ||\r\n\t\t\t\t\t\t\t\t\tpath.includes('tmp_') ||\r\n\t\t\t\t\t\t\t\t\tpath.includes('temp')\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\tconst cloudVideos = this.uploadedVideos.filter(path => {\r\n\t\t\t\t\t\t\t\treturn typeof path === 'string' && (\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('cloud://') ||\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('https://')\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\tif (localVideos.length > 0) {\r\n\t\t\t\t\t\t\t\tconst uploadedUrls = await this.uploadFilesToCloud(localVideos, 'video');\r\n\t\t\t\t\t\t\t\tprocessedVideos = [...cloudVideos, ...uploadedUrls];\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tprocessedVideos = cloudVideos;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error(\"[editWish.vue] 视频上传失败:\", error);\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: `视频上传失败: ${error.message}`,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 🔧 处理音频：上传到云存储\r\n\t\t\t\t\tif (this.uploadedAudios.length > 0) {\r\n\t\t\t\t\t\tconsole.log(\"[editWish.vue] 开始上传音频到云存储，数量:\", this.uploadedAudios.length);\r\n\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst localAudios = this.uploadedAudios.filter(path => {\r\n\t\t\t\t\t\t\t\treturn typeof path === 'string' && (\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('wxfile://') ||\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('file://') ||\r\n\t\t\t\t\t\t\t\t\tpath.includes('tmp_') ||\r\n\t\t\t\t\t\t\t\t\tpath.includes('temp')\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\tconst cloudAudios = this.uploadedAudios.filter(path => {\r\n\t\t\t\t\t\t\t\treturn typeof path === 'string' && (\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('cloud://') ||\r\n\t\t\t\t\t\t\t\t\tpath.startsWith('https://')\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\tif (localAudios.length > 0) {\r\n\t\t\t\t\t\t\t\tconst uploadedUrls = await this.uploadFilesToCloud(localAudios, 'audio');\r\n\t\t\t\t\t\t\t\tprocessedAudios = [...cloudAudios, ...uploadedUrls];\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tprocessedAudios = cloudAudios;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error(\"[editWish.vue] 音频上传失败:\", error);\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: `音频上传失败: ${error.message}`,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst wishData = {\r\n\t\t\t\t\t\t...this.wishForm,\r\n\t\t\t\t\t\t// 🔧 修复：确保保存的是字符串数组，与WishCard组件期望的格式一致\r\n\t\t\t\t\t\timage: processedImages.length > 0 ? processedImages : (this.wishForm.image || []),\r\n\t\t\t\t\t\tvideo: processedVideos.length > 0 ? processedVideos : (this.wishForm.video || []),\r\n\t\t\t\t\t\taudio: processedAudios.length > 0 ? processedAudios : (this.wishForm.audio || []),\r\n\t\t\t\t\t\tupdateDate: new Date().toISOString()\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (!this.isEdit) {\r\n\t\t\t\t\t\twishData.createDate = new Date().toISOString();\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (this.isEdit) {\r\n\t\t\t\t\t\tawait this.wishStore.updateWish({ ...wishData, _id: this.wishId });\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tawait this.wishStore.addWish(wishData);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.hideLoading().catch(() => {})\r\n\r\n\t\t\t\t\t// 保存成功后清理可能存在的备份数据\r\n\t\t\t\t\tuni.removeStorageSync('editWish_tempData');\r\n\r\n\t\t\t\t\tuni.showToast({ title: this.isEdit ? '更新成功' : '保存成功', icon: 'success' });\r\n\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.safeNavigateBack();\r\n\t\t\t\t\t}, 1500);\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.hideLoading().catch(() => {})\r\n\t\t\t\t\tconsole.error('保存心愿失败 (actualSaveWish):', error);\r\n\t\t\t\t\tuni.showToast({ title: error.message || (this.isEdit ? '更新失败' : '保存失败'), icon: 'none' });\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 安全的返回导航\r\n\t\t\tsafeNavigateBack() {\r\n\t\t\t\t// 获取页面栈\r\n\t\t\t\tconst pages = getCurrentPages()\r\n\t\t\t\t\r\n\t\t\t\tif (pages.length > 1) {\r\n\t\t\t\t\t// 页面栈中有多个页面，可以安全返回\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 页面栈中只有一个页面，导航到首页\r\n\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 返回上一页\r\n\t\t\tgoBack() {\r\n\t\t\t\tthis.safeNavigateBack()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示添加分组弹窗\r\n\t\t\tshowAddGroupDialog() {\r\n\t\t\t\t// 重置输入框\r\n\t\t\t\tthis.newGroupName = ''\r\n\t\t\t\t\r\n\t\t\t\t// 显示输入框\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '添加标签',\r\n\t\t\t\t\tplaceholderText: '请输入标签名称',\r\n\t\t\t\t\teditable: true,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm && res.content) {\r\n\t\t\t\t\t\t\t// 用户点击确定，并且输入了内容\r\n\t\t\t\t\t\t\tthis.newGroupName = res.content.trim()\r\n\t\t\t\t\t\t\t// 直接调用确认方法\r\n\t\t\t\t\t\t\tthis.confirmAddGroup()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t// 其他平台使用普通输入框\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '添加标签',\r\n\t\t\t\t\tcontent: '',\r\n\t\t\t\t\tshowCancel: true,\r\n\t\t\t\t\teditable: true,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm && res.content) {\r\n\t\t\t\t\t\t\tthis.newGroupName = res.content.trim()\r\n\t\t\t\t\t\t\tthis.confirmAddGroup()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认添加分组\r\n\t\t\tasync confirmAddGroup() {\r\n\t\t\t\tconsole.log('添加标签按钮点击，准备调用ensureAuthenticated');\r\n\t\t\t\t\r\n\t\t\t\t// 如果已经登录，直接添加\r\n\t\t\t\tif (this.userStore.isLogin) {\r\n\t\t\t\t\tawait this._actualConfirmAddGroup();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tawait authService.ensureAuthenticated({\r\n\t\t\t\t\t// 不传递 onAuthenticated，让登录后回到当前页面，用户可以重新添加标签\r\n\t\t\t\t\tpromptMessage: '添加标签需要登录，是否继续？',\r\n\t\t\t\t\tonCancelled: () => {\r\n\t\t\t\t\t\tconsole.log('[editWish.vue confirmAddGroup] 用户取消登录，未添加标签。');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tasync _actualConfirmAddGroup() {\r\n\t\t\t\tconst name = this.newGroupName.trim();\r\n\t\t\t\tif (!name) {\r\n\t\t\t\t\tuni.showToast({ title: '标签名称不能为空', icon: 'none' });\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 确保 groupStore 初始化 (如果之前未登录时跳过了)\r\n\t\t\t\tif (!this.groupStore || (this.groupStore.groups.length === 0 && this.userStore.isLogin)) {\r\n\t\t\t\t\tawait this.groupStore.initGroups();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (this.groupStore.groups.some(group => group.name.toLowerCase() === name.toLowerCase())) {\r\n\t\t\t\t\tuni.showToast({ title: '已存在相同名称的标签', icon: 'none' });\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.showLoading({ title: '正在添加标签...' });\r\n\t\t\t\t\tconst newGroupId = await this.groupStore.addGroup(name); \r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (newGroupId) {\r\n\t\t\t\t\t\tif (!this.wishForm.groupIds) {\r\n\t\t\t\t\t\t\tthis.wishForm.groupIds = ['all'];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (!this.wishForm.groupIds.includes(newGroupId)) {\r\n\t\t\t\t\t\t\tthis.wishForm.groupIds.push(newGroupId);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('创建分组标签失败，newGroupId 为 null');\r\n\t\t\t\t\t\t uni.showToast({ title: '添加失败，请重试', icon: 'none' });\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('添加标签错误 (_actualConfirmAddGroup catch):', e);\r\n\t\t\t\t\tuni.showToast({ title: e.message || '添加失败，请重试', icon: 'none' });\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 设置权限\r\n\t\t\tsetPermission(permission) {\r\n\t\t\t\tthis.wishForm.permission = permission;\r\n\t\t\t\t\r\n\t\t\t\t// 确保groupIds数组已初始化\r\n\t\t\t\tif (!this.wishForm.groupIds) {\r\n\t\t\t\t\tthis.wishForm.groupIds = ['all'];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 联动处理分组标签\r\n\t\t\t\tif (permission === 'friends') {\r\n\t\t\t\t\t// 设置为朋友可见权限时，自动添加到朋友可见分组\r\n\t\t\t\t\tif (!this.wishForm.groupIds.includes('friend-visible')) {\r\n\t\t\t\t\t\tthis.wishForm.groupIds.push('friend-visible');\r\n\t\t\t\t\t\tconsole.log('自动添加到朋友可见分组');\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 设置为其他权限时，从朋友可见分组移除\r\n\t\t\t\t\tconst index = this.wishForm.groupIds.indexOf('friend-visible');\r\n\t\t\t\t\tif (index > -1) {\r\n\t\t\t\t\t\tthis.wishForm.groupIds.splice(index, 1);\r\n\t\t\t\t\t\tconsole.log('自动从朋友可见分组移除');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('权限设置为:', permission, '，当前分组:', this.wishForm.groupIds);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化日期用于显示\r\n\t\t\tformatDate(dateStr) {\r\n\t\t\t\tif (!dateStr) return ''\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst date = new Date(dateStr)\r\n\t\t\t\t\tif (isNaN(date.getTime())) return dateStr\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('日期格式化错误:', error)\r\n\t\t\t\t\treturn dateStr\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化日期用于picker组件\r\n\t\t\tformatPickerDate(dateStr) {\r\n\t\t\t\tif (!dateStr) return ''\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst date = new Date(dateStr)\r\n\t\t\t\t\tif (isNaN(date.getTime())) return ''\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('Picker日期格式化错误:', error)\r\n\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n/* 页面容器 */\r\n.page-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f9fafc;\r\n\tpadding: 30rpx;\r\n\tpadding-bottom: 180rpx;\r\n}\r\n\r\n/* 表单标签 */\r\n.form-label {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\tmargin: 30rpx 0 16rpx;\r\n}\r\n\r\n/* 表单输入框 */\r\n.form-input {\r\n\twidth: 100%;\r\n\theight: 88rpx;\r\n\tpadding: 0 24rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tborder: 2rpx solid #e6e8eb;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n/* 多行文本输入框 */\r\n.form-textarea {\r\n\twidth: 100%;\r\n\theight: 120rpx; /* 减小高度从160rpx到120rpx */\r\n\tpadding: 20rpx 24rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tborder: 2rpx solid #e6e8eb;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n/* 冲突警告样式 */\r\n.input-conflict {\r\n\tborder-color: #ff6b6b !important;\r\n\tbackground-color: #fff5f5 !important;\r\n}\r\n\r\n.conflict-warning {\r\n\tfont-size: 24rpx;\r\n\tcolor: #ff6b6b;\r\n\tmargin-top: 8rpx;\r\n\tmargin-bottom: 16rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n/* 媒体类型选项卡 */\r\n.media-type-tabs {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.media-tab {\r\n\tpadding: 10rpx 30rpx;\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tbackground-color: #f0f2f5;\r\n\tmargin-right: 20rpx;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.media-tab.active {\r\n\tbackground-color: #8a2be2;\r\n\tcolor: #fff;\r\n}\r\n\r\n/* 图片上传网格 */\r\n.image-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin: 0 -10rpx;\r\n}\r\n\r\n.image-item, .image-uploader {\r\n\twidth: calc(33.33% - 20rpx);\r\n\theight: 200rpx;\r\n\tmargin: 10rpx;\r\n\tborder-radius: 8rpx;\r\n\toverflow: hidden;\r\n\tposition: relative;\r\n}\r\n\r\n.thumbnail {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.delete-icon {\r\n\tposition: absolute;\r\n\ttop: 8rpx;\r\n\tright: 8rpx;\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tcolor: #fff;\r\n\tborder-radius: 50%;\r\n\ttext-align: center;\r\n\tline-height: 36rpx;\r\n\tfont-size: 32rpx;\r\n\tz-index: 2;\r\n}\r\n\r\n.image-uploader, .video-uploader, .audio-uploader {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground-color: #f5f5f5;\r\n\tborder: 2rpx dashed #ddd;\r\n}\r\n\r\n.upload-icon {\r\n\tfont-size: 60rpx;\r\n\tcolor: #bbb;\r\n}\r\n\r\n.upload-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n/* 视频上传网格 */\r\n.video-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin: 0 -10rpx;\r\n}\r\n\r\n.video-item {\r\n\twidth: calc(50% - 20rpx);\r\n\theight: 320rpx;\r\n\tmargin: 10rpx;\r\n\tborder-radius: 8rpx;\r\n\toverflow: hidden;\r\n\tposition: relative;\r\n}\r\n\r\n.video-thumbnail {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n\tbackground-color: #000;\r\n}\r\n\r\n/* 音频上传列表 */\r\n.audio-list {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.audio-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tborder: 2rpx solid #e6e8eb;\r\n}\r\n\r\n.audio-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.audio-icon {\r\n\tfont-size: 40rpx;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.audio-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.audio-controls {\r\n\tdisplay: flex;\r\n}\r\n\r\n.audio-play, .audio-delete {\r\n\tpadding: 10rpx 20rpx;\r\n\tfont-size: 24rpx;\r\n\tborder-radius: 30rpx;\r\n\tmargin-left: 20rpx;\r\n}\r\n\r\n.audio-play {\r\n\tbackground-color: #f0f2f5;\r\n\tcolor: #666;\r\n}\r\n\r\n.audio-delete {\r\n\tbackground-color: #fff5f5;\r\n\tcolor: #f56c6c;\r\n}\r\n\r\n.audio-uploader {\r\n\tpadding: 30rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tborder-radius: 8rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tborder: 2rpx dashed #ddd;\r\n}\r\n\r\n/* 日期选择区域 */\r\n.date-section {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.date-item {\r\n\twidth: 48%;\r\n}\r\n\r\n.date-picker {\r\n\twidth: 100%;\r\n}\r\n\r\n.picker-value {\r\n\theight: 88rpx;\r\n\tpadding: 0 20rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tborder: 2rpx solid #e6e8eb;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.picker-placeholder {\r\n\tcolor: #999;\r\n}\r\n\r\n/* 加载中提示 */\r\n.no-groups {\r\n\tpadding: 20rpx;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 分组选择器 */\r\n.group-container {\r\n\tmargin-bottom: 30rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8rpx;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.group-list-wrap {\r\n\tpadding: 0 20rpx;\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n}\r\n\r\n.group-item {\r\n\theight: 60rpx;\r\n\tpadding: 0 30rpx;\r\n\tmargin-right: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbackground-color: #f0f0f0;\r\n\tborder-radius: 30rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\ttext-align: center;\r\n\tbox-sizing: border-box;\r\n\tposition: relative;\r\n}\r\n\r\n.group-item:not(.add-group):active {\r\n\topacity: 0.7;\r\n\tbackground-color: #e0e0e0;\r\n}\r\n\r\n.group-item.active {\r\n\tbackground-color: #8a2be2;\r\n\tcolor: #fff;\r\n}\r\n\r\n.group-item.add-group {\r\n\tbackground-color: #f0e6ff;\r\n\tborder: 1px dashed #8a2be2;\r\n\tcolor: #8a2be2;\r\n}\r\n\r\n.add-icon-wrapper {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.add-icon {\r\n\tfont-size: 28rpx;\r\n\tmargin-right: 6rpx;\r\n}\r\n\r\n.add-text {\r\n\tfont-size: 28rpx;\r\n\tline-height: 1;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.action-footer {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 20rpx 30rpx 50rpx;\r\n\tbackground-color: #fff;\r\n\tborder-top: 2rpx solid #f0f0f0;\r\n\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.btn {\r\n\twidth: 46%;\r\n\theight: 88rpx;\r\n\tborder-radius: 44rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 30rpx;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.btn-cancel {\r\n\tbackground-color: #f5f7fa;\r\n\tcolor: #666;\r\n}\r\n\r\n.btn-save {\r\n\tbackground-color: #8a2be2;\r\n\tcolor: #fff;\r\n}\r\n\r\n/* 权限选项 */\r\n.permission-options {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.permission-option {\r\n\tflex: 1;\r\n\tmargin: 0 10rpx;\r\n\tpadding: 20rpx 10rpx;\r\n\tbackground-color: #f0f2f5;\r\n\tborder-radius: 8rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.permission-option:first-child {\r\n\tmargin-left: 0;\r\n}\r\n\r\n.permission-option:last-child {\r\n\tmargin-right: 0;\r\n}\r\n\r\n.permission-option.active {\r\n\tbackground-color: #8a2be2;\r\n\tcolor: #fff;\r\n}\r\n\r\n.permission-icon {\r\n\tfont-size: 40rpx;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.permission-name {\r\n\tfont-size: 24rpx;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/subpkg-wish/pages/editWish/editWish.vue'\nwx.createPage(MiniProgramPage)"], "names": ["groupTagOperations", "uni", "uniCloud", "useWishStore", "useGroupStore", "useUserStore", "authService"], "mappings": ";;;;;;;;AAwLC,MAAK,YAAU;AAAA,EACd,QAAQ,CAACA,0BAAAA,kBAAkB;AAAA,EAC3B,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,QACT,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO,CAAE;AAAA,QACT,OAAO,CAAE;AAAA,QACT,OAAO,CAAE;AAAA,QACT,aAAa;AAAA,QACb,WAAW;AAAA,QACX,cAAc;AAAA,QACd,UAAU,CAAC,KAAK;AAAA,QAChB,YAAY;AAAA,MACZ;AAAA,MACD,kBAAkB;AAAA;AAAA,MAClB,gBAAgB,CAAE;AAAA,MAClB,gBAAgB,CAAE;AAAA,MAClB,gBAAgB,CAAE;AAAA,MAClB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA;AAAA,MAEd,sBAAsB;AAAA,IACvB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,kBAAkB;AAEjB,aAAO,KAAK,aAAa,KAAK,WAAW,eAAe,CAAC;AAAA,IAC1D;AAAA,EACA;AAAA,EACD,UAAU;AACTC,kBAAAA,MAAY,MAAA,OAAA,kDAAA,wCAAwC;AAEpD,SAAK,WAAU;AAAA,EACf;AAAA,EACD,OAAO,SAAS;AAEfA,kBAAAA,MAAY,MAAA,OAAA,kDAAA,uCAAuC;AACnD,SAAK,WAAW;AAGhB,QAAI,WAAW,QAAQ,IAAI;AAC1B,WAAK,SAAS,QAAQ;AACtB,WAAK,SAAS;AACd,WAAK,aAAa;AAAA,IACnB;AAGA,SAAK,wBAAuB;AAG5B,QAAI,WAAW,QAAQ,WAAW,QAAQ,YAAY,OAAO;AAC5DA,oBAAA,MAAA,MAAA,OAAA,kDAAY,YAAY,QAAQ,OAAO;AAGvC,UAAI,CAAC,KAAK,SAAS,UAAU;AAC5B,aAAK,SAAS,WAAW,CAAC,KAAK;AAAA,MAChC;AAGA,UAAI,CAAC,KAAK,SAAS,SAAS,SAAS,QAAQ,OAAO,GAAG;AACtD,aAAK,SAAS,SAAS,KAAK,QAAQ,OAAO;AAC3CA,sBAAA,MAAA,MAAA,OAAA,kDAAY,cAAc,QAAQ,OAAO;AAAA,MAC1C;AAGA,UAAI,QAAQ,YAAY,kBAAkB;AACzC,aAAK,SAAS,aAAa;AAC3BA,sBAAAA,qEAAY,aAAa;AAAA,MAC1B;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAER,QAAI,KAAK,aAAa,KAAK,UAAU,SAAS;AAE7C,UAAI,KAAK,eAAe,CAAC,KAAK,WAAW,gBAAgB,KAAK,WAAW,aAAa,WAAW,IAAI;AACpGA,sBAAAA,MAAY,MAAA,OAAA,kDAAA,0DAA0D;AACtE,aAAK,WAAW;MACjB;AAGA,WAAK,wBAAuB;AAAA,IAC7B;AAAA,EACA;AAAA,EACD,UAAU;AAET,QAAI,CAAC,KAAK,YAAY;AACrBA,oBAAAA,MAAA,MAAA,OAAA,kDAAY,8CAA8C;AAC1D,WAAK,WAAW;AAAA,IACjB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,gBAAgB,MAAM;AACrB,WAAK,mBAAmB;AAAA,IACxB;AAAA;AAAA,IAGD,MAAM,mBAAmB,OAAO,WAAW,SAAS;AACnD,UAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AACjC,eAAO;MACR;AAEA,YAAM,eAAe,CAAA;AACrB,YAAM,SAAS,KAAK,UAAU;AAE9B,UAAI,CAAC,QAAQ;AACZ,cAAM,IAAI,MAAM,cAAc;AAAA,MAC/B;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,cAAM,WAAW,MAAM,CAAC;AAExB,YAAI;AAEH,gBAAM,YAAY,KAAK;AACvB,gBAAM,YAAY,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC;AAC3D,gBAAM,gBAAgB,SAAS,MAAM,GAAG,EAAE,IAAI,MAAM,aAAa,UAAU,QAAQ;AACnF,gBAAM,YAAY,cAAc,MAAM,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,IAAI,aAAa;AAE9FA,wBAAAA,MAAY,MAAA,OAAA,kDAAA,yBAAyB,IAAI,CAAC,IAAI,MAAM,MAAM,KAAK;AAAA,YAC9D;AAAA,YACA;AAAA,YACA;AAAA,UACD,CAAC;AAGD,gBAAM,eAAe,MAAMC,cAAQ,GAAC,WAAW;AAAA,YAC9C;AAAA,YACA;AAAA,YACA,kBAAkB,CAAC,kBAAkB;AACpC,oBAAM,WAAW,KAAK,MAAO,cAAc,SAAS,cAAc,QAAS,GAAG;AAC9ED,iGAAY,0BAA0B,QAAQ,GAAG;AAGjDA,4BAAAA,MAAI,YAAY;AAAA,gBACf,OAAO,OAAO,IAAI,CAAC,IAAI,MAAM,MAAM,KAAK,QAAQ;AAAA,cACjD,CAAC;AAAA,YACF;AAAA,UACD,CAAC;AAED,cAAI,aAAa,QAAQ;AACxB,yBAAa,KAAK,aAAa,MAAM;AACrCA,gCAAY,MAAA,OAAA,kDAAA,0BAA0B,aAAa,MAAM;AAAA,iBACnD;AACNA,0BAAA,MAAA,MAAA,SAAA,kDAAc,oCAAoC,YAAY;AAC9D,kBAAM,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS;AAAA,UACnC;AAAA,QAEC,SAAO,OAAO;AACfA,+FAAc,0BAA0B,KAAK;AAC7C,gBAAM,IAAI,MAAM,IAAI,IAAI,CAAC,YAAY,MAAM,OAAO,EAAE;AAAA,QACrD;AAAA,MACD;AAEAA,oBAAA,MAAA,MAAA,OAAA,kDAAY,4BAA4B,YAAY;AACpD,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,aAAa;AACZ,UAAI;AACH,aAAK,YAAYE,wBAAa;AAC9B,aAAK,aAAaC,0BAAc;AAChC,aAAK,YAAYC,wBAAa;AAG9B,YAAI,KAAK,cAAc,CAAC,KAAK,WAAW,aAAa,UAAU,KAAK,UAAU,SAAS;AACtF,eAAK,WAAW,WAAW;AAAA,QAC5B;AAAA,MACD,SAAS,GAAG;AACXJ,sBAAAA,MAAA,MAAA,SAAA,kDAAc,gCAAgC,CAAC;AAG/CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,0BAA0B;AACzB,UAAI;AACH,cAAM,WAAWA,cAAAA,MAAI,eAAe,mBAAmB;AACvD,YAAI,UAAU;AACbA,wBAAA,MAAA,MAAA,OAAA,kDAAY,yBAAyB,QAAQ;AAG7C,gBAAM,WAAW,KAAK,IAAG,KAAM,SAAS,aAAa;AACrD,cAAI,WAAW,KAAK,KAAK,KAAK,KAAM;AACnCA,0BAAAA,MAAA,MAAA,OAAA,kDAAY,uBAAuB;AACnCA,gCAAI,kBAAkB,mBAAmB;AACzC;AAAA,UACD;AAGA,eAAK,WAAW,SAAS;AACzB,eAAK,iBAAiB,SAAS,kBAAkB,CAAA;AACjD,eAAK,iBAAiB,SAAS,kBAAkB,CAAA;AACjD,eAAK,iBAAiB,SAAS,kBAAkB,CAAA;AACjD,eAAK,mBAAmB,SAAS,oBAAoB;AAGrD,eAAK,iCAAgC;AAGrC,cAAI,SAAS,UAAU,SAAS,QAAQ;AACvC,iBAAK,SAAS,SAAS;AACvB,iBAAK,SAAS,SAAS;AAAA,UACxB;AAGAA,8BAAI,kBAAkB,mBAAmB;AAEzCA,wBAAAA,MAAA,MAAA,OAAA,kDAAY,qBAAqB;AAAA,QAClC;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,kDAAc,wBAAwB,KAAK;AAE3CA,4BAAI,kBAAkB,mBAAmB;AAAA,MAC1C;AAAA,IACA;AAAA;AAAA,IAGD,aAAa,GAAG;AAEf,WAAK,uBAAuB,KAAK;IACjC;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACxB,UAAI,CAAC,KAAK,aAAa,CAAC,OAAO;AAC9B,eAAO;AAAA,MACR;AAEA,YAAM,eAAe,MAAM;AAC3B,UAAI,CAAC,cAAc;AAClB,eAAO;AAAA,MACR;AAGA,YAAM,YAAY,KAAK,UAAU,YAAY,CAAA;AAG7C,aAAO,UAAU,KAAK,UAAQ;AAE7B,YAAI,KAAK,UAAU;AAClB,iBAAO;AAAA,QACR;AAGA,YAAI,KAAK,WAAW,KAAK,QAAQ,KAAK,UAAU,KAAK,OAAO,KAAK,SAAS;AACzE,iBAAO;AAAA,QACR;AAGA,cAAM,aAAa,KAAK,SAAS,IAAI,KAAI;AACzC,eAAO,UAAU,YAAW,MAAO,aAAa,YAAW;AAAA,MAC5D,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,0BAA0B;AACzB,YAAM,eAAe,KAAK,kBAAkB,KAAK,SAAS,KAAK;AAC/D,aAAO,CAAC,CAAC;AAAA,IACT;AAAA;AAAA,IAGD,qBAAqB;AACpB,YAAM,eAAe,KAAK,kBAAkB,KAAK,SAAS,KAAK;AAE/D,UAAI,cAAc;AAEjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,eAAe,aAAa,KAAK;AAAA,UAC1C,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,MAAM;AAEdA,0BAAAA,MAAA,MAAA,OAAA,kDAAY,uBAAuB;AAAA,UACpC;AAAA,QACD,CAAC;AAEDA,sBAAAA,MAAY,MAAA,OAAA,kDAAA,uBAAuB;AAAA,UAClC,UAAU,KAAK,SAAS,MAAM,KAAM;AAAA,UACpC;AAAA,QACD,CAAC;AAED,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,CAAC,KAAK;AAAW;AAErB,YAAM,OAAO,KAAK,UAAU,YAAY,KAAK,MAAM;AACnD,UAAI,MAAM;AACT,aAAK,WAAW;AAAA,UACf,OAAO,KAAK,SAAS;AAAA,UACrB,aAAa,KAAK,eAAe;AAAA,UACjC,OAAO,MAAM,QAAQ,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAK,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI;UAClF,OAAO,MAAM,QAAQ,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAK,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI;UAClF,OAAO,MAAM,QAAQ,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAK,KAAK,QAAQ,CAAC,KAAK,KAAK,IAAI;UAClF,aAAa,CAAC,CAAC,KAAK;AAAA,UACpB,WAAW,KAAK,aAAa;AAAA,UAC7B,cAAc,KAAK,gBAAgB;AAAA,UACnC,UAAU,KAAK,WAAW,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK;AAAA,UACrD,YAAY,KAAK,cAAc;AAAA,QAChC;AAGA,aAAK,iCAAgC;AAGrC,aAAK,iBAAiB,CAAC,GAAG,KAAK,SAAS,KAAK;AAC7C,aAAK,iBAAiB,CAAC,GAAG,KAAK,SAAS,KAAK;AAC7C,aAAK,iBAAiB,CAAC,GAAG,KAAK,SAAS,KAAK;AAAA,aACvC;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD,mBAAW,MAAM;AAChB,eAAK,iBAAiB;AAAA,QACtB,GAAE,IAAI;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,aAAa,MAAM,MAAM;AAExB,YAAM,WAAW;AAAA,QAChB,OAAO;AAAA;AAAA,QACP,OAAO;AAAA;AAAA,QACP,OAAO;AAAA;AAAA,MACR;AAGA,YAAM,eAAe;AAAA,QACpB,OAAO,CAAC,OAAO,QAAQ,OAAO,QAAQ,KAAK;AAAA,QAC3C,OAAO,CAAC,OAAO,OAAO,OAAO,KAAK;AAAA,QAClC,OAAO,CAAC,OAAO,OAAO,OAAO,KAAK;AAAA,MACnC;AAGA,YAAM,aAAa,CAAC,SAAS;AAC5B,cAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,eAAO,MAAM,SAAS,IAAI,MAAM,IAAK,EAAC,YAAW,IAAK;AAAA,MACvD;AAGA,UAAI,KAAK,QAAQ,KAAK,OAAO,SAAS,IAAI,IAAI,OAAO,MAAM;AAC1DA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,GAAG,SAAS,UAAU,OAAO,SAAS,UAAU,OAAO,IAAI,SAAS,SAAS,IAAI,CAAC;AAAA,UACzF,MAAM;AAAA,UACN,UAAU;AAAA,SACV;AACD,eAAO;AAAA,MACR;AAGA,YAAM,MAAM,WAAW,KAAK,QAAQ,KAAK,gBAAgB,EAAE;AAC3D,UAAI,OAAO,CAAC,aAAa,IAAI,EAAE,SAAS,GAAG,GAAG;AAC7CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,OAAO,SAAS,UAAU,OAAO,SAAS,UAAU,OAAO,IAAI;AAAA,UACtE,MAAM;AAAA,UACN,UAAU;AAAA,SACV;AACD,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,IAAI,KAAK,eAAe;AAAA,QAC/B,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,cAAI,OAAO,IAAI,iBAAiB,IAAI,cAAc,SAAS,GAAG;AAE7D,kBAAM,aAAa,IAAI,cAAc,OAAO,CAAC,MAAM,UAAU;AAC5D,oBAAM,OAAO,IAAI,YAAY,IAAI,UAAU,KAAK,IAAI,EAAE,MAAM,cAAc,KAAK;AAC/E,qBAAO,KAAK,aAAa,MAAM,OAAO;AAAA,aACtC;AAED,gBAAI,WAAW,SAAS,GAAG;AAC1B,mBAAK,iBAAiB,CAAC,GAAG,KAAK,gBAAgB,GAAG,UAAU;AAE5D,mBAAK,SAAS,QAAQ,CAAC,GAAG,KAAK,cAAc;AAE7C,kBAAI,WAAW,SAAS,IAAI,cAAc,QAAQ;AACjDA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO,MAAM,WAAW,MAAM,OAAO,IAAI,cAAc,SAAS,WAAW,MAAM;AAAA,kBACjF,MAAM;AAAA,iBACN;AAAA,cACF;AAAA,YACD;AAAA,UACD;AAAA,QACA;AAAA,QACD,MAAM,CAAC,UAAU;AAChBA,wBAAAA,MAAc,MAAA,SAAA,kDAAA,WAAW,KAAK;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,QACF;AAAA,OACA;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS,CAAC,QAAQ;AACjB,cAAI,OAAO,IAAI,cAAc;AAE5B,kBAAM,OAAO;AAAA,cACZ,MAAM,IAAI;AAAA,cACV,cAAc,IAAI;AAAA,cAClB,MAAM,IAAI;AAAA,YACX;AAEA,gBAAI,KAAK,aAAa,MAAM,OAAO,GAAG;AACrC,mBAAK,iBAAiB,CAAC,GAAG,KAAK,gBAAgB,IAAI,YAAY;AAE/D,mBAAK,SAAS,QAAQ,CAAC,GAAG,KAAK,cAAc;AAAA,YAC9C;AAAA,UACD;AAAA,QACA;AAAA,QACD,MAAM,CAAC,UAAU;AAChBA,wBAAAA,MAAc,MAAA,SAAA,kDAAA,WAAW,KAAK;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,QACF;AAAA,OACA;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEbA,oBAAAA,MAAI,WAAW;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAClC,SAAS,CAAC,QAAQ;AACjB,cAAI,OAAO,IAAI,iBAAiB,IAAI,cAAc,SAAS,GAAG;AAE7D,kBAAM,OAAO;AAAA,cACZ,MAAM,IAAI,cAAc,CAAC;AAAA,cACzB,cAAc,IAAI,cAAc,CAAC;AAAA,cACjC,MAAM,IAAI,YAAY,IAAI,UAAU,CAAC,EAAE,OAAO;AAAA,YAC/C;AAEA,gBAAI,KAAK,aAAa,MAAM,OAAO,GAAG;AACrC,mBAAK,iBAAiB,CAAC,GAAG,KAAK,gBAAgB,IAAI,cAAc,CAAC,CAAC;AAEnE,mBAAK,SAAS,QAAQ,CAAC,GAAG,KAAK,cAAc;AAAA,YAC9C;AAAA,UACD;AAAA,QACA;AAAA,QACD,MAAM,CAAC,UAAU;AAChBA,wBAAAA,MAAc,MAAA,SAAA,kDAAA,WAAW,KAAK;AAE9BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,QACF;AAAA,OACA;AAAA,IACD;AAAA;AAAA,IAGD,UAAU,KAAK;AACd,YAAM,eAAeA,cAAG,MAAC,wBAAwB;AACjD,mBAAa,MAAM;AACnB,mBAAa,KAAK;AAAA,IAClB;AAAA;AAAA,IAGD,aAAa,OAAO;AACnB,UAAI,KAAK,eAAe,SAAS,GAAG;AACnCA,sBAAAA,MAAI,aAAa;AAAA,UAChB,SAAS,KAAK,eAAe,KAAK;AAAA,UAClC,MAAM,KAAK;AAAA,SACX;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,OAAO;AAClB,WAAK,eAAe,OAAO,OAAO,CAAC;AAEnC,WAAK,SAAS,QAAQ,CAAC,GAAG,KAAK,cAAc;AAAA,IAC7C;AAAA;AAAA,IAGD,YAAY,OAAO;AAClB,WAAK,eAAe,OAAO,OAAO,CAAC;AAEnC,WAAK,SAAS,QAAQ,CAAC,GAAG,KAAK,cAAc;AAAA,IAC7C;AAAA;AAAA,IAGD,YAAY,OAAO;AAClB,WAAK,eAAe,OAAO,OAAO,CAAC;AAEnC,WAAK,SAAS,QAAQ,CAAC,GAAG,KAAK,cAAc;AAAA,IAC7C;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACxB,UAAI,CAAC,KAAK,SAAS,UAAU;AAC5B,eAAO,YAAY;AAAA,MACpB;AACA,aAAO,KAAK,SAAS,SAAS,QAAQ,OAAO,IAAI;AAAA,IACjD;AAAA;AAAA,IAGD,mCAAmC;AAElC,UAAI,CAAC,KAAK,SAAS,UAAU;AAC5B,aAAK,SAAS,WAAW,CAAC,KAAK;AAAA,MAChC;AAEA,YAAM,wBAAwB,KAAK,SAAS,SAAS,SAAS,gBAAgB;AAC9E,YAAM,sBAAsB,KAAK,SAAS,eAAe;AAGzD,UAAI,uBAAuB,CAAC,uBAAuB;AAClD,aAAK,SAAS,SAAS,KAAK,gBAAgB;AAC5CA,sBAAAA,MAAA,MAAA,OAAA,kDAAY,oBAAoB;AAAA,MACjC;AAGA,UAAI,yBAAyB,CAAC,qBAAqB;AAClD,aAAK,SAAS,aAAa;AAC3BA,sBAAAA,MAAA,MAAA,OAAA,kDAAY,oBAAoB;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,SAAS;AAEpB,UAAI,CAAC,KAAK,SAAS,UAAU;AAC5B,aAAK,SAAS,WAAW,CAAC,KAAK;AAAA,MAChC;AAGA,UAAI,YAAY,OAAO;AACtB;AAAA,MACD;AAEA,YAAM,QAAQ,KAAK,SAAS,SAAS,QAAQ,OAAO;AACpD,UAAI,QAAQ,IAAI;AAEf,aAAK,SAAS,SAAS,OAAO,OAAO,CAAC;AAGtC,YAAI,YAAY,oBAAoB,KAAK,SAAS,eAAe,WAAW;AAC3E,eAAK,SAAS,aAAa;AAC3BA,wBAAAA,MAAA,MAAA,OAAA,kDAAY,oBAAoB;AAAA,QACjC;AAAA,aACM;AAEN,aAAK,SAAS,SAAS,KAAK,OAAO;AAGnC,YAAI,YAAY,kBAAkB;AACjC,eAAK,SAAS,aAAa;AAC3BA,wBAAAA,MAAA,MAAA,OAAA,kDAAY,sBAAsB;AAAA,QACnC;AAAA,MACD;AAEAA,oBAAAA,MAAA,MAAA,OAAA,kDAAY,YAAY,KAAK,SAAS,UAAU,UAAU,KAAK,SAAS,UAAU;AAAA,IAClF;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,CAAC,KAAK,SAAS,SAAS,CAAC,KAAK,SAAS,MAAM,QAAQ;AACxDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,kBAAkB,GAAG;AACpB,UAAI;AACH,cAAM,UAAU,EAAE,OAAO;AACzB,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAI,CAAC,MAAM,KAAK,QAAS,CAAA,GAAG;AAC3B,eAAK,SAAS,YAAY,KAAK,YAAW;AAC1CA,8BAAA,MAAA,OAAA,kDAAY,YAAY,KAAK,SAAS,SAAS;AAAA,eACzC;AACNA,wBAAc,MAAA,MAAA,SAAA,kDAAA,WAAW,OAAO;AAChC,eAAK,SAAS,YAAY;AAAA,QAC3B;AAAA,MACC,SAAO,OAAO;AACfA,6FAAc,WAAW,KAAK;AAC9B,aAAK,SAAS,YAAY,EAAE,OAAO;AAAA,MACpC;AAAA,IACA;AAAA;AAAA,IAGD,qBAAqB,GAAG;AACvB,UAAI;AACH,cAAM,UAAU,EAAE,OAAO;AACzB,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAI,CAAC,MAAM,KAAK,QAAS,CAAA,GAAG;AAC3B,eAAK,SAAS,eAAe,KAAK,YAAW;AAC7CA,8BAAY,MAAA,OAAA,kDAAA,YAAY,KAAK,SAAS,YAAY;AAAA,eAC5C;AACNA,wBAAc,MAAA,MAAA,SAAA,kDAAA,WAAW,OAAO;AAChC,eAAK,SAAS,eAAe;AAAA,QAC9B;AAAA,MACC,SAAO,OAAO;AACfA,6FAAc,WAAW,KAAK;AAC9B,aAAK,SAAS,eAAe,EAAE,OAAO;AAAA,MACvC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,WAAW;AAEhB,UAAI,KAAK,UAAU,SAAS;AAC3B,cAAM,KAAK;AACX;AAAA,MACD;AAGA,YAAM,eAAe;AAAA,QACpB,UAAU,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,QAClD,gBAAgB,CAAC,GAAG,KAAK,cAAc;AAAA,QACvC,gBAAgB,CAAC,GAAG,KAAK,cAAc;AAAA,QACvC,gBAAgB,CAAC,GAAG,KAAK,cAAc;AAAA,QACvC,kBAAkB,KAAK;AAAA,QACvB,QAAQ,KAAK;AAAA,QACb,QAAQ,KAAK;AAAA,QACb,WAAW,KAAK,IAAI;AAAA;AAErBA,oBAAAA,MAAI,eAAe,qBAAqB,YAAY;AACpDA,oBAAA,MAAA,MAAA,OAAA,kDAAY,2BAA2B,YAAY;AAEnD,YAAMK,qBAAAA,YAAY,oBAAoB;AAAA;AAAA,QAErC,eAAe;AAAA,QACf,aAAa,MAAM;AAClBL,wBAAAA,MAAY,MAAA,OAAA,kDAAA,qCAAqC;AAEjDA,8BAAI,kBAAkB,mBAAmB;AAAA,QAC1C;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AAEvB,UAAI,CAAC,KAAK,SAAS,MAAM,KAAI,GAAI;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,UAAI,KAAK,sBAAsB;AAC9B;AAAA,MACD;AAGA,UAAI,kBAAkB,CAAA;AACtB,UAAI,kBAAkB,CAAA;AACtB,UAAI,kBAAkB,CAAA;AAEtB,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAW,CAAA;AAGpC,YAAI,KAAK,eAAe,SAAS,GAAG;AACnCA,8BAAA,MAAA,OAAA,kDAAY,iCAAiC,KAAK,eAAe,MAAM;AAEvE,cAAI;AAEH,kBAAM,cAAc,KAAK,eAAe,OAAO,UAAQ;AACtD,qBAAO,OAAO,SAAS,aACtB,KAAK,WAAW,WAAW,KAC3B,KAAK,WAAW,SAAS,KACzB,KAAK,SAAS,MAAM,KACpB,KAAK,SAAS,MAAM;AAAA,YAEtB,CAAC;AAGD,kBAAM,cAAc,KAAK,eAAe,OAAO,UAAQ;AACtD,qBAAO,OAAO,SAAS,aACtB,KAAK,WAAW,UAAU,KAC1B,KAAK,WAAW,UAAU;AAAA,YAE5B,CAAC;AAEDA,0BAAA,MAAA,MAAA,OAAA,kDAAY,wBAAwB,WAAW;AAC/CA,0BAAA,MAAA,MAAA,OAAA,kDAAY,wBAAwB,WAAW;AAG/C,gBAAI,YAAY,SAAS,GAAG;AAC3B,oBAAM,eAAe,MAAM,KAAK,mBAAmB,aAAa,OAAO;AACvE,gCAAkB,CAAC,GAAG,aAAa,GAAG,YAAY;AAAA,mBAC5C;AACN,gCAAkB;AAAA,YACnB;AAEAA,0BAAA,MAAA,MAAA,OAAA,kDAAY,0BAA0B,eAAe;AAAA,UAEpD,SAAO,OAAO;AACfA,iGAAc,0BAA0B,KAAK;AAC7CA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,WAAW,MAAM,OAAO;AAAA,cAC/B,MAAM;AAAA,cACN,UAAU;AAAA,YACX,CAAC;AACD;AAAA,UACD;AAAA,QACD;AAGA,YAAI,KAAK,eAAe,SAAS,GAAG;AACnCA,8BAAA,MAAA,OAAA,kDAAY,iCAAiC,KAAK,eAAe,MAAM;AAEvE,cAAI;AACH,kBAAM,cAAc,KAAK,eAAe,OAAO,UAAQ;AACtD,qBAAO,OAAO,SAAS,aACtB,KAAK,WAAW,WAAW,KAC3B,KAAK,WAAW,SAAS,KACzB,KAAK,SAAS,MAAM,KACpB,KAAK,SAAS,MAAM;AAAA,YAEtB,CAAC;AAED,kBAAM,cAAc,KAAK,eAAe,OAAO,UAAQ;AACtD,qBAAO,OAAO,SAAS,aACtB,KAAK,WAAW,UAAU,KAC1B,KAAK,WAAW,UAAU;AAAA,YAE5B,CAAC;AAED,gBAAI,YAAY,SAAS,GAAG;AAC3B,oBAAM,eAAe,MAAM,KAAK,mBAAmB,aAAa,OAAO;AACvE,gCAAkB,CAAC,GAAG,aAAa,GAAG,YAAY;AAAA,mBAC5C;AACN,gCAAkB;AAAA,YACnB;AAAA,UAEC,SAAO,OAAO;AACfA,iGAAc,0BAA0B,KAAK;AAC7CA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,WAAW,MAAM,OAAO;AAAA,cAC/B,MAAM;AAAA,cACN,UAAU;AAAA,YACX,CAAC;AACD;AAAA,UACD;AAAA,QACD;AAGA,YAAI,KAAK,eAAe,SAAS,GAAG;AACnCA,8BAAA,MAAA,OAAA,kDAAY,iCAAiC,KAAK,eAAe,MAAM;AAEvE,cAAI;AACH,kBAAM,cAAc,KAAK,eAAe,OAAO,UAAQ;AACtD,qBAAO,OAAO,SAAS,aACtB,KAAK,WAAW,WAAW,KAC3B,KAAK,WAAW,SAAS,KACzB,KAAK,SAAS,MAAM,KACpB,KAAK,SAAS,MAAM;AAAA,YAEtB,CAAC;AAED,kBAAM,cAAc,KAAK,eAAe,OAAO,UAAQ;AACtD,qBAAO,OAAO,SAAS,aACtB,KAAK,WAAW,UAAU,KAC1B,KAAK,WAAW,UAAU;AAAA,YAE5B,CAAC;AAED,gBAAI,YAAY,SAAS,GAAG;AAC3B,oBAAM,eAAe,MAAM,KAAK,mBAAmB,aAAa,OAAO;AACvE,gCAAkB,CAAC,GAAG,aAAa,GAAG,YAAY;AAAA,mBAC5C;AACN,gCAAkB;AAAA,YACnB;AAAA,UAEC,SAAO,OAAO;AACfA,kGAAc,0BAA0B,KAAK;AAC7CA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,WAAW,MAAM,OAAO;AAAA,cAC/B,MAAM;AAAA,cACN,UAAU;AAAA,YACX,CAAC;AACD;AAAA,UACD;AAAA,QACD;AAEA,cAAM,WAAW;AAAA,UAChB,GAAG,KAAK;AAAA;AAAA,UAER,OAAO,gBAAgB,SAAS,IAAI,kBAAmB,KAAK,SAAS,SAAS;UAC9E,OAAO,gBAAgB,SAAS,IAAI,kBAAmB,KAAK,SAAS,SAAS;UAC9E,OAAO,gBAAgB,SAAS,IAAI,kBAAmB,KAAK,SAAS,SAAS;UAC9E,aAAY,oBAAI,KAAM,GAAC,YAAY;AAAA;AAGpC,YAAI,CAAC,KAAK,QAAQ;AACjB,mBAAS,cAAa,oBAAI,KAAM,GAAC,YAAW;AAAA,QAC7C;AAEA,YAAI,KAAK,QAAQ;AAChB,gBAAM,KAAK,UAAU,WAAW,EAAE,GAAG,UAAU,KAAK,KAAK,OAAO,CAAC;AAAA,eAC3D;AACN,gBAAM,KAAK,UAAU,QAAQ,QAAQ;AAAA,QACtC;AAEAA,sBAAAA,MAAI,YAAW,EAAG,MAAM,MAAM;AAAA,QAAA,CAAE;AAGhCA,4BAAI,kBAAkB,mBAAmB;AAEzCA,sBAAAA,MAAI,UAAU,EAAE,OAAO,KAAK,SAAS,SAAS,QAAQ,MAAM,UAAQ,CAAG;AAEvE,mBAAW,MAAM;AAChB,eAAK,iBAAgB;AAAA,QACrB,GAAE,IAAI;AAAA,MAEN,SAAO,OAAO;AACfA,sBAAAA,MAAI,YAAW,EAAG,MAAM,MAAM;AAAA,QAAA,CAAE;AAChCA,sBAAc,MAAA,MAAA,SAAA,mDAAA,4BAA4B,KAAK;AAC/CA,sBAAAA,MAAI,UAAU,EAAE,OAAO,MAAM,YAAY,KAAK,SAAS,SAAS,SAAS,MAAM,OAAQ,CAAA;AAAA,MACxF;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB;AAElB,YAAM,QAAQ,gBAAgB;AAE9B,UAAI,MAAM,SAAS,GAAG;AAErBA,sBAAAA,MAAI,aAAa;AAAA,aACX;AAENA,sBAAAA,MAAI,SAAS;AAAA,UACZ,KAAK;AAAA,SACL;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,SAAS;AACR,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA,IAGD,qBAAqB;AAEpB,WAAK,eAAe;AAIpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,WAAW,IAAI,SAAS;AAE/B,iBAAK,eAAe,IAAI,QAAQ,KAAK;AAErC,iBAAK,gBAAgB;AAAA,UACtB;AAAA,QACD;AAAA,OACA;AAAA,IAkBD;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACvBA,oBAAAA,sEAAY,kCAAkC;AAG9C,UAAI,KAAK,UAAU,SAAS;AAC3B,cAAM,KAAK;AACX;AAAA,MACD;AAEA,YAAMK,qBAAAA,YAAY,oBAAoB;AAAA;AAAA,QAErC,eAAe;AAAA,QACf,aAAa,MAAM;AAClBL,wBAAAA,sEAAY,8CAA8C;AAAA,QAC3D;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,MAAM,yBAAyB;AAC9B,YAAM,OAAO,KAAK,aAAa,KAAI;AACnC,UAAI,CAAC,MAAM;AACVA,sBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAK,CAAG;AACjD;AAAA,MACD;AAGA,UAAI,CAAC,KAAK,cAAe,KAAK,WAAW,OAAO,WAAW,KAAK,KAAK,UAAU,SAAU;AACxF,cAAM,KAAK,WAAW;MACvB;AAEA,UAAI,KAAK,WAAW,OAAO,KAAK,WAAS,MAAM,KAAK,kBAAkB,KAAK,YAAa,CAAA,GAAG;AAC1FA,sBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AACnD;AAAA,MACD;AAEA,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,YAAa,CAAA;AACtC,cAAM,aAAa,MAAM,KAAK,WAAW,SAAS,IAAI;AACtDA,sBAAG,MAAC,YAAW;AAEf,YAAI,YAAY;AACf,cAAI,CAAC,KAAK,SAAS,UAAU;AAC5B,iBAAK,SAAS,WAAW,CAAC,KAAK;AAAA,UAChC;AACA,cAAI,CAAC,KAAK,SAAS,SAAS,SAAS,UAAU,GAAG;AACjD,iBAAK,SAAS,SAAS,KAAK,UAAU;AAAA,UACvC;AAAA,eACM;AACNA,wBAAAA,wEAAc,4BAA4B;AACzCA,wBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAK,CAAG;AAAA,QACnD;AAAA,MACD,SAAS,GAAG;AACXA,sBAAG,MAAC,YAAW;AACfA,8FAAc,0CAA0C,CAAC;AACzDA,4BAAI,UAAU,EAAE,OAAO,EAAE,WAAW,YAAY,MAAM,OAAO,CAAC;AAAA,MAC/D;AAAA,IACA;AAAA;AAAA,IAGD,cAAc,YAAY;AACzB,WAAK,SAAS,aAAa;AAG3B,UAAI,CAAC,KAAK,SAAS,UAAU;AAC5B,aAAK,SAAS,WAAW,CAAC,KAAK;AAAA,MAChC;AAGA,UAAI,eAAe,WAAW;AAE7B,YAAI,CAAC,KAAK,SAAS,SAAS,SAAS,gBAAgB,GAAG;AACvD,eAAK,SAAS,SAAS,KAAK,gBAAgB;AAC5CA,wBAAAA,MAAY,MAAA,OAAA,mDAAA,aAAa;AAAA,QAC1B;AAAA,aACM;AAEN,cAAM,QAAQ,KAAK,SAAS,SAAS,QAAQ,gBAAgB;AAC7D,YAAI,QAAQ,IAAI;AACf,eAAK,SAAS,SAAS,OAAO,OAAO,CAAC;AACtCA,wBAAAA,MAAY,MAAA,OAAA,mDAAA,aAAa;AAAA,QAC1B;AAAA,MACD;AAEAA,oBAAAA,MAAA,MAAA,OAAA,mDAAY,UAAU,YAAY,UAAU,KAAK,SAAS,QAAQ;AAAA,IAClE;AAAA;AAAA,IAGD,WAAW,SAAS;AACnB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAI,MAAM,KAAK,QAAS,CAAA;AAAG,iBAAO;AAElC,eAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,MACtH,SAAO,OAAO;AACfA,sBAAAA,wEAAc,YAAY,KAAK;AAC/B,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB,SAAS;AACzB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAI,MAAM,KAAK,QAAS,CAAA;AAAG,iBAAO;AAElC,eAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,MACtH,SAAO,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,mDAAA,kBAAkB,KAAK;AACrC,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzsCD,GAAG,WAAW,eAAe;"}
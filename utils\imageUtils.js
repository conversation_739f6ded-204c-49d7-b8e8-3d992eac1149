/**
 * 图片处理工具模块
 * 提供统一的图片处理逻辑，避免重复代码
 */

import { devLog } from './envUtils.js'

/**
 * 解析图片URL
 * @param {string|Array|Object} imageData - 图片数据
 * @returns {string} 第一张图片的URL
 */
export function parseImageUrl(imageData) {
  if (!imageData) return ''

  // 字符串类型直接返回
  if (typeof imageData === 'string') {
    return imageData.trim()
  }

  // 数组类型，取第一个有效图片
  if (Array.isArray(imageData) && imageData.length > 0) {
    const firstImage = imageData[0]
    if (typeof firstImage === 'string') {
      return firstImage.trim()
    } else if (firstImage && typeof firstImage === 'object' && firstImage.url) {
      return firstImage.url.trim()
    }
  }

  // 对象类型，直接取url字段
  if (imageData && typeof imageData === 'object' && imageData.url) {
    return imageData.url.trim()
  }

  devLog.warn('[ImageUtils] 无法解析图片URL:', imageData)
  return ''
}

/**
 * 转换云存储URL为可访问的临时URL
 * @param {string} cloudUrl - 云存储URL
 * @returns {Promise<string>} 临时访问URL
 */
export async function convertCloudUrl(cloudUrl) {
  if (!cloudUrl || typeof cloudUrl !== 'string') {
    return ''
  }

  // 如果不是云存储URL，直接返回
  if (!cloudUrl.startsWith('cloud://')) {
    return cloudUrl
  }

  try {
    const result = await uniCloud.getTempFileURL({
      fileList: [cloudUrl]
    })

    if (result && result.fileList && result.fileList.length > 0) {
      return result.fileList[0].tempFileURL || cloudUrl
    }

    return cloudUrl
  } catch (error) {
    devLog.error('[ImageUtils] 云存储URL转换失败:', error)
    return cloudUrl
  }
}

/**
 * 解析并转换图片URL（支持云存储）
 * @param {string|Array|Object} imageData - 图片数据
 * @returns {Promise<string>} 可访问的图片URL
 */
export async function parseAndConvertImageUrl(imageData) {
  const rawUrl = parseImageUrl(imageData)
  if (!rawUrl) return ''

  return await convertCloudUrl(rawUrl)
}

/**
 * 计算有效图片数量
 * @param {string|Array|Object} imageData - 图片数据
 * @returns {number} 有效图片数量
 */
export function getImageCount(imageData) {
  if (!imageData) return 0

  // 字符串类型
  if (typeof imageData === 'string') {
    return imageData.trim() !== '' ? 1 : 0
  }

  // 数组类型
  if (Array.isArray(imageData)) {
    return imageData.filter(img => {
      if (typeof img === 'string') {
        return img.trim() !== ''
      } else if (img && typeof img === 'object' && img.url) {
        return img.url.trim() !== ''
      }
      return false
    }).length
  }

  // 对象类型
  if (imageData && typeof imageData === 'object' && imageData.url) {
    return imageData.url.trim() !== '' ? 1 : 0
  }

  return 0
}

/**
 * 检查是否有有效图片
 * @param {string|Array|Object} imageData - 图片数据
 * @returns {boolean} 是否有有效图片
 */
export function hasValidImage(imageData) {
  return getImageCount(imageData) > 0
}

/**
 * 获取所有有效图片URL
 * @param {string|Array|Object} imageData - 图片数据
 * @returns {Array<string>} 所有有效图片URL数组
 */
export function getAllImageUrls(imageData) {
  if (!imageData) return []

  // 字符串类型
  if (typeof imageData === 'string') {
    return imageData.trim() !== '' ? [imageData.trim()] : []
  }

  // 数组类型
  if (Array.isArray(imageData)) {
    return imageData
      .map(img => {
        if (typeof img === 'string') {
          return img.trim()
        } else if (img && typeof img === 'object' && img.url) {
          return img.url.trim()
        }
        return ''
      })
      .filter(url => url !== '')
  }

  // 对象类型
  if (imageData && typeof imageData === 'object' && imageData.url) {
    const url = imageData.url.trim()
    return url !== '' ? [url] : []
  }

  return []
}

/**
 * 图片加载错误处理
 * @param {Event} e - 错误事件
 * @param {Object} context - 上下文信息
 */
export function handleImageError(e, context = {}) {
  const src = e?.target?.src || e?.detail?.src || '未知图片源'
  
  devLog.error('[ImageUtils] 图片加载失败:', {
    src,
    ...context,
    error: e
  })

  // 可以在这里添加默认图片逻辑
  // if (e.target && e.target.src !== '/static/images/default-image.png') {
  //   e.target.src = '/static/images/default-image.png'
  // }
}

/**
 * 图片加载成功处理
 * @param {Event} e - 加载事件
 * @param {Object} context - 上下文信息
 */
export function handleImageLoad(e, context = {}) {
  const src = e?.target?.src || e?.detail?.src || '未知图片源'
  
  devLog.log('[ImageUtils] 图片加载成功:', src, context)
}

/**
 * 图片懒加载管理器
 */
export class ImageLazyLoader {
  constructor(options = {}) {
    this.loadedImages = new Set()
    this.loadingImages = new Set()
    this.failedImages = new Set()
    this.retryCount = options.retryCount || 3
    this.retryDelay = options.retryDelay || 1000
  }

  /**
   * 检查图片是否已加载
   */
  isLoaded(url) {
    return this.loadedImages.has(url)
  }

  /**
   * 检查图片是否正在加载
   */
  isLoading(url) {
    return this.loadingImages.has(url)
  }

  /**
   * 加载图片
   */
  async loadImage(url, retryCount = 0) {
    if (this.isLoaded(url)) {
      return Promise.resolve()
    }

    if (this.isLoading(url)) {
      return Promise.resolve()
    }

    this.loadingImages.add(url)

    try {
      await new Promise((resolve, reject) => {
        const img = new Image()
        img.onload = () => {
          this.loadedImages.add(url)
          this.loadingImages.delete(url)
          this.failedImages.delete(url)
          resolve()
        }
        img.onerror = () => {
          reject(new Error(`图片加载失败: ${url}`))
        }
        img.src = url
      })
    } catch (error) {
      this.loadingImages.delete(url)
      
      if (retryCount < this.retryCount) {
        devLog.warn(`[ImageLazyLoader] 图片加载失败，${this.retryDelay}ms后重试: ${url}`)
        await new Promise(resolve => setTimeout(resolve, this.retryDelay))
        return this.loadImage(url, retryCount + 1)
      } else {
        this.failedImages.add(url)
        devLog.error(`[ImageLazyLoader] 图片加载失败，已达到最大重试次数: ${url}`)
        throw error
      }
    }
  }

  /**
   * 清除缓存
   */
  clear() {
    this.loadedImages.clear()
    this.loadingImages.clear()
    this.failedImages.clear()
  }
}

// 创建全局懒加载实例
export const globalImageLoader = new ImageLazyLoader()

export default {
  parseImageUrl,
  getImageCount,
  hasValidImage,
  getAllImageUrls,
  handleImageError,
  handleImageLoad,
  ImageLazyLoader,
  globalImageLoader
} 
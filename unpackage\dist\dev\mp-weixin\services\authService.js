"use strict";
const common_vendor = require("../common/vendor.js");
const store_user = require("../store/user.js");
const YOUR_API_BASE_URL = "https://api.example.com/v1";
async function ensureAuthenticated(options = {}) {
  const userStore = store_user.useUserStore();
  const {
    onAuthenticated,
    onCancelled,
    redirectPath,
    promptMessage = "此操作需要登录，请先登录。",
    // 默认提示信息
    actionPayload = null
  } = options;
  if (userStore.checkLoginStatus()) {
    if (typeof onAuthenticated === "function") {
      try {
        await onAuthenticated(actionPayload);
      } catch (e) {
        common_vendor.index.__f__("error", "at services/authService.js:33", "[authService] Error executing onAuthenticated callback:", e);
      }
    }
    return true;
  }
  try {
    const res = await common_vendor.index.showModal({
      title: "登录提示",
      content: promptMessage,
      confirmText: "去登录",
      cancelText: "暂不登录"
    });
    if (res.confirm) {
      let targetRedirectPath = redirectPath;
      if (!targetRedirectPath) {
        const pages = getCurrentPages();
        if (pages.length) {
          const currentPage = pages[pages.length - 1];
          targetRedirectPath = `/${currentPage.route}`;
          if (currentPage.options && Object.keys(currentPage.options).length) {
            const query = Object.entries(currentPage.options).map(([k, v]) => `${k}=${encodeURIComponent(v)}`).join("&");
            targetRedirectPath += `?${query}`;
          }
        } else {
          targetRedirectPath = "/pages/index/index";
        }
      }
      if (typeof onAuthenticated === "function") {
        userStore.setPostLoginAction(async () => {
          try {
            await onAuthenticated(actionPayload);
          } catch (e) {
            common_vendor.index.__f__("error", "at services/authService.js:75", "[authService] Error executing stored postLoginAction:", e);
          }
        });
      }
      common_vendor.index.__f__("log", "at services/authService.js:80", `[authService] Redirecting to login. Target redirect: ${targetRedirectPath}`);
      common_vendor.index.navigateTo({
        url: "/pages/login/login?redirect=" + encodeURIComponent(targetRedirectPath)
      });
      return false;
    } else {
      if (typeof onCancelled === "function") {
        onCancelled();
      }
      common_vendor.index.showToast({ title: "操作已取消", icon: "none" });
      return false;
    }
  } catch (modalError) {
    common_vendor.index.__f__("error", "at services/authService.js:94", "[authService] Error showing login modal:", modalError);
    if (typeof onCancelled === "function") {
      onCancelled();
    }
    return false;
  }
}
function setupRequestInterceptor() {
  const userStore = store_user.useUserStore();
  common_vendor.index.addInterceptor("request", {
    invoke(args) {
      if (args.url.startsWith(YOUR_API_BASE_URL) || args.url.includes("/uniCloudFunctions/") || args.url.includes("__uni__")) {
        if (userStore.isLogin && userStore.token) {
          if (!args.header) {
            args.header = {};
          }
          args.header["Authorization"] = `Bearer ${userStore.token}`;
          args.header["uni-id-token"] = userStore.token;
          common_vendor.index.__f__("log", "at services/authService.js:120", "[RequestInterceptor] Added token to request for:", args.url);
        } else {
          common_vendor.index.__f__("log", "at services/authService.js:122", "[RequestInterceptor] No token or not logged in for API request:", args.url);
        }
      }
      return args;
    },
    async complete(res) {
      var _a;
      if (res.statusCode === 401) {
        common_vendor.index.__f__("warn", "at services/authService.js:131", "[RequestInterceptor] Received 401 Unauthorized for:", (_a = res.config) == null ? void 0 : _a.url);
        userStore.logout();
        const pages = getCurrentPages();
        const currentPageRoute = pages.length ? pages[pages.length - 1].route : "";
        if (currentPageRoute !== "pages/login/login") {
          common_vendor.index.__f__("log", "at services/authService.js:142", "[RequestInterceptor] 401: Not on login page, attempting to redirect to login.");
          ensureAuthenticated({
            promptMessage: "您的登录已过期或无效，请重新登录。"
            // onAuthenticated: null, // 登录成功后通常是返回原页面或首页
            // onCancelled: () => { uni.reLaunch({ url: '/pages/index/index' }); } // 如果取消，可能返回首页
          }).catch((e) => common_vendor.index.__f__("error", "at services/authService.js:150", "[RequestInterceptor] Error in ensureAuthenticated after 401:", e));
        }
      }
      return res;
    }
  });
  common_vendor.index.__f__("log", "at services/authService.js:158", "[authService] Uni.request interceptor set up.");
}
const authService = {
  ensureAuthenticated,
  setupRequestInterceptor
  // 可以根据需要添加更多认证相关辅助函数，例如：
  // isLoggedIn: () => useUserStore().isLogin,
  // getCurrentUser: () => useUserStore().userInfo,
  // hasRole: (role) => useUserStore().hasRole(role),
};
exports.authService = authService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/services/authService.js.map

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.group-sort-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  position: relative;
}
.group-sort-container .group-sort-list {
  flex: 1;
  padding: 20rpx;
  padding-top: 30rpx;
}
.group-sort-container .group-sort-list .empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
.group-sort-container .group-sort-list .sort-list {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.group-sort-container .group-sort-list .group-sort-item {
  width: 100%;
  height: 100rpx;
  position: relative;
  border-bottom: 1rpx solid #f0f0f0;
}
.group-sort-container .group-sort-list .group-sort-item.active {
  background-color: #f5f5f5;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
}
.group-sort-container .group-sort-list .group-sort-item.is-fixed {
  background-color: #f8f8f8;
}
.group-sort-container .group-sort-list .group-sort-item.is-fixed .fixed-tag {
  font-size: 24rpx;
  color: #999;
  background-color: #eee;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
}
.group-sort-container .group-sort-list .group-sort-item.is-default {
  background-color: #fafafa;
}
.group-sort-container .group-sort-list .group-sort-item:last-child {
  border-bottom: none;
}
.group-sort-container .group-sort-list .group-sort-item .group-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 30rpx;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .group-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #8a2be2;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 20rpx;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .group-icon.all-icon {
  background-color: #409eff;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .group-icon.gift-icon {
  background-color: #ff9800;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .group-icon.friend-icon {
  background-color: #67c23a;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .group-icon.custom-icon {
  background-color: #8a2be2;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .group-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .default-tag {
  font-size: 24rpx;
  color: #999;
  background-color: #eee;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .drag-handle {
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}
.group-sort-container .group-sort-list .group-sort-item .group-content .drag-handle .drag-icon {
  font-size: 40rpx;
  line-height: 40rpx;
  color: #bbb;
  font-weight: bold;
}
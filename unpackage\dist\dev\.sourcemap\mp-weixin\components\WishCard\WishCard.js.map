{"version": 3, "file": "WishCard.js", "sources": ["components/WishCard/WishCard.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQ3lrMTYvRGVza3RvcC93aXNobGlzdC11bmlhcHAvY29tcG9uZW50cy9XaXNoQ2FyZC9XaXNoQ2FyZC52dWU"], "sourcesContent": ["<template>\n\t<view class=\"wish-card-container\" :class=\"{'is-dragging': isDragging || isDraggingElement}\">\n\t\t<view class=\"swipe-action-wrapper\" ref=\"swipeRef\"\n\t\t\t@touchstart=\"touchStart\"\n\t\t\t@touchmove=\"touchMove\"\n\t\t\t@touchend=\"touchEnd\"\n\t\t\t@touchcancel=\"touchEnd\">\n\t\t\t<!-- 主卡片内容 -->\n\t\t\t<view class=\"swipe-content\" :style=\"contentStyle\">\n\t\t\t\t<view class=\"wish-card\" @click=\"goToDetail\">\n\t\t\t\t\t<view class=\"wish-card-header\">\n\t\t\t\t\t\t<view class=\"wish-card-order\">{{ index + 1 }}</view>\n\t\t\t\t\t\t<view class=\"wish-card-title text-ellipsis\">{{ wish?.title || '未知标题' }}</view>\n\t\t\t\t\t\t<view class=\"wish-card-status\">\n\t\t\t\t\t\t\t<view v-if=\"wish?.permission === 'private'\" class=\"private-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"lock-filled\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view v-if=\"wish?.isCompleted\" class=\"completed-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button open-type=\"share\" class=\"share-btn\" @click.stop=\"shareWish\" :data-index=\"index\">\n\t\t\t\t\t\t\t<image src=\"/static/tabbar/share.png\" class=\"share-icon\"></image>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"wish-card-content\">\n\t\t\t\t\t\t<view class=\"wish-card-desc\" v-if=\"wish?.description\">{{ wish.description }}</view>\n\t\t\t\t\t\t<!-- 🔧 优化图片显示逻辑，支持多种数据格式并添加错误处理 -->\n\t\t\t\t\t\t<view class=\"image-container\" v-if=\"hasValidImage\">\n\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\tclass=\"wish-card-image\"\n\t\t\t\t\t\t\t\t:src=\"getFirstImageUrl\"\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\t\t\t@error=\"onImageError\"\n\t\t\t\t\t\t\t\t@load=\"onImageLoad\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t<view v-if=\"getImageCount > 1\" class=\"multi-image-badge\">+{{getImageCount}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"wish-card-footer\">\n\t\t\t\t\t\t<view class=\"wish-card-date\">{{ formatDate(wish?.createDate) }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 滑动操作按钮 -->\n\t\t\t<view class=\"swipe-buttons\">\n\t\t\t\t<view class=\"swipe-button complete\" @click=\"handleButtonClick(0)\">完成</view>\n\t\t\t\t<view class=\"swipe-button delete\" @click=\"handleButtonClick(1)\">删除</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue';\nimport { AnimationUtils } from '@/utils/animationPerformance.js';\nimport { convertCloudUrl } from '@/utils/imageUtils.js';\n\nexport default {\n\tname: 'WishCard',\n\tprops: {\n\t\twish: {\n\t\t\ttype: Object,\n\t\t\trequired: true\n\t\t},\n\t\tindex: {\n\t\t\ttype: Number,\n\t\t\tdefault: 0\n\t\t},\n\t\t// 添加一个新属性接收活动卡片的ID，用于判断当前卡片是否需要保持打开状态\n\t\tactiveCardId: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 添加是否可拖拽的属性\n\t\tisDraggable: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 添加是否为当前被拖拽的元素\n\t\tisDraggingElement: {\n\t\t\ttype: Boolean, \n\t\t\tdefault: false\n\t\t}\n\t},\n\tsetup(props, { emit }) {\n\t\t// 滑动状态\n\t\tconst swiping = ref(false);\n\t\tconst startX = ref(0);\n\t\tconst moveX = ref(0);\n\t\tconst startY = ref(0);\n\t\tconst swipeRef = ref(null);\n\t\t\n\t\t// 添加一个变量记录当前卡片的滑动状态\n\t\tconst isOpen = ref(false);\n\t\t\n\t\t// 添加拖拽相关状态\n\t\tconst longPressTimer = ref(null);\n\t\tconst isDragging = ref(false);\n\t\tconst dragStartY = ref(0);\n\t\tconst touchMoveThrottled = ref(false); // 添加节流控制变量\n\t\t// 新增：动画帧ID用于优化性能\n\t\tconst animationFrameId = ref(null);\n\t\t\n\t\t// 按钮总宽度 (完成按钮宽度 + 删除按钮宽度)\n\t\tconst buttonsWidth = 120;  // 2个按钮共120px宽度\n\t\t\n\t\t// 添加全局滚动监听\n\t\tconst handleGlobalScroll = () => {\n\t\t\tif (isOpen.value) {\n\t\t\t\tresetSwipe();\n\t\t\t}\n\t\t};\n\t\t\n\t\t// 在组件挂载时添加滚动监听\n\t\tonMounted(() => {\n\t\t\t// 添加uni-app特定的滚动监听\n\t\t\t\n\t\t\t// 订阅自定义页面滚动通知事件\n\t\t\tuni.$on('page-scroll-event', (data) => {\n\t\t\t\tif (isOpen.value && data && data.shouldClose) {\n\t\t\t\t\t// 使用强制样式重置\n\t\t\t\t\tresetSwipeWithClass();\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 订阅小程序页面滚动事件\n\t\t\tuni.$on('onPageScroll', () => {\n\t\t\t\tif (isOpen.value) {\n\t\t\t\t\t// 使用强制样式重置\n\t\t\t\t\tresetSwipeWithClass();\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 订阅强制关闭卡片事件\n\t\t\tuni.$on('force-close-cards', (data) => {\n\t\t\t\tif (isOpen.value) {\n\t\t\t\t\tresetSwipeWithClass();\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 小程序平台的滚动事件处理\n\t\t\tif (typeof window !== 'undefined') {\n\t\t\t\t// 浏览器环境\n\t\t\t\twindow.addEventListener('scroll', handleGlobalScroll, true);\n\t\t\t\tdocument.addEventListener('scroll', handleGlobalScroll, true);\n\t\t\t\t// 监听父容器的滚动事件\n\t\t\t\tconst parentElement = document.querySelector('.wish-container');\n\t\t\t\tif (parentElement) {\n\t\t\t\t\tparentElement.addEventListener('scroll', handleGlobalScroll, true);\n\t\t\t\t}\n\t\t\t\t// 监听列表的滚动事件\n\t\t\t\tconst listElement = document.querySelector('.wish-list');\n\t\t\t\tif (listElement) {\n\t\t\t\t\tlistElement.addEventListener('scroll', handleGlobalScroll, true);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\t\n\t\t// 在组件卸载时移除滚动监听\n\t\tonUnmounted(() => {\n\t\t\t// 移除uni-app特定的事件监听\n\t\t\tuni.$off('onPageScroll');\n\t\t\tuni.$off('page-scroll-event');\n\t\t\tuni.$off('force-close-cards');\n\n\t\t\t// 清理动画帧\n\t\t\tif (animationFrameId.value) {\n\t\t\t\tAnimationUtils.cancelRAF(animationFrameId.value);\n\t\t\t}\n\n\t\t\t// 清理定时器\n\t\t\tif (longPressTimer.value) {\n\t\t\t\tclearTimeout(longPressTimer.value);\n\t\t\t}\n\n\t\t\t// 如果在浏览器环境，还需移除原生事件监听\n\t\t\tif (typeof window !== 'undefined') {\n\t\t\t\twindow.removeEventListener('scroll', handleGlobalScroll, true);\n\t\t\t\tdocument.removeEventListener('scroll', handleGlobalScroll, true);\n\t\t\t}\n\t\t});\n\t\t\n\t\t// 内容样式\n\t\tconst contentStyle = computed(() => {\n\t\t\tlet x = moveX.value - startX.value;\n\t\t\t\n\t\t\t// 限制只能左滑（负值）\n\t\t\tif (x > 0) {\n\t\t\t\tx = 0;\n\t\t\t}\n\t\t\t\n\t\t\t// 添加滑动阻尼效果，使滑动感觉更像微信\n\t\t\tif (x < 0) {\n\t\t\t\t// 当超过滑动阈值时增加阻尼\n\t\t\t\tif (x < -buttonsWidth) {\n\t\t\t\t\t// 计算超出部分，添加0.2的阻尼系数\n\t\t\t\t\tx = -buttonsWidth + (x + buttonsWidth) * 0.2;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 🔧 图片处理相关的计算属性和方法\n\n\t\t\t// 检查是否有有效的图片\n\t\t\tconst hasValidImage = computed(() => {\n\t\t\t\tconst images = wishData.value.image;\n\t\t\t\tif (!images) return false;\n\n\t\t\t\tif (Array.isArray(images)) {\n\t\t\t\t\treturn images.length > 0 && images.some(img => {\n\t\t\t\t\t\tif (typeof img === 'string') {\n\t\t\t\t\t\t\treturn img.trim() !== '';\n\t\t\t\t\t\t} else if (img && typeof img === 'object' && img.url) {\n\t\t\t\t\t\t\treturn img.url.trim() !== '';\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tif (typeof images === 'string') {\n\t\t\t\t\treturn images.trim() !== '';\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\t\t\t});\n\n\t\t\t// 获取第一张图片的URL（支持云存储转换）\n\t\t\tconst rawImageUrl = computed(() => {\n\t\t\t\tconst images = wishData.value.image;\n\t\t\t\tif (!images) return '';\n\n\t\t\t\tif (Array.isArray(images) && images.length > 0) {\n\t\t\t\t\tconst firstImage = images[0];\n\t\t\t\t\tif (typeof firstImage === 'string') {\n\t\t\t\t\t\treturn firstImage;\n\t\t\t\t\t} else if (firstImage && typeof firstImage === 'object' && firstImage.url) {\n\t\t\t\t\t\treturn firstImage.url;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (typeof images === 'string') {\n\t\t\t\t\treturn images;\n\t\t\t\t}\n\n\t\t\t\treturn '';\n\t\t\t});\n\n\t\t\t// 转换后的图片URL（处理云存储）\n\t\t\tconst getFirstImageUrl = ref('');\n\n\t\t\t// 监听原始URL变化，进行云存储转换\n\t\t\twatch(rawImageUrl, async (newUrl) => {\n\t\t\t\tif (!newUrl) {\n\t\t\t\t\tgetFirstImageUrl.value = '';\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconst convertedUrl = await convertCloudUrl(newUrl);\n\t\t\t\t\tgetFirstImageUrl.value = convertedUrl;\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[WishCard] 图片URL转换失败:', error);\n\t\t\t\t\tgetFirstImageUrl.value = newUrl; // 转换失败时使用原始URL\n\t\t\t\t}\n\t\t\t}, { immediate: true });\n\n\t\t\t// 获取图片数量\n\t\t\tconst getImageCount = computed(() => {\n\t\t\t\tconst images = wishData.value.image;\n\t\t\t\tif (!images) return 0;\n\n\t\t\t\tif (Array.isArray(images)) {\n\t\t\t\t\treturn images.filter(img => {\n\t\t\t\t\t\tif (typeof img === 'string') {\n\t\t\t\t\t\t\treturn img.trim() !== '';\n\t\t\t\t\t\t} else if (img && typeof img === 'object' && img.url) {\n\t\t\t\t\t\t\treturn img.url.trim() !== '';\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}).length;\n\t\t\t\t}\n\n\t\t\t\tif (typeof images === 'string' && images.trim() !== '') {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\n\t\t\t\treturn 0;\n\t\t\t});\n\n\t\t\t// 🔧 图片加载错误处理 - 添加安全检查\n\t\t\tconst onImageError = (e) => {\n\t\t\t\tconst src = e?.target?.src || e?.detail?.src || '未知图片源';\n\t\t\t\tconsole.error('[WishCard] 图片加载失败:', {\n\t\t\t\t\tsrc: src,\n\t\t\t\t\twishId: wishData.value.id || wishData.value._id,\n\t\t\t\t\timageData: wishData.value.image,\n\t\t\t\t\tisCloudUrl: src.startsWith('cloud://'),\n\t\t\t\t\terror: e\n\t\t\t\t});\n\n\t\t\t\t// 可以在这里添加默认图片或其他错误处理逻辑\n\t\t\t\t// e.target.src = '/static/images/default-image.png';\n\t\t\t};\n\n\t\t\t// 🔧 图片加载成功处理 - 添加安全检查\n\t\t\tconst onImageLoad = (e) => {\n\t\t\t\tconst src = e?.target?.src || e?.detail?.src || '未知图片源';\n\t\t\t\tconsole.log('[WishCard] 图片加载成功:', src);\n\t\t\t};\n\n\t\t\treturn {\n\t\t\t\t// 使用 translate3d 启用硬件加速\n\t\t\t\ttransform: `translate3d(${x}px, 0, 0)`,\n\t\t\t\ttransition: swiping.value ? 'none' : 'transform 0.25s cubic-bezier(0.3, 0.9, 0.3, 1)',\n\t\t\t\t// 性能优化\n\t\t\t\twillChange: swiping.value ? 'transform' : 'auto',\n\t\t\t\tbackfaceVisibility: 'hidden'\n\t\t\t};\n\t\t});\n\t\t\n\t\t// 创建计算属性以保持对wish的响应式引用\n\t\tconst wishData = computed(() => props.wish);\n\t\t\n\t\t// 触摸开始\n\t\tconst touchStart = (e) => {\n\t\t\t// 如果组件未启用拖拽功能，则只处理滑动\n\t\t\tif (!props.isDraggable) {\n\t\t\t\thandleSwipeStart(e);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 记录触摸开始状态\n\t\t\tswiping.value = true;\n\t\t\tstartX.value = e.touches[0].clientX;\n\t\t\t\n\t\t\t// 重要修复：不要直接重置moveX，保持当前滑动状态\n\t\t\t// 如果卡片当前是关闭状态，才设置moveX为startX\n\t\t\tif (!isOpen.value) {\n\t\t\t\tmoveX.value = startX.value;\n\t\t\t}\n\t\t\t// 如果卡片已经打开，保持当前的moveX值不变\n\t\t\t\n\t\t\t// 记录初始触摸点Y坐标，用于检测垂直滚动和拖拽\n\t\t\tstartY.value = e.touches[0].clientY;\n\t\t\tdragStartY.value = e.touches[0].clientY;\n\t\t\t\n\t\t\t// 如果存在活动卡片但不是当前卡片，通知父组件当前卡片将开始滑动\n\t\t\tif (props.activeCardId && props.activeCardId !== props.wish.id) {\n\t\t\t\temit('card-swipe-start', props.wish.id);\n\t\t\t}\n\t\t\t\n\t\t\t// 使用定时器实现长按检测\n\t\t\tif (longPressTimer.value) {\n\t\t\t\tclearTimeout(longPressTimer.value);\n\t\t\t}\n\t\t\t\n\t\t\tlongPressTimer.value = setTimeout(() => {\n\t\t\t\t// 长按触发，开始拖拽模式\n\t\t\t\tisDragging.value = true;\n\t\t\t\t\n\t\t\t\t// 阻止页面滚动以获得更好的拖拽体验 - 使用更强的方式防止滚动\n\t\t\t\tuni.$emit('disable-page-scroll', { cardId: props.wish.id, force: true });\n\t\t\t\t\n\t\t\t\t// 立即禁用页面所有触摸滚动行为\n\t\t\t\tlockPageScroll();\n\t\t\t\t\n\t\t\t\t// 震动反馈\n\t\t\t\ttry {\n\t\t\t\t\tuni.vibrateShort({\n\t\t\t\t\t\tsuccess: function () {\n\t\t\t\t\t\t\t// 长按振动触发\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('震动API调用失败:', e);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 发送开始拖拽事件\n\t\t\t\temit('drag-start', {\n\t\t\t\t\twishId: props.wish.id,\n\t\t\t\t\tindex: props.index,\n\t\t\t\t\tclientX: startX.value,\n\t\t\t\t\tclientY: startY.value\n\t\t\t\t});\n\t\t\t}, 500);\n\t\t};\n\t\t\n\t\t// 处理滑动开始，与触摸分离\n\t\tconst handleSwipeStart = (e) => {\n\t\t\tswiping.value = true;\n\t\t\tstartX.value = e.touches[0].clientX;\n\t\t\t\n\t\t\t// 重要修复：不要直接重置moveX，保持当前滑动状态\n\t\t\t// 如果卡片当前是关闭状态，才设置moveX为startX\n\t\t\tif (!isOpen.value) {\n\t\t\t\tmoveX.value = startX.value;\n\t\t\t}\n\t\t\t// 如果卡片已经打开，保持当前的moveX值不变\n\t\t\t\n\t\t\tstartY.value = e.touches[0].clientY;\n\t\t\t\n\t\t\t// 如果存在活动卡片但不是当前卡片，通知父组件当前卡片将开始滑动\n\t\t\tif (props.activeCardId && props.activeCardId !== props.wish.id) {\n\t\t\t\temit('card-swipe-start', props.wish.id);\n\t\t\t}\n\t\t};\n\t\t\n\t\t// 触摸移动\n\t\tconst touchMove = (e) => {\n\t\t\t// 获取当前触摸位置与起始位置的差值\n\t\t\tconst currentX = e.touches[0].clientX;\n\t\t\tconst currentY = e.touches[0].clientY;\n\t\t\tconst deltaX = Math.abs(currentX - startX.value);\n\t\t\tconst deltaY = Math.abs(currentY - startY.value);\n\t\t\t\n\t\t\t// 如果移动距离超过阈值，取消长按定时器\n\t\t\t// 对于水平滑动（左滑功能），提高阈值减少冲突\n\t\t\tif (longPressTimer.value && (deltaX > 20 || deltaY > 15)) {\n\t\t\t\tclearTimeout(longPressTimer.value);\n\t\t\t\tlongPressTimer.value = null;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果已经进入拖拽模式\n\t\t\tif (isDragging.value) {\n\t\t\t\t// 更强力地阻止默认行为和事件冒泡，确保页面不会滚动\n\t\t\t\te.stopPropagation();\n\t\t\t\tif (typeof e.preventDefault === 'function') {\n\t\t\t\t\te.preventDefault();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 尝试其他方式阻止滚动\n\t\t\t\tif (e.cancelable) {\n\t\t\t\t\te.cancelable && e.preventDefault();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 获取直接相对位移并应用到卡片上\n\t\t\t\tconst currentOffsetY = currentY - startY.value;\n\t\t\t\t\n\t\t\t\t// 优化后的DOM操作 - 使用 requestAnimationFrame\n\t\t\t\tif (swipeRef.value && !props.isDraggingElement) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst moveY = currentOffsetY * 0.3;\n\n\t\t\t\t\t\t// 取消之前的动画帧\n\t\t\t\t\t\tif (animationFrameId.value) {\n\t\t\t\t\t\t\tAnimationUtils.cancelRAF(animationFrameId.value);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 使用 RAF 优化性能\n\t\t\t\t\t\tanimationFrameId.value = AnimationUtils.optimizedRAF(() => {\n\t\t\t\t\t\t\tif (swipeRef.value) {\n\t\t\t\t\t\t\t\t// 使用 translate3d 启用硬件加速\n\t\t\t\t\t\t\t\tswipeRef.value.style.transform = `translate3d(0, ${moveY}px, 0)`;\n\t\t\t\t\t\t\t\tswipeRef.value.style.transition = 'none';\n\t\t\t\t\t\t\t\tswipeRef.value.style.willChange = 'transform';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('应用拖拽变换失败:', e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 发送拖拽移动事件\n\t\t\t\tconst moveData = {\n\t\t\t\t\twishId: props.wish.id,\n\t\t\t\t\tindex: props.index,\n\t\t\t\t\tclientY: e.touches[0].clientY,\n\t\t\t\t\tclientX: e.touches[0].clientX,\n\t\t\t\t\tdeltaY: e.touches[0].clientY - dragStartY.value,\n\t\t\t\t\ttimestamp: Date.now() // 添加时间戳以便于调试和节流\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 添加节流控制，避免快速发送过多事件\n\t\t\t\tif (!touchMoveThrottled.value) {\n\t\t\t\t\temit('drag-move', moveData);\n\t\t\t\t\t\n\t\t\t\t\t// 设置节流标志\n\t\t\t\t\ttouchMoveThrottled.value = true;\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\ttouchMoveThrottled.value = false;\n\t\t\t\t\t}, 16); // 降低到16ms (约60fps)，提供更流畅的响应\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn false; // 返回false进一步阻止事件传播\n\t\t\t}\n\t\t\t\n\t\t\t// 如果不是拖拽模式，则处理滑动\n\t\t\thandleSwipeMove(e);\n\t\t};\n\t\t\n\t\t// 处理滑动移动，与触摸分离\n\t\tconst handleSwipeMove = (e) => {\n\t\t\tif (!swiping.value) return;\n\t\t\t\n\t\t\t// 获取当前触摸位置\n\t\t\tconst currentX = e.touches[0].clientX;\n\t\t\tconst currentY = e.touches[0].clientY;\n\t\t\t\n\t\t\t// 检测是否是垂直滚动\n\t\t\tconst deltaY = Math.abs(currentY - startY.value);\n\t\t\tconst deltaX = Math.abs(currentX - startX.value);\n\t\t\t\n\t\t\t// 仅当垂直移动明显大于水平移动，才判定为滚动\n\t\t\t// 提高阈值到30px，减少误触发，允许用户更自然地保持左滑状态\n\t\t\tif (deltaY > deltaX * 2 && deltaY > 30) {\n\t\t\t\tswiping.value = false;\n\t\t\t\t\n\t\t\t\t// 重要修复：如果卡片未打开，才重置moveX；如果已打开，保持左滑状态\n\t\t\t\tif (!isOpen.value) {\n\t\t\t\t\tmoveX.value = startX.value;\n\t\t\t\t}\n\t\t\t\t// 如果卡片已经打开，不重置moveX，保持左滑状态\n\t\t\t\t\n\t\t\t\t// 通知父组件滚动发生（但不强制关闭卡片）\n\t\t\t\temit('card-scroll-detected', {\n\t\t\t\t\tcardId: props.wish.id,\n\t\t\t\t\tdeltaY: deltaY\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 防止继续处理\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果位置未变化，避免不必要的状态更新\n\t\t\tif (currentX === moveX.value) return;\n\t\t\t\n\t\t\t// 更新当前位置\n\t\t\tmoveX.value = currentX;\n\t\t};\n\t\t\n\t\t// 触摸结束\n\t\tconst touchEnd = (e) => {\n\t\t\t// 清除长按定时器\n\t\t\tif (longPressTimer.value) {\n\t\t\t\tclearTimeout(longPressTimer.value);\n\t\t\t\tlongPressTimer.value = null;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果是拖拽模式\n\t\t\tif (isDragging.value) {\n\t\t\t\tisDragging.value = false;\n\t\t\t\t\n\t\t\t\t// 恢复页面滚动\n\t\t\t\tuni.$emit('enable-page-scroll', { cardId: props.wish.id });\n\t\t\t\t\n\t\t\t\t// 重置卡片位置为原始位置，清除所有变换效果\n\t\t\t\tif (swipeRef.value) {\n\t\t\t\t\tswipeRef.value.style.transform = '';\n\t\t\t\t\tswipeRef.value.style.transition = 'transform 0.3s ease'; // 添加过渡效果使恢复更平滑\n\t\t\t\t\t\n\t\t\t\t\t// 确保在过渡完成后移除过渡属性\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tif (swipeRef.value) {\n\t\t\t\t\t\t\tswipeRef.value.style.transition = '';\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 300);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 发送拖拽结束事件\n\t\t\t\temit('drag-end', {\n\t\t\t\t\twishId: props.wish.id,\n\t\t\t\t\tindex: props.index\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果不是拖拽模式，则处理滑动结束\n\t\t\thandleSwipeEnd(e);\n\t\t};\n\t\t\n\t\t// 处理滑动结束，与触摸分离\n\t\tconst handleSwipeEnd = (e) => {\n\t\t\tswiping.value = false;\n\t\t\t\n\t\t\t// 计算滑动距离\n\t\t\tconst distance = moveX.value - startX.value;\n\t\t\t\n\t\t\t// 使用微信风格的滑动判断逻辑\n\t\t\t// 如果滑动距离超过按钮宽度的1/5，则显示按钮\n\t\t\tif (distance < -buttonsWidth / 5) {\n\t\t\t\t// 如果超过50%，显示全部按钮\n\t\t\t\tif (distance < -buttonsWidth / 2) {\n\t\t\t\t\tmoveX.value = startX.value - buttonsWidth;\n\t\t\t\t} else {\n\t\t\t\t\t// 否则仅显示部分按钮\n\t\t\t\t\tmoveX.value = startX.value - buttonsWidth;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 更新状态并通知父组件\n\t\t\t\tisOpen.value = true;\n\t\t\t\temit('card-open', props.wish.id);\n\t\t\t} else {\n\t\t\t\t// 回弹到初始位置\n\t\t\t\tmoveX.value = startX.value;\n\t\t\t\tisOpen.value = false;\n\t\t\t\t\n\t\t\t\t// 如果当前卡片是活动卡片但已关闭，通知父组件\n\t\t\t\tif (props.activeCardId === props.wish.id) {\n\t\t\t\t\temit('card-close');\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\t\n\t\t// 🔧 按钮点击处理 - 添加安全检查\n\t\tconst handleButtonClick = (index) => {\n\t\t\t// 检查wish对象是否存在\n\t\t\tif (!props.wish || !props.wish.id) {\n\t\t\t\tconsole.warn('[WishCard] 无效的心愿数据，无法执行操作');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (index === 0) {\n\t\t\t\t// 完成心愿\n\t\t\t\temit('complete', props.wish.id);\n\t\t\t} else if (index === 1) {\n\t\t\t\t// 删除心愿 - 添加删除动画\n\t\t\t\thandleDeleteWithAnimation();\n\t\t\t}\n\n\t\t\t// 立即重置滑动状态\n\t\t\tmoveX.value = startX.value;\n\t\t};\n\t\t\n\t\t// 删除动画状态\n\t\tconst isDeleting = ref(false);\n\t\t\n\t\t// 🔧 带确认的删除处理 - 添加安全检查\n\t\tconst handleDeleteWithAnimation = () => {\n\t\t\t// 检查wish对象是否存在\n\t\t\tif (!props.wish || !props.wish.id) {\n\t\t\t\tconsole.warn('[WishCard] 无效的心愿数据，无法删除');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 先确认删除操作\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: '确定要删除这个心愿吗？',\n\t\t\t\tconfirmText: '删除',\n\t\t\t\tconfirmColor: '#fa5151',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 直接触发删除，由父组件的transition-group处理动画\n\t\t\t\t\t\temit('delete', props.wish.id);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t};\n\t\t\n\t\t// 🔧 跳转到详情页 - 添加安全检查\n\t\tconst goToDetail = () => {\n\t\t\t// 检查wish对象是否存在\n\t\t\tif (!props.wish || !props.wish.id) {\n\t\t\t\tconsole.warn('[WishCard] 无效的心愿数据，无法跳转到详情页');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 如果正在滑动或滑动菜单已打开，不要跳转\n\t\t\tif (swiping.value || moveX.value !== startX.value) {\n\t\t\t\t// 如果菜单已打开，则关闭菜单\n\t\t\t\tif (moveX.value !== startX.value) {\n\t\t\t\t\tmoveX.value = startX.value;\n\t\t\t\t\tisOpen.value = false;\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/subpkg-wish/pages/wishDetail/wishDetail?id=${props.wish.id}`\n\t\t\t});\n\t\t};\n\t\t\n\t\t// 🔧 分享心愿 - 添加安全检查\n\t\tconst shareWish = (e) => {\n\t\t\te.stopPropagation()\n\n\t\t\t// 检查wish对象是否存在\n\t\t\tif (!props.wish || !props.wish.id) {\n\t\t\t\tconsole.warn('[WishCard] 无效的心愿数据，无法分享');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 发出共享事件，让父级组件处理登录检查和共享逻辑\n\t\t\temit('share', props.wish.id)\n\t\t\t\n\t\t\t// 注释掉直接显示分享菜单的代码，改为完全由父组件处理分享逻辑\n\t\t\t// 这样可以确保登录检查在展示分享选项之前进行\n\t\t\t/* \n\t\t\t// 使用button open-type=\"share\"来触发小程序原生分享\n\t\t\t// 但在这里我们需要模拟点击事件\n\t\t\ttry {\n\t\t\t\t// 触发微信分享菜单\n\t\t\t\tuni.showShareMenu({\n\t\t\t\t\twithShareTicket: true,\n\t\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline'],\n\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t// 触发振动反馈\n\t\t\t\t\t\tuni.vibrateShort({\n\t\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\t\t// 振动成功\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\tfail(e) {\n\t\t\t\t\t\t// 显示分享菜单失败\n\t\t\t\t\t\t// 回退到自定义分享逻辑\n\t\t\t\t\t\temit('share', props.wish.id)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error('分享操作失败:', err)\n\t\t\t\t// 回退到自定义分享逻辑\n\t\t\t\temit('share', props.wish.id)\n\t\t\t}\n\t\t\t*/\n\t\t};\n\t\t\n\t\t// 🔧 格式化日期 - 添加安全检查\n\t\tconst formatDate = (dateString) => {\n\t\t\tif (!dateString) return '未知日期';\n\n\t\t\ttry {\n\t\t\t\tconst date = new Date(dateString);\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\treturn '无效日期';\n\t\t\t\t}\n\t\t\t\treturn `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('[WishCard] 日期格式化错误:', error);\n\t\t\t\treturn '日期错误';\n\t\t\t}\n\t\t};\n\t\t\n\t\t// 重置滑动状态\n\t\tconst resetSwipe = () => {\n\t\t\t\n\t\t\t// 记录重置前的状态\n\t\t\tconst wasOpen = isOpen.value;\n\t\t\t\n\t\t\t// 重置滑动状态数据\n\t\t\tmoveX.value = startX.value;\n\t\t\tisOpen.value = false;\n\t\t\tswiping.value = false;\n\t\t\t\n\t\t\t// 只有在卡片真的从打开状态关闭时才触发关闭事件\n\t\t\t// 避免不必要的事件触发导致循环调用\n\t\t\tif (wasOpen && props.activeCardId === props.wish.id) {\n\t\t\t\temit('card-close', props.wish.id);\n\t\t\t}\n\t\t\t\n\t\t\t// 注意：不直接操作DOM，让Vue的响应式系统通过contentStyle来处理样式\n\t\t\t// 这样避免了数据状态和DOM样式不一致的问题\n\t\t};\n\t\t\n\t\t// 强制重置滑动状态（用于极端情况）\n\t\tconst resetSwipeWithClass = () => {\n\t\t\t\n\t\t\t// 记录重置前的状态\n\t\t\tconst wasOpen = isOpen.value;\n\t\t\t\n\t\t\t// 重置数据状态\n\t\t\tmoveX.value = startX.value;\n\t\t\tisOpen.value = false;\n\t\t\tswiping.value = false;\n\t\t\t\n\t\t\t// 只有在卡片真的从打开状态关闭时才触发关闭事件\n\t\t\tif (wasOpen && props.activeCardId === props.wish.id) {\n\t\t\t\temit('card-close', props.wish.id);\n\t\t\t}\n\t\t\t\n\t\t\t// 在极端情况下，可能需要强制同步DOM状态\n\t\t\t// 但优先使用Vue的响应式系统\n\t\t};\n\t\t\n\t\t// 监听activeCardId变化\n\t\twatch(\n\t\t\t() => props.activeCardId,\n\t\t\t(newActiveId, oldActiveId) => {\n\t\t\t\t// 如果卡片已打开，但活动卡片不是当前卡片或为空，则关闭当前卡片\n\t\t\t\tif (isOpen.value && (newActiveId !== props.wish.id || newActiveId === null)) {\n\t\t\t\t\tresetSwipe();\n\t\t\t\t}\n\t\t\t},\n\t\t\t{ immediate: true } // 立即执行一次，确保组件挂载时状态正确\n\t\t);\n\t\t\n\t\t// 监听 wish 属性变化，确保组件能够实时响应数据更新\n\t\twatch(\n\t\t\t() => props.wish, \n\t\t\t(newWishData, oldWishData) => {\n\t\t\t\t// 响应数据变化\n\t\t\t},\n\t\t\t{ deep: true } // 深度监听对象变化\n\t\t);\n\t\t\n\t\t// 添加锁定页面滚动函数\n\t\tconst lockPageScroll = () => {\n\t\t\t// 在触发拖动时直接应用CSS样式阻止滚动\n\t\t\t// 在父组件同步处理之前先做一些紧急处理\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 使用uni-app事件通知所有页面组件\n\t\t\t\tuni.$emit('lock-page-scroll', { source: 'drag-start' });\n\t\t\t\t\n\t\t\t\t// 针对不同环境采取不同措施\n\t\t\t\tif (typeof uni.disablePageScroll === 'function') {\n\t\t\t\t\t// 如果uni-app提供了原生的禁用滚动方法，使用它\n\t\t\t\t\tuni.disablePageScroll();\n\t\t\t\t} else if (typeof uni.pageScrollTo === 'function') {\n\t\t\t\t\t// 尝试使用pageScrollTo固定当前位置\n\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\tscrollTop: 0,\n\t\t\t\t\t\tduration: 0\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 在小程序环境中触发额外的阻止滚动机制\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 再次发送禁止滚动事件，确保页面组件收到\n\t\t\t\t\tuni.$emit('disable-page-scroll', { \n\t\t\t\t\t\tcardId: props.wish.id, \n\t\t\t\t\t\tforce: true, \n\t\t\t\t\t\ttimestamp: Date.now() \n\t\t\t\t\t});\n\t\t\t\t}, 50);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('锁定页面滚动失败:', e);\n\t\t\t}\n\t\t}\n\n\t\t// 🔧 图片处理相关计算属性和方法\n\t\tconst hasValidImage = computed(() => {\n\t\t\tconst image = props.wish?.image;\n\t\t\tif (!image) return false;\n\n\t\t\tif (typeof image === 'string') {\n\t\t\t\treturn image.trim() !== '';\n\t\t\t}\n\n\t\t\tif (Array.isArray(image)) {\n\t\t\t\treturn image.length > 0 && image.some(img => {\n\t\t\t\t\tif (typeof img === 'string') {\n\t\t\t\t\t\treturn img.trim() !== '';\n\t\t\t\t\t} else if (img && typeof img === 'object' && img.url) {\n\t\t\t\t\t\treturn img.url.trim() !== '';\n\t\t\t\t\t}\n\t\t\t\t\treturn false;\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (image && typeof image === 'object' && image.url) {\n\t\t\t\treturn image.url.trim() !== '';\n\t\t\t}\n\n\t\t\treturn false;\n\t\t});\n\n\t\tconst getFirstImageUrl = computed(() => {\n\t\t\tconst image = props.wish?.image;\n\t\t\tif (!image) return '';\n\n\t\t\tif (typeof image === 'string') {\n\t\t\t\treturn image;\n\t\t\t}\n\n\t\t\tif (Array.isArray(image) && image.length > 0) {\n\t\t\t\tconst firstImage = image[0];\n\t\t\t\tif (typeof firstImage === 'string') {\n\t\t\t\t\treturn firstImage;\n\t\t\t\t} else if (firstImage && typeof firstImage === 'object' && firstImage.url) {\n\t\t\t\t\treturn firstImage.url;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (image && typeof image === 'object' && image.url) {\n\t\t\t\treturn image.url;\n\t\t\t}\n\n\t\t\tconsole.warn('[WishCard] 无法解析图片URL:', image);\n\t\t\treturn '';\n\t\t});\n\n\t\tconst getImageCount = computed(() => {\n\t\t\tconst image = props.wish?.image;\n\t\t\tif (!image) return 0;\n\n\t\t\tif (typeof image === 'string' && image.trim() !== '') {\n\t\t\t\treturn 1;\n\t\t\t}\n\n\t\t\tif (Array.isArray(image)) {\n\t\t\t\treturn image.filter(img => {\n\t\t\t\t\tif (typeof img === 'string') {\n\t\t\t\t\t\treturn img.trim() !== '';\n\t\t\t\t\t} else if (img && typeof img === 'object' && img.url) {\n\t\t\t\t\t\treturn img.url.trim() !== '';\n\t\t\t\t\t}\n\t\t\t\t\treturn false;\n\t\t\t\t}).length;\n\t\t\t}\n\n\t\t\tif (image && typeof image === 'object' && image.url && image.url.trim() !== '') {\n\t\t\t\treturn 1;\n\t\t\t}\n\n\t\t\treturn 0;\n\t\t});\n\n\t\t// 🔧 图片加载成功处理 - 添加安全检查 (第二个定义)\n\t\tconst onImageLoad = (e) => {\n\t\t\tconst src = e?.target?.src || e?.detail?.src || '未知图片源';\n\t\t\tconsole.log('[WishCard] 图片加载成功:', src);\n\t\t};\n\n\t\t// 🔧 图片加载错误处理 - 添加安全检查 (第二个定义)\n\t\tconst onImageError = (e) => {\n\t\t\tconst src = e?.target?.src || e?.detail?.src || '未知图片源';\n\t\t\tconsole.error('[WishCard] 图片加载失败:', {\n\t\t\t\tsrc: src,\n\t\t\t\terror: e\n\t\t\t});\n\t\t};\n\n\t\treturn {\n\t\t\tswipeRef,\n\t\t\tcontentStyle,\n\t\t\ttouchStart,\n\t\t\ttouchMove,\n\t\t\ttouchEnd,\n\t\t\tisDeleting,\n\t\t\thandleButtonClick,\n\t\t\tgoToDetail,\n\t\t\tshareWish,\n\t\t\tformatDate,\n\t\t\tresetSwipe,\n\t\t\tisOpen,\n\t\t\t// 暴露这些变量给父组件用于紧急状态修复\n\t\t\tmoveX,\n\t\t\tstartX,\n\t\t\tstartY,\n\t\t\tbuttonsWidth,\n\t\t\twish: wishData,\n\t\t\tisDragging,  // 导出拖拽状态\n\t\t\ttouchMoveThrottled, // 导出节流控制变量\n\t\t\t// 🔧 图片处理相关\n\t\t\thasValidImage,\n\t\t\tgetFirstImageUrl,\n\t\t\tgetImageCount,\n\t\t\tonImageError,\n\t\t\tonImageLoad,\n\t\t};\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n.wish-card-container {\n\tmargin: 10rpx 20rpx; /* 使用固定边距避免百分比计算问题 */\n\twidth: calc(100% - 40rpx); /* 使用calc确保不会超出容器宽度 */\n\tmax-width: calc(100% - 40rpx); /* 防止内容溢出 */\n\tbox-sizing: border-box;\n\tposition: relative; /* 添加相对定位，为拖拽指示器提供基础 */\n\t\n\t/* 添加拖拽中的过渡动画，提高流畅度 */\n\ttransition: transform 0.2s cubic-bezier(0.2, 0, 0, 1), \n\t            box-shadow 0.2s cubic-bezier(0.2, 0, 0, 1), \n\t            opacity 0.2s cubic-bezier(0.2, 0, 0, 1);\n\t\n\t/* 拖拽时的状态样式 */\n\t&.is-dragging {\n\t\ttransform: scale(1.05);\n\t\tbox-shadow: 0 10rpx 30rpx rgba(138, 43, 226, 0.4);\n\t\topacity: 0.95;\n\t\tz-index: 100;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12rpx;\n\t\ttransition: transform 0.05s linear, box-shadow 0.05s linear; /* 拖拽中使用更快的过渡 */\n\t}\n}\n\n.swipe-action-wrapper {\n\tposition: relative;\n\toverflow: hidden;\n\tborder-radius: 12rpx;\n\tdisplay: flex;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\twidth: 100%; /* 确保宽度充满父容器 */\n\tmax-width: 100%; /* 防止内容溢出 */\n\tbox-sizing: border-box; /* 确保边框和内边距包含在宽度内 */\n}\n\n.swipe-content {\n\tflex: 1;\n\twidth: 100%;\n\tmax-width: 100%; /* 防止内容溢出 */\n\tz-index: 2;\n\tbackground-color: #fff;\n\ttransition: transform 0.3s ease;\n\tborder-radius: 12rpx;\n\tbox-sizing: border-box; /* 确保边框和内边距包含在宽度内 */\n\toverflow: hidden; /* 防止内容溢出 */\n}\n\n.swipe-buttons {\n\tposition: absolute;\n\ttop: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 1;\n\tdisplay: flex;\n\theight: 100%;\n}\n\n.swipe-button {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 60px;\n\tcolor: white;\n\tfont-size: 26rpx;\n\tfont-weight: 500;\n\theight: 100%;\n\tflex-direction: column;\n\t\n\t/* 微信风格的触摸反馈 */\n\t&:active {\n\t\topacity: 0.85;\n\t}\n}\n\n.complete {\n\tbackground-color: #19ad19; /* 微信操作按钮绿色 */\n}\n\n.delete {\n\tbackground-color: #fa5151; /* 微信删除按钮红色 */\n}\n\n.wish-card {\n\tpadding: 24rpx;\n\tbox-sizing: border-box;\n\tbackground-color: #ffffff;\n\tborder-radius: 12rpx;\n\twidth: 100%; /* 确保宽度充满父容器 */\n\tmax-width: 100%; /* 防止内容溢出 */\n\toverflow: hidden; /* 防止内容溢出 */\n\t\n\t.wish-card-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 16rpx;\n\t\tposition: relative;\n\t\t\n\t\t.wish-card-order {\n\t\t\twidth: 40rpx;\n\t\t\theight: 40rpx;\n\t\t\tborder-radius: 20rpx;\n\t\t\tbackground-color: #8a2be2;\n\t\t\tcolor: #fff;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tfont-size: 24rpx;\n\t\t\tmargin-right: 16rpx;\n\t\t}\n\t\t\n\t\t.wish-card-title {\n\t\t\tflex: 1;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tmargin-right: 60rpx; /* 为分享按钮留出空间 */\n\t\t}\n\t\t\n\t\t.wish-card-status {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin-right: 16rpx;\n\t\t\t\n\t\t\t.private-icon, .completed-icon {\n\t\t\t\tmargin-left: 16rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.share-btn {\n\t\t\tposition: absolute;\n\t\t\tright: -8rpx;\n\t\t\ttop: -8rpx;\n\t\t\tpadding: 6rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbackground-color: #f0f0f0;\n\t\t\tborder-radius: 50%;\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);\n\t\t\tz-index: 10;\n\t\t\tmargin: 0;\n\t\t\tline-height: 1;\n\t\t\t\n\t\t\t&::after {\n\t\t\t\tborder: none;\n\t\t\t}\n\t\t\t\n\t\t\t.share-icon {\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.wish-card-content {\n\t\tmargin-bottom: 16rpx;\n\t\tposition: relative; /* 确保能相对定位 */\n\t\t\n\t\t.image-container {\n\t\t\tposition: relative;\n\t\t\twidth: 100%;\n\t\t\theight: 300rpx;\n\t\t\tmargin-top: 16rpx;\n\t\t\tborder-radius: 8rpx;\n\t\t\toverflow: hidden;\n\t\t}\n\t\t\n\t\t.wish-card-desc {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #666;\n\t\t\tmargin-bottom: 16rpx;\n\t\t\tdisplay: -webkit-box;\n\t\t\t-webkit-box-orient: vertical;\n\t\t\t-webkit-line-clamp: 3; /* 🔧 修改为最多显示3行 */\n\t\t\toverflow: hidden;\n\t\t}\n\t\t\n\t\t.wish-card-image {\n\t\t\twidth: 100%;\n\t\t\theight: 300rpx;\n\t\t\tborder-radius: 8rpx;\n\t\t\t/* 🔧 移除 object-fit: cover，使用 mode=\"aspectFit\" 完整显示图片 */\n\t\t}\n\t\t\n\t\t.multi-image-badge {\n\t\t\tposition: absolute;\n\t\t\tright: 12rpx;\n\t\t\tbottom: 12rpx;\n\t\t\tbackground-color: rgba(0, 0, 0, 0.6);\n\t\t\tcolor: white;\n\t\t\tpadding: 4rpx 12rpx;\n\t\t\tborder-radius: 16rpx;\n\t\t\tfont-size: 20rpx;\n\t\t\tz-index: 5;\n\t\t}\n\t}\n\t\n\t.wish-card-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\t\n\t\t.wish-card-date {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #999;\n\t\t}\n\t}\n}\n\n// 优化后的拖拽动画 - 解决重复定义和性能问题\n.swipe-action-wrapper.is-dragging {\n\t// 启用硬件加速\n\ttransform: scale(1.05) translateZ(0);\n\topacity: 0.95;\n\tz-index: 100;\n\tbackground-color: #fff;\n\tborder: 4rpx solid #8a2be2;\n\tborder-radius: 12rpx;\n\n\t// 性能优化：只对需要动画的属性设置过渡，避免使用 all\n\ttransition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1),\n\t           opacity 0.15s ease-out,\n\t           box-shadow 0.2s ease-out;\n\n\t// 提前告知浏览器需要优化的属性\n\twill-change: transform, opacity, box-shadow;\n\n\t// 避免闪烁\n\tbackface-visibility: hidden;\n\n\t// 阴影效果单独处理，避免影响主动画性能\n\tbox-shadow: 0 8rpx 16rpx rgba(138, 43, 226, 0.3);\n}\n\n// 拖拽时的微妙脉动效果 - 使用更高性能的实现\n@keyframes drag-pulse-optimized {\n\t0% {\n\t\ttransform: scale(1.05) translateZ(0);\n\t\tbox-shadow: 0 8rpx 16rpx rgba(138, 43, 226, 0.3);\n\t}\n\t50% {\n\t\ttransform: scale(1.07) translateZ(0);\n\t\tbox-shadow: 0 12rpx 24rpx rgba(138, 43, 226, 0.4);\n\t}\n\t100% {\n\t\ttransform: scale(1.05) translateZ(0);\n\t\tbox-shadow: 0 8rpx 16rpx rgba(138, 43, 226, 0.3);\n\t}\n}\n\n// 应用优化后的动画\n.swipe-action-wrapper.is-dragging.with-pulse {\n\tanimation: drag-pulse-optimized 2s infinite ease-in-out;\n}\n\n\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/wishlist-uniapp/components/WishCard/WishCard.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni", "onUnmounted", "AnimationUtils", "computed", "getFirstImageUrl", "watch", "convertCloudUrl", "e"], "mappings": ";;;;;AA6DA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACV;AAAA,IACD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,MAAM,OAAO,EAAE,QAAQ;AAEtB,UAAM,UAAUA,kBAAI,KAAK;AACzB,UAAM,SAASA,kBAAI,CAAC;AACpB,UAAM,QAAQA,kBAAI,CAAC;AACnB,UAAM,SAASA,kBAAI,CAAC;AACpB,UAAM,WAAWA,kBAAI,IAAI;AAGzB,UAAM,SAASA,kBAAI,KAAK;AAGxB,UAAM,iBAAiBA,kBAAI,IAAI;AAC/B,UAAM,aAAaA,kBAAI,KAAK;AAC5B,UAAM,aAAaA,kBAAI,CAAC;AACxB,UAAM,qBAAqBA,kBAAI,KAAK;AAEpC,UAAM,mBAAmBA,kBAAI,IAAI;AAGjC,UAAM,eAAe;AAGrB,UAAM,qBAAqB,MAAM;AAChC,UAAI,OAAO,OAAO;AACjB;MACD;AAAA;AAIDC,kBAAAA,UAAU,MAAM;AAIfC,oBAAAA,MAAI,IAAI,qBAAqB,CAAC,SAAS;AACtC,YAAI,OAAO,SAAS,QAAQ,KAAK,aAAa;AAE7C;QACD;AAAA,MACD,CAAC;AAGDA,0BAAI,IAAI,gBAAgB,MAAM;AAC7B,YAAI,OAAO,OAAO;AAEjB;QACD;AAAA,MACD,CAAC;AAGDA,oBAAAA,MAAI,IAAI,qBAAqB,CAAC,SAAS;AACtC,YAAI,OAAO,OAAO;AACjB;QACD;AAAA,MACD,CAAC;AAGD,UAAI,OAAO,WAAW,aAAa;AAElC,eAAO,iBAAiB,UAAU,oBAAoB,IAAI;AAC1D,iBAAS,iBAAiB,UAAU,oBAAoB,IAAI;AAE5D,cAAM,gBAAgB,SAAS,cAAc,iBAAiB;AAC9D,YAAI,eAAe;AAClB,wBAAc,iBAAiB,UAAU,oBAAoB,IAAI;AAAA,QAClE;AAEA,cAAM,cAAc,SAAS,cAAc,YAAY;AACvD,YAAI,aAAa;AAChB,sBAAY,iBAAiB,UAAU,oBAAoB,IAAI;AAAA,QAChE;AAAA,MACD;AAAA,IACD,CAAC;AAGDC,kBAAAA,YAAY,MAAM;AAEjBD,0BAAI,KAAK,cAAc;AACvBA,0BAAI,KAAK,mBAAmB;AAC5BA,0BAAI,KAAK,mBAAmB;AAG5B,UAAI,iBAAiB,OAAO;AAC3BE,mCAAAA,eAAe,UAAU,iBAAiB,KAAK;AAAA,MAChD;AAGA,UAAI,eAAe,OAAO;AACzB,qBAAa,eAAe,KAAK;AAAA,MAClC;AAGA,UAAI,OAAO,WAAW,aAAa;AAClC,eAAO,oBAAoB,UAAU,oBAAoB,IAAI;AAC7D,iBAAS,oBAAoB,UAAU,oBAAoB,IAAI;AAAA,MAChE;AAAA,IACD,CAAC;AAGD,UAAM,eAAeC,cAAAA,SAAS,MAAM;AACnC,UAAI,IAAI,MAAM,QAAQ,OAAO;AAG7B,UAAI,IAAI,GAAG;AACV,YAAI;AAAA,MACL;AAGA,UAAI,IAAI,GAAG;AAEV,YAAI,IAAI,CAAC,cAAc;AAEtB,cAAI,CAAC,gBAAgB,IAAI,gBAAgB;AAAA,QAC1C;AAAA,MACD;AAKsBA,oBAAAA,SAAS,MAAM;AACpC,cAAM,SAAS,SAAS,MAAM;AAC9B,YAAI,CAAC;AAAQ,iBAAO;AAEpB,YAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,iBAAO,OAAO,SAAS,KAAK,OAAO,KAAK,SAAO;AAC9C,gBAAI,OAAO,QAAQ,UAAU;AAC5B,qBAAO,IAAI,KAAK,MAAM;AAAA,YACvB,WAAW,OAAO,OAAO,QAAQ,YAAY,IAAI,KAAK;AACrD,qBAAO,IAAI,IAAI,KAAI,MAAO;AAAA,YAC3B;AACA,mBAAO;AAAA,UACR,CAAC;AAAA,QACF;AAEA,YAAI,OAAO,WAAW,UAAU;AAC/B,iBAAO,OAAO,WAAW;AAAA,QAC1B;AAEA,eAAO;AAAA,MACR,CAAC;AAGD,YAAM,cAAcA,cAAAA,SAAS,MAAM;AAClC,cAAM,SAAS,SAAS,MAAM;AAC9B,YAAI,CAAC;AAAQ,iBAAO;AAEpB,YAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,GAAG;AAC/C,gBAAM,aAAa,OAAO,CAAC;AAC3B,cAAI,OAAO,eAAe,UAAU;AACnC,mBAAO;AAAA,UACR,WAAW,cAAc,OAAO,eAAe,YAAY,WAAW,KAAK;AAC1E,mBAAO,WAAW;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,OAAO,WAAW,UAAU;AAC/B,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,CAAC;AAGD,YAAMC,oBAAmBN,kBAAI,EAAE;AAG/BO,0BAAM,aAAa,OAAO,WAAW;AACpC,YAAI,CAAC,QAAQ;AACZ,UAAAD,kBAAiB,QAAQ;AACzB;AAAA,QACD;AAEA,YAAI;AACH,gBAAM,eAAe,MAAME,iCAAgB,MAAM;AACjD,UAAAF,kBAAiB,QAAQ;AAAA,QACxB,SAAO,OAAO;AACfJ,wBAAA,MAAA,MAAA,SAAA,2CAAc,yBAAyB,KAAK;AAC5C,UAAAI,kBAAiB,QAAQ;AAAA,QAC1B;AAAA,MACD,GAAG,EAAE,WAAW,KAAG,CAAG;AAGAD,oBAAAA,SAAS,MAAM;AACpC,cAAM,SAAS,SAAS,MAAM;AAC9B,YAAI,CAAC;AAAQ,iBAAO;AAEpB,YAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,iBAAO,OAAO,OAAO,SAAO;AAC3B,gBAAI,OAAO,QAAQ,UAAU;AAC5B,qBAAO,IAAI,KAAK,MAAM;AAAA,YACvB,WAAW,OAAO,OAAO,QAAQ,YAAY,IAAI,KAAK;AACrD,qBAAO,IAAI,IAAI,KAAI,MAAO;AAAA,YAC3B;AACA,mBAAO;AAAA,UACP,CAAA,EAAE;AAAA,QACJ;AAEA,YAAI,OAAO,WAAW,YAAY,OAAO,KAAI,MAAO,IAAI;AACvD,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,CAAC;AAuBD,aAAO;AAAA;AAAA,QAEN,WAAW,eAAe,CAAC;AAAA,QAC3B,YAAY,QAAQ,QAAQ,SAAS;AAAA;AAAA,QAErC,YAAY,QAAQ,QAAQ,cAAc;AAAA,QAC1C,oBAAoB;AAAA;IAEtB,CAAC;AAGD,UAAM,WAAWA,cAAQ,SAAC,MAAM,MAAM,IAAI;AAG1C,UAAM,aAAa,CAAC,MAAM;AAEzB,UAAI,CAAC,MAAM,aAAa;AACvB,yBAAiB,CAAC;AAClB;AAAA,MACD;AAGA,cAAQ,QAAQ;AAChB,aAAO,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAI5B,UAAI,CAAC,OAAO,OAAO;AAClB,cAAM,QAAQ,OAAO;AAAA,MACtB;AAIA,aAAO,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAC5B,iBAAW,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAGhC,UAAI,MAAM,gBAAgB,MAAM,iBAAiB,MAAM,KAAK,IAAI;AAC/D,aAAK,oBAAoB,MAAM,KAAK,EAAE;AAAA,MACvC;AAGA,UAAI,eAAe,OAAO;AACzB,qBAAa,eAAe,KAAK;AAAA,MAClC;AAEA,qBAAe,QAAQ,WAAW,MAAM;AAEvC,mBAAW,QAAQ;AAGnBH,sBAAAA,MAAI,MAAM,uBAAuB,EAAE,QAAQ,MAAM,KAAK,IAAI,OAAO,KAAG,CAAG;AAGvE;AAGA,YAAI;AACHA,wBAAAA,MAAI,aAAa;AAAA,YAChB,SAAS,WAAY;AAAA,YAErB;AAAA,UACD,CAAC;AAAA,QACF,SAASO,IAAG;AACXP,wFAAc,cAAcO,EAAC;AAAA,QAC9B;AAGA,aAAK,cAAc;AAAA,UAClB,QAAQ,MAAM,KAAK;AAAA,UACnB,OAAO,MAAM;AAAA,UACb,SAAS,OAAO;AAAA,UAChB,SAAS,OAAO;AAAA,QACjB,CAAC;AAAA,MACD,GAAE,GAAG;AAAA;AAIP,UAAM,mBAAmB,CAAC,MAAM;AAC/B,cAAQ,QAAQ;AAChB,aAAO,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAI5B,UAAI,CAAC,OAAO,OAAO;AAClB,cAAM,QAAQ,OAAO;AAAA,MACtB;AAGA,aAAO,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAG5B,UAAI,MAAM,gBAAgB,MAAM,iBAAiB,MAAM,KAAK,IAAI;AAC/D,aAAK,oBAAoB,MAAM,KAAK,EAAE;AAAA,MACvC;AAAA;AAID,UAAM,YAAY,CAAC,MAAM;AAExB,YAAM,WAAW,EAAE,QAAQ,CAAC,EAAE;AAC9B,YAAM,WAAW,EAAE,QAAQ,CAAC,EAAE;AAC9B,YAAM,SAAS,KAAK,IAAI,WAAW,OAAO,KAAK;AAC/C,YAAM,SAAS,KAAK,IAAI,WAAW,OAAO,KAAK;AAI/C,UAAI,eAAe,UAAU,SAAS,MAAM,SAAS,KAAK;AACzD,qBAAa,eAAe,KAAK;AACjC,uBAAe,QAAQ;AAAA,MACxB;AAGA,UAAI,WAAW,OAAO;AAErB,UAAE,gBAAe;AACjB,YAAI,OAAO,EAAE,mBAAmB,YAAY;AAC3C,YAAE,eAAc;AAAA,QACjB;AAGA,YAAI,EAAE,YAAY;AACjB,YAAE,cAAc,EAAE;QACnB;AAGA,cAAM,iBAAiB,WAAW,OAAO;AAGzC,YAAI,SAAS,SAAS,CAAC,MAAM,mBAAmB;AAC/C,cAAI;AACH,kBAAM,QAAQ,iBAAiB;AAG/B,gBAAI,iBAAiB,OAAO;AAC3BL,yCAAAA,eAAe,UAAU,iBAAiB,KAAK;AAAA,YAChD;AAGA,6BAAiB,QAAQA,0CAAe,aAAa,MAAM;AAC1D,kBAAI,SAAS,OAAO;AAEnB,yBAAS,MAAM,MAAM,YAAY,kBAAkB,KAAK;AACxD,yBAAS,MAAM,MAAM,aAAa;AAClC,yBAAS,MAAM,MAAM,aAAa;AAAA,cACnC;AAAA,YACD,CAAC;AAAA,UACF,SAASK,IAAG;AACXP,0BAAc,MAAA,MAAA,SAAA,2CAAA,aAAaO,EAAC;AAAA,UAC7B;AAAA,QACD;AAGA,cAAM,WAAW;AAAA,UAChB,QAAQ,MAAM,KAAK;AAAA,UACnB,OAAO,MAAM;AAAA,UACb,SAAS,EAAE,QAAQ,CAAC,EAAE;AAAA,UACtB,SAAS,EAAE,QAAQ,CAAC,EAAE;AAAA,UACtB,QAAQ,EAAE,QAAQ,CAAC,EAAE,UAAU,WAAW;AAAA,UAC1C,WAAW,KAAK,IAAM;AAAA;AAAA;AAIvB,YAAI,CAAC,mBAAmB,OAAO;AAC9B,eAAK,aAAa,QAAQ;AAG1B,6BAAmB,QAAQ;AAC3B,qBAAW,MAAM;AAChB,+BAAmB,QAAQ;AAAA,UAC3B,GAAE,EAAE;AAAA,QACN;AAEA,eAAO;AAAA,MACR;AAGA,sBAAgB,CAAC;AAAA;AAIlB,UAAM,kBAAkB,CAAC,MAAM;AAC9B,UAAI,CAAC,QAAQ;AAAO;AAGpB,YAAM,WAAW,EAAE,QAAQ,CAAC,EAAE;AAC9B,YAAM,WAAW,EAAE,QAAQ,CAAC,EAAE;AAG9B,YAAM,SAAS,KAAK,IAAI,WAAW,OAAO,KAAK;AAC/C,YAAM,SAAS,KAAK,IAAI,WAAW,OAAO,KAAK;AAI/C,UAAI,SAAS,SAAS,KAAK,SAAS,IAAI;AACvC,gBAAQ,QAAQ;AAGhB,YAAI,CAAC,OAAO,OAAO;AAClB,gBAAM,QAAQ,OAAO;AAAA,QACtB;AAIA,aAAK,wBAAwB;AAAA,UAC5B,QAAQ,MAAM,KAAK;AAAA,UACnB;AAAA,QACD,CAAC;AAGD;AAAA,MACD;AAGA,UAAI,aAAa,MAAM;AAAO;AAG9B,YAAM,QAAQ;AAAA;AAIf,UAAM,WAAW,CAAC,MAAM;AAEvB,UAAI,eAAe,OAAO;AACzB,qBAAa,eAAe,KAAK;AACjC,uBAAe,QAAQ;AAAA,MACxB;AAGA,UAAI,WAAW,OAAO;AACrB,mBAAW,QAAQ;AAGnBP,4BAAI,MAAM,sBAAsB,EAAE,QAAQ,MAAM,KAAK,GAAG,CAAC;AAGzD,YAAI,SAAS,OAAO;AACnB,mBAAS,MAAM,MAAM,YAAY;AACjC,mBAAS,MAAM,MAAM,aAAa;AAGlC,qBAAW,MAAM;AAChB,gBAAI,SAAS,OAAO;AACnB,uBAAS,MAAM,MAAM,aAAa;AAAA,YACnC;AAAA,UACA,GAAE,GAAG;AAAA,QACP;AAGA,aAAK,YAAY;AAAA,UAChB,QAAQ,MAAM,KAAK;AAAA,UACnB,OAAO,MAAM;AAAA,QACd,CAAC;AACD;AAAA,MACD;AAGA,qBAAgB;AAAA;AAIjB,UAAM,iBAAiB,CAAC,MAAM;AAC7B,cAAQ,QAAQ;AAGhB,YAAM,WAAW,MAAM,QAAQ,OAAO;AAItC,UAAI,WAAW,CAAC,eAAe,GAAG;AAEjC,YAAI,WAAW,CAAC,eAAe,GAAG;AACjC,gBAAM,QAAQ,OAAO,QAAQ;AAAA,eACvB;AAEN,gBAAM,QAAQ,OAAO,QAAQ;AAAA,QAC9B;AAGA,eAAO,QAAQ;AACf,aAAK,aAAa,MAAM,KAAK,EAAE;AAAA,aACzB;AAEN,cAAM,QAAQ,OAAO;AACrB,eAAO,QAAQ;AAGf,YAAI,MAAM,iBAAiB,MAAM,KAAK,IAAI;AACzC,eAAK,YAAY;AAAA,QAClB;AAAA,MACD;AAAA;AAID,UAAM,oBAAoB,CAAC,UAAU;AAEpC,UAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,KAAK,IAAI;AAClCA,sBAAAA,MAAA,MAAA,QAAA,2CAAa,2BAA2B;AACxC;AAAA,MACD;AAEA,UAAI,UAAU,GAAG;AAEhB,aAAK,YAAY,MAAM,KAAK,EAAE;AAAA,MAC/B,WAAW,UAAU,GAAG;AAEvB;MACD;AAGA,YAAM,QAAQ,OAAO;AAAA;AAItB,UAAM,aAAaF,kBAAI,KAAK;AAG5B,UAAM,4BAA4B,MAAM;AAEvC,UAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,KAAK,IAAI;AAClCE,sBAAAA,MAAA,MAAA,QAAA,2CAAa,yBAAyB;AACtC;AAAA,MACD;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhB,iBAAK,UAAU,MAAM,KAAK,EAAE;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,CAAC;AAAA;AAIF,UAAM,aAAa,MAAM;AAExB,UAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,KAAK,IAAI;AAClCA,sBAAAA,MAAA,MAAA,QAAA,2CAAa,6BAA6B;AAC1C;AAAA,MACD;AAGA,UAAI,QAAQ,SAAS,MAAM,UAAU,OAAO,OAAO;AAElD,YAAI,MAAM,UAAU,OAAO,OAAO;AACjC,gBAAM,QAAQ,OAAO;AACrB,iBAAO,QAAQ;AAAA,QAChB;AACA;AAAA,MACD;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,+CAA+C,MAAM,KAAK,EAAE;AAAA,MAClE,CAAC;AAAA;AAIF,UAAM,YAAY,CAAC,MAAM;AACxB,QAAE,gBAAgB;AAGlB,UAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,KAAK,IAAI;AAClCA,sBAAAA,MAAA,MAAA,QAAA,2CAAa,yBAAyB;AACtC;AAAA,MACD;AAGA,WAAK,SAAS,MAAM,KAAK,EAAE;AAAA;AAmC5B,UAAM,aAAa,CAAC,eAAe;AAClC,UAAI,CAAC;AAAY,eAAO;AAExB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAI,MAAM,KAAK,QAAO,CAAE,GAAG;AAC1B,iBAAO;AAAA,QACR;AACA,eAAO,GAAG,KAAK,YAAW,CAAE,KAAK,KAAK,SAAW,IAAE,GAAG,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,QAAS,EAAC,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAAA,MAC9H,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,2CAAc,uBAAuB,KAAK;AAC1C,eAAO;AAAA,MACR;AAAA;AAID,UAAM,aAAa,MAAM;AAGxB,YAAM,UAAU,OAAO;AAGvB,YAAM,QAAQ,OAAO;AACrB,aAAO,QAAQ;AACf,cAAQ,QAAQ;AAIhB,UAAI,WAAW,MAAM,iBAAiB,MAAM,KAAK,IAAI;AACpD,aAAK,cAAc,MAAM,KAAK,EAAE;AAAA,MACjC;AAAA;AAOD,UAAM,sBAAsB,MAAM;AAGjC,YAAM,UAAU,OAAO;AAGvB,YAAM,QAAQ,OAAO;AACrB,aAAO,QAAQ;AACf,cAAQ,QAAQ;AAGhB,UAAI,WAAW,MAAM,iBAAiB,MAAM,KAAK,IAAI;AACpD,aAAK,cAAc,MAAM,KAAK,EAAE;AAAA,MACjC;AAAA;AAODK,kBAAK;AAAA,MACJ,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa,gBAAgB;AAE7B,YAAI,OAAO,UAAU,gBAAgB,MAAM,KAAK,MAAM,gBAAgB,OAAO;AAC5E;QACD;AAAA,MACA;AAAA,MACD,EAAE,WAAW,KAAO;AAAA;AAAA;AAIrBA,kBAAK;AAAA,MACJ,MAAM,MAAM;AAAA,MACZ,CAAC,aAAa,gBAAgB;AAAA,MAE7B;AAAA,MACD,EAAE,MAAM,KAAO;AAAA;AAAA;AAIhB,UAAM,iBAAiB,MAAM;AAI5B,UAAI;AAEHL,sBAAG,MAAC,MAAM,oBAAoB,EAAE,QAAQ,aAAc,CAAA;AAGtD,YAAI,OAAOA,cAAAA,MAAI,sBAAsB,YAAY;AAEhDA,wBAAG,MAAC,kBAAiB;AAAA,QACtB,WAAW,OAAOA,cAAAA,MAAI,iBAAiB,YAAY;AAElDA,wBAAAA,MAAI,aAAa;AAAA,YAChB,WAAW;AAAA,YACX,UAAU;AAAA,UACX,CAAC;AAAA,QACF;AAGA,mBAAW,MAAM;AAEhBA,wBAAG,MAAC,MAAM,uBAAuB;AAAA,YAChC,QAAQ,MAAM,KAAK;AAAA,YACnB,OAAO;AAAA,YACP,WAAW,KAAK,IAAI;AAAA,UACrB,CAAC;AAAA,QACD,GAAE,EAAE;AAAA,MACN,SAAS,GAAG;AACXA,sFAAc,aAAa,CAAC;AAAA,MAC7B;AAAA,IACD;AAGA,UAAM,gBAAgBG,cAAAA,SAAS,MAAM;;AACpC,YAAM,SAAQ,WAAM,SAAN,mBAAY;AAC1B,UAAI,CAAC;AAAO,eAAO;AAEnB,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO,MAAM,WAAW;AAAA,MACzB;AAEA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO,MAAM,SAAS,KAAK,MAAM,KAAK,SAAO;AAC5C,cAAI,OAAO,QAAQ,UAAU;AAC5B,mBAAO,IAAI,KAAK,MAAM;AAAA,UACvB,WAAW,OAAO,OAAO,QAAQ,YAAY,IAAI,KAAK;AACrD,mBAAO,IAAI,IAAI,KAAI,MAAO;AAAA,UAC3B;AACA,iBAAO;AAAA,QACR,CAAC;AAAA,MACF;AAEA,UAAI,SAAS,OAAO,UAAU,YAAY,MAAM,KAAK;AACpD,eAAO,MAAM,IAAI,KAAI,MAAO;AAAA,MAC7B;AAEA,aAAO;AAAA,IACR,CAAC;AAED,UAAM,mBAAmBA,cAAAA,SAAS,MAAM;;AACvC,YAAM,SAAQ,WAAM,SAAN,mBAAY;AAC1B,UAAI,CAAC;AAAO,eAAO;AAEnB,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO;AAAA,MACR;AAEA,UAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS,GAAG;AAC7C,cAAM,aAAa,MAAM,CAAC;AAC1B,YAAI,OAAO,eAAe,UAAU;AACnC,iBAAO;AAAA,QACR,WAAW,cAAc,OAAO,eAAe,YAAY,WAAW,KAAK;AAC1E,iBAAO,WAAW;AAAA,QACnB;AAAA,MACD;AAEA,UAAI,SAAS,OAAO,UAAU,YAAY,MAAM,KAAK;AACpD,eAAO,MAAM;AAAA,MACd;AAEAH,oBAAa,MAAA,MAAA,QAAA,2CAAA,yBAAyB,KAAK;AAC3C,aAAO;AAAA,IACR,CAAC;AAED,UAAM,gBAAgBG,cAAAA,SAAS,MAAM;;AACpC,YAAM,SAAQ,WAAM,SAAN,mBAAY;AAC1B,UAAI,CAAC;AAAO,eAAO;AAEnB,UAAI,OAAO,UAAU,YAAY,MAAM,KAAI,MAAO,IAAI;AACrD,eAAO;AAAA,MACR;AAEA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO,MAAM,OAAO,SAAO;AAC1B,cAAI,OAAO,QAAQ,UAAU;AAC5B,mBAAO,IAAI,KAAK,MAAM;AAAA,UACvB,WAAW,OAAO,OAAO,QAAQ,YAAY,IAAI,KAAK;AACrD,mBAAO,IAAI,IAAI,KAAI,MAAO;AAAA,UAC3B;AACA,iBAAO;AAAA,QACP,CAAA,EAAE;AAAA,MACJ;AAEA,UAAI,SAAS,OAAO,UAAU,YAAY,MAAM,OAAO,MAAM,IAAI,KAAK,MAAM,IAAI;AAC/E,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACR,CAAC;AAGD,UAAM,cAAc,CAAC,MAAM;;AAC1B,YAAM,QAAM,4BAAG,WAAH,mBAAW,UAAO,4BAAG,WAAH,mBAAW,QAAO;AAChDH,oBAAY,MAAA,MAAA,OAAA,2CAAA,sBAAsB,GAAG;AAAA;AAItC,UAAM,eAAe,CAAC,MAAM;;AAC3B,YAAM,QAAM,4BAAG,WAAH,mBAAW,UAAO,4BAAG,WAAH,mBAAW,QAAO;AAChDA,oBAAAA,MAAA,MAAA,SAAA,2CAAc,sBAAsB;AAAA,QACnC;AAAA,QACA,OAAO;AAAA,MACR,CAAC;AAAA;AAGF,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN;AAAA;AAAA,MACA;AAAA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;EAEF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC17BA,GAAG,gBAAgB,SAAS;"}
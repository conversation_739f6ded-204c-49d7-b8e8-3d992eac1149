/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.wish-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.card {
  margin-bottom: 20rpx;
  border-radius: 16rpx;
}
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}
.section-title .comment-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}
.wish-detail {
  padding: 30rpx;
}
.wish-detail .wish-header {
  margin-bottom: 30rpx;
}
.wish-detail .wish-header .wish-title-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.wish-detail .wish-header .wish-title-wrap .wish-title {
  font-size: 36rpx;
  font-weight: 600;
}
.wish-detail .wish-header .wish-status {
  display: flex;
}
.wish-detail .wish-header .wish-status .tag {
  display: flex;
  align-items: center;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  margin-right: 16rpx;
}
.wish-detail .wish-header .wish-status .tag .uni-icons {
  margin-right: 6rpx;
}
.wish-detail .wish-header .wish-status .tag.private-tag {
  background-color: #ff9800;
}
.wish-detail .wish-header .wish-status .tag.friends-tag {
  background-color: #2196f3;
}
.wish-detail .wish-header .wish-status .tag.public-tag {
  background-color: #4caf50;
}
.wish-detail .wish-header .wish-status .tag.completed-tag {
  background-color: #409eff;
}
.wish-detail .wish-content {
  padding: 20rpx 0;
}
.wish-detail .wish-content .wish-desc {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 20rpx;
}
.wish-detail .wish-content .image-swiper {
  width: 100%;
  height: 500rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.wish-detail .wish-content .swiper-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.wish-detail .wish-content .wish-image {
  width: 100%;
  max-height: 500rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.wish-detail .wish-content .wish-time-info {
  margin-top: 20rpx;
}
.wish-detail .wish-content .wish-time-info .time-item {
  display: flex;
  margin-bottom: 10rpx;
}
.wish-detail .wish-content .wish-time-info .time-item .time-label {
  color: #666;
  font-size: 26rpx;
  margin-right: 10rpx;
}
.wish-detail .wish-content .wish-time-info .time-item .time-value {
  color: #333;
  font-size: 26rpx;
}
.wish-detail .wish-actions {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
}
.wish-detail .wish-actions .action-btn, .wish-detail .wish-actions .share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  background: none;
  margin: 0;
  padding: 10rpx 20rpx;
  line-height: 1.5;
}
.wish-detail .wish-actions .action-btn::after, .wish-detail .wish-actions .share-btn::after {
  border: none;
}
.wish-detail .wish-actions .action-btn.delete, .wish-detail .wish-actions .share-btn.delete {
  color: #f56c6c;
}
.wish-groups {
  padding: 30rpx;
}
.wish-groups .group-tags {
  display: flex;
  flex-wrap: wrap;
}
.wish-groups .group-tags .group-tag {
  padding: 6rpx 16rpx;
  background-color: #f0e6ff;
  color: #8a2be2;
  border-radius: 24rpx;
  font-size: 26rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.wish-comments {
  padding: 30rpx;
}
.wish-comments .empty-comment {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}
.wish-comments .comment-list {
  margin-bottom: 30rpx;
}
.wish-comments .comment-list .comment-item {
  display: flex;
  margin-bottom: 30rpx;
}
.wish-comments .comment-list .comment-item:last-child {
  margin-bottom: 0;
}
.wish-comments .comment-list .comment-item .comment-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}
.wish-comments .comment-list .comment-item .comment-content {
  flex: 1;
}
.wish-comments .comment-list .comment-item .comment-content .comment-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.wish-comments .comment-list .comment-item .comment-content .comment-user .comment-nickname {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.wish-comments .comment-list .comment-item .comment-content .comment-user .comment-time {
  font-size: 24rpx;
  color: #999;
}
.wish-comments .comment-list .comment-item .comment-content .comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
}
.wish-comments .comment-list .comment-item .comment-content .comment-image {
  width: 100%;
  max-width: 450rpx;
  border-radius: 12rpx;
}
.wish-comments .floating-delete-btn {
  position: fixed;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f56c6c;
  color: #fff;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  box-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.4);
  animation: fadeInScale 0.2s ease-out;
  white-space: nowrap;
}
.wish-comments .floating-delete-btn text {
  margin-left: 6rpx;
}
.wish-comments .floating-delete-btn:active {
  transform: scale(0.9);
  background-color: #e85a5a;
}
@keyframes fadeInScale {
0% {
    opacity: 0;
    transform: scale(0.5);
}
100% {
    opacity: 1;
    transform: scale(1);
}
}
.wish-comments .comment-form {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 35rpx;
}
.wish-comments .comment-form .comment-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
}
.wish-comments .comment-form .comment-btn {
  padding: 0 10rpx;
}
{"version": 3, "file": "groupManage.js", "sources": ["subpkg-wish/pages/groupManage/groupManage.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGtnLXdpc2hccGFnZXNcZ3JvdXBNYW5hZ2VcZ3JvdXBNYW5hZ2UudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"group-manage-container\">\r\n\t\t<view class=\"group-list\">\r\n\t\t\t<view class=\"section-title\">\r\n\t\t\t\t<text>分组管理</text>\r\n\t\t\t\t<view class=\"add-btn\" @click=\"showAddGroupModal\">\r\n\t\t\t\t\t<uni-icons type=\"plus\" size=\"18\" color=\"#8a2be2\"></uni-icons>\r\n\t\t\t\t\t<text class=\"add-text\">添加分组</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-if=\"groups.length === 0\" class=\"empty-tip\">\r\n\t\t\t\t<text>暂无分组，点击上方按钮添加</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-else class=\"group-items\">\r\n\t\t\t\t<view v-for=\"group in groups\" :key=\"group.id\" class=\"group-item card\">\r\n\t\t\t\t\t<view class=\"group-info\">\r\n\t\t\t\t\t\t<view class=\"group-name\">{{ group.name }}</view>\r\n\t\t\t\t\t\t<view v-if=\"group.isDefault\" class=\"default-tag\">默认</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"group-actions\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"action-btn\" \r\n\t\t\t\t\t\t\t:class=\"{ disabled: group.isDefault }\"\r\n\t\t\t\t\t\t\t@click=\"!group.isDefault && showEditGroupModal(group)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"18\" :color=\"group.isDefault ? '#ccc' : '#8a2be2'\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"action-btn delete\" \r\n\t\t\t\t\t\t\t:class=\"{ disabled: group.isDefault }\"\r\n\t\t\t\t\t\t\t@click=\"!group.isDefault && showDeleteConfirm(group)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"18\" :color=\"group.isDefault ? '#ccc' : '#f56c6c'\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加分组弹窗 -->\r\n\t\t<uni-popup ref=\"addGroupPopup\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog\r\n\t\t\t\ttitle=\"添加分组\"\r\n\t\t\t\tmode=\"input\"\r\n\t\t\t\tplaceholder=\"请输入分组名称\"\r\n\t\t\t\t:before-close=\"true\"\r\n\t\t\t\t@confirm=\"confirmAddGroup\"\r\n\t\t\t\t@close=\"closeAddGroupModal\"\r\n\t\t\t></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 编辑分组弹窗 -->\r\n\t\t<uni-popup ref=\"editGroupPopup\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog\r\n\t\t\t\ttitle=\"编辑分组\"\r\n\t\t\t\tmode=\"input\"\r\n\t\t\t\t:value=\"editingGroupName\"\r\n\t\t\t\tplaceholder=\"请输入分组名称\"\r\n\t\t\t\t:before-close=\"true\"\r\n\t\t\t\t@confirm=\"confirmEditGroup\"\r\n\t\t\t\t@close=\"closeEditGroupModal\"\r\n\t\t\t></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 删除确认弹窗 -->\r\n\t\t<uni-popup ref=\"deletePopup\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog\r\n\t\t\t\ttitle=\"删除分组\"\r\n\t\t\t\tcontent=\"确定要删除此分组吗？删除后无法恢复。\"\r\n\t\t\t\t:before-close=\"true\"\r\n\t\t\t\t@confirm=\"confirmDeleteGroup\"\r\n\t\t\t\t@close=\"closeDeleteConfirm\"\r\n\t\t\t></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { useGroupStore } from '@/store/group.js'\r\n\timport { useUserStore } from '@/store/user.js'\r\n\t\r\n\texport default {\r\n\t\tsetup() {\r\n\t\t\tconst groupStore = useGroupStore()\r\n\t\t\tconst userStore = useUserStore()\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\tgroupStore,\r\n\t\t\t\tuserStore\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\taddGroupName: '',\r\n\t\t\t\teditingGroupId: null,\r\n\t\t\t\teditingGroupName: '',\r\n\t\t\t\tdeletingGroupId: null\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tgroups() {\r\n\t\t\t\treturn this.groupStore.getAllGroups\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 显示添加分组弹窗\r\n\t\t\tshowAddGroupModal() {\r\n\t\t\t\tthis.$refs.addGroupPopup.open()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 关闭添加分组弹窗\r\n\t\t\tcloseAddGroupModal() {\r\n\t\t\t\tthis.$refs.addGroupPopup.close()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认添加分组\r\n\t\t\tconfirmAddGroup(value) {\r\n\t\t\t\tif (!value.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '分组名称不能为空',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查用户是否已登录\r\n\t\t\t\tif (!this.userStore.checkLoginAndRedirect()) {\r\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页面')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.groupStore.addGroup(value.trim())\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '添加成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t\tthis.closeAddGroupModal()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示编辑分组弹窗\r\n\t\t\tshowEditGroupModal(group) {\r\n\t\t\t\tthis.editingGroupId = group.id\r\n\t\t\t\tthis.editingGroupName = group.name\r\n\t\t\t\tthis.$refs.editGroupPopup.open()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 关闭编辑分组弹窗\r\n\t\t\tcloseEditGroupModal() {\r\n\t\t\t\tthis.editingGroupId = null\r\n\t\t\t\tthis.editingGroupName = ''\r\n\t\t\t\tthis.$refs.editGroupPopup.close()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认编辑分组\r\n\t\t\tconfirmEditGroup(value) {\r\n\t\t\t\tif (!this.editingGroupId || !value.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '分组名称不能为空',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查用户是否已登录\r\n\t\t\t\tif (!this.userStore.checkLoginAndRedirect()) {\r\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页面')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.groupStore.updateGroup(this.editingGroupId, value.trim())\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '更新成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t\tthis.closeEditGroupModal()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示删除确认\r\n\t\t\tshowDeleteConfirm(group) {\r\n\t\t\t\tthis.deletingGroupId = group.id\r\n\t\t\t\tthis.$refs.deletePopup.open()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 关闭删除确认\r\n\t\t\tcloseDeleteConfirm() {\r\n\t\t\t\tthis.deletingGroupId = null\r\n\t\t\t\tthis.$refs.deletePopup.close()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认删除分组\r\n\t\t\tconfirmDeleteGroup() {\r\n\t\t\t\tif (!this.deletingGroupId) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查用户是否已登录\r\n\t\t\t\tif (!this.userStore.checkLoginAndRedirect()) {\r\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页面')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.groupStore.deleteGroup(this.deletingGroupId)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已删除',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t\tthis.closeDeleteConfirm()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.group-manage-container {\r\n\t\tpadding: 20rpx;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\t\r\n\t.group-list {\r\n\t\t.section-title {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 20rpx 10rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tborder-bottom: 1px solid #eee;\r\n\t\t\t\r\n\t\t\t.add-btn {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 6rpx 20rpx;\r\n\t\t\t\tbackground-color: #f0e6ff;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t.add-text {\r\n\t\t\t\t\tmargin-left: 6rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #8a2be2;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.empty-tip {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 100rpx 0;\r\n\t\t\tcolor: #999;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.group-items {\r\n\t\t\t.group-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t.group-info {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.group-name {\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.default-tag {\r\n\t\t\t\t\t\tmargin-left: 16rpx;\r\n\t\t\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t\t\t\tbackground-color: #f0f0f0;\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.group-actions {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.action-btn {\r\n\t\t\t\t\t\tpadding: 16rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.delete {\r\n\t\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.disabled {\r\n\t\t\t\t\t\t\topacity: 0.5;\r\n\t\t\t\t\t\t\tpointer-events: none;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/subpkg-wish/pages/groupManage/groupManage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useGroupStore", "useUserStore", "uni"], "mappings": ";;;;AAmFC,MAAK,YAAU;AAAA,EACd,QAAQ;AACP,UAAM,aAAaA,YAAAA,cAAc;AACjC,UAAM,YAAYC,WAAAA,aAAa;AAE/B,WAAO;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,IAClB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,SAAS;AACR,aAAO,KAAK,WAAW;AAAA,IACxB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,oBAAoB;AACnB,WAAK,MAAM,cAAc,KAAK;AAAA,IAC9B;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,MAAM,cAAc,MAAM;AAAA,IAC/B;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACtB,UAAI,CAAC,MAAM,QAAQ;AAClBC,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAGA,UAAI,CAAC,KAAK,UAAU,yBAAyB;AAC5CA,sBAAAA,2EAAY,eAAe;AAC3B;AAAA,MACD;AAEA,WAAK,WAAW,SAAS,MAAM,KAAI,CAAE;AACrCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AACD,WAAK,mBAAmB;AAAA,IACxB;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACzB,WAAK,iBAAiB,MAAM;AAC5B,WAAK,mBAAmB,MAAM;AAC9B,WAAK,MAAM,eAAe,KAAK;AAAA,IAC/B;AAAA;AAAA,IAGD,sBAAsB;AACrB,WAAK,iBAAiB;AACtB,WAAK,mBAAmB;AACxB,WAAK,MAAM,eAAe,MAAM;AAAA,IAChC;AAAA;AAAA,IAGD,iBAAiB,OAAO;AACvB,UAAI,CAAC,KAAK,kBAAkB,CAAC,MAAM,KAAI,GAAI;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAGA,UAAI,CAAC,KAAK,UAAU,yBAAyB;AAC5CA,sBAAAA,2EAAY,eAAe;AAC3B;AAAA,MACD;AAEA,WAAK,WAAW,YAAY,KAAK,gBAAgB,MAAM,MAAM;AAC7DA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AACD,WAAK,oBAAoB;AAAA,IACzB;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACxB,WAAK,kBAAkB,MAAM;AAC7B,WAAK,MAAM,YAAY,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,kBAAkB;AACvB,WAAK,MAAM,YAAY,MAAM;AAAA,IAC7B;AAAA;AAAA,IAGD,qBAAqB;AACpB,UAAI,CAAC,KAAK,iBAAiB;AAC1B;AAAA,MACD;AAGA,UAAI,CAAC,KAAK,UAAU,yBAAyB;AAC5CA,sBAAAA,2EAAY,eAAe;AAC3B;AAAA,MACD;AAEA,WAAK,WAAW,YAAY,KAAK,eAAe;AAChDA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AACD,WAAK,mBAAmB;AAAA,IACzB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClND,GAAG,WAAW,eAAe;"}
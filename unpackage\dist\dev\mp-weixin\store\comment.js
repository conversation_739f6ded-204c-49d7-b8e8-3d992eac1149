"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const utils_envUtils = require("../utils/envUtils.js");
const useCommentStore = common_vendor.defineStore("comment", {
  state: () => ({
    // 按心愿ID分组的评论数据
    commentsByWish: {},
    // 同步时间戳，记录每个心愿的最后同步时间
    syncTimestamps: {},
    // 加载状态
    isLoading: false,
    // 网络状态
    isOnline: true,
    // 事件监听器
    listeners: {}
  }),
  getters: {
    // 获取指定心愿的评论列表
    getCommentsByWish: (state) => (wishId) => {
      return state.commentsByWish[wishId] || [];
    },
    // 获取指定心愿的评论数量
    getCommentCount: (state) => (wishId) => {
      return (state.commentsByWish[wishId] || []).length;
    },
    // 检查指定心愿是否有评论数据
    hasComments: (state) => (wishId) => {
      return state.commentsByWish[wishId] && state.commentsByWish[wishId].length > 0;
    }
  },
  actions: {
    // 加载指定心愿的评论
    async loadComments(wishId, refresh = false) {
      var _a;
      if (!wishId)
        return [];
      try {
        if (!refresh && this.commentsByWish[wishId]) {
          return this.commentsByWish[wishId];
        }
        this.isLoading = true;
        utils_envUtils.devLog.log(`[CommentStore] 加载心愿 ${wishId} 的评论`);
        const commentCenter = common_vendor.nr.importObject("comment-center");
        const result = await commentCenter.getCommentsByWish(wishId);
        if (result.errCode === 0) {
          this.commentsByWish[wishId] = result.data || [];
          this.syncTimestamps[wishId] = Date.now();
          this._saveToStorage();
          utils_envUtils.devLog.log(`[CommentStore] 成功加载 ${((_a = result.data) == null ? void 0 : _a.length) || 0} 条评论`);
          return this.commentsByWish[wishId];
        } else {
          throw new Error(result.errMsg || "加载评论失败");
        }
      } catch (error) {
        utils_envUtils.devLog.error("[CommentStore] 加载评论失败:", error);
        return this.commentsByWish[wishId] || [];
      } finally {
        this.isLoading = false;
      }
    },
    // 添加评论
    async addComment(wishId, commentData) {
      if (!wishId || !commentData)
        return null;
      try {
        utils_envUtils.devLog.log(`[CommentStore] 添加评论到心愿 ${wishId}`);
        const commentCenter = common_vendor.nr.importObject("comment-center");
        const result = await commentCenter.createComment({
          wishId,
          ...commentData
        });
        if (result.errCode === 0) {
          if (!this.commentsByWish[wishId]) {
            this.commentsByWish[wishId] = [];
          }
          this.commentsByWish[wishId].unshift(result.data);
          this.syncTimestamps[wishId] = Date.now();
          this._saveToStorage();
          utils_envUtils.devLog.log(`[CommentStore] 评论添加成功`);
          return result.data;
        } else {
          throw new Error(result.errMsg || "添加评论失败");
        }
      } catch (error) {
        utils_envUtils.devLog.error("[CommentStore] 添加评论失败:", error);
        throw error;
      }
    },
    // 删除评论
    async deleteComment(commentId, wishId) {
      if (!commentId)
        return false;
      try {
        utils_envUtils.devLog.log(`[CommentStore] 删除评论 ${commentId}`);
        const commentCenter = common_vendor.nr.importObject("comment-center");
        const result = await commentCenter.deleteComment(commentId);
        if (result.errCode === 0) {
          if (wishId && this.commentsByWish[wishId]) {
            this.commentsByWish[wishId] = this.commentsByWish[wishId].filter(
              (comment) => comment._id !== commentId
            );
            this.syncTimestamps[wishId] = Date.now();
            this._saveToStorage();
          }
          utils_envUtils.devLog.log(`[CommentStore] 评论删除成功`);
          return true;
        } else {
          throw new Error(result.errMsg || "删除评论失败");
        }
      } catch (error) {
        utils_envUtils.devLog.error("[CommentStore] 删除评论失败:", error);
        throw error;
      }
    },
    // 同步指定心愿的评论（供 syncManager 调用）
    async syncCommentsForWish(wishId, force = false, silent = false) {
      if (!wishId)
        return;
      try {
        if (!silent) {
          utils_envUtils.devLog.log(`[CommentStore] 同步心愿 ${wishId} 的评论数据`);
        }
        await this.loadComments(wishId, force);
        this._emit("sync_completed", { wishId });
      } catch (error) {
        utils_envUtils.devLog.error(`[CommentStore] 同步评论失败:`, error);
        throw error;
      }
    },
    // 从同步中添加评论（供 syncManager 调用）
    addCommentFromSync(commentData) {
      if (!commentData || !commentData.wishId)
        return;
      const wishId = commentData.wishId;
      if (!this.commentsByWish[wishId]) {
        this.commentsByWish[wishId] = [];
      }
      const existingIndex = this.commentsByWish[wishId].findIndex(
        (comment) => comment._id === commentData._id
      );
      if (existingIndex === -1) {
        this.commentsByWish[wishId].unshift(commentData);
        utils_envUtils.devLog.log(`[CommentStore] 从同步添加新评论: ${commentData._id}`);
      }
      this._saveToStorage();
    },
    // 从同步中更新评论（供 syncManager 调用）
    updateCommentFromSync(commentData) {
      if (!commentData || !commentData.wishId)
        return;
      const wishId = commentData.wishId;
      if (!this.commentsByWish[wishId])
        return;
      const existingIndex = this.commentsByWish[wishId].findIndex(
        (comment) => comment._id === commentData._id
      );
      if (existingIndex !== -1) {
        this.commentsByWish[wishId][existingIndex] = commentData;
        utils_envUtils.devLog.log(`[CommentStore] 从同步更新评论: ${commentData._id}`);
        this._saveToStorage();
      }
    },
    // 从同步中删除评论（供 syncManager 调用）
    removeCommentById(commentId) {
      let removed = false;
      Object.keys(this.commentsByWish).forEach((wishId) => {
        const originalLength = this.commentsByWish[wishId].length;
        this.commentsByWish[wishId] = this.commentsByWish[wishId].filter(
          (comment) => comment._id !== commentId
        );
        if (this.commentsByWish[wishId].length < originalLength) {
          removed = true;
          utils_envUtils.devLog.log(`[CommentStore] 从同步删除评论: ${commentId}`);
        }
      });
      if (removed) {
        this._saveToStorage();
      }
    },
    // 清理本地数据
    clearLocalData() {
      this.commentsByWish = {};
      this.syncTimestamps = {};
      this._saveToStorage();
      utils_envUtils.devLog.log("[CommentStore] 清理本地评论数据");
    },
    // 保存到本地存储
    _saveToStorage() {
      try {
        common_vendor.index.setStorageSync("commentsByWish", JSON.stringify(this.commentsByWish));
        common_vendor.index.setStorageSync("commentSyncTimestamps", JSON.stringify(this.syncTimestamps));
      } catch (error) {
        utils_envUtils.devLog.error("[CommentStore] 保存到本地存储失败:", error);
      }
    },
    // 从本地存储加载
    _loadFromStorage() {
      try {
        const storedComments = common_vendor.index.getStorageSync("commentsByWish");
        if (storedComments) {
          this.commentsByWish = JSON.parse(storedComments);
        }
        const storedTimestamps = common_vendor.index.getStorageSync("commentSyncTimestamps");
        if (storedTimestamps) {
          this.syncTimestamps = JSON.parse(storedTimestamps);
        }
      } catch (error) {
        utils_envUtils.devLog.error("[CommentStore] 从本地存储加载失败:", error);
      }
    },
    // 事件监听
    on(event, callback) {
      if (!this.listeners[event]) {
        this.listeners[event] = [];
      }
      this.listeners[event].push(callback);
    },
    // 触发事件
    _emit(event, data) {
      if (this.listeners[event]) {
        this.listeners[event].forEach((callback) => {
          try {
            callback(data);
          } catch (error) {
            utils_envUtils.devLog.error("[CommentStore] 事件回调执行失败:", error);
          }
        });
      }
    }
  }
});
function initCommentStore() {
  try {
    const commentStore = useCommentStore();
    commentStore._loadFromStorage();
    return commentStore;
  } catch (error) {
    common_vendor.index.__f__("error", "at store/comment.js:299", "[CommentStore] 初始化失败:", error);
    return null;
  }
}
exports.initCommentStore = initCommentStore;
exports.useCommentStore = useCommentStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/comment.js.map

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.address-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.empty-address {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}
.empty-address .empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}
.empty-address .add-btn {
  display: flex;
  align-items: center;
  background-color: #8a2be2;
  color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-top: 30rpx;
}
.empty-address .add-btn .uni-icons {
  margin-right: 8rpx;
}
.empty-address .add-btn text {
  color: #fff;
}
.address-list .address-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.address-list .address-item .address-info {
  margin-bottom: 20rpx;
}
.address-list .address-item .address-info .address-top {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.address-list .address-item .address-info .address-top .name {
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 20rpx;
}
.address-list .address-item .address-info .address-top .phone {
  font-size: 28rpx;
  color: #666;
}
.address-list .address-item .address-info .address-top .default-tag {
  margin-left: auto;
  background-color: #8a2be2;
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.address-list .address-item .address-info .address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}
.address-list .address-item .address-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}
.address-list .address-item .address-actions .action-buttons {
  display: flex;
}
.address-list .address-item .address-actions .action-buttons .action-btn {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  margin-left: 30rpx;
}
.address-list .address-item .address-actions .action-buttons .action-btn .uni-icons {
  margin-right: 4rpx;
}
.address-list .address-item .address-actions .action-buttons .action-btn.edit {
  color: #8a2be2;
}
.address-list .address-item .address-actions .action-buttons .action-btn.delete {
  color: #f56c6c;
}
.address-list .address-item .address-actions .default-switch {
  display: flex;
  align-items: center;
}
.address-list .address-item .address-actions .default-switch .switch-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}
.bottom-add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #8a2be2;
  color: #fff;
  padding: 24rpx 0;
  border-radius: 12rpx;
  font-size: 30rpx;
  margin-top: 30rpx;
}
.bottom-add-btn .uni-icons {
  margin-right: 10rpx;
}
.bottom-add-btn text {
  color: #fff;
}
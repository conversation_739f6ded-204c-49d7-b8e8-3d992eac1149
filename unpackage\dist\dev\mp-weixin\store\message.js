"use strict";
const common_vendor = require("../common/vendor.js");
const useMessageStore = common_vendor.defineStore("message", {
  state: () => ({
    messages: [],
    isInitialized: false
  }),
  getters: {
    // 获取所有消息
    getAllMessages: (state) => state.messages,
    // 获取未读消息数量
    getUnreadCount: (state) => {
      return state.messages.filter((msg) => !msg.isRead).length;
    }
  },
  actions: {
    // 初始化消息数据
    initMessages() {
      common_vendor.index.__f__("log", "at store/message.js:22", "[messageStore] Initializing messages...");
      const storedMessages = common_vendor.index.getStorageSync("messages");
      if (storedMessages) {
        this.messages = JSON.parse(storedMessages);
      } else {
        this.messages = [
          {
            id: "1",
            type: "system",
            title: "欢迎使用心愿清单",
            content: "欢迎使用心愿清单应用，开始记录您的心愿吧！",
            createDate: (/* @__PURE__ */ new Date()).toISOString(),
            isRead: false
          }
        ];
        this._saveToStorage();
      }
      this.isInitialized = true;
      common_vendor.index.__f__("log", "at store/message.js:43", "[messageStore] Messages initialized, count:", this.messages.length);
    },
    // 添加消息
    addMessage(message) {
      message.id = Date.now().toString();
      message.createDate = (/* @__PURE__ */ new Date()).toISOString();
      message.isRead = false;
      this.messages.unshift(message);
      this._saveToStorage();
    },
    // 标记消息为已读
    markAsRead(id) {
      const message = this.messages.find((msg) => msg.id === id);
      if (message) {
        message.isRead = true;
        this._saveToStorage();
      }
    },
    // 标记所有消息为已读
    markAllAsRead() {
      this.messages.forEach((msg) => {
        msg.isRead = true;
      });
      this._saveToStorage();
    },
    // 删除消息
    deleteMessage(id) {
      this.messages = this.messages.filter((msg) => msg.id !== id);
      this._saveToStorage();
    },
    // 清理本地数据（用于用户退出登录时清除旧用户数据）
    clearLocalData() {
      common_vendor.index.__f__("log", "at store/message.js:84", "[messageStore] Clearing user-specific data...");
      this.messages = this.messages.filter((msg) => msg.type === "system");
      if (this.messages.length === 0) {
        this.messages = [
          {
            id: "welcome_" + Date.now(),
            type: "system",
            title: "欢迎使用心愿清单",
            content: "请登录后开始记录您的心愿吧！",
            createDate: (/* @__PURE__ */ new Date()).toISOString(),
            isRead: false
          }
        ];
      }
      this.isInitialized = false;
      common_vendor.index.removeStorageSync("messages");
      common_vendor.index.removeStorageSync("messagesLastSyncTime");
      this._saveToStorage();
      common_vendor.index.__f__("log", "at store/message.js:112", "[messageStore] User-specific data cleared, system messages preserved");
    },
    // 保存到本地存储
    _saveToStorage() {
      common_vendor.index.setStorageSync("messages", JSON.stringify(this.messages));
    }
  }
});
exports.useMessageStore = useMessageStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/message.js.map

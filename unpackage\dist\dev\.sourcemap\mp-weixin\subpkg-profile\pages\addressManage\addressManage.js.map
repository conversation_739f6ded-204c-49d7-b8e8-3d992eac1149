{"version": 3, "file": "addressManage.js", "sources": ["subpkg-profile/pages/addressManage/addressManage.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGtnLXByb2ZpbGVccGFnZXNcYWRkcmVzc01hbmFnZVxhZGRyZXNzTWFuYWdlLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"address-container\">\r\n\t\t<view class=\"address-list\">\r\n\t\t\t<!-- 空状态 -->\r\n\t\t\t<view v-if=\"addressList.length === 0 && !loading\" class=\"empty-address\">\r\n\t\t\t\t<text class=\"empty-text\">暂无地址，请添加</text>\r\n\t\t\t\t<view class=\"add-btn\" @click=\"addNewAddress\">\r\n\t\t\t\t\t<uni-icons type=\"plusempty\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t<text>新增地址</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 地址列表 -->\r\n\t\t\t<view v-else>\r\n\t\t\t\t<view class=\"address-item\" v-for=\"(address, index) in addressList\" :key=\"address._id\">\r\n\t\t\t\t\t<view class=\"address-info\">\r\n\t\t\t\t\t\t<view class=\"address-top\">\r\n\t\t\t\t\t\t\t<text class=\"name\">{{ address.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"phone\">{{ address.phone }}</text>\r\n\t\t\t\t\t\t\t<view v-if=\"address.isDefault\" class=\"default-tag\">默认</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"address-detail\">{{ address.province }}{{ address.city }}{{ address.district }}{{ address.address }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"address-actions\">\r\n\t\t\t\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"action-btn edit\" @click=\"editAddress(address, index)\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"16\" color=\"#8a2be2\"></uni-icons>\r\n\t\t\t\t\t\t\t\t<text>编辑</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"action-btn delete\" @click=\"deleteAddress(address._id, index)\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"16\" color=\"#f56c6c\"></uni-icons>\r\n\t\t\t\t\t\t\t\t<text>删除</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"default-switch\">\r\n\t\t\t\t\t\t\t<text class=\"switch-label\">默认地址</text>\r\n\t\t\t\t\t\t\t<switch \r\n\t\t\t\t\t\t\t\t:checked=\"address.isDefault\" \r\n\t\t\t\t\t\t\t\t@change=\"toggleDefaultAddress(address._id, index, $event)\"\r\n\t\t\t\t\t\t\t\tcolor=\"#ff9500\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform:scale(0.8)\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 底部添加按钮 -->\r\n\t\t\t\t<view class=\"bottom-add-btn\" @click=\"addNewAddress\">\r\n\t\t\t\t\t<uni-icons type=\"plusempty\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t<text>新增地址</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { useAddressStore } from '../../store/address.js'\r\nimport { useUserStore } from '@/store/user.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\taddressList: [],\r\n\t\t\tloading: false,\r\n\t\t\taddressStore: null,\r\n\t\t\tuserStore: null\r\n\t\t}\r\n\t},\r\n\r\n\tonLoad() {\r\n\t\tthis.initStore()\r\n\r\n\t\t// 检查登录状态，如果未登录则由导航拦截器处理跳转\r\n\t\tif (!this.userStore.isLogin) {\r\n\t\t\tconsole.log('[addressManage] 用户未登录，等待导航拦截器处理')\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tthis.getAddressList();\r\n\t},\r\n\t\r\n\tonShow() {\r\n\t\t// 页面显示时检查登录状态，如果已登录则检查并同步地址数据\r\n\t\tif (this.userStore && this.userStore.isLogin) {\r\n\t\t\tthis.checkAndSyncAddressData();\r\n\t\t}\r\n\t},\r\n\r\n\tmethods: {\r\n\t\t// 初始化Store\r\n\t\tinitStore() {\r\n\t\t\tthis.addressStore = useAddressStore()\r\n\t\t\tthis.userStore = useUserStore()\r\n\t\t},\r\n\t\t\r\n\t\t// 检查并同步地址数据\r\n\t\tasync checkAndSyncAddressData() {\r\n\t\t\tif (this.loading) return;\r\n\r\n\t\t\tconsole.log('[AddressManage] 检查地址数据同步状态...');\r\n\r\n\t\t\t// 检查本地是否有缓存数据\r\n\t\t\tconst hasLocalData = this.addressStore && this.addressStore.addressList.length > 0;\r\n\t\t\tconst lastSyncTime = this.addressStore ? this.addressStore.lastSyncTime : null;\r\n\t\t\tconst now = Date.now();\r\n\r\n\t\t\t// 如果没有本地数据，或者超过5分钟未同步，则从云端获取\r\n\t\t\tconst shouldSync = !hasLocalData || !lastSyncTime || (now - lastSyncTime > 5 * 60 * 1000);\r\n\r\n\t\t\tif (shouldSync) {\r\n\t\t\t\tconsole.log('[AddressManage] 需要同步地址数据，原因:',\r\n\t\t\t\t\t!hasLocalData ? '无本地数据' :\r\n\t\t\t\t\t!lastSyncTime ? '未记录同步时间' : '超过5分钟未同步');\r\n\t\t\t\tawait this.getAddressList();\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('[AddressManage] 使用本地缓存数据');\r\n\t\t\t\tthis.addressList = this.addressStore.addressList;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 获取地址列表\r\n\t\tasync getAddressList() {\r\n\t\t\tif (this.loading) return;\r\n\r\n\t\t\tthis.loading = true;\r\n\t\t\ttry {\r\n\t\t\t\tconst result = await uniCloud.importObject('address-center').getAddressList();\r\n\r\n\t\t\t\tif (result.code === 0) {\r\n\t\t\t\t\tthis.addressList = result.data.list;\r\n\t\t\t\t\t// 同步到Store并记录同步时间\r\n\t\t\t\t\tif (this.addressStore) {\r\n\t\t\t\t\t\tthis.addressStore.setAddressList(result.data.list);\r\n\t\t\t\t\t\tthis.addressStore.lastSyncTime = Date.now();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log('[AddressManage] 地址数据同步完成，共', result.data.list.length, '条');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: result.message || '获取地址列表失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取地址列表失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '网络错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 添加新地址\r\n\t\taddNewAddress() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/subpkg-profile/pages/addressEdit/addressEdit'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 编辑地址\r\n\t\teditAddress(address, index) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/subpkg-profile/pages/addressEdit/addressEdit?id=${address._id}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 切换默认地址状态\r\n\t\tasync toggleDefaultAddress(addressId, index, event) {\r\n\t\t\tif (!addressId) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '地址ID无效',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst isChecked = event.detail.value;\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tuni.showLoading({ title: isChecked ? '设置中...' : '取消中...' });\r\n\t\t\t\t\r\n\t\t\t\tlet result;\r\n\t\t\t\tif (isChecked) {\r\n\t\t\t\t\t// 使用Store设置默认地址\r\n\t\t\t\t\tif (this.addressStore) {\r\n\t\t\t\t\t\tresult = await this.addressStore.setDefaultAddress(addressId);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// fallback to direct call\r\n\t\t\t\t\tresult = await uniCloud.importObject('address-center').setDefaultAddress(addressId);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 取消默认地址\r\n\t\t\t\t\tresult = await uniCloud.importObject('address-center').updateAddress(addressId, {\r\n\t\t\t\t\t\tis_default: false\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新Store中的状态\r\n\t\t\t\t\tif (this.addressStore && result.code === 0) {\r\n\t\t\t\t\t\tthis.addressStore._clearDefaultAddress();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (result.code === 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: isChecked ? '设置成功' : '已取消默认',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 刷新地址列表\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.getAddressList();\r\n\t\t\t\t\t}, 800);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: result.message || '操作失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 如果操作失败，刷新列表恢复状态\r\n\t\t\t\t\tthis.getAddressList();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('操作异常:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '操作失败: ' + (error.message || error),\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 3000\r\n\t\t\t\t});\r\n\t\t\t\t// 如果操作失败，刷新列表恢复状态\r\n\t\t\t\tthis.getAddressList();\r\n\t\t\t} finally {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 删除地址\r\n\t\tasync deleteAddress(addressId, index) {\r\n\t\t\tconst confirmResult = await new Promise((resolve) => {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '删除地址',\r\n\t\t\t\t\tcontent: '确定要删除这个地址吗？',\r\n\t\t\t\t\tsuccess: (res) => resolve(res.confirm)\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tif (!confirmResult) return;\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tuni.showLoading({ title: '删除中...' });\r\n\t\t\t\t\r\n\t\t\t\tconst result = await uniCloud.importObject('address-center').deleteAddress(addressId);\r\n\t\t\t\t\r\n\t\t\t\tif (result.code === 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.getAddressList();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: result.message || '删除失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('删除地址失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '网络错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t} finally {\r\n\t\t\t\tuni.hideLoading().catch(() => {})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.address-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.empty-address {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 80rpx 0;\r\n\t\r\n\t.empty-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.add-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #8a2be2;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 16rpx 30rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t\t\r\n\t\t.uni-icons {\r\n\t\t\tmargin-right: 8rpx;\r\n\t\t}\r\n\t\t\r\n\t\ttext {\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.address-list {\r\n\t.address-item {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.address-info {\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.address-top {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\r\n\t\t\t\t.name {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.phone {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.default-tag {\r\n\t\t\t\t\tmargin-left: auto;\r\n\t\t\t\t\tbackground-color: #8a2be2;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.address-detail {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.address-actions {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tborder-top: 1rpx solid #f0f0f0;\r\n\t\t\tpadding-top: 20rpx;\r\n\t\t\t\r\n\t\t\t.action-buttons {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t\r\n\t\t\t\t.action-btn {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tmargin-left: 30rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.uni-icons {\r\n\t\t\t\t\t\tmargin-right: 4rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.edit {\r\n\t\t\t\t\t\tcolor: #8a2be2;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.delete {\r\n\t\t\t\t\t\tcolor: #f56c6c;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.default-switch {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.switch-label {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.bottom-add-btn {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground-color: #8a2be2;\r\n\tcolor: #fff;\r\n\tpadding: 24rpx 0;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 30rpx;\r\n\tmargin-top: 30rpx;\r\n\t\r\n\t.uni-icons {\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\ttext {\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/subpkg-profile/pages/addressManage/addressManage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "useAddressStore", "useUserStore", "uniCloud"], "mappings": ";;;;AA8DA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,aAAa,CAAE;AAAA,MACf,SAAS;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,UAAU;AAGf,QAAI,CAAC,KAAK,UAAU,SAAS;AAC5BA,oBAAAA,MAAA,MAAA,OAAA,8DAAY,iCAAiC;AAC7C;AAAA,IACD;AAEA,SAAK,eAAc;AAAA,EACnB;AAAA,EAED,SAAS;AAER,QAAI,KAAK,aAAa,KAAK,UAAU,SAAS;AAC7C,WAAK,wBAAuB;AAAA,IAC7B;AAAA,EACA;AAAA,EAED,SAAS;AAAA;AAAA,IAER,YAAY;AACX,WAAK,eAAeC,4CAAgB;AACpC,WAAK,YAAYC,wBAAa;AAAA,IAC9B;AAAA;AAAA,IAGD,MAAM,0BAA0B;AAC/B,UAAI,KAAK;AAAS;AAElBF,oBAAAA,MAAA,MAAA,OAAA,+DAAY,+BAA+B;AAG3C,YAAM,eAAe,KAAK,gBAAgB,KAAK,aAAa,YAAY,SAAS;AACjF,YAAM,eAAe,KAAK,eAAe,KAAK,aAAa,eAAe;AAC1E,YAAM,MAAM,KAAK;AAGjB,YAAM,aAAa,CAAC,gBAAgB,CAAC,gBAAiB,MAAM,eAAe,IAAI,KAAK;AAEpF,UAAI,YAAY;AACfA,sBAAAA,MAAA;AAAA,UAAA;AAAA,UAAA;AAAA,UAAY;AAAA,UACX,CAAC,eAAe,UAChB,CAAC,eAAe,YAAY;AAAA,QAAU;AACvC,cAAM,KAAK;aACL;AACNA,sBAAAA,MAAA,MAAA,OAAA,+DAAY,0BAA0B;AACtC,aAAK,cAAc,KAAK,aAAa;AAAA,MACtC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACtB,UAAI,KAAK;AAAS;AAElB,WAAK,UAAU;AACf,UAAI;AACH,cAAM,SAAS,MAAMG,cAAQ,GAAC,aAAa,gBAAgB,EAAE;AAE7D,YAAI,OAAO,SAAS,GAAG;AACtB,eAAK,cAAc,OAAO,KAAK;AAE/B,cAAI,KAAK,cAAc;AACtB,iBAAK,aAAa,eAAe,OAAO,KAAK,IAAI;AACjD,iBAAK,aAAa,eAAe,KAAK,IAAG;AAAA,UAC1C;AACAH,wBAAAA,MAAY,MAAA,OAAA,+DAAA,8BAA8B,OAAO,KAAK,KAAK,QAAQ,GAAG;AAAA,eAChE;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,+DAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,SAAS,OAAO;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,oDAAoD,QAAQ,GAAG;AAAA,MACrE,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,qBAAqB,WAAW,OAAO,OAAO;AACnD,UAAI,CAAC,WAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,YAAM,YAAY,MAAM,OAAO;AAE/B,UAAI;AACHA,sBAAG,MAAC,YAAY,EAAE,OAAO,YAAY,WAAW,SAAS,CAAC;AAE1D,YAAI;AACJ,YAAI,WAAW;AAEd,cAAI,KAAK,cAAc;AACtB,qBAAS,MAAM,KAAK,aAAa,kBAAkB,SAAS;AAAA,iBACtD;AAEP,qBAAS,MAAMG,cAAAA,GAAS,aAAa,gBAAgB,EAAE,kBAAkB,SAAS;AAAA,UAClF;AAAA,eACM;AAEN,mBAAS,MAAMA,cAAAA,GAAS,aAAa,gBAAgB,EAAE,cAAc,WAAW;AAAA,YAC/E,YAAY;AAAA,UACb,CAAC;AAGD,cAAI,KAAK,gBAAgB,OAAO,SAAS,GAAG;AAC3C,iBAAK,aAAa;UACnB;AAAA,QACD;AAEA,YAAI,OAAO,SAAS,GAAG;AACtBH,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,YAAY,SAAS;AAAA,YAC5B,MAAM;AAAA,UACP,CAAC;AAED,qBAAW,MAAM;AAChB,iBAAK,eAAc;AAAA,UACnB,GAAE,GAAG;AAAA,eACA;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAED,eAAK,eAAc;AAAA,QACpB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,+DAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAED,aAAK,eAAc;AAAA,MACpB,UAAU;AACTA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,cAAc,WAAW,OAAO;AACrC,YAAM,gBAAgB,MAAM,IAAI,QAAQ,CAAC,YAAY;AACpDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ,QAAQ,IAAI,OAAO;AAAA,QACtC,CAAC;AAAA,MACF,CAAC;AAED,UAAI,CAAC;AAAe;AAEpB,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,cAAM,SAAS,MAAMG,iBAAS,aAAa,gBAAgB,EAAE,cAAc,SAAS;AAEpF,YAAI,OAAO,SAAS,GAAG;AACtBH,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD,eAAK,eAAc;AAAA,eACb;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,+DAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACTA,sBAAAA,MAAI,YAAW,EAAG,MAAM,MAAM;AAAA,QAAA,CAAE;AAAA,MACjC;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrRA,GAAG,WAAW,eAAe;"}
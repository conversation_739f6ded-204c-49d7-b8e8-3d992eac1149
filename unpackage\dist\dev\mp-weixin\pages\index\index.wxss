/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 全局禁用水平滚动 - 最强规则 */
page {
  overflow-x: hidden !important;
  overflow-y: auto !important;
  width: 100vw !important;
  max-width: 100vw !important;
  min-width: 100vw !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* 确保body也不会水平滚动 */
body {
  overflow-x: hidden !important;
  overflow-y: auto !important;
  width: 100vw !important;
  max-width: 100vw !important;
  min-width: 100vw !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* 确保主要元素都不会导致水平滚动 */
view, text, image, button, input, textarea {
  box-sizing: border-box;
}

/* 主容器样式 - 禁用水平滚动 */
.wish-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 120rpx;
  /* 调整底部填充，为tabbar预留空间 */
  box-sizing: border-box;
  overflow-x: hidden !important;
  /* 强制禁止水平滚动 */
  overflow-y: auto !important;
  /* 允许垂直滚动 */
  width: 100vw !important;
  /* 确保容器宽度等于视口 */
  max-width: 100vw !important;
  /* 防止内容溢出 */
  min-width: 100vw !important;
  /* 确保最小宽度 */
  left: 0 !important;
  /* 确保容器从左边开始 */
  right: 0 !important;
  /* 确保容器到右边结束 */
  touch-action: pan-y;
  /* 只允许垂直滑动 */
  -webkit-user-select: none;
  user-select: none;
  /* 全局下拉刷新禁用样式 */
  /* 手势禁用状态的强化保护 */
}
.wish-container.pull-refresh-disabled {
  /* 完全禁用下拉刷新的各种可能触发方式 */
  overflow-y: hidden !important;
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
  overscroll-behavior-y: contain !important;
  /* 禁用所有可能的滚动反弹效果 */
  -webkit-overflow-scrolling: auto !important;
  /* 在某些平台上阻止下拉刷新的额外CSS */
  position: relative !important;
}
.wish-container.pull-refresh-disabled::before {
  content: "";
  position: absolute;
  top: -100px;
  left: 0;
  right: 0;
  height: 100px;
  background: transparent;
  pointer-events: none;
  z-index: 9999;
}
.wish-container.gesture-disabled {
  /* 最强的下拉刷新禁用策略 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
  touch-action: none !important;
  -webkit-overflow-scrolling: auto !important;
  overscroll-behavior: none !important;
  overscroll-behavior-y: none !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  user-select: none !important;
  /* 阻止所有可能的下拉触发 */
}
.wish-container.gesture-disabled::before {
  content: "";
  position: absolute;
  top: -200px;
  left: 0;
  right: 0;
  height: 200px;
  background: transparent;
  pointer-events: none !important;
  z-index: 99999;
}
.wish-container.drag-mode {
  /* 拖拽模式样式 */
  /* 完全禁止滚动 */
  overflow: hidden !important;
  touch-action: none !important;
  /* 禁止所有触摸操作 */
  overscroll-behavior: none !important;
  /* 禁止所有过度滚动行为 */
  /* 额外的下拉刷新禁用 */
  -webkit-overflow-scrolling: auto !important;
  /* 禁用iOS弹性滚动 */
  overscroll-behavior-y: contain !important;
  /* 防止Y轴过度滚动 */
  /* 防止任何可能触发下拉刷新的行为 */
}
.wish-container.drag-mode::before, .wish-container.drag-mode::after {
  pointer-events: none !important;
}
.wish-container.drag-mode .add-wish-btn {
  opacity: 0.5;
}
.wish-container.drag-mode .wish-list {
  scroll-behavior: smooth;
  transition: scroll-behavior 0.3s;
  pointer-events: none;
  /* 在拖拽时禁用滚动容器的指针事件 */
  /* 强化禁用下拉刷新 */
  overflow-y: hidden !important;
  touch-action: none !important;
  overscroll-behavior-y: none !important;
  /* 但允许卡片本身的指针事件 */
}
.wish-container.drag-mode .wish-list .wish-card-container {
  pointer-events: auto;
}
.wish-container.drag-mode .wish-card-container {
  /* 性能优化：避免使用 transition: all，只对需要的属性设置过渡 */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-out;
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: auto;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.wish-container.drag-mode .wish-card-container:not(.is-dragging) {
  opacity: 0.8;
  transform: scale(0.98) translateZ(0);
}
.wish-container.drag-mode .wish-card-container.is-dragging {
  transform: scale(1.05) translateZ(0);
  opacity: 1;
  z-index: 100;
  background-color: #fff;
  will-change: transform, opacity;
  /* 阴影单独处理，避免影响主动画 */
  box-shadow: 0 10rpx 30rpx rgba(138, 43, 226, 0.5);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.2s ease-out, box-shadow 0.3s ease-out;
  border-radius: 12rpx;
  position: relative;
}

/* 隐藏滚动条但保留滚动功能 */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

/* 心愿列表容器 */
.wish-list {
  padding: 0;
  /* 移除内边距，让卡片控制自己的边距 */
  position: relative;
  padding-bottom: 150rpx;
  /* 增加底部内边距，确保最后一个卡片不被tabbar遮挡 */
  overflow-x: hidden;
  /* 禁止水平滚动 */
  width: 100%;
  /* 确保宽度不超过父容器 */
  max-width: 100%;
  /* 防止内容溢出 */
}

/* 卡片列表内容容器 */
.wish-list-content {
  width: 100%;
  max-width: 100%;
  /* 防止内容溢出 */
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  /* 禁止水平滚动 */
  /* 移除居中对齐，让卡片根据自己的边距自然排列 */
}
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100rpx;
  padding-bottom: 100rpx;
}
.empty-list .empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}
.add-wish-btn {
  position: fixed;
  right: 40rpx;
  bottom: 160rpx;
  /* 调整底部位置，确保不被tabbar遮挡 */
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #8a2be2;
  box-shadow: 0 6rpx 16rpx rgba(138, 43, 226, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
}
.add-wish-btn .plus-icon {
  width: 50rpx;
  height: 50rpx;
  position: relative;
}
.add-wish-btn .plus-icon .h-line, .add-wish-btn .plus-icon .v-line {
  background-color: #fff;
  position: absolute;
  border-radius: 4rpx;
}
.add-wish-btn .plus-icon .h-line {
  width: 50rpx;
  height: 6rpx;
  top: 22rpx;
  /* 居中位置 (50-6)/2 */
  left: 0;
}
.add-wish-btn .plus-icon .v-line {
  height: 50rpx;
  width: 6rpx;
  left: 22rpx;
  /* 居中位置 (50-6)/2 */
  top: 0;
}
.sync-debug-btn {
  position: fixed;
  right: 40rpx;
  bottom: 280rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 150, 255, 0.8);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 98;
}
.sync-debug-btn .debug-text {
  color: white;
  font-size: 32rpx;
}

/* Vue transition-group 动画样式 */
.list-transition-group {
  position: relative;
}

/* 优化后的删除动画 - 分阶段执行 */
.list-item-leave-active {
  /* 第一阶段：快速淡出和移动 */
  transition: opacity 0.2s ease-out, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  will-change: opacity, transform;
}
.list-item-leave-to {
  opacity: 0;
  /* 使用 translate3d 启用硬件加速 */
  transform: translate3d(-30px, 0, 0) scale(0.9);
}

/* 高度收缩动画 - 延迟执行 */
.list-item-height-collapse {
  transition: height 0.2s ease-out 0.2s, margin 0.2s ease-out 0.2s, padding 0.2s ease-out 0.2s;
  height: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  overflow: hidden;
}

/* 其他元素位置调整动画 - 优化性能 */
.list-item-move {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

/* 优化后的进入动画 */
.list-item-enter-active {
  transition: opacity 0.3s ease-out, transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity, transform;
}
.list-item-enter-from {
  opacity: 0;
  /* 使用 translate3d 启用硬件加速 */
  transform: translate3d(30px, 0, 0) scale(0.9);
}

/* 响应式动画优化 */
/* 尊重用户的动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
.wish-card-container,
.list-item-leave-active,
.list-item-enter-active,
.list-item-move {
    transition: none !important;
    animation: none !important;
}

  /* 保持基本的视觉反馈，但移除动画 */
.wish-card-container.is-dragging {
    transform: scale(1.02) translateZ(0);
    box-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.3);
}
}
/* 低端设备优化 */
@media (max-device-width: 768px) and (-webkit-max-device-pixel-ratio: 1) {
.wish-card-container {
    transition-duration: 0.2s;
    /* 缩短动画时间 */
}
.list-item-leave-active,
.list-item-enter-active {
    transition-duration: 0.25s;
    /* 缩短列表动画时间 */
}

  /* 移除复杂的阴影效果 */
.wish-card-container.is-dragging {
    box-shadow: 0 4rpx 8rpx rgba(138, 43, 226, 0.2);
}
}
/* 高刷新率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 1.25), (min-resolution: 120dpi) {
.wish-card-container,
.list-item-move {
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
}
/* 按钮点击反馈动画优化 */
.add-wish-btn {
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.1s ease-out;
  transform: translateZ(0);
  will-change: auto;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.add-wish-btn:active {
  transform: translate3d(0, 2px, 0) scale(0.98);
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.3);
  will-change: transform, box-shadow;
}

/* 支持 hover 的设备上的悬停效果 */
@media (hover: hover) and (pointer: fine) {
.add-wish-btn:hover {
    transform: translate3d(0, -1px, 0);
    box-shadow: 0 4px 12px rgba(138, 43, 226, 0.2);
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}
}
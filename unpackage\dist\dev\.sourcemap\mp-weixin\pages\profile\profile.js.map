{"version": 3, "file": "profile.js", "sources": ["pages/profile/profile.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9wcm9maWxlLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"profile-container\">\r\n\t\t<!-- 用户信息区 -->\r\n\t\t<view class=\"user-card card glass\">\r\n\t\t\t<view v-if=\"!isLogin\" class=\"login-btn\" @click=\"goToLogin\">\r\n\t\t\t\t<text>登录/注册</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"user-info\">\r\n\t\t\t\t<image class=\"avatar\" :src=\"userStore.avatarUrl\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"user-detail\">\r\n\t\t\t\t\t<view class=\"username\">{{ userStore.nickname }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"edit-btn\" @click=\"goToUserEdit\">\r\n\t\t\t\t\t<uni-icons type=\"gear\" size=\"24\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 功能区 -->\r\n\t\t<view class=\"function-list card\">\r\n\t\t\t<view class=\"function-item\" @click=\"goToPage('/subpkg-profile/pages/addressManage/addressManage')\">\r\n\t\t\t\t<view class=\"function-icon primary-bg\">\r\n\t\t\t\t\t<uni-icons type=\"location\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-title\">地址管理</view>\r\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"function-item\" @click=\"goToPage('/subpkg-wish/pages/groupManage/groupManage')\">\r\n\t\t\t\t<view class=\"function-icon primary-bg\">\r\n\t\t\t\t\t<uni-icons type=\"bars\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-title\">分组管理</view>\r\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t</view>\r\n\r\n\t\t<view class=\"function-item\" @click=\"goToPage('/subpkg-profile/pages/historyWish/historyWish')\">\r\n\t\t\t\t<view class=\"function-icon primary-bg\">\r\n\t\t\t\t\t<uni-icons type=\"checkbox-filled\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-title\">历史心愿</view>\r\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"function-item\" @click=\"showShareOptions\">\r\n\t\t\t\t<view class=\"function-icon primary-bg\">\r\n\t\t\t\t\t<uni-icons type=\"redo-filled\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-title\">分享应用</view>\r\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"function-item\" @click=\"goToPage('/subpkg-profile/pages/about/about')\">\r\n\t\t\t\t<view class=\"function-icon primary-bg\">\r\n\t\t\t\t\t<uni-icons type=\"info-filled\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-title\">关于我们</view>\r\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 退出登录 -->\r\n\t\t<view v-if=\"isLogin\" class=\"logout-btn\" @click=\"showLogoutConfirm\">\r\n\t\t\t退出登录\r\n\t\t</view>\r\n\t\t\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { useUserStore } from '@/store/user.js'\r\n\timport { useGroupStore } from '@/store/group.js'\r\n\timport { useWishStore } from '@/store/wish.js'\r\n\timport { useMessageStore } from '@/store/message.js'\r\n\t\r\n\texport default {\r\n\t\tsetup() {\r\n\t\t\tconst userStore = useUserStore()\r\n\t\t\tconst groupStore = useGroupStore()\r\n\t\t\tconst wishStore = useWishStore()\r\n\t\t\tconst messageStore = useMessageStore()\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\tuserStore,\r\n\t\t\t\tgroupStore,\r\n\t\t\t\twishStore,\r\n\t\t\t\tmessageStore\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisLogin() {\r\n\t\t\t\treturn this.userStore.hasLogin\r\n\t\t\t},\r\n\t\t\tuserInfo() {\r\n\t\t\t\treturn this.userStore.getUserInfo || {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// 页面显示时的处理\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\r\n\t\t\t// 跳转到登录页\r\n\t\t\tgoToLogin() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 通用页面跳转\r\n\t\t\tgoToPage(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示分享选项\r\n\t\t\tshowShareOptions() {\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['分享给微信好友', '分享到朋友圈'],\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t// 这里只是模拟，实际需要调用分享API\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '分享成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示退出确认\r\n\t\t\tshowLogoutConfirm() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '退出登录',\r\n\t\t\t\t\tcontent: '确定要退出当前账号吗？',\r\n\t\t\t\t\tshowCancel: true,\r\n\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\tconfirmText: '确定',\r\n\t\t\t\t\tconfirmColor: '#e60012',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击确定退出登录');\r\n\t\t\t\t\t\t\tthis.logout();\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('显示退出确认弹窗失败:', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 退出登录\r\n\t\t\tlogout() {\r\n\t\t\t\tthis.userStore.logout()\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已退出登录',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到用户编辑页\r\n\t\t\tgoToUserEdit() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/subpkg-profile/pages/userEdit/userEdit'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.profile-container {\r\n\t\t/* min-height: 100vh; */\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\t\r\n\t.user-card {\r\n\t\theight: 200rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-image: linear-gradient(135deg, rgba(138, 43, 226, 0.8), rgba(186, 85, 211, 0.8));\r\n\t\tposition: relative;\r\n\t\t\r\n\t\t.login-btn {\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tpadding: 20rpx 60rpx;\r\n\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.6);\r\n\t\t\tborder-radius: 40rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.user-info {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 0 40rpx;\r\n\t\t\t\r\n\t\t\t.avatar {\r\n\t\t\t\twidth: 120rpx;\r\n\t\t\t\theight: 120rpx;\r\n\t\t\t\tborder-radius: 60rpx;\r\n\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\tborder: 4rpx solid rgba(255, 255, 255, 0.6);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.user-detail {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t\r\n\t\t\t\t.username {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.user-id {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.edit-btn {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 20rpx;\r\n\t\t\t\tright: 20rpx;\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tbackground-color: rgba(255, 255, 255, 0.3);\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tz-index: 10;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.function-list {\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.function-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 30rpx 20rpx;\r\n\t\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.function-icon {\r\n\t\t\t\twidth: 70rpx;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tborder-radius: 35rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.primary-bg {\r\n\t\t\t\t\tbackground-color: #8a2be2;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.function-title {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.logout-btn {\r\n\t\tmargin-top: 60rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tcolor: #ff5a5f;\r\n\t\tfont-size: 32rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding: 24rpx 0;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/pages/profile/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useGroupStore", "useWishStore", "useMessageStore", "uni"], "mappings": ";;;;;;AA4EC,MAAK,YAAU;AAAA,EACd,QAAQ;AACP,UAAM,YAAYA,WAAAA,aAAa;AAC/B,UAAM,aAAaC,YAAAA,cAAc;AACjC,UAAM,YAAYC,WAAAA,aAAa;AAC/B,UAAM,eAAeC,cAAAA,gBAAgB;AAErC,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,UAAU;AACT,aAAO,KAAK,UAAU;AAAA,IACtB;AAAA,IACD,WAAW;AACV,aAAO,KAAK,UAAU,eAAe,CAAC;AAAA,IACvC;AAAA,EACA;AAAA,EACD,SAAS;AAAA,EAER;AAAA,EACD,SAAS;AAAA;AAAA,IAGR,YAAY;AACXC,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,SAAS,KAAK;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd;AAAA,OACA;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AAClBA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU,CAAC,WAAW,QAAQ;AAAA,QAC9B,SAAS,SAAO;AAEfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,QACF;AAAA,OACA;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChBA,0BAAAA,MAAA,MAAA,OAAA,oCAAY,YAAY;AACxB,iBAAK,OAAM;AAAA,UACZ,WAAW,IAAI,QAAQ;AACtBA,0BAAAA,MAAY,MAAA,OAAA,oCAAA,QAAQ;AAAA,UACrB;AAAA,QACA;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,oCAAA,eAAe,GAAG;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,SAAS;AACR,WAAK,UAAU,OAAO;AACtBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKD,GAAG,WAAW,eAAe;"}
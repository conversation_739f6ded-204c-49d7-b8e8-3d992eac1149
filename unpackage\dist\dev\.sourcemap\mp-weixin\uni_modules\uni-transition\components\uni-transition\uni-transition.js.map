{"version": 3, "file": "uni-transition.js", "sources": ["uni_modules/uni-transition/components/uni-transition/uni-transition.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQ3lrMTYvRGVza3RvcC93aXNobGlzdC11bmlhcHAvdW5pX21vZHVsZXMvdW5pLXRyYW5zaXRpb24vY29tcG9uZW50cy91bmktdHJhbnNpdGlvbi91bmktdHJhbnNpdGlvbi52dWU"], "sourcesContent": ["<template>\r\n  <!-- #ifndef APP-NVUE -->\r\n  <view v-show=\"isShow\" ref=\"ani\" :animation=\"animationData\" :class=\"customClass\" :style=\"transformStyles\" @click=\"onClick\"><slot></slot></view>\r\n  <!-- #endif -->\r\n  <!-- #ifdef APP-NVUE -->\r\n  <view v-if=\"isShow\" ref=\"ani\" :animation=\"animationData\" :class=\"customClass\" :style=\"transformStyles\" @click=\"onClick\"><slot></slot></view>\r\n  <!-- #endif -->\r\n</template>\r\n\r\n<script>\r\nimport { createAnimation } from './createAnimation'\r\n\r\n/**\r\n * Transition 过渡动画\r\n * @description 简单过渡动画组件\r\n * @tutorial https://ext.dcloud.net.cn/plugin?id=985\r\n * @property {Boolean} show = [false|true] 控制组件显示或隐藏\r\n * @property {Array|String} modeClass = [fade|slide-top|slide-right|slide-bottom|slide-left|zoom-in|zoom-out] 过渡动画类型\r\n *  @value fade 渐隐渐出过渡\r\n *  @value slide-top 由上至下过渡\r\n *  @value slide-right 由右至左过渡\r\n *  @value slide-bottom 由下至上过渡\r\n *  @value slide-left 由左至右过渡\r\n *  @value zoom-in 由小到大过渡\r\n *  @value zoom-out 由大到小过渡\r\n * @property {Number} duration 过渡动画持续时间\r\n * @property {Object} styles 组件样式，同 css 样式，注意带’-‘连接符的属性需要使用小驼峰写法如：`backgroundColor:red`\r\n */\r\nexport default {\r\n\tname: 'uniTransition',\r\n\temits:['click','change'],\r\n\tprops: {\r\n\t\tshow: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tmodeClass: {\r\n\t\t\ttype: [Array, String],\r\n\t\t\tdefault() {\r\n\t\t\t\treturn 'fade'\r\n\t\t\t}\r\n\t\t},\r\n\t\tduration: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 300\r\n\t\t},\r\n\t\tstyles: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcustomClass:{\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tonceRender:{\r\n\t\t\ttype:Boolean,\r\n\t\t\tdefault:false\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisShow: false,\r\n\t\t\ttransform: '',\r\n\t\t\topacity: 1,\r\n\t\t\tanimationData: {},\r\n\t\t\tdurationTime: 300,\r\n\t\t\tconfig: {}\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tshow: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\tthis.open()\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 避免上来就执行 close,导致动画错乱\r\n\t\t\t\t\tif (this.isShow) {\r\n\t\t\t\t\t\tthis.close()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// 生成样式数据\r\n\t\tstylesObject() {\r\n\t\t\tlet styles = {\r\n\t\t\t\t...this.styles,\r\n\t\t\t\t'transition-duration': this.duration / 1000 + 's'\r\n\t\t\t}\r\n\t\t\tlet transform = ''\r\n\t\t\tfor (let i in styles) {\r\n\t\t\t\tlet line = this.toLine(i)\r\n\t\t\t\ttransform += line + ':' + styles[i] + ';'\r\n\t\t\t}\r\n\t\t\treturn transform\r\n\t\t},\r\n\t\t// 初始化动画条件\r\n\t\ttransformStyles() {\r\n\t\t\treturn 'transform:' + this.transform + ';' + 'opacity:' + this.opacity + ';' + this.stylesObject\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\t// 动画默认配置\r\n\t\tthis.config = {\r\n\t\t\tduration: this.duration,\r\n\t\t\ttimingFunction: 'ease',\r\n\t\t\ttransformOrigin: '50% 50%',\r\n\t\t\tdelay: 0\r\n\t\t}\r\n\t\tthis.durationTime = this.duration\r\n\t},\r\n\tmethods: {\r\n\t\t/**\r\n\t\t *  ref 触发 初始化动画\r\n\t\t */\r\n\t\tinit(obj = {}) {\r\n\t\t\tif (obj.duration) {\r\n\t\t\t\tthis.durationTime = obj.duration\r\n\t\t\t}\r\n\t\t\tthis.animation = createAnimation(Object.assign(this.config, obj),this)\r\n\t\t},\r\n\t\t/**\r\n\t\t * 点击组件触发回调\r\n\t\t */\r\n\t\tonClick() {\r\n\t\t\tthis.$emit('click', {\r\n\t\t\t\tdetail: this.isShow\r\n\t\t\t})\r\n\t\t},\r\n\t\t/**\r\n\t\t * ref 触发 动画分组\r\n\t\t * @param {Object} obj\r\n\t\t */\r\n\t\tstep(obj, config = {}) {\r\n\t\t\tif (!this.animation) return\r\n\t\t\tfor (let i in obj) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tif(typeof obj[i] === 'object'){\r\n\t\t\t\t\t\tthis.animation[i](...obj[i])\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.animation[i](obj[i])\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error(`方法 ${i} 不存在`)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.animation.step(config)\r\n\t\t\treturn this\r\n\t\t},\r\n\t\t/**\r\n\t\t *  ref 触发 执行动画\r\n\t\t */\r\n\t\trun(fn) {\r\n\t\t\tif (!this.animation) return\r\n\t\t\tthis.animation.run(fn)\r\n\t\t},\r\n\t\t// 开始过度动画\r\n\t\topen() {\r\n\t\t\tclearTimeout(this.timer)\r\n\t\t\tthis.transform = ''\r\n\t\t\tthis.isShow = true\r\n\t\t\tlet { opacity, transform } = this.styleInit(false)\r\n\t\t\tif (typeof opacity !== 'undefined') {\r\n\t\t\t\tthis.opacity = opacity\r\n\t\t\t}\r\n\t\t\tthis.transform = transform\r\n\t\t\t// 确保动态样式已经生效后，执行动画，如果不加 nextTick ，会导致 wx 动画执行异常\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t// TODO 定时器保证动画完全执行，目前有些问题，后面会取消定时器\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.animation = createAnimation(this.config, this)\r\n\t\t\t\t\tthis.tranfromInit(false).step()\r\n\t\t\t\t\tthis.animation.run(() => {\r\n\t\t\t\t\t\tthis.transform = ''\r\n\t\t\t\t\t\tthis.opacity = opacity || 1\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\t\tdetail: this.isShow\r\n\t\t\t\t\t})\r\n\t\t\t\t}, 20)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 关闭过度动画\r\n\t\tclose(type) {\r\n\t\t\tif (!this.animation) return\r\n\t\t\tthis.tranfromInit(true)\r\n\t\t\t\t.step()\r\n\t\t\t\t.run(() => {\r\n\t\t\t\t\tthis.isShow = false\r\n\t\t\t\t\tthis.animationData = null\r\n\t\t\t\t\tthis.animation = null\r\n\t\t\t\t\tlet { opacity, transform } = this.styleInit(false)\r\n\t\t\t\t\tthis.opacity = opacity || 1\r\n\t\t\t\t\tthis.transform = transform\r\n\t\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\t\tdetail: this.isShow\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t},\r\n\t\t// 处理动画开始前的默认样式\r\n\t\tstyleInit(type) {\r\n\t\t\tlet styles = {\r\n\t\t\t\ttransform: ''\r\n\t\t\t}\r\n\t\t\tlet buildStyle = (type, mode) => {\r\n\t\t\t\tif (mode === 'fade') {\r\n\t\t\t\t\tstyles.opacity = this.animationType(type)[mode]\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstyles.transform += this.animationType(type)[mode] + ' '\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (typeof this.modeClass === 'string') {\r\n\t\t\t\tbuildStyle(type, this.modeClass)\r\n\t\t\t} else {\r\n\t\t\t\tthis.modeClass.forEach(mode => {\r\n\t\t\t\t\tbuildStyle(type, mode)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\treturn styles\r\n\t\t},\r\n\t\t// 处理内置组合动画\r\n\t\ttranfromInit(type) {\r\n\t\t\tlet buildTranfrom = (type, mode) => {\r\n\t\t\t\tlet aniNum = null\r\n\t\t\t\tif (mode === 'fade') {\r\n\t\t\t\t\taniNum = type ? 0 : 1\r\n\t\t\t\t} else {\r\n\t\t\t\t\taniNum = type ? '-100%' : '0'\r\n\t\t\t\t\tif (mode === 'zoom-in') {\r\n\t\t\t\t\t\taniNum = type ? 0.8 : 1\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (mode === 'zoom-out') {\r\n\t\t\t\t\t\taniNum = type ? 1.2 : 1\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (mode === 'slide-right') {\r\n\t\t\t\t\t\taniNum = type ? '100%' : '0'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (mode === 'slide-bottom') {\r\n\t\t\t\t\t\taniNum = type ? '100%' : '0'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.animation[this.animationMode()[mode]](aniNum)\r\n\t\t\t}\r\n\t\t\tif (typeof this.modeClass === 'string') {\r\n\t\t\t\tbuildTranfrom(type, this.modeClass)\r\n\t\t\t} else {\r\n\t\t\t\tthis.modeClass.forEach(mode => {\r\n\t\t\t\t\tbuildTranfrom(type, mode)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\treturn this.animation\r\n\t\t},\r\n\t\tanimationType(type) {\r\n\t\t\treturn {\r\n\t\t\t\tfade: type ? 0 : 1,\r\n\t\t\t\t'slide-top': `translateY(${type ? '0' : '-100%'})`,\r\n\t\t\t\t'slide-right': `translateX(${type ? '0' : '100%'})`,\r\n\t\t\t\t'slide-bottom': `translateY(${type ? '0' : '100%'})`,\r\n\t\t\t\t'slide-left': `translateX(${type ? '0' : '-100%'})`,\r\n\t\t\t\t'zoom-in': `scaleX(${type ? 1 : 0.8}) scaleY(${type ? 1 : 0.8})`,\r\n\t\t\t\t'zoom-out': `scaleX(${type ? 1 : 1.2}) scaleY(${type ? 1 : 1.2})`\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 内置动画类型与实际动画对应字典\r\n\t\tanimationMode() {\r\n\t\t\treturn {\r\n\t\t\t\tfade: 'opacity',\r\n\t\t\t\t'slide-top': 'translateY',\r\n\t\t\t\t'slide-right': 'translateX',\r\n\t\t\t\t'slide-bottom': 'translateY',\r\n\t\t\t\t'slide-left': 'translateX',\r\n\t\t\t\t'zoom-in': 'scale',\r\n\t\t\t\t'zoom-out': 'scale'\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 驼峰转中横线\r\n\t\ttoLine(name) {\r\n\t\t\treturn name.replace(/([A-Z])/g, '-$1').toLowerCase()\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style></style>\r\n", "import Component from 'C:/Users/<USER>/Desktop/wishlist-uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue'\nwx.createComponent(Component)"], "names": ["createAnimation", "uni", "type"], "mappings": ";;;AA4BA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAM,CAAC,SAAQ,QAAQ;AAAA,EACvB,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM,CAAC,OAAO,MAAM;AAAA,MACpB,UAAU;AACT,eAAO;AAAA,MACR;AAAA,IACA;AAAA,IACD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AACT,eAAO,CAAC;AAAA,MACT;AAAA,IACA;AAAA,IACD,aAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,YAAW;AAAA,MACV,MAAK;AAAA,MACL,SAAQ;AAAA,IACR;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,eAAe,CAAE;AAAA,MACjB,cAAc;AAAA,MACd,QAAQ,CAAC;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,MAAM;AAAA,MACL,QAAQ,QAAQ;AACf,YAAI,QAAQ;AACX,eAAK,KAAK;AAAA,eACJ;AAEN,cAAI,KAAK,QAAQ;AAChB,iBAAK,MAAM;AAAA,UACZ;AAAA,QACD;AAAA,MACA;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,eAAe;AACd,UAAI,SAAS;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,uBAAuB,KAAK,WAAW,MAAO;AAAA,MAC/C;AACA,UAAI,YAAY;AAChB,eAAS,KAAK,QAAQ;AACrB,YAAI,OAAO,KAAK,OAAO,CAAC;AACxB,qBAAa,OAAO,MAAM,OAAO,CAAC,IAAI;AAAA,MACvC;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAED,kBAAkB;AACjB,aAAO,eAAe,KAAK,YAAY,cAAmB,KAAK,UAAU,MAAM,KAAK;AAAA,IACrF;AAAA,EACA;AAAA,EACD,UAAU;AAET,SAAK,SAAS;AAAA,MACb,UAAU,KAAK;AAAA,MACf,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACR;AACA,SAAK,eAAe,KAAK;AAAA,EACzB;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,KAAK,MAAM,IAAI;AACd,UAAI,IAAI,UAAU;AACjB,aAAK,eAAe,IAAI;AAAA,MACzB;AACA,WAAK,YAAYA,mEAAe,gBAAC,OAAO,OAAO,KAAK,QAAQ,GAAG,GAAE,IAAI;AAAA,IACrE;AAAA;AAAA;AAAA;AAAA,IAID,UAAU;AACT,WAAK,MAAM,SAAS;AAAA,QACnB,QAAQ,KAAK;AAAA,OACb;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,KAAK,KAAK,SAAS,IAAI;AACtB,UAAI,CAAC,KAAK;AAAW;AACrB,eAAS,KAAK,KAAK;AAClB,YAAI;AACH,cAAG,OAAO,IAAI,CAAC,MAAM,UAAS;AAC7B,iBAAK,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;AAAA,iBACvB;AACJ,iBAAK,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,UACzB;AAAA,QACD,SAAS,GAAG;AACXC,wBAAc,MAAA,MAAA,SAAA,kFAAA,MAAM,CAAC,MAAM;AAAA,QAC5B;AAAA,MACD;AACA,WAAK,UAAU,KAAK,MAAM;AAC1B,aAAO;AAAA,IACP;AAAA;AAAA;AAAA;AAAA,IAID,IAAI,IAAI;AACP,UAAI,CAAC,KAAK;AAAW;AACrB,WAAK,UAAU,IAAI,EAAE;AAAA,IACrB;AAAA;AAAA,IAED,OAAO;AACN,mBAAa,KAAK,KAAK;AACvB,WAAK,YAAY;AACjB,WAAK,SAAS;AACd,UAAI,EAAE,SAAS,UAAQ,IAAM,KAAK,UAAU,KAAK;AACjD,UAAI,OAAO,YAAY,aAAa;AACnC,aAAK,UAAU;AAAA,MAChB;AACA,WAAK,YAAY;AAEjB,WAAK,UAAU,MAAM;AAEpB,aAAK,QAAQ,WAAW,MAAM;AAC7B,eAAK,YAAYD,mEAAAA,gBAAgB,KAAK,QAAQ,IAAI;AAClD,eAAK,aAAa,KAAK,EAAE,KAAK;AAC9B,eAAK,UAAU,IAAI,MAAM;AACxB,iBAAK,YAAY;AACjB,iBAAK,UAAU,WAAW;AAAA,WAC1B;AACD,eAAK,MAAM,UAAU;AAAA,YACpB,QAAQ,KAAK;AAAA,WACb;AAAA,QACD,GAAE,EAAE;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAED,MAAM,MAAM;AACX,UAAI,CAAC,KAAK;AAAW;AACrB,WAAK,aAAa,IAAI,EACpB,KAAK,EACL,IAAI,MAAM;AACV,aAAK,SAAS;AACd,aAAK,gBAAgB;AACrB,aAAK,YAAY;AACjB,YAAI,EAAE,SAAS,UAAQ,IAAM,KAAK,UAAU,KAAK;AACjD,aAAK,UAAU,WAAW;AAC1B,aAAK,YAAY;AACjB,aAAK,MAAM,UAAU;AAAA,UACpB,QAAQ,KAAK;AAAA,SACb;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAED,UAAU,MAAM;AACf,UAAI,SAAS;AAAA,QACZ,WAAW;AAAA,MACZ;AACA,UAAI,aAAa,CAACE,OAAM,SAAS;AAChC,YAAI,SAAS,QAAQ;AACpB,iBAAO,UAAU,KAAK,cAAcA,KAAI,EAAE,IAAI;AAAA,eACxC;AACN,iBAAO,aAAa,KAAK,cAAcA,KAAI,EAAE,IAAI,IAAI;AAAA,QACtD;AAAA,MACD;AACA,UAAI,OAAO,KAAK,cAAc,UAAU;AACvC,mBAAW,MAAM,KAAK,SAAS;AAAA,aACzB;AACN,aAAK,UAAU,QAAQ,UAAQ;AAC9B,qBAAW,MAAM,IAAI;AAAA,SACrB;AAAA,MACF;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAED,aAAa,MAAM;AAClB,UAAI,gBAAgB,CAACA,OAAM,SAAS;AACnC,YAAI,SAAS;AACb,YAAI,SAAS,QAAQ;AACpB,mBAASA,QAAO,IAAI;AAAA,eACd;AACN,mBAASA,QAAO,UAAU;AAC1B,cAAI,SAAS,WAAW;AACvB,qBAASA,QAAO,MAAM;AAAA,UACvB;AACA,cAAI,SAAS,YAAY;AACxB,qBAASA,QAAO,MAAM;AAAA,UACvB;AACA,cAAI,SAAS,eAAe;AAC3B,qBAASA,QAAO,SAAS;AAAA,UAC1B;AACA,cAAI,SAAS,gBAAgB;AAC5B,qBAASA,QAAO,SAAS;AAAA,UAC1B;AAAA,QACD;AACA,aAAK,UAAU,KAAK,cAAe,EAAC,IAAI,CAAC,EAAE,MAAM;AAAA,MAClD;AACA,UAAI,OAAO,KAAK,cAAc,UAAU;AACvC,sBAAc,MAAM,KAAK,SAAS;AAAA,aAC5B;AACN,aAAK,UAAU,QAAQ,UAAQ;AAC9B,wBAAc,MAAM,IAAI;AAAA,SACxB;AAAA,MACF;AAEA,aAAO,KAAK;AAAA,IACZ;AAAA,IACD,cAAc,MAAM;AACnB,aAAO;AAAA,QACN,MAAM,OAAO,IAAI;AAAA,QACjB,aAAa,cAAc,OAAO,MAAM,OAAO;AAAA,QAC/C,eAAe,cAAc,OAAO,MAAM,MAAM;AAAA,QAChD,gBAAgB,cAAc,OAAO,MAAM,MAAM;AAAA,QACjD,cAAc,cAAc,OAAO,MAAM,OAAO;AAAA,QAChD,WAAW,UAAU,OAAO,IAAI,GAAG,YAAY,OAAO,IAAI,GAAG;AAAA,QAC7D,YAAY,UAAU,OAAO,IAAI,GAAG,YAAY,OAAO,IAAI,GAAG;AAAA,MAC/D;AAAA,IACA;AAAA;AAAA,IAED,gBAAgB;AACf,aAAO;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,WAAW;AAAA,QACX,YAAY;AAAA,MACb;AAAA,IACA;AAAA;AAAA,IAED,OAAO,MAAM;AACZ,aAAO,KAAK,QAAQ,YAAY,KAAK,EAAE,YAAY;AAAA,IACpD;AAAA,EACD;AACD;;;;;;;;;;;AC5RA,GAAG,gBAAgB,SAAS;"}
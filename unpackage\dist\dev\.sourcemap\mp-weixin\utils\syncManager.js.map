{"version": 3, "file": "syncManager.js", "sources": ["utils/syncManager.js"], "sourcesContent": ["/**\r\n * uni-push 2.0 多设备同步管理器\r\n * 基于事件驱动的真正实时推送同步，无需轮询，资源高效\r\n */\r\n\r\nimport { devLog } from './envUtils.js'\r\nimport { BatchManager } from './debouncer.js'\r\n\r\n// 动态导入Store模块\r\nlet useWishStore = null\r\nlet useGroupStore = null\r\nlet useUserStore = null\r\n\r\n// 动态导入函数\r\nasync function importStores() {\r\n  if (!useWishStore) {\r\n    try {\r\n      const wishModule = await import('@/store/wish.js')\r\n      useWishStore = wishModule.useWishStore\r\n\r\n      const groupModule = await import('@/store/group.js')\r\n      useGroupStore = groupModule.useGroupStore\r\n\r\n      const userModule = await import('@/store/user.js')\r\n      useUserStore = userModule.useUserStore\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 导入Store模块失败:', error)\r\n    }\r\n  }\r\n}\r\n\r\nclass SyncManager {\r\n  constructor() {\r\n    this.userId = null\r\n    this.isInitialized = false\r\n    this.isOnline = true\r\n    this.pushClientId = null\r\n\r\n    // 推送同步状态\r\n    this.pushEnabled = false\r\n    this.syncStatus = {\r\n      issyncing: false,\r\n      lastSyncResult: null,\r\n      needSync: false\r\n    }\r\n\r\n    // 事件监听器\r\n    this.listeners = {}\r\n\r\n    // 同步队列（用于离线时缓存）\r\n    this.syncQueue = []\r\n    this.isProcessingQueue = false\r\n\r\n    // 🚀 增强防重复机制\r\n    this._processedPushes = new Map() // 已处理的推送记录\r\n    this._activeSyncOperations = new Map() // 正在进行的同步操作\r\n    this._syncLocks = new Map() // 同步锁，防止并发同步\r\n\r\n    // 使用批量管理器优化推送逻辑\r\n    this.batchManager = new BatchManager(\r\n      (batch) => this._processBatchPush(batch),\r\n      { delay: 500, maxSize: 20 }\r\n    )\r\n\r\n    devLog.log('[SyncManager] 🚀 初始化 uni-push 2.0 多设备同步管理器')\r\n  }\r\n\r\n  /**\r\n   * 初始化同步管理器\r\n   */\r\n  async init() {\r\n    if (this.isInitialized) {\r\n      devLog.log('[SyncManager] 已初始化，跳过')\r\n      return\r\n    }\r\n\r\n    devLog.log('[SyncManager] 🚀 启动 uni-push 2.0 多设备同步系统...')\r\n\r\n    try {\r\n      // 首先导入Store模块\r\n      await importStores()\r\n\r\n      // 获取用户信息\r\n      this.userId = await this._getCurrentUserId()\r\n      devLog.log('[SyncManager] 获取到用户ID:', this.userId)\r\n\r\n      if (!this.userId) {\r\n        devLog.warn('[SyncManager] 用户未登录，跳过同步初始化')\r\n        return\r\n      }\r\n\r\n      // 初始化 uni-push 推送（允许失败）\r\n      await this._initUniPush()\r\n\r\n      // 设置网络监听\r\n      this._setupNetworkListeners()\r\n\r\n      this.isInitialized = true\r\n\r\n      if (this.pushEnabled) {\r\n        devLog.log('[SyncManager] ✅ uni-push 2.0 多设备同步系统初始化完成')\r\n      } else {\r\n        devLog.log('[SyncManager] ✅ 同步系统初始化完成（无推送模式）')\r\n      }\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 初始化失败:', error)\r\n      throw error\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 初始化 uni-push 推送\r\n   */\r\n  async _initUniPush() {\r\n    try {\r\n      devLog.log('[SyncManager] 🔄 初始化 uni-push 推送...')\r\n\r\n      // 检查是否在支持 uni-push 的环境中\r\n      if (!uni.getPushClientId) {\r\n        devLog.warn('[SyncManager] 当前环境不支持 uni-push，使用降级模式')\r\n        this.pushEnabled = false\r\n        // 在降级模式下，仍然可以进行基本的同步功能\r\n        return\r\n      }\r\n\r\n      // 获取推送客户端标识\r\n      const pushResult = await new Promise((resolve, reject) => {\r\n        uni.getPushClientId({\r\n          success: (res) => {\r\n            devLog.log('[SyncManager] 获取推送客户端ID成功:', res.cid)\r\n            resolve(res)\r\n          },\r\n          fail: (err) => {\r\n            devLog.warn('[SyncManager] 获取推送客户端ID失败:', err)\r\n            // 在开发环境中，uni-push 可能不可用，这是正常的\r\n            if (err.errMsg && err.errMsg.includes('uniPush is not enabled')) {\r\n              devLog.log('[SyncManager] uni-push 未启用，可能在开发环境中，将使用降级模式')\r\n              resolve({ cid: null, devMode: true })\r\n            } else {\r\n              reject(err)\r\n            }\r\n          }\r\n        })\r\n      })\r\n\r\n      if (pushResult.devMode || !pushResult.cid) {\r\n        devLog.log('[SyncManager] ⚠️ 推送功能在当前环境不可用，使用降级模式')\r\n        this.pushEnabled = false\r\n        this.pushClientId = null\r\n      } else {\r\n        this.pushClientId = pushResult.cid\r\n        this.pushEnabled = true\r\n        devLog.log('[SyncManager] ✅ uni-push 推送初始化完成')\r\n      }\r\n\r\n      // 监听推送消息\r\n      this._setupPushListener()\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] uni-push 初始化失败:', error)\r\n      this.pushEnabled = false\r\n      // 不再抛出错误，允许同步管理器在没有推送的情况下继续工作\r\n      devLog.log('[SyncManager] 将在无推送模式下继续运行')\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置推送消息监听\r\n   */\r\n  _setupPushListener() {\r\n    devLog.log('[SyncManager] 🔔 设置推送消息监听')\r\n\r\n    // 监听推送消息（需要在 App.vue 中调用 uni.onPushMessage）\r\n    // 这里提供处理推送消息的方法\r\n  }\r\n\r\n  /**\r\n   * 处理推送消息（由 App.vue 调用）\r\n   */\r\n  async handlePushMessage(message) {\r\n    try {\r\n      devLog.log('[SyncManager] 📨 收到推送消息:', JSON.stringify(message))\r\n\r\n      if (!message || !message.payload) {\r\n        devLog.warn('[SyncManager] 推送消息格式无效')\r\n        return\r\n      }\r\n\r\n      let payload\r\n      try {\r\n        payload = typeof message.payload === 'string'\r\n          ? JSON.parse(message.payload)\r\n          : message.payload\r\n      } catch (parseError) {\r\n        devLog.error('[SyncManager] 推送消息解析失败:', parseError)\r\n        return\r\n      }\r\n\r\n      devLog.log('[SyncManager] 📋 解析后的推送载荷:', payload)\r\n\r\n      // 处理不同类型的推送\r\n      if (payload.type === 'data_sync') {\r\n        await this._handleDataSyncPush(payload)\r\n      } else if (payload.type === 'batch_data_sync') {\r\n        await this._handleBatchDataSyncPush(payload)\r\n      } else if (payload.type === 'user_status') {\r\n        await this._handleUserStatusPush(payload)\r\n      } else {\r\n        devLog.warn('[SyncManager] 未知的推送消息类型:', payload.type)\r\n      }\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 处理推送消息失败:', error)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理数据同步推送\r\n   */\r\n  async _handleDataSyncPush(payload) {\r\n    try {\r\n      const { dataType, action, dataId, timestamp } = payload\r\n      devLog.log(`[SyncManager] 🔄 处理数据同步推送: ${dataType}.${action}`)\r\n\r\n      // 防重复处理：检查是否已经处理过这个推送\r\n      const pushKey = `${dataType}_${action}_${dataId}_${timestamp}`\r\n      if (this._isRecentlyProcessed(pushKey)) {\r\n        devLog.log(`[SyncManager] ⏭️ 跳过重复推送: ${pushKey}`)\r\n        return\r\n      }\r\n\r\n      // 记录已处理的推送\r\n      this._markAsProcessed(pushKey)\r\n\r\n      // 确保Store模块已导入\r\n      await importStores()\r\n\r\n      // 🚀 同步合并：独立同步，减少数据传输\r\n      if (dataType === 'wish' && useWishStore) {\r\n        const wishStore = useWishStore()\r\n        await this._syncSpecificWishData(action, dataId, wishStore)\r\n      } else if (dataType === 'group' && useGroupStore) {\r\n        const groupStore = useGroupStore()\r\n        await this._syncSpecificGroupData(action, dataId, groupStore)\r\n      } else if (dataType === 'comment') {\r\n        const { useCommentStore } = await import('@/store/comment.js')\r\n        const commentStore = useCommentStore()\r\n        await this._syncSpecificCommentData(action, dataId, commentStore)\r\n      }\r\n\r\n      devLog.log(`[SyncManager] ✅ ${dataType}数据同步完成`)\r\n\r\n      // 触发同步完成事件\r\n      this._emit('data_synced', { dataType, action, dataId, timestamp })\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 处理数据同步推送失败:', error)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理批量数据同步推送\r\n   */\r\n  async _handleBatchDataSyncPush(payload) {\r\n    try {\r\n      const { changes, timestamp } = payload\r\n      devLog.log(`[SyncManager] 🔄 处理批量数据同步推送: ${changes.length}个变化`)\r\n\r\n      // 防重复处理\r\n      const batchKey = `batch_${timestamp}_${changes.length}`\r\n      if (this._isRecentlyProcessed(batchKey)) {\r\n        devLog.log(`[SyncManager] ⏭️ 跳过重复批量推送: ${batchKey}`)\r\n        return\r\n      }\r\n      this._markAsProcessed(batchKey)\r\n\r\n      // 确保Store模块已导入\r\n      await importStores()\r\n\r\n      // 🚀 优化：按数据类型分组处理，减少云函数调用\r\n      const changesByType = this._groupChangesByType(changes)\r\n\r\n      // 🚀 同步合并：同时调用但不合并内容，减少数据传输\r\n      const syncPromises = []\r\n\r\n      // 独立处理心愿数据\r\n      if (changesByType.wish && useWishStore) {\r\n        const wishStore = useWishStore()\r\n        syncPromises.push(this._batchSyncWishData(changesByType.wish, wishStore))\r\n      }\r\n\r\n      // 独立处理评论数据\r\n      if (changesByType.comment && useCommentStore) {\r\n        const commentStore = useCommentStore()\r\n        syncPromises.push(this._batchSyncCommentData(changesByType.comment, commentStore))\r\n      }\r\n\r\n      // 独立处理分组数据\r\n      if (changesByType.group && useGroupStore) {\r\n        const groupStore = useGroupStore()\r\n        syncPromises.push(this._batchSyncGroupData(changesByType.group, groupStore))\r\n      }\r\n\r\n      await Promise.allSettled(syncPromises)\r\n\r\n      devLog.log(`[SyncManager] ✅ 批量数据同步完成`)\r\n\r\n      // 触发批量同步完成事件\r\n      this._emit('batch_data_synced', { changes, timestamp })\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 处理批量数据同步推送失败:', error)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理用户状态推送\r\n   */\r\n  async _handleUserStatusPush(payload) {\r\n    try {\r\n      const { action, deviceInfo, timestamp } = payload\r\n      devLog.log(`[SyncManager] 👤 处理用户状态推送: ${action}`)\r\n\r\n      // 触发用户状态变化事件\r\n      this._emit('user_status_changed', { action, deviceInfo, timestamp })\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 处理用户状态推送失败:', error)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 数据变化通知（Store调用）- 支持批量推送优化\r\n   */\r\n  async notifyDataChange(dataType, action, data, options = {}) {\r\n    devLog.log(`[SyncManager] 📝 数据变化通知: ${dataType}.${action}`)\r\n\r\n    if (!this.pushEnabled || !this.userId) {\r\n      devLog.warn('[SyncManager] 推送未启用或用户未登录，跳过推送')\r\n      return\r\n    }\r\n\r\n    // 🚀 优化：支持批量推送，减少云函数调用\r\n    if (options.batch) {\r\n      this._addToBatchQueue(dataType, action, data)\r\n      return\r\n    }\r\n\r\n    try {\r\n      // 调用推送云对象，通知其他设备\r\n      const syncPush = uniCloud.importObject('sync-push')\r\n      const result = await syncPush.pushDataSync({\r\n        userId: this.userId,\r\n        dataType,\r\n        action,\r\n        dataId: data?.id || data?._id,\r\n        data: data\r\n      })\r\n\r\n      if (result.errCode === 0) {\r\n        devLog.log(`[SyncManager] ✅ 推送通知发送成功: ${dataType}.${action}`)\r\n      } else {\r\n        devLog.error('[SyncManager] 推送通知发送失败:', result.errMsg)\r\n      }\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 发送推送通知失败:', error)\r\n      // 推送失败不影响主要业务逻辑\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 手动同步数据（用于用户主动刷新）- 🚀 增强防重复机制\r\n   */\r\n  async manualSync() {\r\n    const syncKey = 'manual_sync'\r\n\r\n    // 🚀 检查是否已有同步操作在进行\r\n    if (this._isSyncInProgress(syncKey)) {\r\n      devLog.log('[SyncManager] ⏭️ 手动同步已在进行中，跳过重复请求')\r\n      return { success: true, skipped: true }\r\n    }\r\n\r\n    // 🚀 获取同步锁\r\n    if (!(await this._acquireSyncLock(syncKey))) {\r\n      devLog.warn('[SyncManager] ⚠️ 无法获取同步锁，跳过同步')\r\n      return { success: false, error: '同步锁获取失败' }\r\n    }\r\n\r\n    try {\r\n      this._markSyncStart(syncKey)\r\n      devLog.log('[SyncManager] 🔄 手动同步数据...')\r\n\r\n      // 确保Store模块已导入\r\n      await importStores()\r\n      devLog.log('[SyncManager] 🔄 开始统一同步...')\r\n\r\n      const results = []\r\n\r\n      // 同步心愿数据\r\n      if (useWishStore) {\r\n        try {\r\n          const wishStore = useWishStore()\r\n          devLog.log('[SyncManager] 同步心愿数据...')\r\n          const result = await wishStore.syncFromCloud()\r\n          results.push({ type: 'wish', ...result })\r\n          devLog.log('[SyncManager] ✅ 心愿数据同步完成:', result)\r\n        } catch (error) {\r\n          devLog.error('[SyncManager] ❌ 心愿数据同步失败:', error)\r\n          results.push({ type: 'wish', success: false, error: error.message })\r\n        }\r\n      }\r\n\r\n      // 同步分组数据\r\n      if (useGroupStore) {\r\n        try {\r\n          const groupStore = useGroupStore()\r\n          devLog.log('[SyncManager] 同步分组数据...')\r\n          const result = await groupStore.syncFromCloud()\r\n          results.push({ type: 'group', ...result })\r\n          devLog.log('[SyncManager] ✅ 分组数据同步完成:', result)\r\n        } catch (error) {\r\n          devLog.error('[SyncManager] ❌ 分组数据同步失败:', error)\r\n          results.push({ type: 'group', success: false, error: error.message })\r\n        }\r\n      }\r\n\r\n      // 统计同步结果\r\n      const successCount = results.filter(r => r.success).length\r\n      const errorCount = results.filter(r => !r.success).length\r\n      const errors = results.filter(r => !r.success).map(r => r.error)\r\n\r\n      const success = errorCount === 0\r\n\r\n      devLog.log(`[SyncManager] ${success ? '✅' : '⚠️'} 手动同步完成，成功: ${successCount}, 错误: ${errorCount}`)\r\n\r\n      this._emit('manual_sync_completed', {\r\n        success,\r\n        results,\r\n        errors: errors.length > 0 ? errors : undefined\r\n      })\r\n\r\n      return { success, results, errors }\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 手动同步失败:', error)\r\n      this._emit('manual_sync_completed', { success: false, error })\r\n      return { success: false, error }\r\n    } finally {\r\n      this._markSyncEnd(syncKey)\r\n      this._releaseSyncLock(syncKey)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取当前用户ID\r\n   */\r\n  async _getCurrentUserId() {\r\n    try {\r\n      // 从存储获取\r\n      const userInfo = uni.getStorageSync('userInfo')\r\n      if (userInfo) {\r\n        // 解析存储的用户信息（可能是字符串）\r\n        let parsedUserInfo = userInfo\r\n        if (typeof userInfo === 'string') {\r\n          try {\r\n            parsedUserInfo = JSON.parse(userInfo)\r\n          } catch (e) {\r\n            devLog.warn('[SyncManager] 用户信息解析失败:', e)\r\n          }\r\n        }\r\n        \r\n        if (parsedUserInfo && parsedUserInfo.uid) {\r\n          return parsedUserInfo.uid\r\n        }\r\n      }\r\n      \r\n      // 从Store获取\r\n      try {\r\n        await importStores()\r\n        if (useUserStore) {\r\n          const userStore = useUserStore()\r\n          if (userStore && userStore.userInfo && userStore.userInfo.uid) {\r\n            return userStore.userInfo.uid\r\n          }\r\n        }\r\n      } catch (storeError) {\r\n        devLog.warn('[SyncManager] Store获取用户信息失败:', storeError)\r\n      }\r\n      \r\n      return null\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 获取用户ID失败:', error)\r\n      return null\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置网络监听\r\n   */\r\n  _setupNetworkListeners() {\r\n    // 监听网络状态变化\r\n    uni.onNetworkStatusChange((res) => {\r\n      const wasOnline = this.isOnline\r\n      this.isOnline = res.isConnected\r\n\r\n      devLog.log(`[SyncManager] 网络状态: ${this.isOnline ? '在线' : '离线'}`)\r\n\r\n      // 网络恢复时触发手动同步\r\n      if (!wasOnline && this.isOnline) {\r\n        devLog.log('[SyncManager] 🌐 网络恢复，触发同步')\r\n        setTimeout(() => {\r\n          devLog.log('[SyncManager] 执行网络恢复同步...')\r\n          this.manualSync().then(result => {\r\n            devLog.log('[SyncManager] 网络恢复同步完成:', result)\r\n          }).catch(error => {\r\n            devLog.error('[SyncManager] 网络恢复同步失败:', error)\r\n          })\r\n        }, 1000)\r\n      }\r\n    })\r\n  }\r\n\r\n  /**\r\n   * 应用前台显示时的处理（由 App.vue 调用）\r\n   */\r\n  onAppShow() {\r\n    devLog.log('[SyncManager] 📱 应用前台显示')\r\n\r\n    // 应用回到前台时，主动同步一次数据\r\n    if (this.isInitialized && this.isOnline) {\r\n      devLog.log('[SyncManager] 应用前台显示，准备同步数据...')\r\n      setTimeout(() => {\r\n        devLog.log('[SyncManager] 执行应用前台同步...')\r\n        this.manualSync().then(result => {\r\n          devLog.log('[SyncManager] 应用前台同步完成:', result)\r\n        }).catch(error => {\r\n          devLog.error('[SyncManager] 应用前台同步失败:', error)\r\n        })\r\n      }, 1000)\r\n    } else {\r\n      devLog.warn('[SyncManager] 应用前台显示，但同步条件不满足:', {\r\n        isInitialized: this.isInitialized,\r\n        isOnline: this.isOnline\r\n      })\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 应用进入后台时的处理（由 App.vue 调用）\r\n   */\r\n  onAppHide() {\r\n    devLog.log('[SyncManager] 📱 应用进入后台')\r\n    // uni-push 2.0 模式下，后台也能接收推送，无需特殊处理\r\n  }\r\n\r\n  /**\r\n   * 获取同步状态\r\n   */\r\n  getSyncStatus() {\r\n    return {\r\n      isInitialized: this.isInitialized,\r\n      isOnline: this.isOnline,\r\n      pushEnabled: this.pushEnabled,\r\n      pushClientId: this.pushClientId,\r\n      issyncing: this.syncStatus.issyncing,\r\n      lastSyncResult: this.syncStatus.lastSyncResult,\r\n      userId: this.userId,\r\n      syncMode: 'uni-push-2.0'\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取推送状态信息\r\n   */\r\n  getPushStatus() {\r\n    return {\r\n      supported: !!uni.getPushClientId,\r\n      enabled: this.pushEnabled,\r\n      clientId: this.pushClientId,\r\n      initialized: this.isInitialized,\r\n      userId: this.userId\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 事件监听\r\n   */\r\n  on(event, callback) {\r\n    if (!this.listeners[event]) {\r\n      this.listeners[event] = []\r\n    }\r\n    this.listeners[event].push(callback)\r\n  }\r\n\r\n  /**\r\n   * 移除事件监听\r\n   */\r\n  off(event, callback) {\r\n    if (!this.listeners[event]) return\r\n    \r\n    const index = this.listeners[event].indexOf(callback)\r\n    if (index > -1) {\r\n      this.listeners[event].splice(index, 1)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 触发事件\r\n   */\r\n  _emit(event, data) {\r\n    if (this.listeners[event]) {\r\n      this.listeners[event].forEach(callback => {\r\n        try {\r\n          callback(data)\r\n        } catch (error) {\r\n          devLog.error('[SyncManager] 事件回调执行失败:', error)\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  // ==================== 新增优化方法 ====================\r\n\r\n  /**\r\n   * 检查推送是否最近已处理（防重复）\r\n   */\r\n  _isRecentlyProcessed(key) {\r\n    const now = Date.now()\r\n    const processed = this._processedPushes.get(key)\r\n\r\n    // 🚀 优化：缩短重复检测时间窗口到2分钟，提高响应性\r\n    if (processed && (now - processed) < 2 * 60 * 1000) {\r\n      devLog.log(`[SyncManager] 🔄 跳过重复处理: ${key}`)\r\n      return true\r\n    }\r\n\r\n    return false\r\n  }\r\n\r\n  /**\r\n   * 标记推送为已处理\r\n   */\r\n  _markAsProcessed(key) {\r\n    const now = Date.now()\r\n    this._processedPushes.set(key, now)\r\n\r\n    // 🚀 优化：定期清理过期记录，避免内存泄漏\r\n    if (this._processedPushes.size > 100) {\r\n      this._cleanupProcessedRecords()\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 🚀 新增：清理过期的处理记录\r\n   */\r\n  _cleanupProcessedRecords() {\r\n    const now = Date.now()\r\n    const tenMinutesAgo = now - 10 * 60 * 1000\r\n\r\n    for (const [key, timestamp] of this._processedPushes.entries()) {\r\n      if (timestamp < tenMinutesAgo) {\r\n        this._processedPushes.delete(key)\r\n      }\r\n    }\r\n\r\n    devLog.log(`[SyncManager] 🧹 清理过期记录，当前记录数: ${this._processedPushes.size}`)\r\n  }\r\n\r\n  /**\r\n   * 🚀 新增：检查同步操作是否正在进行\r\n   */\r\n  _isSyncInProgress(syncKey) {\r\n    return this._activeSyncOperations.has(syncKey)\r\n  }\r\n\r\n  /**\r\n   * 🚀 新增：标记同步操作开始\r\n   */\r\n  _markSyncStart(syncKey) {\r\n    this._activeSyncOperations.set(syncKey, Date.now())\r\n    devLog.log(`[SyncManager] 🔄 开始同步操作: ${syncKey}`)\r\n  }\r\n\r\n  /**\r\n   * 🚀 新增：标记同步操作结束\r\n   */\r\n  _markSyncEnd(syncKey) {\r\n    this._activeSyncOperations.delete(syncKey)\r\n    devLog.log(`[SyncManager] ✅ 完成同步操作: ${syncKey}`)\r\n  }\r\n\r\n  /**\r\n   * 🚀 新增：获取同步锁\r\n   */\r\n  async _acquireSyncLock(lockKey, timeout = 30000) {\r\n    const startTime = Date.now()\r\n\r\n    while (this._syncLocks.has(lockKey)) {\r\n      if (Date.now() - startTime > timeout) {\r\n        devLog.warn(`[SyncManager] ⏰ 获取同步锁超时: ${lockKey}`)\r\n        return false\r\n      }\r\n      await new Promise(resolve => setTimeout(resolve, 100))\r\n    }\r\n\r\n    this._syncLocks.set(lockKey, Date.now())\r\n    devLog.log(`[SyncManager] 🔒 获取同步锁: ${lockKey}`)\r\n    return true\r\n  }\r\n\r\n  /**\r\n   * 🚀 新增：释放同步锁\r\n   */\r\n  _releaseSyncLock(lockKey) {\r\n    this._syncLocks.delete(lockKey)\r\n    devLog.log(`[SyncManager] 🔓 释放同步锁: ${lockKey}`)\r\n  }\r\n\r\n  /**\r\n   * 按数据类型分组变化\r\n   */\r\n  _groupChangesByType(changes) {\r\n    const grouped = {}\r\n    changes.forEach(change => {\r\n      if (!grouped[change.dataType]) {\r\n        grouped[change.dataType] = []\r\n      }\r\n      grouped[change.dataType].push(change)\r\n    })\r\n    return grouped\r\n  }\r\n\r\n  /**\r\n   * 增量同步特定心愿数据（基础方法）\r\n   */\r\n  async _syncSpecificWishData(action, dataId, wishStore) {\r\n    try {\r\n      if (action === 'delete') {\r\n        // 删除操作：直接从本地移除\r\n        wishStore.removeWishById(dataId)\r\n        devLog.log(`[SyncManager] 🗑️ 本地删除心愿: ${dataId}`)\r\n      } else {\r\n        // 创建/更新操作：获取特定数据\r\n        const wishCenter = uniCloud.importObject('wish-center')\r\n        const result = await wishCenter.getWishById(dataId)\r\n\r\n        if (result.errCode === 0 && result.data) {\r\n          if (action === 'create') {\r\n            wishStore.addWishFromSync(result.data)\r\n            devLog.log(`[SyncManager] ➕ 同步新增心愿: ${result.data.title}`)\r\n          } else if (action === 'update') {\r\n            wishStore.updateWishFromSync(result.data)\r\n            devLog.log(`[SyncManager] 🔄 同步更新心愿: ${result.data.title}`)\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      devLog.error(`[SyncManager] 同步特定心愿数据失败:`, error)\r\n      // 失败时回退到全量同步\r\n      await wishStore.syncFromCloud()\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 增量同步特定分组数据\r\n   */\r\n  async _syncSpecificGroupData(action, dataId, groupStore) {\r\n    try {\r\n      if (action === 'delete') {\r\n        groupStore.removeGroupById(dataId)\r\n        devLog.log(`[SyncManager] 🗑️ 本地删除分组: ${dataId}`)\r\n      } else {\r\n        const groupCenter = uniCloud.importObject('group-center')\r\n        const result = await groupCenter.getGroupById(dataId)\r\n\r\n        if (result.errCode === 0 && result.data) {\r\n          if (action === 'create') {\r\n            groupStore.addGroupFromSync(result.data)\r\n            devLog.log(`[SyncManager] ➕ 同步新增分组: ${result.data.name}`)\r\n          } else if (action === 'update') {\r\n            groupStore.updateGroupFromSync(result.data)\r\n            devLog.log(`[SyncManager] 🔄 同步更新分组: ${result.data.name}`)\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      devLog.error(`[SyncManager] 同步特定分组数据失败:`, error)\r\n      // 失败时回退到全量同步\r\n      await groupStore.syncFromCloud()\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 增量同步特定评论数据\r\n   */\r\n  async _syncSpecificCommentData(action, dataId, commentStore) {\r\n    try {\r\n      if (action === 'delete') {\r\n        // 删除操作：直接从本地移除\r\n        commentStore.removeCommentById(dataId)\r\n        devLog.log(`[SyncManager] 🗑️ 本地删除评论: ${dataId}`)\r\n      } else {\r\n        // 创建/更新操作：获取特定数据\r\n        const commentCenter = uniCloud.importObject('comment-center')\r\n        const result = await commentCenter.getCommentById(dataId)\r\n\r\n        if (result.errCode === 0 && result.data) {\r\n          if (action === 'create') {\r\n            commentStore.addCommentFromSync(result.data)\r\n            devLog.log(`[SyncManager] ➕ 同步新增评论: ${dataId}`)\r\n          } else if (action === 'update') {\r\n            commentStore.updateCommentFromSync(result.data)\r\n            devLog.log(`[SyncManager] 🔄 同步更新评论: ${dataId}`)\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      devLog.error(`[SyncManager] 评论增量同步失败:`, error)\r\n      // 增量同步失败时降级为相关心愿的评论全量同步\r\n      if (action !== 'delete' && dataId) {\r\n        try {\r\n          const commentCenter = uniCloud.importObject('comment-center')\r\n          const result = await commentCenter.getCommentById(dataId)\r\n          if (result.errCode === 0 && result.data && result.data.wishId) {\r\n            await commentStore.syncCommentsForWish(result.data.wishId, true, true)\r\n          }\r\n        } catch (fallbackError) {\r\n          devLog.error(`[SyncManager] 评论同步降级失败:`, fallbackError)\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 批量同步评论数据\r\n   */\r\n  async _batchSyncCommentData(changes, commentStore) {\r\n    try {\r\n      devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个评论变化`)\r\n\r\n      // 按心愿ID分组处理\r\n      const changesByWish = {}\r\n      changes.forEach(change => {\r\n        const wishId = change.data?.wishId || change.wishId\r\n        if (wishId) {\r\n          if (!changesByWish[wishId]) {\r\n            changesByWish[wishId] = []\r\n          }\r\n          changesByWish[wishId].push(change)\r\n        }\r\n      })\r\n\r\n      // 并行处理不同心愿的评论变化\r\n      const syncPromises = Object.entries(changesByWish).map(async ([wishId, wishChanges]) => {\r\n        // 对于每个心愿，重新同步其所有评论\r\n        await commentStore.syncCommentsForWish(wishId, true, true)\r\n      })\r\n\r\n      await Promise.allSettled(syncPromises)\r\n      devLog.log(`[SyncManager] ✅ 批量评论同步完成`)\r\n\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 批量评论同步失败:', error)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 批量同步心愿数据（基础方法）\r\n   */\r\n  async _batchSyncWishData(changes, wishStore) {\r\n    try {\r\n      devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个心愿变化`)\r\n\r\n      // 分类处理不同操作\r\n      const deletes = changes.filter(c => c.action === 'delete')\r\n      const updates = changes.filter(c => c.action !== 'delete')\r\n\r\n      // 处理删除操作\r\n      deletes.forEach(change => {\r\n        wishStore.removeWishById(change.dataId)\r\n      })\r\n\r\n      // 批量获取需要更新的数据\r\n      if (updates.length > 0) {\r\n        const wishCenter = uniCloud.importObject('wish-center')\r\n        const dataIds = updates.map(c => c.dataId)\r\n        const result = await wishCenter.getWishesByIds(dataIds)\r\n\r\n        if (result.errCode === 0 && result.data) {\r\n          result.data.forEach(wish => {\r\n            const change = updates.find(c => c.dataId === wish._id)\r\n            if (change.action === 'create') {\r\n              wishStore.addWishFromSync(wish)\r\n            } else if (change.action === 'update') {\r\n              wishStore.updateWishFromSync(wish)\r\n            }\r\n          })\r\n        }\r\n      }\r\n\r\n      devLog.log(`[SyncManager] ✅ 批量心愿同步完成`)\r\n    } catch (error) {\r\n      devLog.error(`[SyncManager] 批量心愿同步失败:`, error)\r\n      // 失败时回退到全量同步\r\n      await wishStore.syncFromCloud()\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 批量同步分组数据\r\n   */\r\n  async _batchSyncGroupData(changes, groupStore) {\r\n    try {\r\n      devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个分组变化`)\r\n\r\n      const deletes = changes.filter(c => c.action === 'delete')\r\n      const updates = changes.filter(c => c.action !== 'delete')\r\n\r\n      deletes.forEach(change => {\r\n        groupStore.removeGroupById(change.dataId)\r\n      })\r\n\r\n      if (updates.length > 0) {\r\n        const groupCenter = uniCloud.importObject('group-center')\r\n        const dataIds = updates.map(c => c.dataId)\r\n        const result = await groupCenter.getGroupsByIds(dataIds)\r\n\r\n        if (result.errCode === 0 && result.data) {\r\n          result.data.forEach(group => {\r\n            const change = updates.find(c => c.dataId === group._id)\r\n            if (change.action === 'create') {\r\n              groupStore.addGroupFromSync(group)\r\n            } else if (change.action === 'update') {\r\n              groupStore.updateGroupFromSync(group)\r\n            }\r\n          })\r\n        }\r\n      }\r\n\r\n      devLog.log(`[SyncManager] ✅ 批量分组同步完成`)\r\n    } catch (error) {\r\n      devLog.error(`[SyncManager] 批量分组同步失败:`, error)\r\n      await groupStore.syncFromCloud()\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 添加到批量推送队列（使用优化的批量管理器）\r\n   */\r\n  _addToBatchQueue(dataType, action, data) {\r\n    this.batchManager.add({\r\n      dataType,\r\n      action,\r\n      dataId: data?.id || data?._id,\r\n      data\r\n    })\r\n  }\r\n\r\n  /**\r\n   * 处理批量推送（由 BatchManager 调用）\r\n   */\r\n  async _processBatchPush(batch) {\r\n    try {\r\n      devLog.log(`[SyncManager] 🚀 批量推送 ${batch.length} 个数据变化`)\r\n      \r\n      // 转换格式以兼容现有 API\r\n      const changes = batch.map(item => ({\r\n        dataType: item.dataType,\r\n        action: item.action,\r\n        dataId: item.dataId,\r\n        data: item.data,\r\n        timestamp: item.timestamp\r\n      }))\r\n      \r\n      const syncPush = uniCloud.importObject('sync-push')\r\n      const result = await syncPush.pushBatchDataSync({\r\n        userId: this.userId,\r\n        changes\r\n      })\r\n\r\n      if (result.errCode === 0) {\r\n        devLog.log(`[SyncManager] ✅ 批量推送成功: ${changes.length}个变化`)\r\n      } else {\r\n        devLog.error('[SyncManager] 批量推送失败:', result.errMsg)\r\n      }\r\n    } catch (error) {\r\n      devLog.error('[SyncManager] 批量推送失败:', error)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 销毁同步管理器\r\n   */\r\n  destroy() {\r\n    devLog.log('[SyncManager] 🗑️ 销毁 uni-push 2.0 同步管理器')\r\n\r\n    // 清理批量管理器\r\n    if (this.batchManager) {\r\n      this.batchManager.clear()\r\n    }\r\n\r\n    // 重置状态\r\n    this.isInitialized = false\r\n    this.pushEnabled = false\r\n    this.pushClientId = null\r\n    this.listeners = {}\r\n    this.syncQueue = []\r\n    this._processedPushes = null\r\n  }\r\n}\r\n\r\n// 创建全局实例\r\nconst syncManager = new SyncManager()\r\n\r\nexport default syncManager \r\n"], "names": ["devLog", "BatchManager", "uni", "useCommentStore", "uniCloud"], "mappings": ";;;;AASA,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AAGnB,eAAe,eAAe;AAC5B,MAAI,CAAC,cAAc;AACjB,QAAI;AACF,YAAM,aAAa,MAAa;AAChC,qBAAe,WAAW;AAE1B,YAAM,cAAc,MAAa;AACjC,sBAAgB,YAAY;AAE5B,YAAM,aAAa,MAAa;AAChC,qBAAe,WAAW;AAAA,IAC3B,SAAQ,OAAO;AACdA,4BAAO,MAAM,8BAA8B,KAAK;AAAA,IACjD;AAAA,EACF;AACH;AAEA,MAAM,YAAY;AAAA,EAChB,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,eAAe;AAGpB,SAAK,cAAc;AACnB,SAAK,aAAa;AAAA,MAChB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,UAAU;AAAA,IACX;AAGD,SAAK,YAAY,CAAE;AAGnB,SAAK,YAAY,CAAE;AACnB,SAAK,oBAAoB;AAGzB,SAAK,mBAAmB,oBAAI,IAAK;AACjC,SAAK,wBAAwB,oBAAI,IAAK;AACtC,SAAK,aAAa,oBAAI,IAAK;AAG3B,SAAK,eAAe,IAAIC,gBAAY;AAAA,MAClC,CAAC,UAAU,KAAK,kBAAkB,KAAK;AAAA,MACvC,EAAE,OAAO,KAAK,SAAS,GAAI;AAAA,IAC5B;AAEDD,mBAAM,OAAC,IAAI,4CAA4C;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,OAAO;AACX,QAAI,KAAK,eAAe;AACtBA,qBAAM,OAAC,IAAI,uBAAuB;AAClC;AAAA,IACD;AAEDA,mBAAM,OAAC,IAAI,6CAA6C;AAExD,QAAI;AAEF,YAAM,aAAc;AAGpB,WAAK,SAAS,MAAM,KAAK,kBAAmB;AAC5CA,qBAAAA,OAAO,IAAI,0BAA0B,KAAK,MAAM;AAEhD,UAAI,CAAC,KAAK,QAAQ;AAChBA,uBAAM,OAAC,KAAK,6BAA6B;AACzC;AAAA,MACD;AAGD,YAAM,KAAK,aAAc;AAGzB,WAAK,uBAAwB;AAE7B,WAAK,gBAAgB;AAErB,UAAI,KAAK,aAAa;AACpBA,uBAAM,OAAC,IAAI,2CAA2C;AAAA,MAC9D,OAAa;AACLA,uBAAM,OAAC,IAAI,kCAAkC;AAAA,MAC9C;AAAA,IAEF,SAAQ,OAAO;AACdA,4BAAO,MAAM,wBAAwB,KAAK;AAC1C,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,eAAe;AACnB,QAAI;AACFA,qBAAM,OAAC,IAAI,qCAAqC;AAGhD,UAAI,CAACE,cAAG,MAAC,iBAAiB;AACxBF,uBAAM,OAAC,KAAK,uCAAuC;AACnD,aAAK,cAAc;AAEnB;AAAA,MACD;AAGD,YAAM,aAAa,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxDE,sBAAAA,MAAI,gBAAgB;AAAA,UAClB,SAAS,CAAC,QAAQ;AAChBF,2BAAAA,OAAO,IAAI,8BAA8B,IAAI,GAAG;AAChD,oBAAQ,GAAG;AAAA,UACZ;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,kCAAO,KAAK,8BAA8B,GAAG;AAE7C,gBAAI,IAAI,UAAU,IAAI,OAAO,SAAS,wBAAwB,GAAG;AAC/DA,6BAAM,OAAC,IAAI,6CAA6C;AACxD,sBAAQ,EAAE,KAAK,MAAM,SAAS,KAAI,CAAE;AAAA,YAClD,OAAmB;AACL,qBAAO,GAAG;AAAA,YACX;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAED,UAAI,WAAW,WAAW,CAAC,WAAW,KAAK;AACzCA,uBAAM,OAAC,IAAI,sCAAsC;AACjD,aAAK,cAAc;AACnB,aAAK,eAAe;AAAA,MAC5B,OAAa;AACL,aAAK,eAAe,WAAW;AAC/B,aAAK,cAAc;AACnBA,uBAAM,OAAC,IAAI,kCAAkC;AAAA,MAC9C;AAGD,WAAK,mBAAoB;AAAA,IAE1B,SAAQ,OAAO;AACdA,4BAAO,MAAM,iCAAiC,KAAK;AACnD,WAAK,cAAc;AAEnBA,qBAAM,OAAC,IAAI,4BAA4B;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,qBAAqB;AACnBA,mBAAM,OAAC,IAAI,2BAA2B;AAAA,EAIvC;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,kBAAkB,SAAS;AAC/B,QAAI;AACFA,qBAAM,OAAC,IAAI,4BAA4B,KAAK,UAAU,OAAO,CAAC;AAE9D,UAAI,CAAC,WAAW,CAAC,QAAQ,SAAS;AAChCA,uBAAM,OAAC,KAAK,wBAAwB;AACpC;AAAA,MACD;AAED,UAAI;AACJ,UAAI;AACF,kBAAU,OAAO,QAAQ,YAAY,WACjC,KAAK,MAAM,QAAQ,OAAO,IAC1B,QAAQ;AAAA,MACb,SAAQ,YAAY;AACnBA,8BAAO,MAAM,2BAA2B,UAAU;AAClD;AAAA,MACD;AAEDA,4BAAO,IAAI,8BAA8B,OAAO;AAGhD,UAAI,QAAQ,SAAS,aAAa;AAChC,cAAM,KAAK,oBAAoB,OAAO;AAAA,MAC9C,WAAiB,QAAQ,SAAS,mBAAmB;AAC7C,cAAM,KAAK,yBAAyB,OAAO;AAAA,MACnD,WAAiB,QAAQ,SAAS,eAAe;AACzC,cAAM,KAAK,sBAAsB,OAAO;AAAA,MAChD,OAAa;AACLA,uBAAAA,OAAO,KAAK,4BAA4B,QAAQ,IAAI;AAAA,MACrD;AAAA,IAEF,SAAQ,OAAO;AACdA,4BAAO,MAAM,2BAA2B,KAAK;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,oBAAoB,SAAS;AACjC,QAAI;AACF,YAAM,EAAE,UAAU,QAAQ,QAAQ,UAAW,IAAG;AAChDA,qBAAM,OAAC,IAAI,8BAA8B,QAAQ,IAAI,MAAM,EAAE;AAG7D,YAAM,UAAU,GAAG,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,SAAS;AAC5D,UAAI,KAAK,qBAAqB,OAAO,GAAG;AACtCA,uBAAAA,OAAO,IAAI,4BAA4B,OAAO,EAAE;AAChD;AAAA,MACD;AAGD,WAAK,iBAAiB,OAAO;AAG7B,YAAM,aAAc;AAGpB,UAAI,aAAa,UAAU,cAAc;AACvC,cAAM,YAAY,aAAc;AAChC,cAAM,KAAK,sBAAsB,QAAQ,QAAQ,SAAS;AAAA,MAClE,WAAiB,aAAa,WAAW,eAAe;AAChD,cAAM,aAAa,cAAe;AAClC,cAAM,KAAK,uBAAuB,QAAQ,QAAQ,UAAU;AAAA,MACpE,WAAiB,aAAa,WAAW;AACjC,cAAM,EAAE,iBAAAG,iBAAe,IAAK,MAAa;AACzC,cAAM,eAAeA,iBAAiB;AACtC,cAAM,KAAK,yBAAyB,QAAQ,QAAQ,YAAY;AAAA,MACjE;AAEDH,qBAAAA,OAAO,IAAI,mBAAmB,QAAQ,QAAQ;AAG9C,WAAK,MAAM,eAAe,EAAE,UAAU,QAAQ,QAAQ,WAAW;AAAA,IAElE,SAAQ,OAAO;AACdA,4BAAO,MAAM,6BAA6B,KAAK;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,yBAAyB,SAAS;AACtC,QAAI;AACF,YAAM,EAAE,SAAS,UAAS,IAAK;AAC/BA,qBAAM,OAAC,IAAI,gCAAgC,QAAQ,MAAM,KAAK;AAG9D,YAAM,WAAW,SAAS,SAAS,IAAI,QAAQ,MAAM;AACrD,UAAI,KAAK,qBAAqB,QAAQ,GAAG;AACvCA,uBAAAA,OAAO,IAAI,8BAA8B,QAAQ,EAAE;AACnD;AAAA,MACD;AACD,WAAK,iBAAiB,QAAQ;AAG9B,YAAM,aAAc;AAGpB,YAAM,gBAAgB,KAAK,oBAAoB,OAAO;AAGtD,YAAM,eAAe,CAAE;AAGvB,UAAI,cAAc,QAAQ,cAAc;AACtC,cAAM,YAAY,aAAc;AAChC,qBAAa,KAAK,KAAK,mBAAmB,cAAc,MAAM,SAAS,CAAC;AAAA,MACzE;AAGD,UAAI,cAAc,WAAW,iBAAiB;AAC5C,cAAM,eAAe,gBAAiB;AACtC,qBAAa,KAAK,KAAK,sBAAsB,cAAc,SAAS,YAAY,CAAC;AAAA,MAClF;AAGD,UAAI,cAAc,SAAS,eAAe;AACxC,cAAM,aAAa,cAAe;AAClC,qBAAa,KAAK,KAAK,oBAAoB,cAAc,OAAO,UAAU,CAAC;AAAA,MAC5E;AAED,YAAM,QAAQ,WAAW,YAAY;AAErCA,4BAAO,IAAI,0BAA0B;AAGrC,WAAK,MAAM,qBAAqB,EAAE,SAAS,UAAS,CAAE;AAAA,IAEvD,SAAQ,OAAO;AACdA,4BAAO,MAAM,+BAA+B,KAAK;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,sBAAsB,SAAS;AACnC,QAAI;AACF,YAAM,EAAE,QAAQ,YAAY,UAAW,IAAG;AAC1CA,qBAAAA,OAAO,IAAI,8BAA8B,MAAM,EAAE;AAGjD,WAAK,MAAM,uBAAuB,EAAE,QAAQ,YAAY,WAAW;AAAA,IAEpE,SAAQ,OAAO;AACdA,4BAAO,MAAM,6BAA6B,KAAK;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,iBAAiB,UAAU,QAAQ,MAAM,UAAU,CAAA,GAAI;AAC3DA,mBAAM,OAAC,IAAI,4BAA4B,QAAQ,IAAI,MAAM,EAAE;AAE3D,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,QAAQ;AACrCA,qBAAM,OAAC,KAAK,gCAAgC;AAC5C;AAAA,IACD;AAGD,QAAI,QAAQ,OAAO;AACjB,WAAK,iBAAiB,UAAU,QAAQ,IAAI;AAC5C;AAAA,IACD;AAED,QAAI;AAEF,YAAM,WAAWI,cAAAA,GAAS,aAAa,WAAW;AAClD,YAAM,SAAS,MAAM,SAAS,aAAa;AAAA,QACzC,QAAQ,KAAK;AAAA,QACb;AAAA,QACA;AAAA,QACA,SAAQ,6BAAM,QAAM,6BAAM;AAAA,QAC1B;AAAA,MACR,CAAO;AAED,UAAI,OAAO,YAAY,GAAG;AACxBJ,uBAAM,OAAC,IAAI,6BAA6B,QAAQ,IAAI,MAAM,EAAE;AAAA,MACpE,OAAa;AACLA,uBAAAA,OAAO,MAAM,2BAA2B,OAAO,MAAM;AAAA,MACtD;AAAA,IAEF,SAAQ,OAAO;AACdA,4BAAO,MAAM,2BAA2B,KAAK;AAAA,IAE9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,aAAa;AACjB,UAAM,UAAU;AAGhB,QAAI,KAAK,kBAAkB,OAAO,GAAG;AACnCA,qBAAM,OAAC,IAAI,mCAAmC;AAC9C,aAAO,EAAE,SAAS,MAAM,SAAS,KAAM;AAAA,IACxC;AAGD,QAAI,CAAE,MAAM,KAAK,iBAAiB,OAAO,GAAI;AAC3CA,qBAAM,OAAC,KAAK,+BAA+B;AAC3C,aAAO,EAAE,SAAS,OAAO,OAAO,UAAW;AAAA,IAC5C;AAED,QAAI;AACF,WAAK,eAAe,OAAO;AAC3BA,qBAAM,OAAC,IAAI,4BAA4B;AAGvC,YAAM,aAAc;AACpBA,qBAAM,OAAC,IAAI,4BAA4B;AAEvC,YAAM,UAAU,CAAE;AAGlB,UAAI,cAAc;AAChB,YAAI;AACF,gBAAM,YAAY,aAAc;AAChCA,yBAAM,OAAC,IAAI,yBAAyB;AACpC,gBAAM,SAAS,MAAM,UAAU,cAAe;AAC9C,kBAAQ,KAAK,EAAE,MAAM,QAAQ,GAAG,OAAM,CAAE;AACxCA,gCAAO,IAAI,6BAA6B,MAAM;AAAA,QAC/C,SAAQ,OAAO;AACdA,gCAAO,MAAM,6BAA6B,KAAK;AAC/C,kBAAQ,KAAK,EAAE,MAAM,QAAQ,SAAS,OAAO,OAAO,MAAM,SAAS;AAAA,QACpE;AAAA,MACF;AAGD,UAAI,eAAe;AACjB,YAAI;AACF,gBAAM,aAAa,cAAe;AAClCA,yBAAM,OAAC,IAAI,yBAAyB;AACpC,gBAAM,SAAS,MAAM,WAAW,cAAe;AAC/C,kBAAQ,KAAK,EAAE,MAAM,SAAS,GAAG,OAAM,CAAE;AACzCA,gCAAO,IAAI,6BAA6B,MAAM;AAAA,QAC/C,SAAQ,OAAO;AACdA,gCAAO,MAAM,6BAA6B,KAAK;AAC/C,kBAAQ,KAAK,EAAE,MAAM,SAAS,SAAS,OAAO,OAAO,MAAM,SAAS;AAAA,QACrE;AAAA,MACF;AAGD,YAAM,eAAe,QAAQ,OAAO,OAAK,EAAE,OAAO,EAAE;AACpD,YAAM,aAAa,QAAQ,OAAO,OAAK,CAAC,EAAE,OAAO,EAAE;AACnD,YAAM,SAAS,QAAQ,OAAO,OAAK,CAAC,EAAE,OAAO,EAAE,IAAI,OAAK,EAAE,KAAK;AAE/D,YAAM,UAAU,eAAe;AAE/BA,qBAAAA,OAAO,IAAI,iBAAiB,UAAU,MAAM,IAAI,eAAe,YAAY,SAAS,UAAU,EAAE;AAEhG,WAAK,MAAM,yBAAyB;AAAA,QAClC;AAAA,QACA;AAAA,QACA,QAAQ,OAAO,SAAS,IAAI,SAAS;AAAA,MAC7C,CAAO;AAED,aAAO,EAAE,SAAS,SAAS,OAAQ;AAAA,IAEpC,SAAQ,OAAO;AACdA,4BAAO,MAAM,yBAAyB,KAAK;AAC3C,WAAK,MAAM,yBAAyB,EAAE,SAAS,OAAO,OAAO;AAC7D,aAAO,EAAE,SAAS,OAAO,MAAO;AAAA,IACtC,UAAc;AACR,WAAK,aAAa,OAAO;AACzB,WAAK,iBAAiB,OAAO;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,oBAAoB;AACxB,QAAI;AAEF,YAAM,WAAWE,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,UAAU;AAEZ,YAAI,iBAAiB;AACrB,YAAI,OAAO,aAAa,UAAU;AAChC,cAAI;AACF,6BAAiB,KAAK,MAAM,QAAQ;AAAA,UACrC,SAAQ,GAAG;AACVF,kCAAO,KAAK,2BAA2B,CAAC;AAAA,UACzC;AAAA,QACF;AAED,YAAI,kBAAkB,eAAe,KAAK;AACxC,iBAAO,eAAe;AAAA,QACvB;AAAA,MACF;AAGD,UAAI;AACF,cAAM,aAAc;AACpB,YAAI,cAAc;AAChB,gBAAM,YAAY,aAAc;AAChC,cAAI,aAAa,UAAU,YAAY,UAAU,SAAS,KAAK;AAC7D,mBAAO,UAAU,SAAS;AAAA,UAC3B;AAAA,QACF;AAAA,MACF,SAAQ,YAAY;AACnBA,8BAAO,KAAK,gCAAgC,UAAU;AAAA,MACvD;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,4BAAO,MAAM,2BAA2B,KAAK;AAC7C,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,yBAAyB;AAEvBE,wBAAI,sBAAsB,CAAC,QAAQ;AACjC,YAAM,YAAY,KAAK;AACvB,WAAK,WAAW,IAAI;AAEpBF,4BAAO,IAAI,uBAAuB,KAAK,WAAW,OAAO,IAAI,EAAE;AAG/D,UAAI,CAAC,aAAa,KAAK,UAAU;AAC/BA,uBAAM,OAAC,IAAI,4BAA4B;AACvC,mBAAW,MAAM;AACfA,yBAAM,OAAC,IAAI,2BAA2B;AACtC,eAAK,WAAU,EAAG,KAAK,YAAU;AAC/BA,kCAAO,IAAI,2BAA2B,MAAM;AAAA,UACxD,CAAW,EAAE,MAAM,WAAS;AAChBA,kCAAO,MAAM,2BAA2B,KAAK;AAAA,UACzD,CAAW;AAAA,QACF,GAAE,GAAI;AAAA,MACR;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY;AACVA,mBAAM,OAAC,IAAI,yBAAyB;AAGpC,QAAI,KAAK,iBAAiB,KAAK,UAAU;AACvCA,qBAAM,OAAC,IAAI,gCAAgC;AAC3C,iBAAW,MAAM;AACfA,uBAAM,OAAC,IAAI,2BAA2B;AACtC,aAAK,WAAU,EAAG,KAAK,YAAU;AAC/BA,gCAAO,IAAI,2BAA2B,MAAM;AAAA,QACtD,CAAS,EAAE,MAAM,WAAS;AAChBA,gCAAO,MAAM,2BAA2B,KAAK;AAAA,QACvD,CAAS;AAAA,MACF,GAAE,GAAI;AAAA,IACb,OAAW;AACLA,qBAAM,OAAC,KAAK,kCAAkC;AAAA,QAC5C,eAAe,KAAK;AAAA,QACpB,UAAU,KAAK;AAAA,MACvB,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY;AACVA,mBAAM,OAAC,IAAI,yBAAyB;AAAA,EAErC;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB;AACd,WAAO;AAAA,MACL,eAAe,KAAK;AAAA,MACpB,UAAU,KAAK;AAAA,MACf,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,MACnB,WAAW,KAAK,WAAW;AAAA,MAC3B,gBAAgB,KAAK,WAAW;AAAA,MAChC,QAAQ,KAAK;AAAA,MACb,UAAU;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB;AACd,WAAO;AAAA,MACL,WAAW,CAAC,CAACE,cAAAA,MAAI;AAAA,MACjB,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,aAAa,KAAK;AAAA,MAClB,QAAQ,KAAK;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,GAAG,OAAO,UAAU;AAClB,QAAI,CAAC,KAAK,UAAU,KAAK,GAAG;AAC1B,WAAK,UAAU,KAAK,IAAI,CAAE;AAAA,IAC3B;AACD,SAAK,UAAU,KAAK,EAAE,KAAK,QAAQ;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKD,IAAI,OAAO,UAAU;AACnB,QAAI,CAAC,KAAK,UAAU,KAAK;AAAG;AAE5B,UAAM,QAAQ,KAAK,UAAU,KAAK,EAAE,QAAQ,QAAQ;AACpD,QAAI,QAAQ,IAAI;AACd,WAAK,UAAU,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,IACtC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,OAAO,MAAM;AACjB,QAAI,KAAK,UAAU,KAAK,GAAG;AACzB,WAAK,UAAU,KAAK,EAAE,QAAQ,cAAY;AACxC,YAAI;AACF,mBAAS,IAAI;AAAA,QACd,SAAQ,OAAO;AACdF,gCAAO,MAAM,2BAA2B,KAAK;AAAA,QAC9C;AAAA,MACT,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,qBAAqB,KAAK;AACxB,UAAM,MAAM,KAAK,IAAK;AACtB,UAAM,YAAY,KAAK,iBAAiB,IAAI,GAAG;AAG/C,QAAI,aAAc,MAAM,YAAa,IAAI,KAAK,KAAM;AAClDA,qBAAAA,OAAO,IAAI,4BAA4B,GAAG,EAAE;AAC5C,aAAO;AAAA,IACR;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB,KAAK;AACpB,UAAM,MAAM,KAAK,IAAK;AACtB,SAAK,iBAAiB,IAAI,KAAK,GAAG;AAGlC,QAAI,KAAK,iBAAiB,OAAO,KAAK;AACpC,WAAK,yBAA0B;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,2BAA2B;AACzB,UAAM,MAAM,KAAK,IAAK;AACtB,UAAM,gBAAgB,MAAM,KAAK,KAAK;AAEtC,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,iBAAiB,WAAW;AAC9D,UAAI,YAAY,eAAe;AAC7B,aAAK,iBAAiB,OAAO,GAAG;AAAA,MACjC;AAAA,IACF;AAEDA,mBAAM,OAAC,IAAI,kCAAkC,KAAK,iBAAiB,IAAI,EAAE;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB,SAAS;AACzB,WAAO,KAAK,sBAAsB,IAAI,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,SAAS;AACtB,SAAK,sBAAsB,IAAI,SAAS,KAAK,IAAG,CAAE;AAClDA,mBAAAA,OAAO,IAAI,4BAA4B,OAAO,EAAE;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,SAAS;AACpB,SAAK,sBAAsB,OAAO,OAAO;AACzCA,mBAAAA,OAAO,IAAI,2BAA2B,OAAO,EAAE;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,iBAAiB,SAAS,UAAU,KAAO;AAC/C,UAAM,YAAY,KAAK,IAAK;AAE5B,WAAO,KAAK,WAAW,IAAI,OAAO,GAAG;AACnC,UAAI,KAAK,QAAQ,YAAY,SAAS;AACpCA,uBAAAA,OAAO,KAAK,4BAA4B,OAAO,EAAE;AACjD,eAAO;AAAA,MACR;AACD,YAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAAA,IACtD;AAED,SAAK,WAAW,IAAI,SAAS,KAAK,IAAG,CAAE;AACvCA,mBAAAA,OAAO,IAAI,2BAA2B,OAAO,EAAE;AAC/C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB,SAAS;AACxB,SAAK,WAAW,OAAO,OAAO;AAC9BA,mBAAAA,OAAO,IAAI,2BAA2B,OAAO,EAAE;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB,SAAS;AAC3B,UAAM,UAAU,CAAE;AAClB,YAAQ,QAAQ,YAAU;AACxB,UAAI,CAAC,QAAQ,OAAO,QAAQ,GAAG;AAC7B,gBAAQ,OAAO,QAAQ,IAAI,CAAE;AAAA,MAC9B;AACD,cAAQ,OAAO,QAAQ,EAAE,KAAK,MAAM;AAAA,IAC1C,CAAK;AACD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,sBAAsB,QAAQ,QAAQ,WAAW;AACrD,QAAI;AACF,UAAI,WAAW,UAAU;AAEvB,kBAAU,eAAe,MAAM;AAC/BA,uBAAAA,OAAO,IAAI,6BAA6B,MAAM,EAAE;AAAA,MACxD,OAAa;AAEL,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,YAAY,MAAM;AAElD,YAAI,OAAO,YAAY,KAAK,OAAO,MAAM;AACvC,cAAI,WAAW,UAAU;AACvB,sBAAU,gBAAgB,OAAO,IAAI;AACrCJ,2BAAM,OAAC,IAAI,2BAA2B,OAAO,KAAK,KAAK,EAAE;AAAA,UACrE,WAAqB,WAAW,UAAU;AAC9B,sBAAU,mBAAmB,OAAO,IAAI;AACxCA,2BAAM,OAAC,IAAI,4BAA4B,OAAO,KAAK,KAAK,EAAE;AAAA,UAC3D;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,qBAAAA,OAAO,MAAM,6BAA6B,KAAK;AAE/C,YAAM,UAAU,cAAe;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,uBAAuB,QAAQ,QAAQ,YAAY;AACvD,QAAI;AACF,UAAI,WAAW,UAAU;AACvB,mBAAW,gBAAgB,MAAM;AACjCA,uBAAAA,OAAO,IAAI,6BAA6B,MAAM,EAAE;AAAA,MACxD,OAAa;AACL,cAAM,cAAcI,cAAAA,GAAS,aAAa,cAAc;AACxD,cAAM,SAAS,MAAM,YAAY,aAAa,MAAM;AAEpD,YAAI,OAAO,YAAY,KAAK,OAAO,MAAM;AACvC,cAAI,WAAW,UAAU;AACvB,uBAAW,iBAAiB,OAAO,IAAI;AACvCJ,2BAAM,OAAC,IAAI,2BAA2B,OAAO,KAAK,IAAI,EAAE;AAAA,UACpE,WAAqB,WAAW,UAAU;AAC9B,uBAAW,oBAAoB,OAAO,IAAI;AAC1CA,2BAAM,OAAC,IAAI,4BAA4B,OAAO,KAAK,IAAI,EAAE;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,qBAAAA,OAAO,MAAM,6BAA6B,KAAK;AAE/C,YAAM,WAAW,cAAe;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,yBAAyB,QAAQ,QAAQ,cAAc;AAC3D,QAAI;AACF,UAAI,WAAW,UAAU;AAEvB,qBAAa,kBAAkB,MAAM;AACrCA,uBAAAA,OAAO,IAAI,6BAA6B,MAAM,EAAE;AAAA,MACxD,OAAa;AAEL,cAAM,gBAAgBI,cAAAA,GAAS,aAAa,gBAAgB;AAC5D,cAAM,SAAS,MAAM,cAAc,eAAe,MAAM;AAExD,YAAI,OAAO,YAAY,KAAK,OAAO,MAAM;AACvC,cAAI,WAAW,UAAU;AACvB,yBAAa,mBAAmB,OAAO,IAAI;AAC3CJ,2BAAAA,OAAO,IAAI,2BAA2B,MAAM,EAAE;AAAA,UAC1D,WAAqB,WAAW,UAAU;AAC9B,yBAAa,sBAAsB,OAAO,IAAI;AAC9CA,2BAAAA,OAAO,IAAI,4BAA4B,MAAM,EAAE;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,qBAAAA,OAAO,MAAM,2BAA2B,KAAK;AAE7C,UAAI,WAAW,YAAY,QAAQ;AACjC,YAAI;AACF,gBAAM,gBAAgBI,cAAAA,GAAS,aAAa,gBAAgB;AAC5D,gBAAM,SAAS,MAAM,cAAc,eAAe,MAAM;AACxD,cAAI,OAAO,YAAY,KAAK,OAAO,QAAQ,OAAO,KAAK,QAAQ;AAC7D,kBAAM,aAAa,oBAAoB,OAAO,KAAK,QAAQ,MAAM,IAAI;AAAA,UACtE;AAAA,QACF,SAAQ,eAAe;AACtBJ,yBAAAA,OAAO,MAAM,2BAA2B,aAAa;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,sBAAsB,SAAS,cAAc;AACjD,QAAI;AACFA,qBAAM,OAAC,IAAI,yBAAyB,QAAQ,MAAM,QAAQ;AAG1D,YAAM,gBAAgB,CAAE;AACxB,cAAQ,QAAQ,YAAU;;AACxB,cAAM,WAAS,YAAO,SAAP,mBAAa,WAAU,OAAO;AAC7C,YAAI,QAAQ;AACV,cAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,0BAAc,MAAM,IAAI,CAAE;AAAA,UAC3B;AACD,wBAAc,MAAM,EAAE,KAAK,MAAM;AAAA,QAClC;AAAA,MACT,CAAO;AAGD,YAAM,eAAe,OAAO,QAAQ,aAAa,EAAE,IAAI,OAAO,CAAC,QAAQ,WAAW,MAAM;AAEtF,cAAM,aAAa,oBAAoB,QAAQ,MAAM,IAAI;AAAA,MACjE,CAAO;AAED,YAAM,QAAQ,WAAW,YAAY;AACrCA,4BAAO,IAAI,0BAA0B;AAAA,IAEtC,SAAQ,OAAO;AACdA,4BAAO,MAAM,2BAA2B,KAAK;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,mBAAmB,SAAS,WAAW;AAC3C,QAAI;AACFA,qBAAM,OAAC,IAAI,yBAAyB,QAAQ,MAAM,QAAQ;AAG1D,YAAM,UAAU,QAAQ,OAAO,OAAK,EAAE,WAAW,QAAQ;AACzD,YAAM,UAAU,QAAQ,OAAO,OAAK,EAAE,WAAW,QAAQ;AAGzD,cAAQ,QAAQ,YAAU;AACxB,kBAAU,eAAe,OAAO,MAAM;AAAA,MAC9C,CAAO;AAGD,UAAI,QAAQ,SAAS,GAAG;AACtB,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,UAAU,QAAQ,IAAI,OAAK,EAAE,MAAM;AACzC,cAAM,SAAS,MAAM,WAAW,eAAe,OAAO;AAEtD,YAAI,OAAO,YAAY,KAAK,OAAO,MAAM;AACvC,iBAAO,KAAK,QAAQ,UAAQ;AAC1B,kBAAM,SAAS,QAAQ,KAAK,OAAK,EAAE,WAAW,KAAK,GAAG;AACtD,gBAAI,OAAO,WAAW,UAAU;AAC9B,wBAAU,gBAAgB,IAAI;AAAA,YAC5C,WAAuB,OAAO,WAAW,UAAU;AACrC,wBAAU,mBAAmB,IAAI;AAAA,YAClC;AAAA,UACb,CAAW;AAAA,QACF;AAAA,MACF;AAEDJ,4BAAO,IAAI,0BAA0B;AAAA,IACtC,SAAQ,OAAO;AACdA,qBAAAA,OAAO,MAAM,2BAA2B,KAAK;AAE7C,YAAM,UAAU,cAAe;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,oBAAoB,SAAS,YAAY;AAC7C,QAAI;AACFA,qBAAM,OAAC,IAAI,yBAAyB,QAAQ,MAAM,QAAQ;AAE1D,YAAM,UAAU,QAAQ,OAAO,OAAK,EAAE,WAAW,QAAQ;AACzD,YAAM,UAAU,QAAQ,OAAO,OAAK,EAAE,WAAW,QAAQ;AAEzD,cAAQ,QAAQ,YAAU;AACxB,mBAAW,gBAAgB,OAAO,MAAM;AAAA,MAChD,CAAO;AAED,UAAI,QAAQ,SAAS,GAAG;AACtB,cAAM,cAAcI,cAAAA,GAAS,aAAa,cAAc;AACxD,cAAM,UAAU,QAAQ,IAAI,OAAK,EAAE,MAAM;AACzC,cAAM,SAAS,MAAM,YAAY,eAAe,OAAO;AAEvD,YAAI,OAAO,YAAY,KAAK,OAAO,MAAM;AACvC,iBAAO,KAAK,QAAQ,WAAS;AAC3B,kBAAM,SAAS,QAAQ,KAAK,OAAK,EAAE,WAAW,MAAM,GAAG;AACvD,gBAAI,OAAO,WAAW,UAAU;AAC9B,yBAAW,iBAAiB,KAAK;AAAA,YAC/C,WAAuB,OAAO,WAAW,UAAU;AACrC,yBAAW,oBAAoB,KAAK;AAAA,YACrC;AAAA,UACb,CAAW;AAAA,QACF;AAAA,MACF;AAEDJ,4BAAO,IAAI,0BAA0B;AAAA,IACtC,SAAQ,OAAO;AACdA,qBAAAA,OAAO,MAAM,2BAA2B,KAAK;AAC7C,YAAM,WAAW,cAAe;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB,UAAU,QAAQ,MAAM;AACvC,SAAK,aAAa,IAAI;AAAA,MACpB;AAAA,MACA;AAAA,MACA,SAAQ,6BAAM,QAAM,6BAAM;AAAA,MAC1B;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,kBAAkB,OAAO;AAC7B,QAAI;AACFA,qBAAM,OAAC,IAAI,yBAAyB,MAAM,MAAM,QAAQ;AAGxD,YAAM,UAAU,MAAM,IAAI,WAAS;AAAA,QACjC,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,QACb,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK;AAAA,QACX,WAAW,KAAK;AAAA,MACxB,EAAQ;AAEF,YAAM,WAAWI,cAAAA,GAAS,aAAa,WAAW;AAClD,YAAM,SAAS,MAAM,SAAS,kBAAkB;AAAA,QAC9C,QAAQ,KAAK;AAAA,QACb;AAAA,MACR,CAAO;AAED,UAAI,OAAO,YAAY,GAAG;AACxBJ,uBAAM,OAAC,IAAI,2BAA2B,QAAQ,MAAM,KAAK;AAAA,MACjE,OAAa;AACLA,uBAAAA,OAAO,MAAM,yBAAyB,OAAO,MAAM;AAAA,MACpD;AAAA,IACF,SAAQ,OAAO;AACdA,4BAAO,MAAM,yBAAyB,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU;AACRA,mBAAM,OAAC,IAAI,yCAAyC;AAGpD,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,MAAO;AAAA,IAC1B;AAGD,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,YAAY,CAAE;AACnB,SAAK,YAAY,CAAE;AACnB,SAAK,mBAAmB;AAAA,EACzB;AACH;AAGK,MAAC,cAAc,IAAI,YAAW;;"}
{"version": 3, "file": "userEdit.js", "sources": ["subpkg-profile/pages/userEdit/userEdit.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGtnLXByb2ZpbGVccGFnZXNcdXNlckVkaXRcdXNlckVkaXQudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"edit-container\">\r\n\t\t<!-- 头像选择 -->\r\n\t\t<view class=\"form-item avatar-item\">\r\n\t\t\t<view class=\"form-label\">用户头像</view>\r\n\t\t\t<view class=\"avatar-wrapper\" @click=\"chooseAvatar\">\r\n\t\t\t\t<image class=\"avatar\" :src=\"avatarDisplayUrl || '/static/default_avatar.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"avatar-edit-icon\">\r\n\t\t\t\t\t<uni-icons type=\"camera-filled\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 昵称输入 -->\r\n\t\t<view class=\"form-item\">\r\n\t\t\t<view class=\"form-label\">昵称</view>\r\n\t\t\t<input \r\n\t\t\t\tclass=\"form-input\" \r\n\t\t\t\ttype=\"text\"\r\n\t\t\t\tplaceholder=\"请输入昵称\" \r\n\t\t\t\tv-model=\"nickname\"\r\n\t\t\t\tmaxlength=\"20\"\r\n\t\t\t/>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 保存按钮 -->\r\n\t\t<view class=\"save-btn\" @click=\"saveUserInfo\">保存</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { useUserStore } from '@/store/user.js'\r\n\timport { ref, computed, onMounted } from 'vue'\r\n\t\r\n\texport default {\r\n\t\tsetup() {\r\n\t\t\tconst userStore = useUserStore()\r\n\t\t\t\r\n\t\t\t// 用于模板显示的头像路径，优先显示新选择的，否则显示store中的\r\n\t\t\tconst avatarDisplayUrl = ref('')\r\n\t\t\t// 存储新选择的头像的本地临时路径\r\n\t\t\tconst newAvatarTempPath = ref(null)\r\n\t\t\t\r\n\t\t\t// 昵称输入框的值，从 store 初始化\r\n\t\t\tconst nickname = ref('')\r\n\r\n\t\t\t// 初始化时从store获取数据\r\n\t\t\tonMounted(() => {\r\n\t\t\t\tconst currentUserInfo = userStore.getUserInfo;\r\n\t\t\t\tnickname.value = currentUserInfo?.nickname || '';\r\n\t\t\t\tavatarDisplayUrl.value = userStore.avatarUrl; // 直接使用 getter\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\tuserStore,\r\n\t\t\t\tavatarDisplayUrl,\r\n\t\t\t\tnewAvatarTempPath,\r\n\t\t\t\tnickname\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 选择头像\r\n\t\t\tchooseAvatar() {\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsizeType: ['compressed'],\r\n\t\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconst tempFilePath = res.tempFilePaths[0];\r\n\t\t\t\t\t\tthis.avatarDisplayUrl = tempFilePath; // 更新预览\r\n\t\t\t\t\t\tthis.newAvatarTempPath = tempFilePath; // 存储新头像路径以备上传\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '头像已选择',\r\n\t\t\t\t\t\t\ticon: 'none' // 避免与成功图标混淆，仅提示\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 保存用户信息\r\n\t\t\tasync saveUserInfo() {\r\n\t\t\t\tconst currentNickname = this.userStore.getUserInfo?.nickname || '';\r\n\t\t\t\tconst nicknameChanged = this.nickname.trim() !== currentNickname;\r\n\t\t\t\tconst avatarChanged = !!this.newAvatarTempPath;\r\n\r\n\t\t\t\tif (!this.nickname.trim()) {\r\n\t\t\t\t\tuni.showToast({ title: '昵称不能为空', icon: 'none' });\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!nicknameChanged && !avatarChanged) {\r\n\t\t\t\t\tuni.showToast({ title: '信息未作修改', icon: 'none' });\r\n\t\t\t\t\t// uni.navigateBack(); // 可选：如果未修改也直接返回\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.showLoading({ title: '正在保存...' });\r\n\r\n\t\t\t\tconst updateDataForDB = {};\r\n\t\t\t\tconst updateDataForStore = {};\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 1. 如果头像已更改，则上传新头像\r\n\t\t\t\t\tif (avatarChanged) {\r\n\t\t\t\t\t\tconst uploadResult = await uniCloud.uploadFile({\r\n\t\t\t\t\t\t\tfilePath: this.newAvatarTempPath,\r\n\t\t\t\t\t\t\tcloudPath: `user_avatars/${this.userStore.userId}/avatar_${Date.now()}.${this.newAvatarTempPath.split('.').pop() || 'png'}`,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (uploadResult.fileID) {\r\n\t\t\t\t\t\t\tupdateDataForDB.avatar_file = { url: uploadResult.fileID };\r\n\t\t\t\t\t\t\tupdateDataForStore.avatarUrl = uploadResult.fileID; // 更新 store 时用 avatarUrl 字段\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthrow new Error('头像上传失败，未返回fileID');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 2. 如果昵称已更改\r\n\t\t\t\t\tif (nicknameChanged) {\r\n\t\t\t\t\t\tupdateDataForDB.nickname = this.nickname.trim();\r\n\t\t\t\t\t\tupdateDataForStore.nickname = this.nickname.trim();\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 3. 更新数据库 (如果 avatar 或 nickname 任意一个发生变化)\r\n\t\t\t\t\tif (Object.keys(updateDataForDB).length > 0) {\r\n\t\t\t\t\t\tconst usersTable = uniCloud.database().collection('uni-id-users');\r\n\t\t\t\t\t\tconst dbResult = await usersTable.where('_id==$env.uid').update(updateDataForDB);\r\n\t\t\t\t\t\tif (!dbResult || !(dbResult.result && (dbResult.result.updated > 0 || dbResult.result.updated === 0))) { // updated===0 也可能是因为数据未变\r\n\t\t\t\t\t\t\tconsole.error('DB update failed or no docs matched:', dbResult);\r\n\t\t\t\t\t\t\tlet errMsg = '数据库更新失败';\r\n\t\t\t\t\t\t\tif(dbResult && dbResult.result && dbResult.result.errCode) errMsg = `错误码: ${dbResult.result.errCode}, ${dbResult.result.errMsg}`;\r\n\t\t\t\t\t\t\telse if (dbResult && dbResult.errMsg) errMsg = dbResult.errMsg;\r\n\t\t\t\t\t\t\tthrow new Error(errMsg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 4. 更新本地 Store (使用准备好的 updateDataForStore)\r\n\t\t\t\t\tif (Object.keys(updateDataForStore).length > 0) {\r\n\t\t\t\t\t\tthis.userStore.updateUserInfo(updateDataForStore);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({ title: '保存成功', icon: 'success', duration: 1500 });\r\n\t\t\t\t\tsetTimeout(() => uni.navigateBack(), 1500);\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('Save user info error:', error);\r\n\t\t\t\t\tuni.showToast({ icon: 'none', title: error.message || '保存失败，请重试', duration: 3000 });\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.edit-container {\r\n\t\tpadding: 40rpx;\r\n\t\t/* min-height: 100vh; */\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\t\r\n\t.form-item {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.form-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.form-input {\r\n\t\t\theight: 80rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\twidth: 100%;\r\n\t\t\tborder-bottom: 1px solid #eee;\r\n\t\t\tpadding: 0 10rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.avatar-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\t\r\n\t\t.avatar-wrapper {\r\n\t\t\twidth: 200rpx;\r\n\t\t\theight: 200rpx;\r\n\t\t\tborder-radius: 100rpx;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t.avatar {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tborder-radius: 100rpx;\r\n\t\t\t\tborder: 4rpx solid #8a2be2;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.avatar-edit-icon {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tbackground-color: #8a2be2;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.save-btn {\r\n\t\tbackground-color: #8a2be2;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32rpx;\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 45rpx;\r\n\t\tmargin-top: 60rpx;\r\n\t}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/subpkg-profile/pages/userEdit/userEdit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "onMounted", "uni", "uniCloud"], "mappings": ";;;AAkCC,MAAK,YAAU;AAAA,EACd,QAAQ;AACP,UAAM,YAAYA,WAAAA,aAAa;AAG/B,UAAM,mBAAmBC,cAAG,IAAC,EAAE;AAE/B,UAAM,oBAAoBA,cAAG,IAAC,IAAI;AAGlC,UAAM,WAAWA,cAAG,IAAC,EAAE;AAGvBC,kBAAAA,UAAU,MAAM;AACf,YAAM,kBAAkB,UAAU;AAClC,eAAS,SAAQ,mDAAiB,aAAY;AAC9C,uBAAiB,QAAQ,UAAU;AAAA,IACpC,CAAC;AAED,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,eAAe;AACdC,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,gBAAM,eAAe,IAAI,cAAc,CAAC;AACxC,eAAK,mBAAmB;AACxB,eAAK,oBAAoB;AACzBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,eAAe;;AACpB,YAAM,oBAAkB,UAAK,UAAU,gBAAf,mBAA4B,aAAY;AAChE,YAAM,kBAAkB,KAAK,SAAS,KAAI,MAAO;AACjD,YAAM,gBAAgB,CAAC,CAAC,KAAK;AAE7B,UAAI,CAAC,KAAK,SAAS,QAAQ;AAC1BA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAC/C;AAAA,MACD;AAEA,UAAI,CAAC,mBAAmB,CAAC,eAAe;AACvCA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAE/C;AAAA,MACD;AAEAA,oBAAAA,MAAI,YAAY,EAAE,OAAO,UAAW,CAAA;AAEpC,YAAM,kBAAkB,CAAA;AACxB,YAAM,qBAAqB,CAAA;AAE3B,UAAI;AAEH,YAAI,eAAe;AAClB,gBAAM,eAAe,MAAMC,cAAQ,GAAC,WAAW;AAAA,YAC9C,UAAU,KAAK;AAAA,YACf,WAAW,gBAAgB,KAAK,UAAU,MAAM,WAAW,KAAK,IAAG,CAAE,IAAI,KAAK,kBAAkB,MAAM,GAAG,EAAE,IAAG,KAAM,KAAK;AAAA,UAC1H,CAAC;AACD,cAAI,aAAa,QAAQ;AACxB,4BAAgB,cAAc,EAAE,KAAK,aAAa,OAAK;AACvD,+BAAmB,YAAY,aAAa;AAAA,iBACtC;AACN,kBAAM,IAAI,MAAM,kBAAkB;AAAA,UACnC;AAAA,QACD;AAGA,YAAI,iBAAiB;AACpB,0BAAgB,WAAW,KAAK,SAAS,KAAI;AAC7C,6BAAmB,WAAW,KAAK,SAAS,KAAI;AAAA,QACjD;AAGA,YAAI,OAAO,KAAK,eAAe,EAAE,SAAS,GAAG;AAC5C,gBAAM,aAAaA,cAAAA,GAAS,SAAU,EAAC,WAAW,cAAc;AAChE,gBAAM,WAAW,MAAM,WAAW,MAAM,eAAe,EAAE,OAAO,eAAe;AAC/E,cAAI,CAAC,YAAY,EAAE,SAAS,WAAW,SAAS,OAAO,UAAU,KAAK,SAAS,OAAO,YAAY,KAAK;AACtGD,0BAAA,MAAA,MAAA,SAAA,qDAAc,wCAAwC,QAAQ;AAC9D,gBAAI,SAAS;AACb,gBAAG,YAAY,SAAS,UAAU,SAAS,OAAO;AAAS,uBAAS,QAAQ,SAAS,OAAO,OAAO,KAAK,SAAS,OAAO,MAAM;AAAA,qBACrH,YAAY,SAAS;AAAQ,uBAAS,SAAS;AACxD,kBAAM,IAAI,MAAM,MAAM;AAAA,UACvB;AAAA,QACD;AAGA,YAAI,OAAO,KAAK,kBAAkB,EAAE,SAAS,GAAG;AAC/C,eAAK,UAAU,eAAe,kBAAkB;AAAA,QACjD;AAEAA,sBAAG,MAAC,YAAW;AACfA,4BAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW,UAAU,KAAK,CAAC;AAChE,mBAAW,MAAMA,cAAG,MAAC,aAAc,GAAE,IAAI;AAAA,MAExC,SAAO,OAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,qDAAc,yBAAyB,KAAK;AAC5CA,sBAAAA,MAAI,UAAU,EAAE,MAAM,QAAQ,OAAO,MAAM,WAAW,YAAY,UAAU,IAAM,CAAA;AAAA,MACnF;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;ACtJD,GAAG,WAAW,eAAe;"}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.group-manage-container {
  padding: 20rpx;
  min-height: 100vh;
}
.group-list .section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 10rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border-bottom: 1px solid #eee;
}
.group-list .section-title .add-btn {
  display: flex;
  align-items: center;
  padding: 6rpx 20rpx;
  background-color: #f0e6ff;
  border-radius: 30rpx;
}
.group-list .section-title .add-btn .add-text {
  margin-left: 6rpx;
  font-size: 26rpx;
  color: #8a2be2;
}
.group-list .empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
.group-list .group-items .group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.group-list .group-items .group-item .group-info {
  display: flex;
  align-items: center;
}
.group-list .group-items .group-item .group-info .group-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}
.group-list .group-items .group-item .group-info .default-tag {
  margin-left: 16rpx;
  padding: 4rpx 12rpx;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #999;
}
.group-list .group-items .group-item .group-actions {
  display: flex;
  align-items: center;
}
.group-list .group-items .group-item .group-actions .action-btn {
  padding: 16rpx;
}
.group-list .group-items .group-item .group-actions .action-btn.delete {
  margin-left: 10rpx;
}
.group-list .group-items .group-item .group-actions .action-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}
import { defineStore } from 'pinia'
import loadingManager from '@/utils/loadingManager.js'
import { getCurrentInstance } from 'vue'
import syncManager from '@/utils/syncManager.js'

// uni-id-co的云对象，延迟初始化
let uniIdCo = null;

// 获取uni-id-co云对象的函数
function getUniIdCo() {
  if (!uniIdCo) {
    try {
      if (typeof uniCloud !== 'undefined' && uniCloud.importObject) {
	uniIdCo = uniCloud.importObject('uni-id-co', {
          customUI: true
	});
        // uni-id-co 初始化成功
} else {
        console.error('[store/user.js] uniCloud not available or not initialized');
        return null;
      }
    } catch (error) {
      console.error('[store/user.js] Failed to initialize uni-id-co:', error);
      return null;
    }
  }
  return uniIdCo;
}

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: (() => {
      try {
        const stored = uni.getStorageSync('userInfo');
        return stored ? (typeof stored === 'string' ? JSON.parse(stored) : stored) : {};
      } catch (e) {
        console.error('[userStore] Error parsing userInfo from storage:', e);
        return {};
      }
    })(),
    isLogin: false,
    token: uni.getStorageSync('uni_id_token') || uni.getStorageSync('token') || '', // 优先从uni_id_token读取
    tokenExpired: 0,

    // 是否跳过登录检查，设置为false时要求用户登录才能执行关键操作
    skipLoginCheck: false,
    // 新增：用于存储登录成功后需要执行的动作
    postLoginAction: null, // 可以是一个函数引用或一个描述对象

    // 🚀 新增：同步状态管理
    _isSyncInProgress: false, // 防止重复同步

    // 🚀 新增：智能验证缓存机制
    lastTokenVerifyTime: 0, // 上次token验证时间
    tokenVerifyInterval: 5 * 60 * 1000, // 验证间隔：5分钟
    isTokenVerifying: false, // 是否正在验证token，避免并发验证
    appLastActiveTime: Date.now(), // 应用最后活跃时间
    quickAuthCache: { // 快速验证缓存
      isValid: false,
      timestamp: 0,
      ttl: 30 * 1000 // 30秒内有效
    },
    
    // 🔒 防重复导航机制
    lastNavigationTime: 0, // 上次导航时间
    navigationCooldown: 1000, // 导航冷却时间：1秒
  }),
  
  getters: {
    hasLogin: (state) => state.isLogin,
    getUserInfo: (state) => state.userInfo,
    // isLoggedIn 主要依赖 state.isLogin，skipLoginCheck 可作为特殊用途保留
    isLoggedIn: (state) => state.isLogin || state.skipLoginCheck,
    // Getter to safely get nickname
    nickname: (state) => (state.userInfo && state.userInfo.nickname) ? state.userInfo.nickname : '未登录',
    // Getter to safely get avatar
    avatarUrl: (state) => {
      if (state.userInfo) {
        if (state.userInfo.avatarUrl) { // 1. 检查我们明确设置的 avatarUrl (通常是更新后)
          return state.userInfo.avatarUrl;
        }
        if (state.userInfo.avatar_file && state.userInfo.avatar_file.url) { // 2. 检查 avatar_file.url (通常是数据库加载的)
          return state.userInfo.avatar_file.url;
        }
        if (state.userInfo.avatar) { // 3. 检查 uni-id 可能使用的顶层 avatar 字段
          return state.userInfo.avatar;
        }
      }
      return '/static/default_avatar.png'; // 默认头像
    },
    userId: (state) => (state.userInfo && state.userInfo.uid) ? state.userInfo.uid : null,
  },
  
  actions: {
    // 登录成功时的处理逻辑
    async loginSuccess(data, showSuccessToast = false) {
      this.isLogin = true
      this.userInfo = data.userInfo || {}
      this.token = data.token || ''
      this.tokenExpired = data.tokenExpired || 0
      
      // 🚀 更新验证缓存
      this.lastTokenVerifyTime = Date.now()
      this.quickAuthCache = {
        isValid: true,
        timestamp: Date.now(),
        ttl: 30 * 1000
      }
      
      // 确保userInfo为对象，不是字符串
      if (typeof this.userInfo === 'string') {
        try {
          this.userInfo = JSON.parse(this.userInfo);
        } catch (e) {
          console.error('[userStore] Error parsing userInfo from string:', e);
          this.userInfo = {};
        }
      }
      
      // 存储到本地
      uni.setStorageSync('userInfo', JSON.stringify(this.userInfo))
      if (this.token) {
        // 优先使用uni-id的标准key
        uni.setStorageSync('uni_id_token', this.token);
        // 为了兼容性，也保存一份到token key
        uni.setStorageSync('token', this.token);
      } else {
        uni.removeStorageSync('uni_id_token');
        uni.removeStorageSync('token');
      }

      // 登录成功后，重新初始化需要认证的stores
      await this.initDependentStores(showSuccessToast);

      // 检查并执行登录后动作
      if (typeof this.postLoginAction === 'function') {
        try {
          this.postLoginAction();
        } catch (e) {
          console.error('[userStore] Error executing postLoginAction:', e);
        }
        this.clearPostLoginAction(); // 执行后清除
      } else if (this.postLoginAction && typeof this.postLoginAction.handlerName === 'string') {
        // 如果是更复杂的场景，可能需要根据handlerName和payload去调用预定义的函数
        this.clearPostLoginAction();
      }
    },
    
    // 退出登录
    logout() {
      
      this.userInfo = {}
      this.isLogin = false
      this.token = ''
      this.tokenExpired = 0
      
      // 🚀 清除验证缓存
      this.lastTokenVerifyTime = 0
      this.quickAuthCache = {
        isValid: false,
        timestamp: 0,
        ttl: 30 * 1000
      }
      this.isTokenVerifying = false
      
      // 清除所有可能的token存储
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token') // 旧的 token key，以防万一
      uni.removeStorageSync('uni_id_token')
      uni.removeStorageSync('uni_id_token_expired')
      uni.removeStorageSync('uid') // 旧的 uid key

      // 清除用户相关的业务数据存储
      uni.removeStorageSync('wishList')
      uni.removeStorageSync('wishLastSyncTime')
      uni.removeStorageSync('groups')
      uni.removeStorageSync('groupsLastSyncTime')
      uni.removeStorageSync('messages')
      uni.removeStorageSync('messagesLastSyncTime')
      
      // 清理其他 store 的数据
      try {
        // 使用动态导入避免循环依赖
        const { useWishStore } = require('./wish.js')
        const { useGroupStore } = require('./group.js')
        
        const wishStore = useWishStore()
        const groupStore = useGroupStore()
        
        // 清理 wishStore
        if (wishStore) {
          wishStore.clearLocalData()
        }
        
        // 清理 groupStore
        if (groupStore && typeof groupStore.clearLocalData === 'function') {
          groupStore.clearLocalData()
        }
      } catch (error) {
        console.warn('[userStore] Error clearing other stores:', error)
        // 即使其他 store 清理失败，也要继续完成登出
      }

      // 销毁同步管理器
      try {
        syncManager.destroy()
      } catch (syncError) {
        console.warn('[userStore] Error destroying sync manager:', syncError)
      }
    },
    
    // 🚀 智能验证方法：本地快速检查
    quickLocalAuthCheck() {
      let now
      try {
        // 0. 确保缓存对象存在
        if (!this.quickAuthCache) {
          this.quickAuthCache = {
            isValid: false,
            timestamp: 0,
            ttl: 30 * 1000
          }
        }
        
        // 1. 检查快速缓存
        now = Date.now()
        if (this.quickAuthCache.isValid && 
            (now - this.quickAuthCache.timestamp) < this.quickAuthCache.ttl) {
          return { needsVerification: false, isAuthenticated: true }
        }
        
        // 2. 检查基本状态
        if (!this._performLoginCheck()) {
          return { needsVerification: false, isAuthenticated: false }
        }
      } catch (error) {
        console.error('[userStore] Error in quickLocalAuthCheck initial checks:', error)
        return { needsVerification: false, isAuthenticated: false }
      }
      
      // 3. 检查token是否明显过期（本地解析）
      try {
        const tokenParts = this.token.split('.')
        if (tokenParts.length === 3) {
          // 使用 uni-app 兼容的 base64 解码方式
          const base64Str = tokenParts[1]
          // 添加填充字符以确保长度是4的倍数
          const paddedBase64 = base64Str + '==='.slice((base64Str.length + 3) % 4)
          
          let decodedStr = ''
          if (typeof atob !== 'undefined') {
            // 浏览器环境
            decodedStr = atob(paddedBase64)
          } else {
            // 小程序环境：使用简单的 base64 解码替代方案
            try {
              // 先尝试 uni 的方法
              if (uni.base64ToArrayBuffer) {
                const buffer = uni.base64ToArrayBuffer(paddedBase64)
                decodedStr = String.fromCharCode.apply(null, new Uint8Array(buffer))
              } else {
                // 跳过本地token解析，直接进行服务端验证
              }
            } catch (decodeError) {
              console.warn('[userStore] Base64 decode failed, skipping local token check:', decodeError)
            }
          }
          
          if (decodedStr) {
            const payload = JSON.parse(decodedStr)
            if (payload.exp && payload.exp * 1000 < Date.now()) {
              return { needsVerification: false, isAuthenticated: false }
            }
          }
        }
      } catch (e) {
        console.warn('[userStore] Error parsing token locally:', e)
        // 解析失败不影响整体流程，继续后续检查
      }
      
      try {
        // 4. 检查是否需要服务端验证
        if (!now) {
          now = Date.now() // 确保 now 有值
        }
        const needsServerVerification = (now - this.lastTokenVerifyTime) > this.tokenVerifyInterval
        if (!needsServerVerification) {
          this.quickAuthCache = { isValid: true, timestamp: now, ttl: 30 * 1000 }
          return { needsVerification: false, isAuthenticated: true }
        }
        
        return { needsVerification: true, isAuthenticated: true }
      } catch (error) {
        console.error('[userStore] Error in quickLocalAuthCheck final checks:', error)
        return { needsVerification: false, isAuthenticated: false }
      }
    },
    
    // 🚀 智能验证方法：按安全级别分层验证
    async smartAuthCheck(securityLevel = 'basic') {
      // 更新应用活跃时间
      this.appLastActiveTime = Date.now()
      
      // 1. 快速本地检查
      const quickCheck = this.quickLocalAuthCheck()
      if (!quickCheck.needsVerification) {
        return quickCheck.isAuthenticated
      }
      
      // 2. 防止并发验证
      if (this.isTokenVerifying) {
        // 等待当前验证完成
        let retries = 0
        while (this.isTokenVerifying && retries < 20) { // 最多等待2秒
          await new Promise(resolve => setTimeout(resolve, 100))
          retries++
        }
        // 重新检查快速缓存
        const recheckResult = this.quickLocalAuthCheck()
        return !recheckResult.needsVerification && recheckResult.isAuthenticated
      }
      
      // 3. 根据安全级别决定是否进行服务端验证
      switch (securityLevel) {
        case 'low':
          // 低安全级别：信任本地状态，只在长时间未验证时才检查
          if ((Date.now() - this.lastTokenVerifyTime) < this.tokenVerifyInterval * 3) {
            this.quickAuthCache = { isValid: true, timestamp: Date.now(), ttl: 30 * 1000 }
            return true
          }
          break
          
        case 'high':
          // 高安全级别：每次都验证
          break
          
        case 'basic':
        default:
          // 基础级别：按正常间隔验证
          break
      }
      
      // 4. 执行服务端验证
      return await this.performServerTokenVerification()
    },
    
    // 🚀 执行服务端token验证
    async performServerTokenVerification() {
      this.isTokenVerifying = true
      
      try {
        const localToken = this.token
        if (!localToken) {
          this.logout()
          return false
        }
        
        const uniIdCo = getUniIdCo()
        if (!uniIdCo) {
          console.error('[userStore] uni-id-co not available for token check.')
          this.logout()
          return false
        }
        
        const res = await uniIdCo.getAccountInfo()
        
        if (res.errCode === 0) {
          // 验证成功，更新缓存
          this.lastTokenVerifyTime = Date.now()
          this.quickAuthCache = {
            isValid: true,
            timestamp: Date.now(),
            ttl: 30 * 1000
          }
          console.log('[userStore] Server token verification successful')
          return true
        } else if (res.errCode === 'uni-id-token-expired' || res.errCode === 'uni-id-check-token-failed') {
          console.warn('[userStore] Token expired or invalid on server:', res.errMsg)
          this.logout()
          return false
        } else {
          console.error('[userStore] Server token verification error:', res.errMsg)
          this.logout()
          return false
        }
      } catch (error) {
        console.error('[userStore] Exception during server token verification:', error)
        this.logout()
        return false
      } finally {
        this.isTokenVerifying = false
      }
    },
    
    // 🚀 优化后的检查登录并重定向方法
    async checkLoginAndRedirect(securityLevel = 'basic') {
      // 快速检查，大多数情况下不需要网络请求
      const isAuthenticated = await this.smartAuthCheck(securityLevel)
      
      if (!isAuthenticated && !this.skipLoginCheck) {
        this.safeNavigateToLogin()
        return false
      }
      
      return isAuthenticated
    },
    
    // 🔧 检测是否在开发者工具环境
    isDevTool() {
      // 简化检测逻辑，减少可能的问题
      return process.env.NODE_ENV === 'development';
    },

    // 🔒 安全导航方法：防止重复导航（针对开发者工具优化）
    safeNavigateToLogin() {
      const now = Date.now()
      // 🔧 开发者工具环境：增加冷却时间
      const cooldown = this.isDevTool() ? this.navigationCooldown * 2 : this.navigationCooldown
      
      if (now - this.lastNavigationTime < cooldown) {
        return false
      }
      
      this.lastNavigationTime = now
      
      // 🔧 开发者工具环境：添加延迟和重试机制
      const performNavigation = () => {
        uni.navigateTo({
          url: '/pages/login/login',
          fail: (error) => {
            console.error('[userStore] Navigation to login failed:', error)
            
            // 🔧 开发者工具环境：如果是超时错误，尝试重试
            if (this.isDevTool() && error.errMsg && error.errMsg.includes('timeout')) {
              setTimeout(() => {
                uni.navigateTo({
                  url: '/pages/login/login',
                  fail: (retryError) => {
                    console.error('[userStore] Retry login navigation also failed:', retryError)
                    this.lastNavigationTime = 0 // 重置时间，允许再次重试
                    uni.showToast({
                      title: '跳转登录页失败，请重试',
                      icon: 'none',
                      duration: 2000
                    })
                  }
                })
              }, 1000)
            } else {
              // 导航失败时重置时间，允许重试
              this.lastNavigationTime = 0
            }
          }
        })
      }
      
      // 🔧 开发者工具环境：添加短暂延迟
      if (this.isDevTool()) {
        setTimeout(performNavigation, 200)
      } else {
        performNavigation()
      }
      
      return true
    },

    // 🚀 按需验证：根据操作类型选择验证级别
    async ensureAuthenticated(options = {}) {
      const {
        operation = 'read',     // 'read', 'write', 'sensitive'
        showToast = true,
        redirectOnFail = true
      } = options
      
      // 根据操作类型确定安全级别
      let securityLevel = 'basic'
      switch (operation) {
        case 'read':
          securityLevel = 'low'    // 读操作：低安全级别
          break
        case 'write':
          securityLevel = 'basic'  // 写操作：基础级别  
          break
        case 'sensitive':
          securityLevel = 'high'   // 敏感操作：高安全级别
          break
      }
      
      const isAuthenticated = await this.smartAuthCheck(securityLevel)
      
      if (!isAuthenticated) {
        if (showToast) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
        }
        
        if (redirectOnFail) {
          this.safeNavigateToLogin()
        }
      }
      
      return isAuthenticated
    },
    
    // 用于 uni.getUserProfile 获取信息后更新 store 和（可选）后端
    async updateProfileDetails(profileFromWx) { // profileFromWx 是 uni.getUserProfile 返回的原始对象
      if (!this.userInfo || !this.userInfo.uid) {
        console.error('Cannot update profile, user or UID not found in store.');
        return;
      }
      
      const newProfileData = {
        nickname: profileFromWx.nickName,
        avatarUrl: profileFromWx.avatarUrl,
        gender: profileFromWx.gender, // 0: 未知, 1: 男性, 2: 女性
        country: profileFromWx.country,
        province: profileFromWx.province,
        city: profileFromWx.city,
      };

      this.userInfo = {
        ...this.userInfo,
        ...newProfileData
      };
      uni.setStorageSync('userInfo', JSON.stringify(this.userInfo));

      // 重要：将更新后的用户信息发送到 uni-id 后端进行持久化
      try {
        const uniIdCo = getUniIdCo();
        if (!uniIdCo) {
          console.error('Cannot update profile: uni-id-co not available');
          return;
        }
        
        // uni-id 可能期望 avatarUrl 存储在 avatar 字段，或需要特殊处理文件
        // 检查 uni-id 文档关于 updateUser 或类似方法的具体参数
        const updateResult = await uniIdCo.updateUser({
          uid: this.userInfo.uid, // 确保传递了uid
          nickname: newProfileData.nickname,
          avatar: newProfileData.avatarUrl // uni-id users表通常用 avatar 字段存头像URL
          // 根据需要可以传递 gender 等其他信息
        });
        if (updateResult.errCode !== 0) {
          console.error('Failed to update profile on uni-id server:', updateResult.errMsg);
        }
      } catch (error) {
        console.error('Error calling uni-id to update user profile:', error);
      }
    },

    // 从本地存储加载并验证用户状态
    // 🚀 优化后的从本地存储加载用户数据（仅在应用启动时使用）
    async loadUserFromStorage() {
      const localToken = uni.getStorageSync('uni_id_token') || uni.getStorageSync('token');
      
      if (!localToken) {
        this.logout();
        return Promise.resolve(false);
      }
      
      // 设置基本状态，但不立即验证（除非必要）
      this.token = localToken;
      
      // 尝试从本地存储恢复用户信息
      let storedUserInfo = {};
      try {
        const stored = uni.getStorageSync('userInfo');
        storedUserInfo = stored ? (typeof stored === 'string' ? JSON.parse(stored) : stored) : {};
      } catch (e) {
        console.error('[userStore] Error parsing stored userInfo:', e);
        storedUserInfo = {};
      }
      
      if (storedUserInfo && Object.keys(storedUserInfo).length > 0) {
        // 有本地用户信息，先恢复状态
        this.isLogin = true;
        this.userInfo = storedUserInfo;
        
        // 延迟验证token（非阻塞）
        setTimeout(() => {
          this.smartAuthCheck('basic').catch(error => {
            console.error('[userStore] Background token verification failed:', error);
          });
        }, 1000);
        
        return Promise.resolve(true);
      } else {
        // 没有本地用户信息，需要立即验证
        return await this.performServerTokenVerification();
      }
    },

    // 通用登录状态检查方法（避免重复逻辑）
    _performLoginCheck() {
      // 基础检查：token 和登录状态
      if (!this.token || !this.isLogin) {
        return false;
      }

      // 检查用户信息完整性
      if (!this.userInfo || Object.keys(this.userInfo).length === 0) {
        return false;
      }

      return true;
    },

    // 检查登录状态 (可被 loadUserFromStorage 替代或基于其结果)
    checkLoginStatus() {
      this.loadUserFromStorage(); // 确保状态最新
      return this._performLoginCheck();
    },
    

    
    setSkipLoginCheck(skip) {
      this.skipLoginCheck = skip;
      // console.log(`登录检查已${skip ? '跳过' : '启用'}`);
    },
    
    updateUserInfo(updatedInfo) {
      this.userInfo = { ...this.userInfo, ...updatedInfo }
      uni.setStorageSync('userInfo', JSON.stringify(this.userInfo))
      return true
    },
    

    
    clearStorage() {
      try {
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('token')
        uni.removeStorageSync('uni_id_token')
        uni.removeStorageSync('uni_id_token_expired')
        uni.removeStorageSync('uid')
        
        this.userInfo = null
        this.isLogin = false
        this.token = ''
        this.tokenExpired = 0
        

        
        return true
      } catch (e) {
        console.error('清除存储数据失败:', e)
        return false
      }
    },

    // 设置登录后要执行的动作
    setPostLoginAction(action) {
      // action 可以是一个函数，或者一个描述对象 { handlerName: 'name', payload: {} }
      this.postLoginAction = action;
    },

    // 清除登录后动作
    clearPostLoginAction() {
      this.postLoginAction = null;
    },

    // 检查用户是否有特定角色 (如果未来引入RBAC)
    hasRole(role) {
      if (!this.isLogin || !this.userInfo || !this.userInfo.roles) {
        return false;
      }
      return this.userInfo.roles.includes(role);
    },

    // 强制清除所有认证数据的方法
    forceLogout() {
      this.logout();
      // 额外清理一些可能遗留的数据
      uni.removeStorageSync('uni_id_token_expired');
      uni.removeStorageSync('uni_id_user');
      uni.removeStorageSync('uni_id_uid');
    },

    // 修复损坏的userInfo数据
    fixUserInfoData() {
      try {
        const stored = uni.getStorageSync('userInfo');
        
        // 如果userInfo是错误的格式（类数组对象），清除它
        if (stored && typeof stored === 'object' && !Array.isArray(stored) && stored.hasOwnProperty('0')) {
          uni.removeStorageSync('userInfo');
          this.userInfo = {};
          this.logout(); // 强制重新登录
          return true;
        }
        
        return false;
      } catch (e) {
        console.error('[userStore] Error during userInfo fix:', e);
        uni.removeStorageSync('userInfo');
        this.userInfo = {};
        this.logout();
        return true;
      }
    },

    // 登录成功后，重新初始化需要认证的stores
    async initDependentStores(showSuccessToast = false) {
      try {
        // 优化方案：智能同步而不是清理本地数据
        // 使用动态导入避免循环依赖
        const { useGroupStore } = require('./group.js');
        const { useWishStore } = require('./wish.js');
        
        const groupStore = useGroupStore();
        const wishStore = useWishStore();
        
        // 统一的数据初始化和同步
        console.log('[userStore] 开始初始化和同步数据...')

        try {
          // 初始化分组数据（包含同步）
          console.log('[userStore] 初始化分组数据...')
          await groupStore.initGroups();
          console.log('[userStore] ✅ 分组数据初始化完成')
        } catch (error) {
          console.error('[userStore] ❌ 分组数据初始化失败:', error)
        }

        try {
          // 初始化心愿数据（包含同步）
          console.log('[userStore] 初始化心愿数据...')
          await wishStore.initWishList();
          console.log('[userStore] ✅ 心愿数据初始化完成')
        } catch (error) {
          console.error('[userStore] ❌ 心愿数据初始化失败:', error)
        }
        
        // 初始化同步管理器
        try {
          // 启动同步管理器
          await syncManager.init();
        } catch (syncError) {
          console.error('[userStore] ❌ Sync manager initialization failed:', syncError);
          // 同步管理器初始化失败不影响登录流程
        }
        
        // 只在真正登录时显示成功提示，token验证时不显示
        if (showSuccessToast) {
          uni.showToast({
            title: '登录成功！',
            icon: 'success',
            duration: 1500
          });
        }
        
        // 短暂延迟后发送全局刷新事件
        setTimeout(() => {
          uni.$emit('user-login-success', {
            userId: this.userId,
            timestamp: Date.now()
          });
        }, 500);
        
      } catch (error) {
        console.error('[userStore] Error reinitializing dependent stores:', error);
        
        // 显示提示
        uni.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });
      }
    },

    // 手动同步所有数据（用于下拉刷新）- 智能增量同步
    async manualSyncAllData(silent = false) {

      if (!this.isLogin) {
        if (!silent) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          });
        }
        return false;
      }

      try {
        // 根据silent参数决定是否使用loadingManager
        if (silent) {
          // 静默模式：直接调用同步方法，不显示任何加载提示
          const syncResult = await this.performIntelligentSync(silent);
          return syncResult;
        } else {
          // 非静默模式：使用loadingManager显示加载提示
          const syncResult = await loadingManager.wrap(
            () => this.performIntelligentSync(silent),
            {
              title: '正在检查数据更新...',
              id: 'user_intelligent_sync',
              timeout: 15000,
              silent: false,
              showSuccess: false, // 智能同步方法内部处理成功提示
              showError: false, // 智能同步方法内部处理错误
              errorTitle: '同步失败，请重试'
            }
          );
          return syncResult;
        }
      } catch (error) {
        console.error('[userStore] Manual sync failed:', error);
        return false;
      }
    },

    // 执行智能增量同步 - 🚀 增强防重复机制
    async performIntelligentSync(silent = false) {
      console.log('[userStore] 开始执行智能增量同步, silent:', silent);

      // 🚀 防重复检查：如果正在同步中，直接返回
      if (this._isSyncInProgress) {
        console.log('[userStore] 智能同步已在进行中，跳过重复请求');
        return false;
      }

      try {
        // 🚀 标记同步开始
        this._isSyncInProgress = true;
        console.log('[userStore] 同步状态标记为进行中');

        // 使用动态导入避免循环依赖
        const { useGroupStore } = require('./group.js');
        const { useWishStore } = require('./wish.js');

        const groupStore = useGroupStore();
        const wishStore = useWishStore();

        console.log('[userStore] Store实例获取成功:', {
          groupStore: !!groupStore,
          wishStore: !!wishStore,
          groupSmartSync: typeof groupStore.smartSync,
          wishSmartSync: typeof wishStore.smartSync
        });

        let hasUpdates = false;
        const syncResults = {
          groups: { checked: false, updated: false, count: 0 },
          wishes: { checked: false, updated: false, count: 0 }
        };

        // 1. 智能同步分组数据
        console.log('[userStore] 开始同步分组数据...');
        try {
          const groupSyncResult = await groupStore.smartSync();
          console.log('[userStore] 分组同步结果:', groupSyncResult);
          syncResults.groups.checked = true;
          syncResults.groups.updated = groupSyncResult.hasUpdates;
          syncResults.groups.count = groupSyncResult.updatedCount || 0;

          if (groupSyncResult.hasUpdates) {
            hasUpdates = true;
          }
        } catch (groupError) {
          console.error('[userStore] 分组同步出错:', groupError);
          // 捕获并处理分组同步错误，但不中断整个同步流程
          if (this._isMultiDeviceConflictError(groupError)) {
            console.log('[userStore] 分组多设备冲突，静默处理');
            // 多设备冲突静默处理
          } else {
            console.error('[userStore] Group sync failed:', groupError);
            // 非冲突错误继续抛出
            throw groupError;
          }
        }

        // 2. 智能同步心愿数据
        console.log('[userStore] 开始同步心愿数据...');
        try {
          const wishSyncResult = await wishStore.smartSync();
          console.log('[userStore] 心愿同步结果:', wishSyncResult);
          syncResults.wishes.checked = true;
          syncResults.wishes.updated = wishSyncResult.hasUpdates;
          syncResults.wishes.count = wishSyncResult.updatedCount || 0;

          if (wishSyncResult.hasUpdates) {
            hasUpdates = true;
          }
        } catch (wishError) {
          console.error('[userStore] 心愿同步出错:', wishError);
          // 捕获并处理心愿同步错误，但不中断整个同步流程
          if (this._isMultiDeviceConflictError(wishError)) {
            console.log('[userStore] 心愿多设备冲突，静默处理');
            // 多设备冲突静默处理
          } else {
            console.error('[userStore] Wish sync failed:', wishError);
            // 非冲突错误继续抛出
            throw wishError;
          }
        }

        // 3. 显示同步结果（仅在非静默模式下）
        console.log('[userStore] 同步完成，最终结果:', {
          hasUpdates,
          syncResults,
          silent
        });

        if (!silent) {
          if (hasUpdates) {
            let message = '数据已更新';
            const updates = [];
            if (syncResults.groups.updated) updates.push(`分组${syncResults.groups.count}个`);
            if (syncResults.wishes.updated) updates.push(`心愿${syncResults.wishes.count}个`);

            if (updates.length > 0) {
              message = `更新了${updates.join('、')}`;
            }

            // 显示同步成功提示
            console.log('[userStore] 同步完成:', message);
            uni.showToast({
              title: message,
              icon: 'success',
              duration: 2000
            });
          } else {
            // 显示数据已是最新提示
            console.log('[userStore] 数据已是最新');
            uni.showToast({
              title: '数据已是最新',
              icon: 'success',
              duration: 1500
            });
          }
        }

        return hasUpdates;

      } catch (error) {
        // 检查是否是多设备冲突错误 - 🔧 完全静默处理
        if (this._isMultiDeviceConflictError(error)) {
          // 冲突错误完全静默处理，不显示任何弹窗
          console.log('[userStore] 多设备冲突已静默处理');
          return false;
        }
        
        // 检查是否是getSyncSummary相关的错误 - 🔧 静默处理
        if (error.message && (
          error.message.includes('getSyncSummary') ||
          error.message.includes('获取云端') ||
          error.message.includes('摘要失败')
        )) {
          console.error('[userStore] Sync summary API error:', error);
          // 🔧 删除弹窗，静默处理
          return false;
        }

        console.error('[userStore] Intelligent sync failed:', error);
        // 🔧 删除同步失败弹窗，静默处理
        return false; // 不抛出错误，避免阻塞用户操作
      } finally {
        // 🚀 重置同步状态标记
        this._isSyncInProgress = false;
      }
    },

    // 检查是否是多设备冲突错误
    _isMultiDeviceConflictError(error) {
      if (!error || !error.message) return false;
      
      const conflictMessages = [
        '心愿不存在或无权限',
        '分组不存在或无权限',
        'not found',
        'permission denied',
        '无权限操作'
      ];
      
      return conflictMessages.some(msg => 
        error.message.toLowerCase().includes(msg.toLowerCase())
      );
    }
  }
}) 
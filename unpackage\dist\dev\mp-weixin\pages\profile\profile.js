"use strict";
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const store_group = require("../../store/group.js");
const store_wish = require("../../store/wish.js");
const store_message = require("../../store/message.js");
const _sfc_main = {
  setup() {
    const userStore = store_user.useUserStore();
    const groupStore = store_group.useGroupStore();
    const wishStore = store_wish.useWishStore();
    const messageStore = store_message.useMessageStore();
    return {
      userStore,
      groupStore,
      wishStore,
      messageStore
    };
  },
  computed: {
    isLogin() {
      return this.userStore.hasLogin;
    },
    userInfo() {
      return this.userStore.getUserInfo || {};
    }
  },
  onShow() {
  },
  methods: {
    // 跳转到登录页
    goToLogin() {
      common_vendor.index.navigateTo({
        url: "/pages/login/login"
      });
    },
    // 通用页面跳转
    goToPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 显示分享选项
    showShareOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["分享给微信好友", "分享到朋友圈"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    },
    // 显示退出确认
    showLogoutConfirm() {
      common_vendor.index.showModal({
        title: "退出登录",
        content: "确定要退出当前账号吗？",
        showCancel: true,
        cancelText: "取消",
        confirmText: "确定",
        confirmColor: "#e60012",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.__f__("log", "at pages/profile/profile.vue:143", "用户点击确定退出登录");
            this.logout();
          } else if (res.cancel) {
            common_vendor.index.__f__("log", "at pages/profile/profile.vue:146", "用户点击取消");
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/profile/profile.vue:150", "显示退出确认弹窗失败:", err);
        }
      });
    },
    // 退出登录
    logout() {
      this.userStore.logout();
      common_vendor.index.showToast({
        title: "已退出登录",
        icon: "success"
      });
    },
    // 跳转到用户编辑页
    goToUserEdit() {
      common_vendor.index.navigateTo({
        url: "/subpkg-profile/pages/userEdit/userEdit"
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$options.isLogin
  }, !$options.isLogin ? {
    b: common_vendor.o((...args) => $options.goToLogin && $options.goToLogin(...args))
  } : {
    c: $setup.userStore.avatarUrl,
    d: common_vendor.t($setup.userStore.nickname),
    e: common_vendor.p({
      type: "gear",
      size: "24",
      color: "#fff"
    }),
    f: common_vendor.o((...args) => $options.goToUserEdit && $options.goToUserEdit(...args))
  }, {
    g: common_vendor.p({
      type: "location",
      size: "20",
      color: "#fff"
    }),
    h: common_vendor.p({
      type: "right",
      size: "16",
      color: "#ccc"
    }),
    i: common_vendor.o(($event) => $options.goToPage("/subpkg-profile/pages/addressManage/addressManage")),
    j: common_vendor.p({
      type: "bars",
      size: "20",
      color: "#fff"
    }),
    k: common_vendor.p({
      type: "right",
      size: "16",
      color: "#ccc"
    }),
    l: common_vendor.o(($event) => $options.goToPage("/subpkg-wish/pages/groupManage/groupManage")),
    m: common_vendor.p({
      type: "checkbox-filled",
      size: "20",
      color: "#fff"
    }),
    n: common_vendor.p({
      type: "right",
      size: "16",
      color: "#ccc"
    }),
    o: common_vendor.o(($event) => $options.goToPage("/subpkg-profile/pages/historyWish/historyWish")),
    p: common_vendor.p({
      type: "redo-filled",
      size: "20",
      color: "#fff"
    }),
    q: common_vendor.p({
      type: "right",
      size: "16",
      color: "#ccc"
    }),
    r: common_vendor.o((...args) => $options.showShareOptions && $options.showShareOptions(...args)),
    s: common_vendor.p({
      type: "info-filled",
      size: "20",
      color: "#fff"
    }),
    t: common_vendor.p({
      type: "right",
      size: "16",
      color: "#ccc"
    }),
    v: common_vendor.o(($event) => $options.goToPage("/subpkg-profile/pages/about/about")),
    w: $options.isLogin
  }, $options.isLogin ? {
    x: common_vendor.o((...args) => $options.showLogoutConfirm && $options.showLogoutConfirm(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/profile.js.map

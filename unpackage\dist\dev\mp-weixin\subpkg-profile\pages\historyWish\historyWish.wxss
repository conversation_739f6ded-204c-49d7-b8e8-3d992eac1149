/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.history-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  flex-direction: column;
}
.history-content {
  flex: 1;
  padding: 30rpx;
}
.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  min-height: 60vh;
}
.empty-icon {
  margin-bottom: 40rpx;
  opacity: 0.6;
}
.empty-content {
  text-align: center;
  margin-bottom: 60rpx;
}
.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #666666;
  margin-bottom: 16rpx;
}
.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
}
.empty-action {
  margin-top: 20rpx;
}
.go-create-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  background: linear-gradient(135deg, #8a2be2 0%, #9c4ccc 100%);
  color: #ffffff;
  padding: 24rpx 48rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(138, 43, 226, 0.3);
  transition: all 0.3s ease;
}
.go-create-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.4);
}
.go-create-btn text {
  color: #ffffff;
}
.history-list {
  flex: 1;
  padding-bottom: 20rpx;
}
.list-bottom-space {
  height: 40rpx;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100vh;
  padding: 40rpx 30rpx;
  background-color: #F8F8F8;
  background-color: #F8F8F8;
  box-sizing: border-box;
}
.login-header.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}
.login-header .login-logo.data-v-e4e4508d {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}
.login-header .login-title.data-v-e4e4508d {
  font-size: 48rpx;
  font-weight: bold;
  color: #8A2BE2;
  margin-bottom: 10rpx;
}
.login-header .login-subtitle.data-v-e4e4508d {
  font-size: 28rpx;
  color: #666;
}
.card.data-v-e4e4508d {
  width: 100%;
  background-color: #FFFFFF;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 40rpx;
  padding: 40rpx;
  box-sizing: border-box;
}
.login-form .form-content.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.login-form .simplified-title.data-v-e4e4508d {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 50rpx;
  font-weight: 500;
}
.login-form .third-party-login.data-v-e4e4508d {
  width: 100%;
}
.login-form .third-party-login .third-party-btns.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.login-form .third-party-login .third-party-btns .third-party-btn.data-v-e4e4508d {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  height: 88rpx;
  border-radius: 16rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #FFFFFF;
  color: #FFFFFF;
  text-align: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: opacity 0.2s;
}
.login-form .third-party-login .third-party-btns .third-party-btn uni-icons.data-v-e4e4508d, .login-form .third-party-login .third-party-btns .third-party-btn .third-party-icon.data-v-e4e4508d {
  margin-right: 16rpx;
}
.login-form .third-party-login .third-party-btns .third-party-btn .third-party-icon.data-v-e4e4508d {
  width: 40rpx;
  height: 40rpx;
}
.login-form .third-party-login .third-party-btns .third-party-btn.data-v-e4e4508d:active {
  opacity: 0.8;
}
.login-form .third-party-login .third-party-btns .wechat-btn.data-v-e4e4508d {
  background-color: #4CAF50;
  background-color: #4CAF50;
}
.login-form .third-party-login .third-party-btns .alipay-btn.data-v-e4e4508d {
  background-color: #007BFF;
  background-color: #007BFF;
}
.profile-completion-form .form-content.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
}
.profile-completion-form .form-title.data-v-e4e4508d {
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 15rpx;
}
.profile-completion-form .form-subtitle.data-v-e4e4508d {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 50rpx;
}
.profile-completion-form .form-item.data-v-e4e4508d {
  margin-bottom: 40rpx;
}
.profile-completion-form .form-item .label.data-v-e4e4508d {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}
.profile-completion-form .avatar-chooser.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.profile-completion-form .avatar-chooser .label.data-v-e4e4508d {
  text-align: center;
  width: 100%;
}
.profile-completion-form .avatar-chooser .avatar-btn.data-v-e4e4508d {
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: auto;
}
.profile-completion-form .avatar-chooser .avatar-btn.data-v-e4e4508d::after {
  border: none;
}
.profile-completion-form .avatar-chooser .avatar-btn .preview-avatar.data-v-e4e4508d {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  margin-bottom: 15rpx;
  border: 2rpx solid #EFEFEF;
  background-color: #F8F8F8;
}
.profile-completion-form .avatar-chooser .avatar-btn .choose-text.data-v-e4e4508d {
  font-size: 26rpx;
  color: #8A2BE2;
}
.profile-completion-form .input-nickname.data-v-e4e4508d {
  height: 88rpx;
  background-color: #F8F8F8;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 30rpx;
  color: #333;
  border: 1rpx solid #EFEFEF;
}
.profile-completion-form .input-nickname.data-v-e4e4508d:focus {
  border-color: #8A2BE2;
  background-color: #FFFFFF;
}
.profile-completion-form .submit-btn.data-v-e4e4508d {
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 16rpx;
  background-image: linear-gradient(to right, #9A4FED, #8A2BE2);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 15rpx rgba(138, 43, 226, 0.3);
  margin-top: 20rpx;
}
.profile-completion-form .submit-btn.data-v-e4e4508d::after {
  border: none;
}
.profile-completion-form .submit-btn.data-v-e4e4508d:active {
  opacity: 0.8;
}
.agreement.data-v-e4e4508d {
  margin-top: 60rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
.agreement .agreement-link.data-v-e4e4508d {
  color: #8A2BE2;
  text-decoration: none;
}
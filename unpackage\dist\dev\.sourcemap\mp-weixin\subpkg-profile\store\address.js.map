{"version": 3, "file": "address.js", "sources": ["subpkg-profile/store/address.js"], "sourcesContent": ["import { defineStore } from 'pinia'\n\nexport const useAddressStore = defineStore('address', {\n  state: () => ({\n    addressList: [],\n    defaultAddress: null,\n    loading: false,\n    lastSyncTime: null // 最后同步时间\n  }),\n\n  getters: {\n    // 获取默认地址\n    getDefaultAddress: (state) => {\n      return state.addressList.find(addr => addr.isDefault) || null\n    },\n\n    // 获取所有有效地址\n    getValidAddresses: (state) => {\n      return state.addressList.filter(addr => addr.status !== 0)\n    }\n  },\n\n  actions: {\n    /**\n     * 设置地址列表\n     */\n    setAddressList(list) {\n      this.addressList = list || []\n    },\n\n    /**\n     * 添加地址\n     */\n    addAddress(address) {\n      if (address) {\n        this.addressList.push(address)\n      }\n    },\n\n    /**\n     * 更新地址\n     */\n    updateAddress(addressId, updatedData) {\n      const index = this.addressList.findIndex(addr => addr._id === addressId)\n      if (index !== -1) {\n        this.addressList[index] = { ...this.addressList[index], ...updatedData }\n      }\n    },\n\n    /**\n     * 删除地址\n     */\n    removeAddress(addressId) {\n      this.addressList = this.addressList.filter(addr => addr._id !== addressId)\n    },\n\n    /**\n     * 设置默认地址\n     */\n    async setDefaultAddress(addressId) {\n      try {\n        // 调用云函数设置默认地址\n        const addressCenter = uniCloud.importObject('address-center')\n        const result = await addressCenter.setDefaultAddress(addressId)\n\n        if (result.code === 0) {\n          // 云端设置成功，更新本地状态\n          this.addressList.forEach(addr => {\n            addr.isDefault = false\n          })\n\n          // 设置新的默认地址\n          const targetAddress = this.addressList.find(addr => addr._id === addressId)\n          if (targetAddress) {\n            targetAddress.isDefault = true\n            this.defaultAddress = targetAddress\n          }\n        }\n\n        return result\n      } catch (error) {\n        console.error('设置默认地址失败:', error)\n        return {\n          code: -1,\n          message: error.message || '设置默认地址失败'\n        }\n      }\n    },\n\n    /**\n     * 清除默认地址状态（仅本地）\n     */\n    _clearDefaultAddress() {\n      this.addressList.forEach(addr => {\n        addr.isDefault = false\n      })\n      this.defaultAddress = null\n    },\n\n    /**\n     * 获取地址列表\n     */\n    async fetchAddressList() {\n      try {\n        this.loading = true\n        const addressCenter = uniCloud.importObject('address-center')\n        const result = await addressCenter.getAddressList()\n        \n        if (result.code === 0) {\n          this.setAddressList(result.data.list)\n          return result.data\n        } else {\n          throw new Error(result.message || '获取地址列表失败')\n        }\n      } catch (error) {\n        console.error('获取地址列表失败:', error)\n        throw error\n      } finally {\n        this.loading = false\n      }\n    },\n\n    /**\n     * 获取默认地址（从云端）\n     */\n    async fetchDefaultAddress() {\n      try {\n        const addressCenter = uniCloud.importObject('address-center')\n        const result = await addressCenter.getDefaultAddress()\n        \n        if (result.code === 0) {\n          this.defaultAddress = result.data\n          return result.data\n        } else {\n          this.defaultAddress = null\n          return null\n        }\n      } catch (error) {\n        console.error('获取默认地址失败:', error)\n        this.defaultAddress = null\n        return null\n      }\n    },\n\n    /**\n     * 清空地址数据\n     */\n    clearAddressData() {\n      this.addressList = []\n      this.defaultAddress = null\n    }\n  }\n})\n"], "names": ["defineStore", "uniCloud", "uni"], "mappings": ";;AAEY,MAAC,kBAAkBA,cAAW,YAAC,WAAW;AAAA,EACpD,OAAO,OAAO;AAAA,IACZ,aAAa,CAAE;AAAA,IACf,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,cAAc;AAAA;AAAA,EAClB;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,mBAAmB,CAAC,UAAU;AAC5B,aAAO,MAAM,YAAY,KAAK,UAAQ,KAAK,SAAS,KAAK;AAAA,IAC1D;AAAA;AAAA,IAGD,mBAAmB,CAAC,UAAU;AAC5B,aAAO,MAAM,YAAY,OAAO,UAAQ,KAAK,WAAW,CAAC;AAAA,IAC1D;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA;AAAA;AAAA,IAIP,eAAe,MAAM;AACnB,WAAK,cAAc,QAAQ,CAAE;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW,SAAS;AAClB,UAAI,SAAS;AACX,aAAK,YAAY,KAAK,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,cAAc,WAAW,aAAa;AACpC,YAAM,QAAQ,KAAK,YAAY,UAAU,UAAQ,KAAK,QAAQ,SAAS;AACvE,UAAI,UAAU,IAAI;AAChB,aAAK,YAAY,KAAK,IAAI,EAAE,GAAG,KAAK,YAAY,KAAK,GAAG,GAAG,YAAa;AAAA,MACzE;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,cAAc,WAAW;AACvB,WAAK,cAAc,KAAK,YAAY,OAAO,UAAQ,KAAK,QAAQ,SAAS;AAAA,IAC1E;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,kBAAkB,WAAW;AACjC,UAAI;AAEF,cAAM,gBAAgBC,cAAAA,GAAS,aAAa,gBAAgB;AAC5D,cAAM,SAAS,MAAM,cAAc,kBAAkB,SAAS;AAE9D,YAAI,OAAO,SAAS,GAAG;AAErB,eAAK,YAAY,QAAQ,UAAQ;AAC/B,iBAAK,YAAY;AAAA,UAC7B,CAAW;AAGD,gBAAM,gBAAgB,KAAK,YAAY,KAAK,UAAQ,KAAK,QAAQ,SAAS;AAC1E,cAAI,eAAe;AACjB,0BAAc,YAAY;AAC1B,iBAAK,iBAAiB;AAAA,UACvB;AAAA,QACF;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,yCAAA,aAAa,KAAK;AAChC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,MAAM,WAAW;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,uBAAuB;AACrB,WAAK,YAAY,QAAQ,UAAQ;AAC/B,aAAK,YAAY;AAAA,MACzB,CAAO;AACD,WAAK,iBAAiB;AAAA,IACvB;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,mBAAmB;AACvB,UAAI;AACF,aAAK,UAAU;AACf,cAAM,gBAAgBD,cAAAA,GAAS,aAAa,gBAAgB;AAC5D,cAAM,SAAS,MAAM,cAAc,eAAgB;AAEnD,YAAI,OAAO,SAAS,GAAG;AACrB,eAAK,eAAe,OAAO,KAAK,IAAI;AACpC,iBAAO,OAAO;AAAA,QACxB,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,WAAW,UAAU;AAAA,QAC7C;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,0CAAA,aAAa,KAAK;AAChC,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,UAAU;AAAA,MAChB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,sBAAsB;AAC1B,UAAI;AACF,cAAM,gBAAgBD,cAAAA,GAAS,aAAa,gBAAgB;AAC5D,cAAM,SAAS,MAAM,cAAc,kBAAmB;AAEtD,YAAI,OAAO,SAAS,GAAG;AACrB,eAAK,iBAAiB,OAAO;AAC7B,iBAAO,OAAO;AAAA,QACxB,OAAe;AACL,eAAK,iBAAiB;AACtB,iBAAO;AAAA,QACR;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,0CAAA,aAAa,KAAK;AAChC,aAAK,iBAAiB;AACtB,eAAO;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,mBAAmB;AACjB,WAAK,cAAc,CAAE;AACrB,WAAK,iBAAiB;AAAA,IACvB;AAAA,EACF;AACH,CAAC;;"}
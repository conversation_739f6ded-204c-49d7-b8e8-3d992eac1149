/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.edit-container {
  padding: 40rpx;
  /* min-height: 100vh; */
  background-color: #f5f5f5;
}
.form-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.form-item .form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.form-item .form-input {
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
  width: 100%;
  border-bottom: 1px solid #eee;
  padding: 0 10rpx;
}
.avatar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatar-item .avatar-wrapper {
  width: 200rpx;
  height: 200rpx;
  border-radius: 100rpx;
  margin-top: 20rpx;
  position: relative;
}
.avatar-item .avatar-wrapper .avatar {
  width: 100%;
  height: 100%;
  border-radius: 100rpx;
  border: 4rpx solid #8a2be2;
}
.avatar-item .avatar-wrapper .avatar-edit-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #8a2be2;
  display: flex;
  align-items: center;
  justify-content: center;
}
.save-btn {
  background-color: #8a2be2;
  color: #fff;
  font-size: 32rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  margin-top: 60rpx;
}
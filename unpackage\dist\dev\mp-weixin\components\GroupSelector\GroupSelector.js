"use strict";
const common_vendor = require("../../common/vendor.js");
const store_group = require("../../store/group.js");
const store_wish = require("../../store/wish.js");
const store_user = require("../../store/user.js");
const mixins_groupTagOperations = require("../../mixins/groupTagOperations.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "GroupSelector",
  mixins: [mixins_groupTagOperations.groupTagOperations],
  setup() {
    const groupStore = store_group.useGroupStore();
    const wishStore = store_wish.useWishStore();
    const userStore = store_user.useUserStore();
    return {
      groupStore,
      wishStore,
      userStore
    };
  },
  // 添加生命周期钩子
  created() {
    if (this.groupStore && !this.groupStore.getAllGroups.length) {
      this.groupStore.initGroups();
    }
    common_vendor.index.$on("page-show", this.onPageShow);
    common_vendor.index.$on("login-success", () => {
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:88", "接收到登录成功事件");
      if (this.isLoggingIn) {
        this.isLoggingIn = false;
        setTimeout(() => {
          const pendingName = common_vendor.index.getStorageSync("pending_group_name");
          if (pendingName && !this.isProcessingPendingName) {
            common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:100", "登录成功延迟检查: 发现待处理分组", pendingName);
            this.checkPendingGroupName();
          }
        }, 1e3);
      }
    });
  },
  async mounted() {
    common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:108", "[GroupSelector] Component mounted, checking group data...");
    if (this.groupStore && !this.groupStore.getAllGroups.length) {
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:112", "[GroupSelector] Groups empty, initializing in mounted hook");
      try {
        await this.groupStore.initGroups();
        common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:115", "[GroupSelector] Groups initialized successfully");
      } catch (error) {
        common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:117", "[GroupSelector] Group initialization failed:", error);
      }
    }
    this.checkPendingGroupName();
  },
  // 组件销毁时清理事件监听
  beforeDestroy() {
    common_vendor.index.$off("page-show", this.onPageShow);
    common_vendor.index.$off("login-success");
  },
  computed: {
    groups() {
      const allGroups = this.groupStore.getAllGroups;
      const groupIds = allGroups.map((g) => g.id || g._id).join(",");
      const currentVersion = `${allGroups.length}_${groupIds}`;
      if (this.groupsVersion === currentVersion && this.cachedGroups.length > 0) {
        return this.cachedGroups;
      }
      const sortedGroups = [...allGroups].sort((a, b) => {
        if (a.id === "all")
          return -1;
        if (b.id === "all")
          return 1;
        if (a.isDefault && !b.isDefault)
          return -1;
        if (!a.isDefault && b.isDefault)
          return 1;
        return (a.order || 0) - (b.order || 0);
      });
      this.cachedGroups = sortedGroups;
      this.groupsVersion = currentVersion;
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:162", "[GroupSelector] Groups computed, version:", currentVersion);
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:163", "[GroupSelector] Sorted groups:", sortedGroups.map((g) => ({ id: g.id, name: g.name, order: g.order })));
      return sortedGroups;
    },
    currentGroupId() {
      return this.wishStore.currentGroupId;
    },
    // 获取当前分组对象 - 独立计算，避免不必要的依赖
    currentGroup() {
      return this.groupStore.getGroupById(this.currentGroupId);
    }
  },
  watch: {
    // 只监听分组store的变化，不监听心愿store的变化
    "groupStore.groups": {
      handler(newGroups, oldGroups) {
        common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:179", "[GroupSelector] Groups changed in store, resetting cache");
        try {
          this.$nextTick(() => {
            this.groupsVersion = 0;
            this.cachedGroups = [];
            common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:188", "[GroupSelector] Cache reset completed");
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:191", "[GroupSelector] Error handling groups change:", error);
          this.groupsVersion = 0;
          this.cachedGroups = [];
        }
      },
      deep: true,
      immediate: false
    },
    // 监听当前分组ID变化
    currentGroupId: {
      handler(newGroupId, oldGroupId) {
        common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:203", "[GroupSelector] Current group ID changed:", oldGroupId, "->", newGroupId);
      },
      immediate: false
    }
  },
  data() {
    return {
      newGroupName: "",
      isShareButtonActive: false,
      showTooltip: false,
      tooltipTimer: null,
      // 跟踪是否正在登录中
      isLoggingIn: false,
      // 添加标志，避免重复处理待创建分组
      isProcessingPendingName: false,
      // 上次处理的分组名称，避免重复处理
      lastProcessedGroupName: "",
      // 上次处理时间戳
      lastProcessTimestamp: 0,
      // 缓存分组数据，避免不必要的重新计算
      cachedGroups: [],
      // 缓存的分组数据版本号
      groupsVersion: 0
    };
  },
  methods: {
    // 页面显示时检查状态
    onPageShow() {
      if (this.isLoggingIn && this.userStore.isLoggedIn) {
        common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:233", "登录状态变更：未登录 -> 已登录");
        this.isLoggingIn = false;
      }
      const pendingName = common_vendor.index.getStorageSync("pending_group_name");
      if (pendingName && this.userStore.isLoggedIn && !this.isProcessingPendingName) {
        const currentTime = Date.now();
        const isSameGroup = pendingName === this.lastProcessedGroupName;
        const isRecentProcess = currentTime - this.lastProcessTimestamp < 3e3;
        if (isSameGroup && isRecentProcess) {
          common_vendor.index.removeStorageSync("pending_group_name");
          return;
        }
        this.checkPendingGroupName();
      }
    },
    // 检查是否有未完成的分组创建
    checkPendingGroupName() {
      try {
        if (this.isProcessingPendingName) {
          common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:262", "已有待处理分组正在处理中，跳过");
          return;
        }
        this.isProcessingPendingName = true;
        const pendingName = common_vendor.index.getStorageSync("pending_group_name");
        if (pendingName) {
          common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:272", "检测到未完成的分组创建:", pendingName);
          if (this.userStore.isLoggedIn) {
            this.lastProcessedGroupName = pendingName;
            this.lastProcessTimestamp = Date.now();
            setTimeout(() => {
              common_vendor.index.showModal({
                title: "添加分组",
                placeholderText: "请输入分组名称",
                editable: true,
                content: pendingName,
                success: (res) => {
                  common_vendor.index.removeStorageSync("pending_group_name");
                  if (res.confirm && res.content) {
                    this.confirmAddGroup(res.content, true);
                  }
                  this.isProcessingPendingName = false;
                },
                fail: () => {
                  common_vendor.index.removeStorageSync("pending_group_name");
                  this.isProcessingPendingName = false;
                }
              });
            }, 500);
          } else {
            this.isProcessingPendingName = false;
          }
        } else {
          this.isProcessingPendingName = false;
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:342", "检查未完成的分组创建失败:", e);
        this.isProcessingPendingName = false;
        common_vendor.index.removeStorageSync("pending_group_name");
      }
    },
    // 处理分享按钮点击
    handleShareClick() {
      this.prepareShareData();
      this.animateShareButton();
      try {
        common_vendor.index.vibrateShort({
          success: () => {
            common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:361", "振动反馈成功");
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:365", "振动触发失败:", e);
      }
      if (common_vendor.index.getPlatform && common_vendor.index.getPlatform() !== "mp-weixin") {
        try {
          common_vendor.index.showShareMenu({
            withShareTicket: true,
            menus: ["shareAppMessage", "shareTimeline"],
            success() {
              common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:375", "显示分享菜单成功");
            },
            fail(e) {
              common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:378", "显示分享菜单失败", e);
            }
          });
        } catch (err) {
          common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:382", "分享操作失败:", err);
        }
      }
    },
    // 准备分享数据
    prepareShareData() {
      const currentGroup = this.currentGroup;
      if (!currentGroup)
        return;
      const groupWishes = this.wishStore.currentGroupWishes;
      const wishCount = groupWishes.length;
      const shareData = {
        groupId: currentGroup.id,
        groupName: currentGroup.name,
        wishCount,
        timestamp: Date.now()
      };
      common_vendor.index.setStorageSync("current_share_group", shareData);
      common_vendor.index.$emit("share-group-trigger", {
        groupId: currentGroup.id,
        groupName: currentGroup.name,
        wishCount,
        shareType: "group"
      });
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:414", "分享数据已准备:", {
        groupId: currentGroup.id,
        groupName: currentGroup.name,
        shareType: "group"
      });
      try {
        if (common_vendor.index.canIUse && common_vendor.index.canIUse("button.open-type.share")) {
          common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:423", '当前环境支持 open-type="share"');
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:426", "检查分享能力失败:", e);
      }
    },
    // 显示分享提示
    showShareTooltip() {
      try {
        common_vendor.index.vibrateShort();
      } catch (e) {
        common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:436", "振动触发失败:", e);
      }
      this.showTooltip = true;
      if (this.tooltipTimer) {
        clearTimeout(this.tooltipTimer);
      }
      this.tooltipTimer = setTimeout(() => {
        this.showTooltip = false;
      }, 2e3);
    },
    // 选择分组
    selectGroup(groupId) {
      this.wishStore.setCurrentGroup(groupId);
      this.$emit("group-change", groupId);
    },
    // 显示添加分组弹窗
    showAddGroupModal() {
      common_vendor.index.showModal({
        title: "添加分组",
        placeholderText: "请输入分组名称",
        editable: true,
        success: (res) => {
          if (res.confirm && res.content) {
            common_vendor.index.setStorageSync("pending_group_name", res.content);
            this.confirmAddGroup(res.content);
          }
        }
      });
    },
    // 确认添加分组
    async confirmAddGroup(value, skipStorageSave = false) {
      const name = value.trim();
      if (!name) {
        common_vendor.index.showToast({
          title: "分组名称不能为空",
          icon: "none"
        });
        common_vendor.index.removeStorageSync("pending_group_name");
        return;
      }
      if (!this.userStore.isLoggedIn) {
        common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:509", "用户未登录，跳转到登录页面");
        if (!skipStorageSave) {
          common_vendor.index.setStorageSync("pending_group_name", name);
          this.isLoggingIn = true;
          this.userStore.checkLoginAndRedirect();
        }
        return;
      }
      if (this.groupStore._checkGroupNameExists && this.groupStore._checkGroupNameExists(name)) {
        common_vendor.index.showToast({
          title: "分组名称已存在",
          icon: "none"
        });
        common_vendor.index.removeStorageSync("pending_group_name");
        return;
      }
      try {
        common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:537", "[GroupSelector] Starting to add group:", name);
        const groupId = await this.groupStore.addGroup(name);
        common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:542", "[GroupSelector] addGroup returned:", groupId);
        if (groupId) {
          common_vendor.index.removeStorageSync("pending_group_name");
          common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:548", "[GroupSelector] Before setting current group - wishStore.currentGroupId:", this.wishStore.currentGroupId);
          common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:549", "[GroupSelector] Available groups:", this.groupStore.getAllGroups.map((g) => ({ id: g.id, name: g.name })));
          this.wishStore.setCurrentGroup(groupId);
          this.$emit("group-change", groupId);
          common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:555", "[GroupSelector] After setting current group - wishStore.currentGroupId:", this.wishStore.currentGroupId);
          common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:556", "[GroupSelector] Computed currentGroupId:", this.currentGroupId);
          this.groupsVersion = 0;
          this.cachedGroups = [];
          this.$forceUpdate();
          this.$nextTick(() => {
            common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:565", "[GroupSelector] After $nextTick - currentGroupId:", this.currentGroupId);
            common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:566", "[GroupSelector] Groups in computed:", this.groups.map((g) => ({ id: g.id, name: g.name, active: this.currentGroupId === g.id })));
          });
          common_vendor.index.showToast({
            title: "创建成功",
            icon: "success"
          });
        } else {
          common_vendor.index.removeStorageSync("pending_group_name");
          common_vendor.index.showToast({
            title: "创建失败",
            icon: "none"
          });
        }
      } catch (e) {
        common_vendor.index.removeStorageSync("pending_group_name");
        common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:586", "添加分组失败:", e);
        common_vendor.index.showToast({
          title: "创建失败",
          icon: "none"
        });
      }
      this.isProcessingPendingName = false;
    },
    // 按钮动画效果
    animateShareButton() {
      this.isShareButtonActive = true;
      setTimeout(() => {
        this.isShareButtonActive = false;
      }, 600);
      setTimeout(() => {
        try {
          common_vendor.index.showToast({
            title: "已准备分享",
            icon: "success",
            duration: 1500
          });
        } catch (e) {
          common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:615", "显示成功提示失败:", e);
        }
      }, 200);
    },
    // 调试：手动触发数据修复（长按分享按钮5秒）
    onShareButtonLongPress() {
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:622", "[GroupSelector] Long press detected on share button");
      common_vendor.index.showModal({
        title: "调试功能",
        content: "是否要修复分组数据？这会清理重复分组并修复排序。",
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await this.groupStore.repairGroupData();
              common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:631", "[GroupSelector] Group data repair result:", result);
              this.groupsVersion = 0;
              common_vendor.index.showToast({
                title: "数据修复完成",
                icon: "success",
                duration: 2e3
              });
            } catch (error) {
              common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:642", "[GroupSelector] Group data repair failed:", error);
              common_vendor.index.showToast({
                title: "修复失败",
                icon: "none",
                duration: 2e3
              });
            }
          }
        }
      });
    },
    // 处理分组长按事件（带调试信息）
    handleGroupLongPress(group) {
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:656", "[GroupSelector] Group long press triggered");
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:657", "[GroupSelector] Group object:", JSON.stringify(group));
      if (!group) {
        common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:661", "[GroupSelector] 分组对象为空");
        common_vendor.index.showToast({
          title: "分组数据错误",
          icon: "none"
        });
        return;
      }
      const groupId = group.id || group._id;
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:672", "[GroupSelector] Group id:", groupId);
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:673", "[GroupSelector] Group name:", group == null ? void 0 : group.name);
      common_vendor.index.__f__("log", "at components/GroupSelector/GroupSelector.vue:674", "[GroupSelector] Group isDefault:", group == null ? void 0 : group.isDefault);
      if (!groupId) {
        common_vendor.index.__f__("error", "at components/GroupSelector/GroupSelector.vue:677", "[GroupSelector] 分组ID为空, group:", group);
        common_vendor.index.showToast({
          title: "分组ID缺失",
          icon: "none"
        });
        return;
      }
      const normalizedGroup = {
        ...group,
        id: groupId
      };
      this.showGroupActionSheet(normalizedGroup);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$2,
    b: $data.showTooltip
  }, $data.showTooltip ? {} : {}, {
    c: common_vendor.o((...args) => $options.handleShareClick && $options.handleShareClick(...args)),
    d: common_vendor.o((...args) => $options.onShareButtonLongPress && $options.onShareButtonLongPress(...args)),
    e: $data.isShareButtonActive ? 1 : "",
    f: $data.showTooltip ? 1 : "",
    g: $options.currentGroupId,
    h: $options.currentGroup ? $options.currentGroup.name : "",
    i: common_vendor.f($options.groups, (group, index, i0) => {
      return {
        a: common_vendor.t(group.name),
        b: group.id,
        c: $options.currentGroupId === group.id ? 1 : "",
        d: group.isDefault ? 1 : "",
        e: common_vendor.o(($event) => $options.selectGroup(group.id), group.id),
        f: common_vendor.o(($event) => $options.handleGroupLongPress(group), group.id),
        g: group.id
      };
    }),
    j: common_vendor.o((...args) => $options.showAddGroupModal && $options.showAddGroupModal(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/GroupSelector/GroupSelector.js.map

"use strict";
const common_vendor = require("../common/vendor.js");
const utils_envUtils = require("./envUtils.js");
class GestureManager {
  constructor() {
    this.isDragging = false;
    this.isPullRefreshing = false;
    this.gestureListeners = /* @__PURE__ */ new Set();
    this.originalPullRefreshConfig = null;
    this.currentPageInstance = null;
    utils_envUtils.devLog.log("🤲 手势管理器初始化完成");
  }
  /**
   * 设置当前页面实例
   * @param {Object} pageInstance - 页面实例
   */
  setPageInstance(pageInstance) {
    this.currentPageInstance = pageInstance;
    utils_envUtils.devLog.log("🤲 设置页面实例:", pageInstance);
  }
  /**
   * 开始拖拽操作
   * @param {Object} options - 拖拽选项
   */
  startDrag(options = {}) {
    if (this.isPullRefreshing) {
      utils_envUtils.devLog.warn("⚠️ 下拉刷新进行中，忽略拖拽开始");
      return false;
    }
    this.isDragging = true;
    utils_envUtils.devLog.log("🤲 开始拖拽，禁用下拉刷新");
    this._disablePullRefresh();
    this._notifyListeners("dragStart", options);
    return true;
  }
  /**
   * 结束拖拽操作
   * @param {Object} options - 拖拽选项
   */
  endDrag(options = {}) {
    if (!this.isDragging) {
      return;
    }
    this.isDragging = false;
    utils_envUtils.devLog.log("🤲 结束拖拽，恢复下拉刷新");
    setTimeout(() => {
      this._enablePullRefresh();
    }, 100);
    this._notifyListeners("dragEnd", options);
  }
  /**
   * 开始下拉刷新操作
   * @param {Object} options - 刷新选项
   */
  startPullRefresh(options = {}) {
    if (this.isDragging) {
      utils_envUtils.devLog.warn("⚠️ 拖拽进行中，忽略下拉刷新");
      return false;
    }
    this.isPullRefreshing = true;
    utils_envUtils.devLog.log("🤲 开始下拉刷新");
    this._notifyListeners("pullRefreshStart", options);
    return true;
  }
  /**
   * 结束下拉刷新操作
   * @param {Object} options - 刷新选项
   */
  endPullRefresh(options = {}) {
    if (!this.isPullRefreshing) {
      return;
    }
    this.isPullRefreshing = false;
    utils_envUtils.devLog.log("🤲 结束下拉刷新");
    this._notifyListeners("pullRefreshEnd", options);
  }
  /**
   * 检查是否可以开始某个手势
   * @param {string} gestureType - 手势类型 'drag' | 'pullRefresh'
   * @returns {boolean}
   */
  canStartGesture(gestureType) {
    if (gestureType === "drag") {
      return !this.isPullRefreshing;
    } else if (gestureType === "pullRefresh") {
      return !this.isDragging;
    }
    return true;
  }
  /**
   * 获取当前手势状态
   * @returns {Object}
   */
  getGestureState() {
    return {
      isDragging: this.isDragging,
      isPullRefreshing: this.isPullRefreshing,
      hasActiveGesture: this.isDragging || this.isPullRefreshing
    };
  }
  /**
   * 添加手势状态监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.gestureListeners.add(listener);
    utils_envUtils.devLog.log("🤲 添加手势监听器，当前监听器数量:", this.gestureListeners.size);
  }
  /**
   * 移除手势状态监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    this.gestureListeners.delete(listener);
    utils_envUtils.devLog.log("🤲 移除手势监听器，当前监听器数量:", this.gestureListeners.size);
  }
  /**
   * 强制重置所有手势状态
   */
  resetAll() {
    const wasActive = this.isDragging || this.isPullRefreshing;
    this.isDragging = false;
    this.isPullRefreshing = false;
    if (wasActive) {
      utils_envUtils.devLog.log("🤲 强制重置所有手势状态");
      this._enablePullRefresh();
      this._notifyListeners("reset", {});
    }
  }
  /**
   * 禁用下拉刷新
   * @private
   */
  _disablePullRefresh() {
    try {
      common_vendor.index.$emit("gesture-disable-pull-refresh", {
        source: "drag",
        timestamp: Date.now()
      });
      if (this.currentPageInstance && this.currentPageInstance.$mp && this.currentPageInstance.$mp.page) {
        const page = this.currentPageInstance.$mp.page;
        if (page.setData) {
          page.setData({
            enablePullDownRefresh: false
          });
        }
      }
      if (typeof document !== "undefined") {
        document.body.style.overflow = "hidden";
        document.documentElement.style.overflow = "hidden";
      }
      utils_envUtils.devLog.log("🤲 已禁用下拉刷新");
    } catch (error) {
      utils_envUtils.devLog.error("❌ 禁用下拉刷新失败:", error);
    }
  }
  /**
   * 启用下拉刷新
   * @private
   */
  _enablePullRefresh() {
    try {
      common_vendor.index.$emit("gesture-enable-pull-refresh", {
        source: "drag-end",
        timestamp: Date.now()
      });
      if (this.currentPageInstance && this.currentPageInstance.$mp && this.currentPageInstance.$mp.page) {
        const page = this.currentPageInstance.$mp.page;
        if (page.setData) {
          page.setData({
            enablePullDownRefresh: true
          });
        }
      }
      if (typeof document !== "undefined") {
        document.body.style.overflow = "";
        document.documentElement.style.overflow = "";
      }
      utils_envUtils.devLog.log("🤲 已启用下拉刷新");
    } catch (error) {
      utils_envUtils.devLog.error("❌ 启用下拉刷新失败:", error);
    }
  }
  /**
   * 通知所有监听器
   * @private
   * @param {string} eventType - 事件类型
   * @param {Object} data - 事件数据
   */
  _notifyListeners(eventType, data) {
    this.gestureListeners.forEach((listener) => {
      try {
        listener(eventType, data, this.getGestureState());
      } catch (error) {
        utils_envUtils.devLog.error("❌ 手势监听器执行失败:", error);
      }
    });
  }
}
const gestureManager = new GestureManager();
exports.gestureManager = gestureManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/gestureManager.js.map


.page-container.data-v-153ba1a6 {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 50rpx;
}
.card-container.data-v-153ba1a6 {
	background-color: #fff;
	margin-bottom: 20rpx;
	padding: 0 30rpx;
}
.form-item.data-v-153ba1a6 {
	display: flex;
	align-items: center;
	padding: 30rpx 0;
}
.border-bottom.data-v-153ba1a6 {
	border-bottom: 1px solid #f5f5f5;
}
.label.data-v-153ba1a6 {
	width: 140rpx;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}
input.data-v-153ba1a6, textarea.data-v-153ba1a6 {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}
.placeholder.data-v-153ba1a6 {
	color: #bbb;
	font-size: 28rpx;
}

/* 手机号输入区域 */
.phone-input.data-v-153ba1a6 {
	display: flex;
	flex: 1;
	align-items: center;
}
.country-code.data-v-153ba1a6 {
	display: flex;
	align-items: center;
	margin-right: 20rpx;
	padding-right: 20rpx;
	border-right: 1px solid #eee;
}
.arrow.data-v-153ba1a6 {
	font-size: 22rpx;
	margin-left: 8rpx;
	color: #999;
}

/* 地址选择区域 */
.address-card.data-v-153ba1a6 {
	margin-top: 20rpx;
}
.address-tabs.data-v-153ba1a6 {
	display: flex;
	border-bottom: 1px solid #f5f5f5;
	padding: 20rpx 0;
}
.tab-item.data-v-153ba1a6 {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #8a2be2;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 10rpx 0;
}
.tab-item.active.data-v-153ba1a6 {
	color: #8a2be2;
	font-weight: bold;
	font-size: 30rpx;
}
.tab-item.active.data-v-153ba1a6::after {
	content: '';
	position: absolute;
	bottom: -20rpx;
	width: 80rpx;
	height: 4rpx;
	background-color: #8a2be2;
}
.address-input.data-v-153ba1a6 {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.address-input input.data-v-153ba1a6 {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}
.location-icon.data-v-153ba1a6 {
	font-size: 40rpx;
	padding: 10rpx;
	margin-left: 10rpx;
	color: #8a2be2;
}

/* 地址粘贴板 */
.paste-wrapper.data-v-153ba1a6 {
	background-color: #fff;
	margin-top: 20rpx;
	margin-bottom: 20rpx;
}
.paste-header.data-v-153ba1a6 {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	font-size: 28rpx;
	color: #666;
	border-bottom: 1px solid #f5f5f5;
}
.arrow-down.data-v-153ba1a6 {
	margin-left: 10rpx;
	font-size: 24rpx;
}
.paste-content.data-v-153ba1a6 {
	padding: 30rpx;
	background-color: #fff;
}
.paste-box.data-v-153ba1a6 {
	position: relative;
	background-color: #f8f8f8;
	border-radius: 12rpx;
	padding: 30rpx;
	min-height: 240rpx;
}
.paste-textarea.data-v-153ba1a6 {
	width: 100%;
	min-height: 180rpx;
	font-size: 28rpx;
	box-sizing: border-box;
	margin-bottom: 60rpx;
	line-height: 1.5;
	color: #333;
	background-color: transparent;
}
.paste-actions.data-v-153ba1a6 {
	position: absolute;
	right: 30rpx;
	bottom: 30rpx;
	display: flex;
	align-items: center;
}
.clear-btn.data-v-153ba1a6 {
	font-size: 28rpx;
	color: #888;
	margin-right: 30rpx;
	padding: 10rpx;
}
.submit-btn.data-v-153ba1a6 {
	background-color: #8a2be2;
	color: #fff;
	font-size: 28rpx;
	height: 70rpx;
	line-height: 70rpx;
	border-radius: 35rpx;
	padding: 0 50rpx;
	margin: 0;
}

/* 默认地址 */
.default-card.data-v-153ba1a6 {
	margin-top: 20rpx;
}
.default-item.data-v-153ba1a6 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
}
.default-left.data-v-153ba1a6 {
	display: flex;
	flex-direction: column;
}
.default-title.data-v-153ba1a6 {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}
.default-tip.data-v-153ba1a6 {
	font-size: 24rpx;
	color: #999;
	margin-top: 6rpx;
}

/* 确认按钮 */
.confirm-btn-container.data-v-153ba1a6 {
	padding: 40rpx 30rpx;
}
.confirm-btn.data-v-153ba1a6 {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	background-color: #8a2be2;
	color: #fff;
	font-size: 32rpx;
	border-radius: 45rpx;
	font-weight: normal;
}

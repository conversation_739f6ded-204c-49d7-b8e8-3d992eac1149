"use strict";
const utils_envUtils = require("./envUtils.js");
class Debouncer {
  constructor(fn, delay = 300, options = {}) {
    this.fn = fn;
    this.delay = delay;
    this.timer = null;
    this.leading = options.leading || false;
    this.trailing = options.trailing !== false;
    this.maxWait = options.maxWait || null;
    this.lastCallTime = 0;
    this.lastInvokeTime = 0;
  }
  invoke(args) {
    this.lastInvokeTime = Date.now();
    return this.fn.apply(this, args);
  }
  execute(...args) {
    const now = Date.now();
    const timeSinceLastCall = now - this.lastCallTime;
    this.lastCallTime = now;
    if (this.leading && (!this.timer && timeSinceLastCall >= this.delay)) {
      return this.invoke(args);
    }
    if (this.timer) {
      clearTimeout(this.timer);
    }
    if (this.maxWait && now - this.lastInvokeTime >= this.maxWait) {
      return this.invoke(args);
    }
    if (this.trailing) {
      this.timer = setTimeout(() => {
        this.timer = null;
        this.invoke(args);
      }, this.delay);
    }
  }
  cancel() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.lastCallTime = 0;
    this.lastInvokeTime = 0;
  }
  flush() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
      return this.invoke();
    }
  }
  pending() {
    return !!this.timer;
  }
}
function debounce(fn, delay = 300, options = {}) {
  const debouncer = new Debouncer(fn, delay, options);
  const debouncedFn = function(...args) {
    return debouncer.execute(...args);
  };
  debouncedFn.cancel = () => debouncer.cancel();
  debouncedFn.flush = () => debouncer.flush();
  debouncedFn.pending = () => debouncer.pending();
  return debouncedFn;
}
class BatchManager {
  constructor(batchProcessor, options = {}) {
    this.batchProcessor = batchProcessor;
    this.batchDelay = options.delay || 500;
    this.maxBatchSize = options.maxSize || 50;
    this.queue = [];
    this.isProcessing = false;
    this.debouncedProcess = debounce(
      () => this.processBatch(),
      this.batchDelay,
      { trailing: true }
    );
  }
  add(item) {
    this.queue.push({
      ...item,
      timestamp: Date.now()
    });
    if (this.queue.length >= this.maxBatchSize) {
      this.debouncedProcess.flush();
    } else {
      this.debouncedProcess();
    }
  }
  async processBatch() {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }
    this.isProcessing = true;
    const batch = [...this.queue];
    this.queue = [];
    try {
      utils_envUtils.devLog.log(`[BatchManager] 处理批量操作: ${batch.length} 项`);
      await this.batchProcessor(batch);
    } catch (error) {
      utils_envUtils.devLog.error("[BatchManager] 批量处理失败:", error);
    } finally {
      this.isProcessing = false;
    }
  }
  clear() {
    this.queue = [];
    this.debouncedProcess.cancel();
  }
  size() {
    return this.queue.length;
  }
}
exports.BatchManager = BatchManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/debouncer.js.map

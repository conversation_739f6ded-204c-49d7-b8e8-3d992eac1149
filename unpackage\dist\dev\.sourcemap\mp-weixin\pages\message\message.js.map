{"version": 3, "file": "message.js", "sources": ["pages/message/message.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWVzc2FnZS9tZXNzYWdlLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"message-container\">\r\n\t\t<view class=\"message-header\">\r\n\t\t\t<view class=\"title\">消息通知</view>\r\n\t\t\t<view class=\"read-all\" @click=\"markAllAsRead\" v-if=\"unreadCount > 0\">全部已读</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"message-list\">\r\n\t\t\t<view v-if=\"messages.length === 0\" class=\"empty-list\">\r\n\t\t\t\t<text class=\"empty-text\">暂无消息</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-else>\r\n\t\t\t\t<!-- 区分不同类型的消息 -->\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"(message, index) in messages\" \r\n\t\t\t\t\t:key=\"message.id\"\r\n\t\t\t\t\tclass=\"message-item card\"\r\n\t\t\t\t\t:class=\"{ 'unread': !message.isRead, 'comment-message': message.type === 'comment' }\"\r\n\t\t\t\t\t@click=\"handleMessageClick(message)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"message-dot\" v-if=\"!message.isRead\"></view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 评论类型消息 -->\r\n\t\t\t\t\t<template v-if=\"message.type === 'comment'\">\r\n\t\t\t\t\t\t<view class=\"message-header-row\">\r\n\t\t\t\t\t\t\t<image class=\"comment-avatar\" :src=\"message.commentAvatar || '/static/default-avatar.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<view class=\"message-title\">{{ message.title }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"message-wish-title\">心愿：{{ message.wishTitle || '未知心愿' }}</view>\r\n\t\t\t\t\t\t<view class=\"message-content comment-content\">{{ message.content }}</view>\r\n\t\t\t\t\t\t<view class=\"message-time\">{{ formatTime(message.createDate) }}</view>\r\n\t\t\t\t\t\t<view class=\"message-action\">点击查看详情</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 普通系统消息 -->\r\n\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t<view class=\"message-title\">{{ message.title }}</view>\r\n\t\t\t\t\t\t<view class=\"message-content\">{{ message.content }}</view>\r\n\t\t\t\t\t\t<view class=\"message-time\">{{ formatTime(message.createDate) }}</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { useMessageStore } from '@/store/message.js'\r\n\timport { useWishStore } from '@/store/wish.js'\r\n\t\r\n\texport default {\r\n\t\tsetup() {\r\n\t\t\tconst messageStore = useMessageStore()\r\n\t\t\tconst wishStore = useWishStore()\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\tmessageStore,\r\n\t\t\t\twishStore\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tmessages() {\r\n\t\t\t\treturn this.messageStore.getAllMessages\r\n\t\t\t},\r\n\t\t\tunreadCount() {\r\n\t\t\t\treturn this.messageStore.getUnreadCount\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 初始化消息\r\n\t\t\tthis.messageStore.initMessages()\r\n\t\t\t\r\n\t\t\t// 更新tabbar徽标\r\n\t\t\tthis.updateTabBarBadge()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// 每次页面显示时更新tabbar徽标\r\n\t\t\tthis.updateTabBarBadge()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 更新tabbar徽标\r\n\t\t\tupdateTabBarBadge() {\r\n\t\t\t\tconst unreadCount = this.unreadCount\r\n\t\t\t\t\r\n\t\t\t\tif (unreadCount > 0) {\r\n\t\t\t\t\t// 显示徽标\r\n\t\t\t\t\tuni.setTabBarBadge({\r\n\t\t\t\t\t\tindex: 1, // 消息选项卡的索引\r\n\t\t\t\t\t\ttext: unreadCount.toString()\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 移除徽标\r\n\t\t\t\t\tuni.removeTabBarBadge({\r\n\t\t\t\t\t\tindex: 1\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理消息点击\r\n\t\t\thandleMessageClick(message) {\r\n\t\t\t\t// 先标记为已读\r\n\t\t\t\tthis.messageStore.markAsRead(message.id)\r\n\t\t\t\t\r\n\t\t\t\t// 根据消息类型执行不同操作\r\n\t\t\t\tif (message.type === 'comment' && message.wishId) {\r\n\t\t\t\t\t// 导航到心愿详情页\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/subpkg-wish/pages/wishDetail/wishDetail?id=${message.wishId}`\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 全部标记为已读\r\n\t\t\tmarkAllAsRead() {\r\n\t\t\t\tthis.messageStore.markAllAsRead()\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已全部标记为已读',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化时间\r\n\t\t\tformatTime(dateString) {\r\n\t\t\t\tconst date = new Date(dateString)\r\n\t\t\t\tconst now = new Date()\r\n\t\t\t\t\r\n\t\t\t\t// 计算时间差（毫秒）\r\n\t\t\t\tconst diff = now - date\r\n\t\t\t\t\r\n\t\t\t\t// 今天内\r\n\t\t\t\tif (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\r\n\t\t\t\t\tconst hours = date.getHours().toString().padStart(2, '0')\r\n\t\t\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0')\r\n\t\t\t\t\treturn `今天 ${hours}:${minutes}`\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 昨天\r\n\t\t\t\tconst yesterday = new Date(now)\r\n\t\t\t\tyesterday.setDate(yesterday.getDate() - 1)\r\n\t\t\t\tif (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth() && date.getFullYear() === yesterday.getFullYear()) {\r\n\t\t\t\t\tconst hours = date.getHours().toString().padStart(2, '0')\r\n\t\t\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0')\r\n\t\t\t\t\treturn `昨天 ${hours}:${minutes}`\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 其他日期\r\n\t\t\t\treturn `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.message-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.message-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\theight: 90rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tbackground-color: #fff;\r\n\t\t\r\n\t\t.title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\t\t\r\n\t\t.read-all {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #8a2be2;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.message-list {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\t\r\n\t.empty-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding-top: 100rpx;\r\n\t\t\r\n\t\t.empty-text {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.message-item {\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tposition: relative;\r\n\t\t\r\n\t\t&.unread {\r\n\t\t\tborder-left: 4rpx solid #8a2be2;\r\n\t\t}\r\n\t\t\r\n\t\t&.comment-message {\r\n\t\t\tbackground-color: #fafafa;\r\n\t\t}\r\n\t\t\r\n\t\t.message-dot {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 16rpx;\r\n\t\t\theight: 16rpx;\r\n\t\t\tbackground-color: #8a2be2;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\ttop: 38rpx;\r\n\t\t\tright: 30rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.message-header-row {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\r\n\t\t\t.comment-avatar {\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.message-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.message-wish-title {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #8a2be2;\r\n\t\t\tmargin-bottom: 12rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.message-content {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tline-height: 1.5;\r\n\t\t\t\r\n\t\t\t&.comment-content {\r\n\t\t\t\tbackground-color: #f0f0f0;\r\n\t\t\t\tpadding: 16rpx;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.message-time {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\ttext-align: right;\r\n\t\t}\r\n\t\t\r\n\t\t.message-action {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #8a2be2;\r\n\t\t\ttext-align: right;\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/pages/message/message.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useMessageStore", "useWishStore", "uni"], "mappings": ";;;;AAkDC,MAAK,YAAU;AAAA,EACd,QAAQ;AACP,UAAM,eAAeA,cAAAA,gBAAgB;AACrC,UAAM,YAAYC,WAAAA,aAAa;AAE/B,WAAO;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,WAAW;AACV,aAAO,KAAK,aAAa;AAAA,IACzB;AAAA,IACD,cAAc;AACb,aAAO,KAAK,aAAa;AAAA,IAC1B;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,aAAa,aAAa;AAG/B,SAAK,kBAAkB;AAAA,EACvB;AAAA,EACD,SAAS;AAER,SAAK,kBAAkB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,oBAAoB;AACnB,YAAM,cAAc,KAAK;AAEzB,UAAI,cAAc,GAAG;AAEpBC,sBAAAA,MAAI,eAAe;AAAA,UAClB,OAAO;AAAA;AAAA,UACP,MAAM,YAAY,SAAS;AAAA,SAC3B;AAAA,aACK;AAENA,sBAAAA,MAAI,kBAAkB;AAAA,UACrB,OAAO;AAAA,SACP;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB,SAAS;AAE3B,WAAK,aAAa,WAAW,QAAQ,EAAE;AAGvC,UAAI,QAAQ,SAAS,aAAa,QAAQ,QAAQ;AAEjDA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,+CAA+C,QAAQ,MAAM;AAAA,SAClE;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB;AACf,WAAK,aAAa,cAAc;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,MAAM,oBAAI,KAAK;AAGrB,YAAM,OAAO,MAAM;AAGnB,UAAI,OAAO,KAAK,KAAK,KAAK,OAAQ,KAAK,QAAQ,MAAM,IAAI,WAAW;AACnE,cAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,cAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,eAAO,MAAM,KAAK,IAAI,OAAO;AAAA,MAC9B;AAGA,YAAM,YAAY,IAAI,KAAK,GAAG;AAC9B,gBAAU,QAAQ,UAAU,QAAO,IAAK,CAAC;AACzC,UAAI,KAAK,QAAQ,MAAM,UAAU,QAAO,KAAM,KAAK,SAAQ,MAAO,UAAU,SAAS,KAAK,KAAK,YAAW,MAAO,UAAU,eAAe;AACzI,cAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,cAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,eAAO,MAAM,KAAK,IAAI,OAAO;AAAA,MAC9B;AAGA,aAAO,GAAG,KAAK,YAAW,CAAE,KAAK,KAAK,SAAS,IAAI,GAAG,WAAW,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,QAAO,EAAG,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AAAA,IAChI;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpJD,GAAG,WAAW,eAAe;"}
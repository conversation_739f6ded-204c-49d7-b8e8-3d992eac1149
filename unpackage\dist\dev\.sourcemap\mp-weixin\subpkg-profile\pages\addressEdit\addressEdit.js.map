{"version": 3, "file": "addressEdit.js", "sources": ["subpkg-profile/pages/addressEdit/addressEdit.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGtnLXByb2ZpbGVccGFnZXNcYWRkcmVzc0VkaXRcYWRkcmVzc0VkaXQudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"page-container\">\r\n\t\t<!-- 主要表单区域 -->\r\n\t\t<view class=\"card-container\">\r\n\t\t\t<!-- 收货人 -->\r\n\t\t\t<view class=\"form-item border-bottom\">\r\n\t\t\t\t<text class=\"label\">收货人</text>\r\n\t\t\t\t<input \r\n\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\tv-model=\"addressForm.name\" \r\n\t\t\t\t\tplaceholder=\"请填写收货人姓名\" \r\n\t\t\t\t\tplaceholder-class=\"placeholder\"\r\n\t\t\t\t/>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 手机号 -->\r\n\t\t\t<view class=\"form-item border-bottom\">\r\n\t\t\t\t<text class=\"label\">手机号</text>\r\n\t\t\t\t<view class=\"phone-input\">\r\n\t\t\t\t\t<view class=\"country-code\" @click=\"showCountryCodePicker\">\r\n\t\t\t\t\t\t<text>+86</text>\r\n\t\t\t\t\t\t<text class=\"arrow\">∨</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"number\" \r\n\t\t\t\t\t\tv-model=\"addressForm.phone\" \r\n\t\t\t\t\t\tplaceholder=\"请填写收货人手机号\" \r\n\t\t\t\t\t\tplaceholder-class=\"placeholder\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 地址选择区域 -->\r\n\t\t<view class=\"card-container address-card\">\r\n\t\t\t<view class=\"address-tabs\">\r\n\t\t\t\t<view class=\"tab-item active\">\r\n\t\t\t\t\t<text>地图选址</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 地址输入 -->\r\n\t\t\t<view class=\"form-item border-bottom\">\r\n\t\t\t\t<text class=\"label\">地址</text>\r\n\t\t\t\t<view class=\"address-input\">\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tv-model=\"regionText\" \r\n\t\t\t\t\t\tplaceholder=\"选择收货地址\" \r\n\t\t\t\t\t\tplaceholder-class=\"placeholder\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<view class=\"location-icon\" @click=\"getLocationAddress\">📍</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 门牌号 -->\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">门牌号</text>\r\n\t\t\t\t<input \r\n\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\tv-model=\"addressForm.address\" \r\n\t\t\t\t\tplaceholder=\"例如：6栋201室\" \r\n\t\t\t\t\tplaceholder-class=\"placeholder\"\r\n\t\t\t\t/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 地址粘贴板 - 单独一块区域 -->\r\n\t\t<view class=\"paste-wrapper\">\r\n\t\t\t<view class=\"paste-header\" @click=\"togglePasteBoard\">\r\n\t\t\t\t<text>地址粘贴板</text>\r\n\t\t\t\t<text class=\"arrow-down\">{{isPasteBoardShown ? '▲' : '▼'}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"paste-content\" v-if=\"isPasteBoardShown\">\r\n\t\t\t\t<view class=\"paste-box\">\r\n\t\t\t\t\t<textarea \r\n\t\t\t\t\t\tclass=\"paste-textarea\"\r\n\t\t\t\t\t\tv-model=\"pasteContent\"\r\n\t\t\t\t\t\tplaceholder=\"粘贴收件人信息（姓名、电话、地址）\" \r\n\t\t\t\t\t\tplaceholder-class=\"placeholder\"\r\n\t\t\t\t\t></textarea>\r\n\t\t\t\t\t<view class=\"paste-actions\">\r\n\t\t\t\t\t\t<text class=\"clear-btn\" @click=\"clearPasteContent\">清除</text>\r\n\t\t\t\t\t\t<button class=\"submit-btn\" @click=\"parseAddress\">提交</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 默认地址选项 -->\r\n\t\t<view class=\"card-container default-card\">\r\n\t\t\t<view class=\"default-item\">\r\n\t\t\t\t<view class=\"default-left\">\r\n\t\t\t\t\t<text class=\"default-title\">设为默认地址</text>\r\n\t\t\t\t\t<text class=\"default-tip\">提醒：下单时会优先使用该地址</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<switch \r\n\t\t\t\t\t:checked=\"addressForm.isDefault\" \r\n\t\t\t\t\t@change=\"toggleDefault\" \r\n\t\t\t\t\tcolor=\"#8a2be2\"\r\n\t\t\t\t\tstyle=\"transform:scale(0.8)\"\r\n\t\t\t\t/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 确认按钮 -->\r\n\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t<button class=\"confirm-btn\" @click=\"saveAddress\">确认</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { ref, reactive, onMounted, watch, nextTick } from 'vue';\r\n\timport { useUserStore } from '@/store/user.js'\r\n\t\r\n\texport default {\r\n\t\tsetup() {\r\n\t\t\tconst userStore = useUserStore();\r\n\t\t\t\r\n\t\t\t// 地址表单数据\r\n\t\t\tconst addressForm = reactive({\r\n\t\t\t\tname: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tprovince: '',\r\n\t\t\t\tcity: '',\r\n\t\t\t\tdistrict: '',\r\n\t\t\t\taddress: '',\r\n\t\t\t\tisDefault: false\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 用于显示/输入地区的文本\r\n\t\t\tconst regionText = ref('');\r\n\t\t\t\r\n\t\t\t// 粘贴板状态\r\n\t\t\tconst isPasteBoardShown = ref(false);\r\n\t\t\tconst pasteContent = ref('');\r\n\t\t\t\r\n\t\t\t// 地址选择方式Tab (移除，不再需要)\r\n\t\t\tconst editIndex = ref(-1); // -1表示新增，其他值表示编辑\r\n\t\t\t\r\n\t\t\t// 是否已经加载过数据，避免重复加载\r\n\t\t\tconst hasLoaded = ref(false);\r\n\t\t\t\r\n\t\t\t// 显示国家代码选择器\r\n\t\t\tconst showCountryCodePicker = () => {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '目前仅支持中国大陆(+86)',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 切换粘贴板显示状态\r\n\t\t\tconst togglePasteBoard = () => {\r\n\t\t\t\tisPasteBoardShown.value = !isPasteBoardShown.value;\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 解析地址\r\n\t\t\tconst parseAddress = () => {\r\n\t\t\t\tif (!pasteContent.value) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先粘贴内容',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 尝试解析地址文本\r\n\t\t\t\tconst text = pasteContent.value;\r\n\t\t\t\t\r\n\t\t\t\t// 简单的解析逻辑，实际应用中可能需要更复杂的算法\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 1. 尝试提取手机号\r\n\t\t\t\t\tconst phoneRegex = /(?:13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])[0-9]{8}/;\r\n\t\t\t\t\tconst phoneMatch = text.match(phoneRegex);\r\n\t\t\t\t\tif (phoneMatch) {\r\n\t\t\t\t\t\taddressForm.phone = phoneMatch[0];\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 2. 尝试提取姓名（假设姓名在文本开头或者在\"收件人\"后面）\r\n\t\t\t\t\tlet nameMatch = null;\r\n\t\t\t\t\tif (text.includes('收件人')) {\r\n\t\t\t\t\t\tconst nameRegex = /收件人[：:]\\s*([^\\s,，。；;]{2,4})/;\r\n\t\t\t\t\t\tnameMatch = text.match(nameRegex);\r\n\t\t\t\t\t\tif (nameMatch) {\r\n\t\t\t\t\t\t\taddressForm.name = nameMatch[1];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 假设开头2-4个字符是姓名\r\n\t\t\t\t\t\tconst firstLine = text.split(/[\\n,，。；;]/)[0];\r\n\t\t\t\t\t\tconst nameRegex = /^[\\u4e00-\\u9fa5]{2,4}/;\r\n\t\t\t\t\t\tnameMatch = firstLine.match(nameRegex);\r\n\t\t\t\t\t\tif (nameMatch) {\r\n\t\t\t\t\t\t\taddressForm.name = nameMatch[0];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 3. 尝试提取省市区\r\n\t\t\t\t\t// 省份正则\r\n\t\t\t\t\tconst provinceRegex = /(北京|天津|上海|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)[省市自治区特别行政区]/;\r\n\t\t\t\t\tconst provinceMatch = text.match(provinceRegex);\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (provinceMatch) {\r\n\t\t\t\t\t\taddressForm.province = provinceMatch[0];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 提取市\r\n\t\t\t\t\t\tconst cityRegex = new RegExp(provinceMatch[1] + '[省市自治区特别行政区]([^市]+市)');\r\n\t\t\t\t\t\tconst cityMatch = text.match(cityRegex) || text.match(/([^市]+市)/g);\r\n\t\t\t\t\t\tif (cityMatch) {\r\n\t\t\t\t\t\t\taddressForm.city = cityMatch[1] || cityMatch[0];\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 提取区县\r\n\t\t\t\t\t\t\tconst districtRegex = new RegExp(cityMatch[1] ? cityMatch[1] : cityMatch[0] + '([^区县]+区|[^区县]+县)');\r\n\t\t\t\t\t\t\tconst districtMatch = text.match(districtRegex) || text.match(/([^区县]+区|[^区县]+县)/);\r\n\t\t\t\t\t\t\tif (districtMatch) {\r\n\t\t\t\t\t\t\t\taddressForm.district = districtMatch[1] || districtMatch[0];\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 尝试提取详细地址\r\n\t\t\t\t\t\t\t\tconst addressStart = text.indexOf(addressForm.district) + addressForm.district.length;\r\n\t\t\t\t\t\t\t\tif (addressStart > 0 && addressStart < text.length) {\r\n\t\t\t\t\t\t\t\t\tconst detailAddress = text.substring(addressStart).trim();\r\n\t\t\t\t\t\t\t\t\tif (detailAddress) {\r\n\t\t\t\t\t\t\t\t\t\taddressForm.address = detailAddress.replace(/[,，。；;]/g, '').substring(0, 50);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新地区显示文本\r\n\t\t\t\t\tupdateRegionText();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 成功提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '已识别地址信息',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 关闭粘贴板\r\n\t\t\t\t\tisPasteBoardShown.value = false;\r\n\t\t\t\t\t\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('地址解析失败:', e);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '地址解析失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 监听regionText变化，解析成省市区\r\n\t\t\twatch(regionText, (newVal) => {\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t// 简单的省市区解析逻辑，仅作示例\r\n\t\t\t\t\t\tif (newVal.length >= 6) { // 确保有足够长度\r\n\t\t\t\t\t\t\tconst provinceEnd = newVal.indexOf('省');\r\n\t\t\t\t\t\t\tconst cityEnd = newVal.indexOf('市');\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (provinceEnd > 0 && cityEnd > provinceEnd) {\r\n\t\t\t\t\t\t\t\taddressForm.province = newVal.substring(0, provinceEnd + 1);\r\n\t\t\t\t\t\t\t\taddressForm.city = newVal.substring(provinceEnd + 1, cityEnd + 1);\r\n\t\t\t\t\t\t\t\taddressForm.district = newVal.substring(cityEnd + 1);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\tconsole.error('地址解析失败:', e);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 清空省市区\r\n\t\t\t\t\taddressForm.province = '';\r\n\t\t\t\t\taddressForm.city = '';\r\n\t\t\t\t\taddressForm.district = '';\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 合并省市区为显示文本\r\n\t\t\tconst updateRegionText = () => {\r\n\t\t\t\tif (addressForm.province || addressForm.city || addressForm.district) {\r\n\t\t\t\t\tregionText.value = `${addressForm.province || ''}${addressForm.city || ''}${addressForm.district || ''}`;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tregionText.value = '';\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 加载编辑数据的方法\r\n\t\t\tconst loadEditData = async () => {\r\n\t\t\t\t// 防止重复加载\r\n\t\t\t\tif (hasLoaded.value) {\r\n\t\t\t\t\tconsole.log('[addressEdit] 已经加载过数据，跳过重复加载');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取编辑ID\r\n\t\t\t\tconst editId = uni.getStorageSync('edit_address_id');\r\n\t\t\t\tconsole.log('[addressEdit] loadEditData - editId:', editId);\r\n\t\t\t\t\r\n\t\t\t\tif (editId) {\r\n\t\t\t\t\tconsole.log('[addressEdit] 编辑模式, id:', editId);\r\n\t\t\t\t\t\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tuni.showLoading({ title: '加载中...' });\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 从云函数获取地址详情\r\n\t\t\t\t\t\tconst result = await uniCloud.importObject('address-center').getAddressDetail(editId);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (result.code === 0) {\r\n\t\t\t\t\t\t\tconst address = result.data;\r\n\t\t\t\t\t\t\tconsole.log('[addressEdit] 获取地址详情成功:', address);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 使用nextTick确保响应式更新\r\n\t\t\t\t\t\t\tnextTick(() => {\r\n\t\t\t\t\t\t\t\tObject.assign(addressForm, {\r\n\t\t\t\t\t\t\t\t\tname: address.name || '',\r\n\t\t\t\t\t\t\t\t\tphone: address.mobile || '',\r\n\t\t\t\t\t\t\t\t\tprovince: address.province || '',\r\n\t\t\t\t\t\t\t\t\tcity: address.city || '',\r\n\t\t\t\t\t\t\t\t\tdistrict: address.district || '',\r\n\t\t\t\t\t\t\t\t\taddress: address.address || '',\r\n\t\t\t\t\t\t\t\t\tisDefault: address.is_default || false\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 更新地区显示文本\r\n\t\t\t\t\t\t\t\tupdateRegionText();\r\n\t\t\t\t\t\t\t\thasLoaded.value = true; // 标记已加载\r\n\t\t\t\t\t\t\t\tconsole.log('[addressEdit] 表单数据已填充:', JSON.stringify(addressForm));\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: result.message || '获取地址失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tconsole.error('[addressEdit] 获取地址详情失败:', result.message);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tconsole.error('[addressEdit] 获取地址详情异常:', error);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '网络错误',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('[addressEdit] 新增模式');\r\n\t\t\t\t\thasLoaded.value = true; // 新增模式也标记已加载\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// onMounted 在 uni-app 中不如 onLoad 可靠，所以移到 onLoad 中处理\r\n\t\t\t\r\n\t\t\t// 获取位置信息\r\n\t\t\tconst getLocationAddress = () => {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '获取位置中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 先获取经纬度\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02', // 使用国测局坐标系\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('获取位置成功', res);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 根据经纬度获取位置详细信息\r\n\t\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\t\turl: `https://apis.map.qq.com/ws/geocoder/v1/?location=${res.latitude},${res.longitude}&key=YOURKEY`, // 实际使用时需要替换为您的腾讯地图API密钥\r\n\t\t\t\t\t\t\tsuccess: (response) => {\r\n\t\t\t\t\t\t\t\tconsole.log('逆地址解析成功', response);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tif (response.statusCode === 200 && response.data.status === 0) {\r\n\t\t\t\t\t\t\t\t\tconst result = response.data.result;\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 填充地址信息\r\n\t\t\t\t\t\t\t\t\taddressForm.province = result.address_component.province;\r\n\t\t\t\t\t\t\t\t\taddressForm.city = result.address_component.city;\r\n\t\t\t\t\t\t\t\t\taddressForm.district = result.address_component.district;\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 更新地区显示文本\r\n\t\t\t\t\t\t\t\t\tupdateRegionText();\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '已获取当前位置',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t// 使用微信小程序内置逆地址解析\r\n\t\t\t\t\t\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\t\t\t\t\t\tsuccess: (chooseRes) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('选择位置成功', chooseRes);\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t// 解析地址\r\n\t\t\t\t\t\t\t\t\t\t\tconst address = chooseRes.address || '';\r\n\t\t\t\t\t\t\t\t\t\t\tconst name = chooseRes.name || '';\r\n\t\t\t\t\t\t\t\t\t\t\tconst fullAddress = address + name;\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t// 尝试从地址中提取省市区\r\n\t\t\t\t\t\t\t\t\t\t\tlet province = '', city = '', district = '';\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t// 简单解析省市区，实际应用可能需要更复杂的解析\r\n\t\t\t\t\t\t\t\t\t\t\tconst provinceMatch = fullAddress.match(/(北京|天津|上海|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆)[省市自治区]/);\r\n\t\t\t\t\t\t\t\t\t\t\tif (provinceMatch) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tprovince = provinceMatch[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t// 尝试提取市\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst cityMatch = fullAddress.match(/[^市县区]+市/g);\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (cityMatch && cityMatch.length > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcity = cityMatch[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// 尝试提取区县\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconst districtMatch = fullAddress.match(/[^市县区]+[区县]/g);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (districtMatch && districtMatch.length > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdistrict = districtMatch[0];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t// 更新地址信息\r\n\t\t\t\t\t\t\t\t\t\t\taddressForm.province = province;\r\n\t\t\t\t\t\t\t\t\t\t\taddressForm.city = city;\r\n\t\t\t\t\t\t\t\t\t\t\taddressForm.district = district;\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t// 更新地区显示文本\r\n\t\t\t\t\t\t\t\t\t\t\tif (province || city || district) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tupdateRegionText();\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tregionText.value = fullAddress;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '已获取选择位置',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '获取位置失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t// 使用微信小程序内置位置选择\r\n\t\t\t\t\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\t\t\t\t\tsuccess: (chooseRes) => {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('选择位置成功', chooseRes);\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t// 解析地址\r\n\t\t\t\t\t\t\t\t\t\tconst address = chooseRes.address || '';\r\n\t\t\t\t\t\t\t\t\t\tconst name = chooseRes.name || '';\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t// 直接显示完整地址\r\n\t\t\t\t\t\t\t\t\t\tregionText.value = address;\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '已获取选择位置',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '获取位置失败',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.log('获取位置失败', err);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果获取当前位置失败，尝试使用位置选择器\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '无法获取当前位置，请手动选择',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log('选择位置成功', res);\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 解析地址\r\n\t\t\t\t\t\t\t\t\tconst address = res.address || '';\r\n\t\t\t\t\t\t\t\t\tconst name = res.name || '';\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 直接显示完整地址\r\n\t\t\t\t\t\t\t\t\tregionText.value = address;\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '已获取选择位置',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 选择省份\r\n\t\t\tconst selectProvince = () => {\r\n\t\t\t\t// 扩展更多省份数据\r\n\t\t\t\tconst provinces = [\r\n\t\t\t\t\t'北京市', '天津市', '上海市', '重庆市', '河北省', \r\n\t\t\t\t\t'山西省', '辽宁省', '吉林省', '黑龙江省', '江苏省', \r\n\t\t\t\t\t'浙江省', '安徽省', '福建省', '江西省', '山东省', \r\n\t\t\t\t\t'河南省', '湖北省', '湖南省', '广东省', '海南省', \r\n\t\t\t\t\t'四川省', '贵州省', '云南省', '陕西省', '甘肃省',\r\n\t\t\t\t\t'青海省', '台湾省', '内蒙古自治区', '广西壮族自治区', \r\n\t\t\t\t\t'西藏自治区', '宁夏回族自治区', '新疆维吾尔自治区', \r\n\t\t\t\t\t'香港特别行政区', '澳门特别行政区'\r\n\t\t\t\t];\r\n\t\t\t\t\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: provinces,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\taddressForm.province = provinces[res.tapIndex];\r\n\t\t\t\t\t\taddressForm.city = ''; // 清空下级选择\r\n\t\t\t\t\t\taddressForm.district = '';\r\n\t\t\t\t\t\tupdateRegionText();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 自动触发城市选择\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tselectCity();\r\n\t\t\t\t\t\t}, 300);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 选择城市\r\n\t\t\tconst selectCity = () => {\r\n\t\t\t\t// 根据选择的省份动态生成城市列表\r\n\t\t\t\tlet cities = [];\r\n\t\t\t\t\r\n\t\t\t\t// 模拟不同省份对应的城市列表\r\n\t\t\t\tif (addressForm.province === '广东省') {\r\n\t\t\t\t\tcities = ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'];\r\n\t\t\t\t} else if (addressForm.province === '北京市') {\r\n\t\t\t\t\tcities = ['北京市'];\r\n\t\t\t\t} else if (addressForm.province === '上海市') {\r\n\t\t\t\t\tcities = ['上海市'];\r\n\t\t\t\t} else if (addressForm.province === '浙江省') {\r\n\t\t\t\t\tcities = ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市'];\r\n\t\t\t\t} else if (addressForm.province === '江苏省') {\r\n\t\t\t\t\tcities = ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'];\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 其他省份显示默认城市列表\r\n\t\t\t\t\tcities = ['城市1', '城市2', '城市3', '城市4', '城市5'];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!addressForm.province) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先选择省份',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: cities,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\taddressForm.city = cities[res.tapIndex];\r\n\t\t\t\t\t\taddressForm.district = ''; // 清空下级选择\r\n\t\t\t\t\t\tupdateRegionText();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 自动触发区县选择\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tselectDistrict();\r\n\t\t\t\t\t\t}, 300);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 选择区县\r\n\t\t\tconst selectDistrict = () => {\r\n\t\t\t\t// 根据选择的省份和城市动态生成区县列表\r\n\t\t\t\tlet districts = [];\r\n\t\t\t\t\r\n\t\t\t\t// 模拟深圳市的区县\r\n\t\t\t\tif (addressForm.province === '广东省' && addressForm.city === '深圳市') {\r\n\t\t\t\t\tdistricts = ['福田区', '罗湖区', '南山区', '宝安区', '龙岗区', '盐田区', '龙华区', '坪山区', '光明区'];\r\n\t\t\t\t} \r\n\t\t\t\t// 模拟广州市的区县\r\n\t\t\t\telse if (addressForm.province === '广东省' && addressForm.city === '广州市') {\r\n\t\t\t\t\tdistricts = ['越秀区', '海珠区', '荔湾区', '天河区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'];\r\n\t\t\t\t}\r\n\t\t\t\t// 其他城市显示默认区县列表\r\n\t\t\t\telse {\r\n\t\t\t\t\tdistricts = ['区县1', '区县2', '区县3', '区县4', '区县5'];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!addressForm.city) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先选择城市',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: districts,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\taddressForm.district = districts[res.tapIndex];\r\n\t\t\t\t\t\tupdateRegionText();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 切换默认地址\r\n\t\t\tconst toggleDefault = (e) => {\r\n\t\t\t\taddressForm.isDefault = e.detail.value;\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 保存地址\r\n\t\t\tconst saveAddress = async () => {\r\n\t\t\t\t// 检查登录状态\r\n\t\t\t\tif (!userStore.checkLoginAndRedirect()) {\r\n\t\t\t\t\tconsole.log('用户未登录，无法保存地址');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 验证表单\r\n\t\t\t\tif (!addressForm.name) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入收货人姓名',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!addressForm.phone) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入手机号码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 验证手机号格式\r\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(addressForm.phone)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的手机号码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!regionText.value) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择或输入地址',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!addressForm.address) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入门牌号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.showLoading({ title: '保存中...' });\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('保存地址：', addressForm);\r\n\t\t\t\t\tconsole.log('地区文本：', regionText.value);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 解析省市区信息\r\n\t\t\t\t\tlet province = addressForm.province;\r\n\t\t\t\t\tlet city = addressForm.city;\r\n\t\t\t\t\tlet district = addressForm.district;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果省市区为空，尝试从regionText解析或使用地址文本\r\n\t\t\t\t\tif (!province && !city) {\r\n\t\t\t\t\t\tconst addressText = regionText.value || '';\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (addressText) {\r\n\t\t\t\t\t\t\t// 简单解析：尝试提取省份和城市\r\n\t\t\t\t\t\t\tif (addressText.includes('省')) {\r\n\t\t\t\t\t\t\t\tconst parts = addressText.split('省');\r\n\t\t\t\t\t\t\t\tprovince = parts[0] + '省';\r\n\t\t\t\t\t\t\t\tcity = parts[1] || addressText; // 如果没有市，使用完整地址\r\n\t\t\t\t\t\t\t} else if (addressText.includes('市')) {\r\n\t\t\t\t\t\t\t\tconst parts = addressText.split('市');\r\n\t\t\t\t\t\t\t\tprovince = parts[0] + '市'; // 直辖市情况\r\n\t\t\t\t\t\t\t\tcity = parts[0] + '市';\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 无法解析时，使用完整地址文本\r\n\t\t\t\t\t\t\t\tprovince = addressText;\r\n\t\t\t\t\t\t\t\tcity = addressText;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果连地址文本都没有，使用基础默认值\r\n\t\t\t\t\t\t\tprovince = '省份';\r\n\t\t\t\t\t\t\tcity = '城市';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 构建地址数据\r\n\t\t\t\t\tconst addressData = {\r\n\t\t\t\t\t\tname: addressForm.name,\r\n\t\t\t\t\t\tmobile: addressForm.phone,\r\n\t\t\t\t\t\tprovince: province || '省份',\r\n\t\t\t\t\t\tcity: city || '城市',\r\n\t\t\t\t\t\tdistrict: district || '区县',\r\n\t\t\t\t\t\taddress: addressForm.address,\r\n\t\t\t\t\t\tis_default: addressForm.isDefault,\r\n\t\t\t\t\t\ttag: 'home' // 默认标签\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\tlet result;\r\n\t\t\t\t\tconst editId = uni.getStorageSync('edit_address_id');\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (editId) {\r\n\t\t\t\t\t\t// 编辑现有地址\r\n\t\t\t\t\t\tresult = await uniCloud.importObject('address-center').updateAddress(editId, addressData);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 添加新地址\r\n\t\t\t\t\t\tresult = await uniCloud.importObject('address-center').addAddress(addressData);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (result.code === 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t// 延迟返回\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: result.message || '保存失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('保存地址失败:', e);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '网络错误，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 清除粘贴内容\r\n\t\t\tconst clearPasteContent = () => {\r\n\t\t\t\tpasteContent.value = '';\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\tuserStore,\r\n\t\t\t\taddressForm,\r\n\t\t\t\tregionText,\r\n\t\t\t\tisPasteBoardShown,\r\n\t\t\t\tpasteContent,\r\n\t\t\t\thasLoaded,\r\n\t\t\t\tsaveAddress,\r\n\t\t\t\ttoggleDefault,\r\n\t\t\t\ttogglePasteBoard,\r\n\t\t\t\tparseAddress,\r\n\t\t\t\tclearPasteContent,\r\n\t\t\t\tshowCountryCodePicker,\r\n\t\t\t\tgetLocationAddress,\r\n\t\t\t\tselectProvince,\r\n\t\t\t\tselectCity,\r\n\t\t\t\tselectDistrict,\r\n\t\t\t\tupdateRegionText,\r\n\t\t\t\tloadEditData\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 页面加载时保存参数\r\n\t\tonLoad(options) {\r\n\t\t\tconsole.log('[addressEdit] onLoad - options:', options);\r\n\t\t\t\r\n\t\t\t// 保存编辑ID到本地存储\r\n\t\t\tif (options.id) {\r\n\t\t\t\tuni.setStorageSync('edit_address_id', options.id);\r\n\t\t\t\tconsole.log('[addressEdit] 保存编辑ID:', options.id);\r\n\t\t\t} else {\r\n\t\t\t\tuni.removeStorageSync('edit_address_id');\r\n\t\t\t\tconsole.log('[addressEdit] 清除编辑ID - 新增模式');\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查登录状态\r\n\t\t\tif (!this.userStore.checkLoginAndRedirect()) {\r\n\t\t\t\tconsole.log('用户未登录，跳转到登录页面');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 已登录，加载编辑数据\r\n\t\t\tthis.loadEditData();\r\n\t\t},\r\n\t\t// 页面显示时调用，从登录页面返回时重新加载数据\r\n\t\tonShow() {\r\n\t\t\t// 避免在首次加载时重复执行，只在从其他页面返回时执行\r\n\t\t\tif (this.hasLoaded && this.userStore.isLoggedIn && this.addressForm.name === '') {\r\n\t\t\t\tthis.loadEditData();\r\n\t\t\t\tconsole.log('地址编辑页面显示，重新加载数据');\r\n\t\t\t}\r\n\t\t\tthis.hasLoaded = true;\r\n\t\t},\r\n\t\t// 页面卸载时清理临时数据\r\n\t\tonUnload() {\r\n\t\t\tuni.removeStorageSync('edit_address_id');\r\n\t\t\tthis.hasLoaded = false; // 重置加载标志\r\n\t\t\tconsole.log('[addressEdit] 页面卸载，清理临时数据');\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n.page-container {\r\n\tbackground-color: #f8f8f8;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 50rpx;\r\n}\r\n\r\n.card-container {\r\n\tbackground-color: #fff;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 0 30rpx;\r\n}\r\n\r\n.form-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 30rpx 0;\r\n}\r\n\r\n.border-bottom {\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.label {\r\n\twidth: 140rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n}\r\n\r\ninput, textarea {\r\n\tflex: 1;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.placeholder {\r\n\tcolor: #bbb;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n/* 手机号输入区域 */\r\n.phone-input {\r\n\tdisplay: flex;\r\n\tflex: 1;\r\n\talign-items: center;\r\n}\r\n\r\n.country-code {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-right: 20rpx;\r\n\tpadding-right: 20rpx;\r\n\tborder-right: 1px solid #eee;\r\n}\r\n\r\n.arrow {\r\n\tfont-size: 22rpx;\r\n\tmargin-left: 8rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 地址选择区域 */\r\n.address-card {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.address-tabs {\r\n\tdisplay: flex;\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.tab-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #8a2be2;\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.tab-item.active {\r\n\tcolor: #8a2be2;\r\n\tfont-weight: bold;\r\n\tfont-size: 30rpx;\r\n}\r\n\r\n.tab-item.active::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\tbottom: -20rpx;\r\n\twidth: 80rpx;\r\n\theight: 4rpx;\r\n\tbackground-color: #8a2be2;\r\n}\r\n\r\n.address-input {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.address-input input {\r\n\tflex: 1;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.location-icon {\r\n\tfont-size: 40rpx;\r\n\tpadding: 10rpx;\r\n\tmargin-left: 10rpx;\r\n\tcolor: #8a2be2;\r\n}\r\n\r\n/* 地址粘贴板 */\r\n.paste-wrapper {\r\n\tbackground-color: #fff;\r\n\tmargin-top: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.paste-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 20rpx 0;\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.arrow-down {\r\n\tmargin-left: 10rpx;\r\n\tfont-size: 24rpx;\r\n}\r\n\r\n.paste-content {\r\n\tpadding: 30rpx;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.paste-box {\r\n\tposition: relative;\r\n\tbackground-color: #f8f8f8;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tmin-height: 240rpx;\r\n}\r\n\r\n.paste-textarea {\r\n\twidth: 100%;\r\n\tmin-height: 180rpx;\r\n\tfont-size: 28rpx;\r\n\tbox-sizing: border-box;\r\n\tmargin-bottom: 60rpx;\r\n\tline-height: 1.5;\r\n\tcolor: #333;\r\n\tbackground-color: transparent;\r\n}\r\n\r\n.paste-actions {\r\n\tposition: absolute;\r\n\tright: 30rpx;\r\n\tbottom: 30rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.clear-btn {\r\n\tfont-size: 28rpx;\r\n\tcolor: #888;\r\n\tmargin-right: 30rpx;\r\n\tpadding: 10rpx;\r\n}\r\n\r\n.submit-btn {\r\n\tbackground-color: #8a2be2;\r\n\tcolor: #fff;\r\n\tfont-size: 28rpx;\r\n\theight: 70rpx;\r\n\tline-height: 70rpx;\r\n\tborder-radius: 35rpx;\r\n\tpadding: 0 50rpx;\r\n\tmargin: 0;\r\n}\r\n\r\n/* 默认地址 */\r\n.default-card {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.default-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.default-left {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.default-title {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.default-tip {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-top: 6rpx;\r\n}\r\n\r\n/* 确认按钮 */\r\n.confirm-btn-container {\r\n\tpadding: 40rpx 30rpx;\r\n}\r\n\r\n.confirm-btn {\r\n\twidth: 100%;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\tbackground-color: #8a2be2;\r\n\tcolor: #fff;\r\n\tfont-size: 32rpx;\r\n\tborder-radius: 45rpx;\r\n\tfont-weight: normal;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/subpkg-profile/pages/addressEdit/addressEdit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "reactive", "ref", "uni", "watch", "uniCloud", "nextTick"], "mappings": ";;;AAqHC,MAAK,YAAU;AAAA,EACd,QAAQ;AACP,UAAM,YAAYA,WAAAA;AAGlB,UAAM,cAAcC,cAAAA,SAAS;AAAA,MAC5B,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,IACZ,CAAC;AAGD,UAAM,aAAaC,kBAAI,EAAE;AAGzB,UAAM,oBAAoBA,kBAAI,KAAK;AACnC,UAAM,eAAeA,kBAAI,EAAE;AAGTA,kBAAG,IAAC,EAAE;AAGxB,UAAM,YAAYA,kBAAI,KAAK;AAG3B,UAAM,wBAAwB,MAAM;AACnCC,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA;AAIF,UAAM,mBAAmB,MAAM;AAC9B,wBAAkB,QAAQ,CAAC,kBAAkB;AAAA;AAI9C,UAAM,eAAe,MAAM;AAC1B,UAAI,CAAC,aAAa,OAAO;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,YAAM,OAAO,aAAa;AAG1B,UAAI;AAEH,cAAM,aAAa;AACnB,cAAM,aAAa,KAAK,MAAM,UAAU;AACxC,YAAI,YAAY;AACf,sBAAY,QAAQ,WAAW,CAAC;AAAA,QACjC;AAGA,YAAI,YAAY;AAChB,YAAI,KAAK,SAAS,KAAK,GAAG;AACzB,gBAAM,YAAY;AAClB,sBAAY,KAAK,MAAM,SAAS;AAChC,cAAI,WAAW;AACd,wBAAY,OAAO,UAAU,CAAC;AAAA,UAC/B;AAAA,eACM;AAEN,gBAAM,YAAY,KAAK,MAAM,WAAW,EAAE,CAAC;AAC3C,gBAAM,YAAY;AAClB,sBAAY,UAAU,MAAM,SAAS;AACrC,cAAI,WAAW;AACd,wBAAY,OAAO,UAAU,CAAC;AAAA,UAC/B;AAAA,QACD;AAIA,cAAM,gBAAgB;AACtB,cAAM,gBAAgB,KAAK,MAAM,aAAa;AAE9C,YAAI,eAAe;AAClB,sBAAY,WAAW,cAAc,CAAC;AAGtC,gBAAM,YAAY,IAAI,OAAO,cAAc,CAAC,IAAI,sBAAsB;AACtE,gBAAM,YAAY,KAAK,MAAM,SAAS,KAAK,KAAK,MAAM,WAAW;AACjE,cAAI,WAAW;AACd,wBAAY,OAAO,UAAU,CAAC,KAAK,UAAU,CAAC;AAG9C,kBAAM,gBAAgB,IAAI,OAAO,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,mBAAmB;AACjG,kBAAM,gBAAgB,KAAK,MAAM,aAAa,KAAK,KAAK,MAAM,mBAAmB;AACjF,gBAAI,eAAe;AAClB,0BAAY,WAAW,cAAc,CAAC,KAAK,cAAc,CAAC;AAG1D,oBAAM,eAAe,KAAK,QAAQ,YAAY,QAAQ,IAAI,YAAY,SAAS;AAC/E,kBAAI,eAAe,KAAK,eAAe,KAAK,QAAQ;AACnD,sBAAM,gBAAgB,KAAK,UAAU,YAAY,EAAE,KAAI;AACvD,oBAAI,eAAe;AAClB,8BAAY,UAAU,cAAc,QAAQ,YAAY,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,gBAC5E;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAGA;AAGAA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGD,0BAAkB,QAAQ;AAAA,MAE3B,SAAS,GAAG;AACXA,sBAAc,MAAA,MAAA,SAAA,2DAAA,WAAW,CAAC;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA;AAIDC,wBAAM,YAAY,CAAC,WAAW;AAC7B,UAAI,QAAQ;AACX,YAAI;AAEH,cAAI,OAAO,UAAU,GAAG;AACvB,kBAAM,cAAc,OAAO,QAAQ,GAAG;AACtC,kBAAM,UAAU,OAAO,QAAQ,GAAG;AAElC,gBAAI,cAAc,KAAK,UAAU,aAAa;AAC7C,0BAAY,WAAW,OAAO,UAAU,GAAG,cAAc,CAAC;AAC1D,0BAAY,OAAO,OAAO,UAAU,cAAc,GAAG,UAAU,CAAC;AAChE,0BAAY,WAAW,OAAO,UAAU,UAAU,CAAC;AAAA,YACpD;AAAA,UACD;AAAA,QACD,SAAS,GAAG;AACXD,wBAAc,MAAA,MAAA,SAAA,2DAAA,WAAW,CAAC;AAAA,QAC3B;AAAA,aACM;AAEN,oBAAY,WAAW;AACvB,oBAAY,OAAO;AACnB,oBAAY,WAAW;AAAA,MACxB;AAAA,IACD,CAAC;AAGD,UAAM,mBAAmB,MAAM;AAC9B,UAAI,YAAY,YAAY,YAAY,QAAQ,YAAY,UAAU;AACrE,mBAAW,QAAQ,GAAG,YAAY,YAAY,EAAE,GAAG,YAAY,QAAQ,EAAE,GAAG,YAAY,YAAY,EAAE;AAAA,aAChG;AACN,mBAAW,QAAQ;AAAA,MACpB;AAAA;AAID,UAAM,eAAe,YAAY;AAEhC,UAAI,UAAU,OAAO;AACpBA,sBAAAA,MAAA,MAAA,OAAA,2DAAY,8BAA8B;AAC1C;AAAA,MACD;AAGA,YAAM,SAASA,cAAAA,MAAI,eAAe,iBAAiB;AACnDA,oBAAY,MAAA,MAAA,OAAA,2DAAA,wCAAwC,MAAM;AAE1D,UAAI,QAAQ;AACXA,sBAAY,MAAA,MAAA,OAAA,2DAAA,2BAA2B,MAAM;AAE7C,YAAI;AACHA,wBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAGnC,gBAAM,SAAS,MAAME,iBAAS,aAAa,gBAAgB,EAAE,iBAAiB,MAAM;AAEpFF,wBAAG,MAAC,YAAW;AAEf,cAAI,OAAO,SAAS,GAAG;AACtB,kBAAM,UAAU,OAAO;AACvBA,wGAAY,2BAA2B,OAAO;AAG9CG,0BAAAA,WAAS,MAAM;AACd,qBAAO,OAAO,aAAa;AAAA,gBAC1B,MAAM,QAAQ,QAAQ;AAAA,gBACtB,OAAO,QAAQ,UAAU;AAAA,gBACzB,UAAU,QAAQ,YAAY;AAAA,gBAC9B,MAAM,QAAQ,QAAQ;AAAA,gBACtB,UAAU,QAAQ,YAAY;AAAA,gBAC9B,SAAS,QAAQ,WAAW;AAAA,gBAC5B,WAAW,QAAQ,cAAc;AAAA,cAClC,CAAC;AAGD;AACA,wBAAU,QAAQ;AAClBH,kCAAA,MAAA,OAAA,2DAAY,0BAA0B,KAAK,UAAU,WAAW,CAAC;AAAA,YAClE,CAAC;AAAA,iBACK;AACNA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,OAAO,WAAW;AAAA,cACzB,MAAM;AAAA,YACP,CAAC;AACDA,0BAAA,MAAA,MAAA,SAAA,2DAAc,2BAA2B,OAAO,OAAO;AAAA,UACxD;AAAA,QACC,SAAO,OAAO;AACfA,wBAAG,MAAC,YAAW;AACfA,wBAAA,MAAA,MAAA,SAAA,2DAAc,2BAA2B,KAAK;AAC9CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,aACM;AACNA,sBAAAA,MAAA,MAAA,OAAA,2DAAY,oBAAoB;AAChC,kBAAU,QAAQ;AAAA,MACnB;AAAA;AAMD,UAAM,qBAAqB,MAAM;AAChCA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACf,MAAM;AAAA;AAAA,QACN,SAAS,CAAC,QAAQ;AACjBA,wBAAY,MAAA,MAAA,OAAA,2DAAA,UAAU,GAAG;AAGzBA,wBAAAA,MAAI,QAAQ;AAAA,YACX,KAAK,oDAAoD,IAAI,QAAQ,IAAI,IAAI,SAAS;AAAA;AAAA,YACtF,SAAS,CAAC,aAAa;AACtBA,4BAAA,MAAA,MAAA,OAAA,2DAAY,WAAW,QAAQ;AAE/B,kBAAI,SAAS,eAAe,OAAO,SAAS,KAAK,WAAW,GAAG;AAC9D,sBAAM,SAAS,SAAS,KAAK;AAG7B,4BAAY,WAAW,OAAO,kBAAkB;AAChD,4BAAY,OAAO,OAAO,kBAAkB;AAC5C,4BAAY,WAAW,OAAO,kBAAkB;AAGhD;AAEAA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACP,CAAC;AAAA,qBACK;AAENA,8BAAAA,MAAI,eAAe;AAAA,kBAClB,SAAS,CAAC,cAAc;AACvBA,kCAAA,MAAA,MAAA,OAAA,2DAAY,UAAU,SAAS;AAG/B,0BAAM,UAAU,UAAU,WAAW;AACrC,0BAAM,OAAO,UAAU,QAAQ;AAC/B,0BAAM,cAAc,UAAU;AAG9B,wBAAI,WAAW,IAAI,OAAO,IAAI,WAAW;AAGzC,0BAAM,gBAAgB,YAAY,MAAM,4GAA4G;AACpJ,wBAAI,eAAe;AAClB,iCAAW,cAAc,CAAC;AAG1B,4BAAM,YAAY,YAAY,MAAM,WAAW;AAC/C,0BAAI,aAAa,UAAU,SAAS,GAAG;AACtC,+BAAO,UAAU,CAAC;AAGlB,8BAAM,gBAAgB,YAAY,MAAM,cAAc;AACtD,4BAAI,iBAAiB,cAAc,SAAS,GAAG;AAC9C,qCAAW,cAAc,CAAC;AAAA,wBAC3B;AAAA,sBACD;AAAA,oBACD;AAGA,gCAAY,WAAW;AACvB,gCAAY,OAAO;AACnB,gCAAY,WAAW;AAGvB,wBAAI,YAAY,QAAQ,UAAU;AACjC;2BACM;AACN,iCAAW,QAAQ;AAAA,oBACpB;AAEAA,kCAAAA,MAAI,UAAU;AAAA,sBACb,OAAO;AAAA,sBACP,MAAM;AAAA,oBACP,CAAC;AAAA,kBACD;AAAA,kBACD,MAAM,MAAM;AACXA,kCAAAA,MAAI,UAAU;AAAA,sBACb,OAAO;AAAA,sBACP,MAAM;AAAA,oBACP,CAAC;AAAA,kBACF;AAAA,gBACD,CAAC;AAAA,cACF;AAAA,YACA;AAAA,YACD,MAAM,MAAM;AAEXA,4BAAAA,MAAI,eAAe;AAAA,gBAClB,SAAS,CAAC,cAAc;AACvBA,gCAAA,MAAA,MAAA,OAAA,2DAAY,UAAU,SAAS;AAG/B,wBAAM,UAAU,UAAU,WAAW;AACxB,4BAAU,QAAQ;AAG/B,6BAAW,QAAQ;AAEnBA,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAO;AAAA,oBACP,MAAM;AAAA,kBACP,CAAC;AAAA,gBACD;AAAA,gBACD,MAAM,MAAM;AACXA,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAO;AAAA,oBACP,MAAM;AAAA,kBACP,CAAC;AAAA,gBACF;AAAA,cACD,CAAC;AAAA,YACD;AAAA,YACD,UAAU,MAAM;AACfA,4BAAG,MAAC,YAAW;AAAA,YAChB;AAAA,UACD,CAAC;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAY,MAAA,MAAA,OAAA,2DAAA,UAAU,GAAG;AAGzBA,wBAAG,MAAC,YAAW;AAEfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAED,qBAAW,MAAM;AAChBA,0BAAAA,MAAI,eAAe;AAAA,cAClB,SAAS,CAAC,QAAQ;AACjBA,8BAAY,MAAA,MAAA,OAAA,2DAAA,UAAU,GAAG;AAGzB,sBAAM,UAAU,IAAI,WAAW;AAClB,oBAAI,QAAQ;AAGzB,2BAAW,QAAQ;AAEnBA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACP,CAAC;AAAA,cACF;AAAA,YACD,CAAC;AAAA,UACD,GAAE,GAAI;AAAA,QACR;AAAA,MACD,CAAC;AAAA;AAIF,UAAM,iBAAiB,MAAM;AAE5B,YAAM,YAAY;AAAA,QACjB;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAC5B;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAQ;AAAA,QAC7B;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAC5B;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAC5B;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAC5B;AAAA,QAAO;AAAA,QAAO;AAAA,QAAU;AAAA,QACxB;AAAA,QAAS;AAAA,QAAW;AAAA,QACpB;AAAA,QAAW;AAAA;AAGZA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU;AAAA,QACV,SAAS,SAAS,KAAK;AACtB,sBAAY,WAAW,UAAU,IAAI,QAAQ;AAC7C,sBAAY,OAAO;AACnB,sBAAY,WAAW;AACvB;AAGA,qBAAW,MAAM;AAChB;UACA,GAAE,GAAG;AAAA,QACP;AAAA,MACD,CAAC;AAAA;AAIF,UAAM,aAAa,MAAM;AAExB,UAAI,SAAS,CAAA;AAGb,UAAI,YAAY,aAAa,OAAO;AACnC,iBAAS,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MAC5J,WAAW,YAAY,aAAa,OAAO;AAC1C,iBAAS,CAAC,KAAK;AAAA,MAChB,WAAW,YAAY,aAAa,OAAO;AAC1C,iBAAS,CAAC,KAAK;AAAA,MAChB,WAAW,YAAY,aAAa,OAAO;AAC1C,iBAAS,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MACtF,WAAW,YAAY,aAAa,OAAO;AAC1C,iBAAS,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,aAC9F;AAEN,iBAAS,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MAC5C;AAEA,UAAI,CAAC,YAAY,UAAU;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEAA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU;AAAA,QACV,SAAS,SAAS,KAAK;AACtB,sBAAY,OAAO,OAAO,IAAI,QAAQ;AACtC,sBAAY,WAAW;AACvB;AAGA,qBAAW,MAAM;AAChB;UACA,GAAE,GAAG;AAAA,QACP;AAAA,MACD,CAAC;AAAA;AAIF,UAAM,iBAAiB,MAAM;AAE5B,UAAI,YAAY,CAAA;AAGhB,UAAI,YAAY,aAAa,SAAS,YAAY,SAAS,OAAO;AACjE,oBAAY,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MAC3E,WAES,YAAY,aAAa,SAAS,YAAY,SAAS,OAAO;AACtE,oBAAY,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MACzF,OAEK;AACJ,oBAAY,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MAC/C;AAEA,UAAI,CAAC,YAAY,MAAM;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEAA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU;AAAA,QACV,SAAS,SAAS,KAAK;AACtB,sBAAY,WAAW,UAAU,IAAI,QAAQ;AAC7C;QACD;AAAA,MACD,CAAC;AAAA;AAIF,UAAM,gBAAgB,CAAC,MAAM;AAC5B,kBAAY,YAAY,EAAE,OAAO;AAAA;AAIlC,UAAM,cAAc,YAAY;AAE/B,UAAI,CAAC,UAAU,yBAAyB;AACvCA,sBAAAA,8EAAY,cAAc;AAC1B;AAAA,MACD;AAGA,UAAI,CAAC,YAAY,MAAM;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,YAAY,OAAO;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,UAAI,CAAC,gBAAgB,KAAK,YAAY,KAAK,GAAG;AAC7CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,WAAW,OAAO;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,YAAY,SAAS;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnCA,sBAAA,MAAA,MAAA,OAAA,2DAAY,SAAS,WAAW;AAChCA,oGAAY,SAAS,WAAW,KAAK;AAGrC,YAAI,WAAW,YAAY;AAC3B,YAAI,OAAO,YAAY;AACvB,YAAI,WAAW,YAAY;AAG3B,YAAI,CAAC,YAAY,CAAC,MAAM;AACvB,gBAAM,cAAc,WAAW,SAAS;AAExC,cAAI,aAAa;AAEhB,gBAAI,YAAY,SAAS,GAAG,GAAG;AAC9B,oBAAM,QAAQ,YAAY,MAAM,GAAG;AACnC,yBAAW,MAAM,CAAC,IAAI;AACtB,qBAAO,MAAM,CAAC,KAAK;AAAA,YACpB,WAAW,YAAY,SAAS,GAAG,GAAG;AACrC,oBAAM,QAAQ,YAAY,MAAM,GAAG;AACnC,yBAAW,MAAM,CAAC,IAAI;AACtB,qBAAO,MAAM,CAAC,IAAI;AAAA,mBACZ;AAEN,yBAAW;AACX,qBAAO;AAAA,YACR;AAAA,iBACM;AAEN,uBAAW;AACX,mBAAO;AAAA,UACR;AAAA,QACD;AAGA,cAAM,cAAc;AAAA,UACnB,MAAM,YAAY;AAAA,UAClB,QAAQ,YAAY;AAAA,UACpB,UAAU,YAAY;AAAA,UACtB,MAAM,QAAQ;AAAA,UACd,UAAU,YAAY;AAAA,UACtB,SAAS,YAAY;AAAA,UACrB,YAAY,YAAY;AAAA,UACxB,KAAK;AAAA;AAAA;AAGN,YAAI;AACJ,cAAM,SAASA,cAAAA,MAAI,eAAe,iBAAiB;AAEnD,YAAI,QAAQ;AAEX,mBAAS,MAAME,cAAQ,GAAC,aAAa,gBAAgB,EAAE,cAAc,QAAQ,WAAW;AAAA,eAClF;AAEN,mBAAS,MAAMA,cAAAA,GAAS,aAAa,gBAAgB,EAAE,WAAW,WAAW;AAAA,QAC9E;AAEAF,sBAAG,MAAC,YAAW;AAEf,YAAI,OAAO,SAAS,GAAG;AACtBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAED,qBAAW,MAAM;AAChBA,0BAAG,MAAC,aAAY;AAAA,UAChB,GAAE,GAAI;AAAA,eACD;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,SAAS,GAAG;AACXA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,2DAAA,WAAW,CAAC;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA;AAID,UAAM,oBAAoB,MAAM;AAC/B,mBAAa,QAAQ;AAAA;AAGtB,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACA;AAAA;AAAA,EAED,OAAO,SAAS;AACfA,kBAAY,MAAA,MAAA,OAAA,2DAAA,mCAAmC,OAAO;AAGtD,QAAI,QAAQ,IAAI;AACfA,oBAAAA,MAAI,eAAe,mBAAmB,QAAQ,EAAE;AAChDA,kGAAY,yBAAyB,QAAQ,EAAE;AAAA,WACzC;AACNA,0BAAI,kBAAkB,iBAAiB;AACvCA,oBAAAA,MAAA,MAAA,OAAA,2DAAY,6BAA6B;AAAA,IAC1C;AAGA,QAAI,CAAC,KAAK,UAAU,yBAAyB;AAC5CA,oBAAAA,MAAA,MAAA,OAAA,2DAAY,eAAe;AAC3B;AAAA,IACD;AAGA,SAAK,aAAY;AAAA,EACjB;AAAA;AAAA,EAED,SAAS;AAER,QAAI,KAAK,aAAa,KAAK,UAAU,cAAc,KAAK,YAAY,SAAS,IAAI;AAChF,WAAK,aAAY;AACjBA,oBAAAA,MAAA,MAAA,OAAA,2DAAY,iBAAiB;AAAA,IAC9B;AACA,SAAK,YAAY;AAAA,EACjB;AAAA;AAAA,EAED,WAAW;AACVA,wBAAI,kBAAkB,iBAAiB;AACvC,SAAK,YAAY;AACjBA,kBAAAA,MAAY,MAAA,OAAA,2DAAA,2BAA2B;AAAA,EACxC;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvzBD,GAAG,WAAW,eAAe;"}
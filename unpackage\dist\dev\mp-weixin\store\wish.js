"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const store_user = require("./user.js");
const ERROR_CODES = {
  NETWORK_ERROR: "NETWORK_ERROR",
  AUTH_ERROR: "AUTH_ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  PERMISSION_ERROR: "PERMISSION_ERROR",
  NOT_FOUND_ERROR: "NOT_FOUND_ERROR",
  SERVER_ERROR: "SERVER_ERROR"
};
const ErrorHandler = {
  getUserFriendlyMessage(error) {
    if (typeof error === "string")
      return error;
    return error.message || error.errMsg || "未知错误";
  },
  isNetworkError(error) {
    if (error.errCode === ERROR_CODES.NETWORK_ERROR)
      return true;
    if (error.code === "NETWORK_ERROR")
      return true;
    return false;
  },
  isVersionConflictError(error) {
    return error && error.message && (error.message.includes("数据版本冲突") || error.message.includes("逻辑时钟冲突") || error.message.includes("version conflict") || error.message.includes("conflict"));
  },
  showError(error, title = "操作失败", context = "unknown") {
    const message = this.getUserFriendlyMessage(error);
    common_vendor.index.showToast({
      title: message,
      icon: "none",
      duration: 3e3
    });
    common_vendor.index.__f__("error", "at store/wish.js:47", `${title}:`, error);
  }
};
const useWishStore = common_vendor.defineStore("wish", {
  state: () => ({
    wishList: [],
    currentGroupId: "all",
    isLoading: false,
    lastSyncTime: null,
    isOnline: true,
    listUpdateCounter: 0,
    // 🚀 添加列表更新计数器
    // 同步状态管理
    syncStatus: {
      issyncing: false,
      lastSyncResult: null,
      pendingCount: 0,
      errorCount: 0,
      lastError: null
    },
    // 防重复同步机制
    _syncOperations: /* @__PURE__ */ new Set(),
    _lastSyncTime: 0
  }),
  getters: {
    activeWishes: (state) => {
      return state.wishList.filter((wish) => !wish._deleted && !wish.isCompleted);
    },
    // 🚀 添加当前分组的心愿列表
    currentGroupWishes: (state) => {
      const activeWishes = state.wishList.filter((wish) => !wish._deleted && !wish.isCompleted);
      if (state.currentGroupId === "all") {
        return activeWishes;
      } else if (state.currentGroupId === "completed") {
        return state.wishList.filter((wish) => !wish._deleted && wish.isCompleted);
      } else {
        return activeWishes.filter(
          (wish) => wish.groupIds && wish.groupIds.includes(state.currentGroupId)
        );
      }
    },
    // 🚀 添加其他必要的 getters
    getWishById: (state) => (id) => {
      return state.wishList.find(
        (wish) => (wish._id === id || wish.id === id) && !wish._deleted
      ) || null;
    }
  },
  actions: {
    async initWishList() {
      common_vendor.index.__f__("log", "at store/wish.js:105", "[wishStore] Initializing wish list...");
      this.isLoading = true;
      try {
        const storedWishes = common_vendor.index.getStorageSync("wishList");
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes);
          this.wishList = parsed.map((wish) => ({
            ...wish,
            id: wish.id || wish._id
          }));
        }
        common_vendor.index.__f__("log", "at store/wish.js:120", "[wishStore] 开始从云端同步最新数据...");
        await this.syncFromCloud();
        common_vendor.index.__f__("log", "at store/wish.js:123", "[wishStore] Wish list initialization completed");
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:125", "[wishStore] Failed to initialize wish list:", error);
      } finally {
        this.isLoading = false;
      }
    },
    // 🚀 添加基本的同步方法来测试云函数修改
    async syncFromCloud() {
      const operation = "syncFromCloud";
      if (!this._canStartSync(operation)) {
        return { success: false, reason: "sync_in_progress" };
      }
      common_vendor.index.__f__("log", "at store/wish.js:140", "[wishStore] 开始从云端同步数据...");
      try {
        this._startSyncOperation(operation);
        this._updateSyncStatus({ issyncing: true });
        const userStore = store_user.useUserStore();
        if (!userStore.isLoggedIn) {
          common_vendor.index.__f__("warn", "at store/wish.js:149", "[wishStore] 用户未登录，跳过云端同步");
          return { success: false, reason: "not_logged_in" };
        }
        common_vendor.index.__f__("log", "at store/wish.js:153", "[wishStore] 调用云函数获取心愿列表...");
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.getWishList({
          page: 1,
          pageSize: 1e3,
          includeCompleted: true
        });
        common_vendor.index.__f__("log", "at store/wish.js:161", "[wishStore] 云函数返回结果:", result);
        if (result.errCode === 0) {
          const cloudWishes = result.data || [];
          common_vendor.index.__f__("log", "at store/wish.js:165", "[wishStore] 从云端获取到", cloudWishes.length, "个心愿");
          if (cloudWishes.length === 0) {
            common_vendor.index.__f__("log", "at store/wish.js:168", "[wishStore] 云端没有心愿数据，可能是新用户或数据被清空");
          }
          const mergedData = this._mergeWishData(this.wishList, cloudWishes);
          this.wishList = mergedData.map((wish) => ({
            ...wish,
            id: wish._id || wish.id
            // 确保有 id 字段
          }));
          common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
          common_vendor.index.setStorageSync("wishListLastSyncTime", this.lastSyncTime);
          common_vendor.index.__f__("log", "at store/wish.js:185", "[wishStore] 智能同步完成，合并后共", this.wishList.length, "个心愿");
          this._updateSyncStatus({
            lastSyncResult: "success",
            errorCount: 0,
            lastError: null
          });
          return { success: true, count: cloudWishes.length, localCount: this.wishList.length };
        } else {
          const errorMsg = `云函数错误 (errCode: ${result.errCode}): ${result.errMsg || "未知错误"}`;
          common_vendor.index.__f__("error", "at store/wish.js:197", "[wishStore] 云函数错误:", errorMsg);
          common_vendor.index.__f__("error", "at store/wish.js:198", "[wishStore] 完整错误信息:", result);
          this._updateSyncStatus({
            lastSyncResult: "error",
            errorCount: this.syncStatus.errorCount + 1,
            lastError: errorMsg
          });
          throw new Error(errorMsg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:210", "[wishStore] 同步出错:", error);
        this._updateSyncStatus({
          lastSyncResult: "error",
          errorCount: this.syncStatus.errorCount + 1,
          lastError: error.message
        });
        throw error;
      } finally {
        this._updateSyncStatus({ issyncing: false });
        this._endSyncOperation(operation);
      }
    },
    // 🚀 更新心愿
    async updateWish(updatedWish) {
      common_vendor.index.__f__("log", "at store/wish.js:228", "[wishStore] 更新心愿:", updatedWish);
      try {
        if (!updatedWish || !updatedWish._id && !updatedWish.id) {
          throw new Error("更新数据缺少必要的ID字段");
        }
        const wishId = updatedWish._id || updatedWish.id;
        const updateData = {
          ...updatedWish,
          updateDate: (/* @__PURE__ */ new Date()).toISOString()
        };
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.updateWish(wishId, updateData);
        if (result.errCode === 0) {
          const index = this.wishList.findIndex((w) => w._id === wishId || w.id === wishId);
          if (index !== -1) {
            const mergedWish = {
              ...this.wishList[index],
              ...updateData,
              id: wishId,
              _id: wishId
            };
            this.wishList[index] = mergedWish;
            common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          }
          common_vendor.index.showToast({
            title: "更新成功",
            icon: "success"
          });
          common_vendor.index.__f__("log", "at store/wish.js:268", "[wishStore] 更新成功");
        } else {
          throw new Error(result.errMsg || "更新心愿失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:273", "[wishStore] 更新心愿失败:", error);
        ErrorHandler.showError(error, "更新失败");
        throw error;
      }
    },
    // 🚀 添加删除方法来测试多设备冲突处理
    async deleteWish(id) {
      common_vendor.index.__f__("log", "at store/wish.js:281", "[wishStore] 删除心愿:", id);
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.deleteWish(id);
        if (result.errCode === 0) {
          const index = this.wishList.findIndex((w) => w._id === id || w.id === id);
          if (index !== -1) {
            this.wishList.splice(index, 1);
            common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          }
          common_vendor.index.showToast({
            title: "删除成功",
            icon: "success"
          });
          common_vendor.index.__f__("log", "at store/wish.js:300", "[wishStore] 删除成功");
        } else {
          common_vendor.index.__f__("error", "at store/wish.js:302", "[wishStore] 删除失败:", result.errMsg);
          ErrorHandler.showError({ message: result.errMsg }, "删除失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:306", "[wishStore] 删除出错:", error);
        ErrorHandler.showError(error, "删除失败");
      }
    },
    // 智能合并本地和云端数据
    _mergeWishData(localWishes, cloudWishes) {
      common_vendor.index.__f__("log", "at store/wish.js:313", "[wishStore] 智能合并本地和云端数据...");
      common_vendor.index.__f__("log", "at store/wish.js:314", "[wishStore] 本地数据:", localWishes.length, "个心愿");
      common_vendor.index.__f__("log", "at store/wish.js:315", "[wishStore] 云端数据:", cloudWishes.length, "个心愿");
      const mergedMap = /* @__PURE__ */ new Map();
      cloudWishes.forEach((cloudWish) => {
        mergedMap.set(cloudWish._id, {
          ...cloudWish,
          _source: "cloud"
        });
      });
      localWishes.forEach((localWish) => {
        const id = localWish._id || localWish.id;
        const existingWish = mergedMap.get(id);
        if (!existingWish) {
          mergedMap.set(id, {
            ...localWish,
            _id: id,
            _source: "local"
          });
        } else {
          const localTime = new Date(localWish.updatedAt || localWish.createdAt || 0).getTime();
          const cloudTime = new Date(existingWish.updatedAt || existingWish.createdAt || 0).getTime();
          if (localTime > cloudTime) {
            mergedMap.set(id, {
              ...localWish,
              _id: id,
              _source: "local_newer"
            });
          }
        }
      });
      const mergedArray = Array.from(mergedMap.values());
      common_vendor.index.__f__("log", "at store/wish.js:357", "[wishStore] 合并完成，最终数据:", mergedArray.length, "个心愿");
      return mergedArray;
    },
    // 检查同步操作是否可以执行
    _canStartSync(operation) {
      const now = Date.now();
      if (this._syncOperations.has(operation)) {
        common_vendor.index.__f__("log", "at store/wish.js:368", `[wishStore] 同步操作 ${operation} 已在进行中`);
        return false;
      }
      if (now - this._lastSyncTime < 1e3) {
        common_vendor.index.__f__("log", "at store/wish.js:374", `[wishStore] 同步操作过于频繁，跳过`);
        return false;
      }
      return true;
    },
    // 开始同步操作
    _startSyncOperation(operation) {
      this._syncOperations.add(operation);
      this._lastSyncTime = Date.now();
      common_vendor.index.__f__("log", "at store/wish.js:385", `[wishStore] 开始同步操作: ${operation}`);
    },
    // 结束同步操作
    _endSyncOperation(operation) {
      this._syncOperations.delete(operation);
      common_vendor.index.__f__("log", "at store/wish.js:391", `[wishStore] 结束同步操作: ${operation}`);
    },
    // 更新同步状态
    _updateSyncStatus(updates) {
      Object.assign(this.syncStatus, updates);
    },
    // 🚀 添加其他必要的方法
    setCurrentGroup(groupId) {
      this.currentGroupId = groupId;
    },
    // 强制同步数据（用于下拉刷新）
    async forceSyncData() {
      common_vendor.index.__f__("log", "at store/wish.js:406", "[wishStore] 强制同步数据...");
      await this.syncFromCloud();
    },
    // 手动同步（兼容原有接口）
    async manualSync(silent = false) {
      if (!silent) {
        common_vendor.index.showLoading({ title: "同步中..." });
      }
      try {
        await this.syncFromCloud();
        if (!silent) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "同步完成",
            icon: "success"
          });
        }
      } catch (error) {
        if (!silent) {
          common_vendor.index.hideLoading();
        }
        throw error;
      }
    },
    // 智能同步方法（兼容 userStore 调用）
    async smartSync() {
      common_vendor.index.__f__("log", "at store/wish.js:436", "[wishStore] Starting smart sync...");
      try {
        const networkInfo = await common_vendor.index.getNetworkType();
        if (networkInfo.networkType === "none") {
          common_vendor.index.__f__("warn", "at store/wish.js:442", "[wishStore] Network unavailable, skipping smart sync");
          return { hasUpdates: false, updatedCount: 0, reason: "offline" };
        }
        const result = await this.syncFromCloud();
        if (result && result.success) {
          return {
            hasUpdates: true,
            updatedCount: result.count || 0,
            reason: "success"
          };
        } else {
          return {
            hasUpdates: false,
            updatedCount: 0,
            reason: "no_updates"
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:463", "[wishStore] Smart sync failed:", error);
        return {
          hasUpdates: false,
          updatedCount: 0,
          reason: "error",
          error: error.message
        };
      }
    },
    // 完成心愿
    async completeWish(id) {
      common_vendor.index.__f__("log", "at store/wish.js:475", "[wishStore] 完成心愿:", id);
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.completeWish(id);
        if (result.errCode === 0) {
          const wish = this.wishList.find((w) => w._id === id || w.id === id);
          if (wish) {
            wish.isCompleted = true;
            wish.completeDate = (/* @__PURE__ */ new Date()).toISOString();
            common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          }
          common_vendor.index.showToast({
            title: "心愿已完成",
            icon: "success"
          });
        } else {
          ErrorHandler.showError({ message: result.errMsg }, "完成失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:498", "[wishStore] 完成心愿出错:", error);
        ErrorHandler.showError(error, "完成失败");
      }
    },
    // 恢复心愿
    async restoreWish(id) {
      common_vendor.index.__f__("log", "at store/wish.js:505", "[wishStore] 恢复心愿:", id);
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.restoreWish(id);
        if (result.errCode === 0) {
          const wish = this.wishList.find((w) => w._id === id || w.id === id);
          if (wish) {
            wish.isCompleted = false;
            wish.completeDate = null;
            common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          }
          common_vendor.index.showToast({
            title: "心愿已恢复",
            icon: "success"
          });
        } else {
          ErrorHandler.showError({ message: result.errMsg }, "恢复失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:528", "[wishStore] 恢复心愿出错:", error);
        ErrorHandler.showError(error, "恢复失败");
      }
    },
    // 🚀 清理本地数据（登录后重新初始化时使用）
    clearLocalData() {
      common_vendor.index.__f__("log", "at store/wish.js:535", "[wishStore] 清理本地数据...");
      this.wishList = [];
      this.currentGroupId = "all";
      this.lastSyncTime = null;
      this.isLoading = false;
      common_vendor.index.removeStorageSync("wishList");
      common_vendor.index.removeStorageSync("wishLastSyncTime");
      common_vendor.index.__f__("log", "at store/wish.js:547", "[wishStore] 本地数据清理完成");
    },
    // 🚀 强制初始化（登录后使用）
    async forceInit() {
      common_vendor.index.__f__("log", "at store/wish.js:552", "[wishStore] 强制初始化...");
      this.clearLocalData();
      await this.syncFromCloud();
      common_vendor.index.__f__("log", "at store/wish.js:560", "[wishStore] 强制初始化完成");
    },
    // 🚀 用户登出时清理数据
    clearUserData() {
      common_vendor.index.__f__("log", "at store/wish.js:565", "[wishStore] 清理用户数据...");
      this.wishList = [];
      this.currentGroupId = "all";
      this.lastSyncTime = null;
      this.isLoading = false;
      common_vendor.index.removeStorageSync("wishList");
      common_vendor.index.removeStorageSync("wishLastSyncTime");
      common_vendor.index.__f__("log", "at store/wish.js:580", "[wishStore] 用户数据清理完成");
    },
    // 🚀 刷新心愿列表（从本地存储重新加载）
    refreshWishList() {
      common_vendor.index.__f__("log", "at store/wish.js:585", "[wishStore] 刷新心愿列表...");
      try {
        const storedWishes = common_vendor.index.getStorageSync("wishList");
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes);
          this.wishList = parsed.map((wish) => ({
            ...wish,
            id: wish.id || wish._id
          }));
          common_vendor.index.__f__("log", "at store/wish.js:595", "[wishStore] 心愿列表已刷新，共", this.wishList.length, "个心愿");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:598", "[wishStore] 刷新心愿列表失败:", error);
      }
    },
    // 🚀 更新心愿排序
    updateWishOrder(orderedIds) {
      common_vendor.index.__f__("log", "at store/wish.js:604", "[wishStore] 更新心愿排序:", orderedIds);
      const reorderedWishes = [];
      orderedIds.forEach((id) => {
        const wish = this.wishList.find((w) => w._id === id || w.id === id);
        if (wish) {
          reorderedWishes.push(wish);
        }
      });
      this.wishList.forEach((wish) => {
        if (!orderedIds.includes(wish._id) && !orderedIds.includes(wish.id)) {
          reorderedWishes.push(wish);
        }
      });
      this.wishList = reorderedWishes;
      common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
      common_vendor.index.__f__("log", "at store/wish.js:625", "[wishStore] 心愿排序已更新");
    },
    // 🚀 设置当前心愿列表
    setCurrentList(newWishList) {
      common_vendor.index.__f__("log", "at store/wish.js:630", "[wishStore] 设置当前心愿列表:", newWishList.length, "个心愿");
      this.wishList = newWishList.map((wish) => ({
        ...wish,
        id: wish.id || wish._id
      }));
      common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
    },
    // 🚀 添加心愿
    async addWish(wishData) {
      common_vendor.index.__f__("log", "at store/wish.js:642", "[wishStore] 添加心愿:", wishData);
      try {
        const enhancedWishData2 = {
          ...wishData,
          createDate: (/* @__PURE__ */ new Date()).toISOString(),
          updateDate: (/* @__PURE__ */ new Date()).toISOString(),
          isCompleted: false,
          userId: "",
          // 会在云函数中设置
          permission: wishData.permission || "private",
          groupIds: wishData.groupIds || ["all"],
          order: this.wishList.length + 1
        };
        if (!this.isOnline) {
          common_vendor.index.__f__("log", "at store/wish.js:659", "当前离线，心愿将保存到本地");
          return this._addWishOffline(enhancedWishData2);
        }
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.createWish(enhancedWishData2);
        if (result.errCode === 0) {
          const wishWithId = {
            ...result.data,
            id: result.data._id
            // 确保有 id 字段供前端组件使用
          };
          this.wishList.push(wishWithId);
          common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          common_vendor.index.showToast({
            title: "心愿创建成功",
            icon: "success"
          });
          return wishWithId;
        } else {
          throw new Error(result.errMsg || "创建心愿失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:686", "添加心愿失败:", error);
        if (ErrorHandler.isNetworkError(error)) {
          this.isOnline = false;
          return this._addWishOffline(enhancedWishData);
        }
        if (ErrorHandler.isVersionConflictError(error)) {
          return this._addWishOffline(enhancedWishData);
        }
        ErrorHandler.showError(error, "创建心愿失败");
        throw error;
      }
    },
    // 🚀 离线模式添加心愿
    _addWishOffline(wishData) {
      common_vendor.index.__f__("log", "at store/wish.js:707", "[wishStore] 离线模式添加心愿:", wishData.title);
      const tempId = "temp_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
      const wish = {
        _id: tempId,
        id: tempId,
        ...wishData,
        _needSync: true,
        // 标记需要同步到云端
        _isLocal: true
        // 标记为本地创建
      };
      this.wishList.push(wish);
      common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
      common_vendor.index.showToast({
        title: this.isOnline ? "已保存到本地，稍后将同步到云端" : "当前离线，已保存到本地",
        icon: "none",
        duration: 2500
      });
      return wish;
    }
  }
});
exports.useWishStore = useWishStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/wish.js.map

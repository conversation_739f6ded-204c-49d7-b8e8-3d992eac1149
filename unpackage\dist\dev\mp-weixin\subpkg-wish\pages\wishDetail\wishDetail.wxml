<view class="wish-detail-container"><view class="wish-detail card"><view class="wish-header"><view class="wish-title-wrap"><view class="wish-title">{{a}}</view></view><view class="wish-status"><view wx:if="{{b}}" class="tag private-tag"><uni-icons wx:if="{{c}}" u-i="1b547c0e-0" bind:__l="__l" u-p="{{c}}"></uni-icons><text>私密</text></view><view wx:elif="{{d}}" class="tag friends-tag"><uni-icons wx:if="{{e}}" u-i="1b547c0e-1" bind:__l="__l" u-p="{{e}}"></uni-icons><text>朋友可见</text></view><view wx:elif="{{f}}" class="tag public-tag"><uni-icons wx:if="{{g}}" u-i="1b547c0e-2" bind:__l="__l" u-p="{{g}}"></uni-icons><text>公开</text></view><view wx:if="{{h}}" class="tag completed-tag"><uni-icons wx:if="{{i}}" u-i="1b547c0e-3" bind:__l="__l" u-p="{{i}}"></uni-icons><text>已完成</text></view></view></view><view class="wish-content"><view wx:if="{{j}}" class="wish-desc">{{k}}</view><image wx:if="{{l}}" class="wish-image" src="{{m}}" mode="widthFix" bindtap="{{n}}" binderror="{{o}}"></image><swiper wx:elif="{{p}}" class="image-swiper" indicator-dots="{{true}}" autoplay="{{false}}" duration="{{500}}" indicator-active-color="#8a2be2"><swiper-item wx:for="{{q}}" wx:for-item="img" wx:key="d"><image class="swiper-image" src="{{img.a}}" mode="widthFix" bindtap="{{img.b}}" binderror="{{img.c}}"></image></swiper-item></swiper><view class="wish-time-info"><view class="time-item"><text class="time-label">创建时间：</text><text class="time-value">{{r}}</text></view><view wx:if="{{s}}" class="time-item"><text class="time-label">开始时间：</text><text class="time-value">{{t}}</text></view><view wx:if="{{v}}" class="time-item"><text class="time-label">完成时间：</text><text class="time-value">{{w}}</text></view></view></view><view class="wish-actions"><view class="action-btn" bindtap="{{y}}"><uni-icons wx:if="{{x}}" u-i="1b547c0e-4" bind:__l="__l" u-p="{{x}}"></uni-icons><text>编辑</text></view><button open-type="share" class="action-btn share-btn" bindtap="{{A}}"><uni-icons wx:if="{{z}}" u-i="1b547c0e-5" bind:__l="__l" u-p="{{z}}"></uni-icons><text>分享</text></button><view wx:if="{{B}}" class="action-btn" bindtap="{{D}}"><uni-icons wx:if="{{C}}" u-i="1b547c0e-6" bind:__l="__l" u-p="{{C}}"></uni-icons><text>完成</text></view><view class="action-btn delete" bindtap="{{F}}"><uni-icons wx:if="{{E}}" u-i="1b547c0e-7" bind:__l="__l" u-p="{{E}}"></uni-icons><text>删除</text></view></view></view><view class="wish-groups card"><view class="section-title"><text>所属分组</text></view><view class="group-tags"><view wx:for="{{G}}" wx:for-item="groupId" wx:key="b" class="group-tag" bindtap="{{groupId.c}}">{{groupId.a}}</view></view></view><view class="wish-comments card" bindtap="{{T}}"><view class="section-title"><text>评论区</text><text class="comment-count">{{H}}条评论</text></view><view wx:if="{{I}}" class="empty-comment"><text>暂无评论，快来添加第一条评论吧</text></view><view wx:else class="comment-list"><view wx:for="{{J}}" wx:for-item="comment" wx:key="g" class="comment-item" bindlongpress="{{comment.h}}" bindtouchstart="{{comment.i}}" bindtouchend="{{comment.j}}" catchtap="{{comment.k}}"><image class="comment-avatar" src="{{comment.a}}" mode="aspectFill"></image><view class="comment-content"><view class="comment-user"><text class="comment-nickname">{{comment.b}}</text><text class="comment-time">{{comment.c}}</text></view><view class="comment-text">{{comment.d}}</view><image wx:if="{{comment.e}}" class="comment-image" src="{{comment.f}}" mode="widthFix"></image></view></view></view><view wx:if="{{K}}" class="floating-delete-btn" style="{{M}}" catchtap="{{N}}"><uni-icons wx:if="{{L}}" u-i="1b547c0e-8" bind:__l="__l" u-p="{{L}}"></uni-icons><text>删除</text></view><view class="comment-form"><input class="comment-input" placeholder="添加评论..." confirm-type="send" bindconfirm="{{O}}" value="{{P}}" bindinput="{{Q}}"/><view class="comment-btn" bindtap="{{S}}"><uni-icons wx:if="{{R}}" u-i="1b547c0e-9" bind:__l="__l" u-p="{{R}}"></uni-icons></view></view></view><uni-popup wx:if="{{Y}}" class="r" u-s="{{['d']}}" u-r="deletePopup" u-i="1b547c0e-10" bind:__l="__l" u-p="{{Y}}"><uni-popup-dialog wx:if="{{W}}" bindconfirm="{{U}}" bindclose="{{V}}" u-i="1b547c0e-11,1b547c0e-10" bind:__l="__l" u-p="{{W}}"></uni-popup-dialog></uni-popup><uni-popup wx:if="{{ad}}" class="r" u-s="{{['d']}}" u-r="deleteCommentPopup" u-i="1b547c0e-12" bind:__l="__l" u-p="{{ad}}"><uni-popup-dialog wx:if="{{ab}}" bindconfirm="{{Z}}" bindclose="{{aa}}" u-i="1b547c0e-13,1b547c0e-12" bind:__l="__l" u-p="{{ab}}"></uni-popup-dialog></uni-popup></view>
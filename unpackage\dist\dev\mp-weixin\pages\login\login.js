"use strict";
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  setup() {
    const userStore = store_user.useUserStore();
    return { userStore };
  },
  data() {
    return {
      errors: {},
      showProfileCompletionForm: false,
      // 控制是否显示补充信息表单
      tempAvatarFile: null,
      // Stores { path: string, name?: string, newUpload?: boolean }
      tempNickname: "",
      redirectUrl: null,
      // To store the redirect path from query params
      formData: {
        username: "",
        password: ""
      },
      rules: {
        username: {
          rules: [{
            required: true,
            errorMessage: "请输入账号"
          }]
        },
        password: {
          rules: [{
            required: true,
            errorMessage: "请输入密码"
          }]
        }
      },
      options: {}
      // 用于存储 onLoad 传入的 options
    };
  },
  created() {
    this.userStore = store_user.useUserStore();
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/login/login.vue:100", "[Login Page] onLoad called with options:", options);
    this.options = options;
    common_vendor.index.__f__("log", "at pages/login/login.vue:104", "[Login Page] userStore state:", {
      isLogin: this.userStore.isLogin,
      token: this.userStore.token ? "EXISTS" : "NULL",
      userInfo: this.userStore.userInfo
    });
    if (this.userStore.checkLoginStatus()) {
      common_vendor.index.__f__("log", "at pages/login/login.vue:112", "[Login Page] User already logged in. Redirecting...");
      const redirectUrl = decodeURIComponent(this.options.redirect || "") || "/pages/index/index";
      this.handleRedirect(redirectUrl);
    }
    if (options && options.redirect) {
      this.redirectUrl = decodeURIComponent(options.redirect);
      common_vendor.index.__f__("log", "at pages/login/login.vue:118", "[Login Page] Redirect URL from query:", this.redirectUrl);
    }
  },
  onShow() {
    common_vendor.index.__f__("log", "at pages/login/login.vue:123", "[Login Page] onShow called");
    common_vendor.index.__f__("log", "at pages/login/login.vue:124", "[Login Page] Current showProfileCompletionForm:", this.showProfileCompletionForm);
  },
  methods: {
    async thirdPartyLogin(provider) {
      let loadingShown = false;
      try {
        common_vendor.index.showLoading({ title: "登录中..." });
        loadingShown = true;
        const uniIdCo = common_vendor.nr.importObject("uni-id-co", {
          customUI: true
        });
        let loginResult;
        if (provider === "wechat") {
          const loginRes = await common_vendor.index.login({ provider: "weixin" });
          loginResult = await uniIdCo.loginByWeixin({ code: loginRes.code });
        } else if (provider === "alipay") {
          const loginRes = await common_vendor.index.login({ provider: "alipay" });
          loginResult = await uniIdCo.loginByAlipay(loginRes);
        } else {
          throw new Error("不支持的登录方式");
        }
        common_vendor.index.__f__("log", "at pages/login/login.vue:148", "uni-id loginResult raw:", JSON.stringify(loginResult));
        if (loginResult.errCode === 0 && loginResult.newToken && loginResult.newToken.token) {
          const completeUserInfo = {
            ...loginResult.userInfo || {},
            uid: loginResult.uid
            // 确保uid被正确设置
          };
          await this.userStore.loginSuccess({
            token: loginResult.newToken.token,
            userInfo: completeUserInfo,
            tokenExpired: loginResult.newToken.tokenExpired
          }, true);
          common_vendor.index.__f__("log", "at pages/login/login.vue:164", "[Login] loginSuccess completed, waiting for data sync...");
          await new Promise((resolve) => setTimeout(resolve, 1e3));
          common_vendor.index.__f__("log", "at pages/login/login.vue:170", "[Login] After loginSuccess and data sync:");
          common_vendor.index.__f__("log", "at pages/login/login.vue:171", "  - userStore.isLogin:", this.userStore.isLogin);
          common_vendor.index.__f__("log", "at pages/login/login.vue:172", "  - userStore.userId:", this.userStore.userId);
          common_vendor.index.__f__("log", "at pages/login/login.vue:173", "  - userStore.token:", this.userStore.token ? `EXISTS (${this.userStore.token.length} chars)` : "NULL");
          common_vendor.index.__f__("log", "at pages/login/login.vue:174", "  - userInfo with uid:", JSON.stringify(this.userStore.userInfo));
          let completeUserInfoFromDB = null;
          try {
            if (this.userStore.isLogin && this.userStore.userId) {
              const usersTable = common_vendor.nr.database().collection("uni-id-users");
              const userRecord = await usersTable.where({ _id: this.userStore.userId }).field({
                "nickname": true,
                "avatar_file": true,
                "avatar": true,
                "avatarUrl": true
              }).limit(1).get();
              common_vendor.index.__f__("log", "at pages/login/login.vue:191", "Fetched user from DB:", JSON.stringify(userRecord));
              if (userRecord.result && userRecord.result.data && userRecord.result.data.length > 0) {
                completeUserInfoFromDB = userRecord.result.data[0];
                this.userStore.updateUserInfo(completeUserInfoFromDB);
              }
            } else {
              common_vendor.index.__f__("warn", "at pages/login/login.vue:197", "User not fully logged in after loginSuccess for DB query, UID:", this.userStore.userId);
            }
          } catch (dbError) {
            common_vendor.index.__f__("error", "at pages/login/login.vue:200", "Failed to fetch user info from DB after login:", dbError);
          }
          const finalUserInfoToCheck = this.userStore.userInfo;
          common_vendor.index.__f__("log", "at pages/login/login.vue:205", "Final userInfo for completion check:", JSON.stringify(finalUserInfoToCheck));
          const hasNickname = finalUserInfoToCheck && finalUserInfoToCheck.nickname && finalUserInfoToCheck.nickname !== "微信用户" && finalUserInfoToCheck.nickname.trim() !== "";
          const hasAvatar = finalUserInfoToCheck && (finalUserInfoToCheck.avatar_file && finalUserInfoToCheck.avatar_file.url && !finalUserInfoToCheck.avatar_file.url.includes("defaultAvatar.png") && finalUserInfoToCheck.avatar_file.url.trim() !== "" || finalUserInfoToCheck.avatarUrl && !finalUserInfoToCheck.avatarUrl.includes("defaultAvatar.png") && finalUserInfoToCheck.avatarUrl.trim() !== "" || finalUserInfoToCheck.avatar && !finalUserInfoToCheck.avatar.includes("defaultAvatar.png") && finalUserInfoToCheck.avatar.trim() !== "");
          common_vendor.index.__f__("log", "at pages/login/login.vue:214", "hasNickname:", hasNickname, "hasAvatar:", hasAvatar);
          const isUserInfoComplete = hasNickname && hasAvatar;
          if (loadingShown) {
            common_vendor.index.hideLoading();
            loadingShown = false;
          }
          if (isUserInfoComplete) {
            common_vendor.index.showToast({ icon: "success", title: "登录成功" });
            this.navigateToNextPage();
          } else {
            this.tempNickname = finalUserInfoToCheck && finalUserInfoToCheck.nickname && finalUserInfoToCheck.nickname !== "微信用户" ? finalUserInfoToCheck.nickname : "";
            this.showProfileCompletionForm = true;
            common_vendor.index.showToast({ icon: "none", title: "请完善您的头像和昵称" });
          }
        } else {
          if (loadingShown) {
            common_vendor.index.hideLoading();
            loadingShown = false;
          }
          common_vendor.index.__f__("error", "at pages/login/login.vue:240", "uni-id-co login error:", loginResult);
          common_vendor.index.showToast({ icon: "none", title: loginResult.errMsg || "登录失败，请稍后重试" });
        }
      } catch (error) {
        if (loadingShown) {
          common_vendor.index.hideLoading();
          loadingShown = false;
        }
        common_vendor.index.__f__("error", "at pages/login/login.vue:249", "Client-side third party login error:", error);
        let errMsg = "登录失败，请检查网络或稍后重试";
        if (error.errMsg) {
          errMsg = error.errMsg;
        }
        common_vendor.index.showToast({ icon: "none", title: errMsg, duration: 3e3 });
      }
    },
    navigateToNextPage() {
      if (this.redirectUrl) {
        common_vendor.index.__f__("log", "at pages/login/login.vue:260", "[Login Page] Navigating to redirectUrl:", this.redirectUrl);
        this.handleRedirect(this.redirectUrl);
      } else {
        const pages = getCurrentPages();
        if (pages.length > 1) {
          common_vendor.index.__f__("log", "at pages/login/login.vue:265", "[Login Page] Navigating back.");
          common_vendor.index.navigateBack();
        } else {
          common_vendor.index.__f__("log", "at pages/login/login.vue:268", "[Login Page] Relaunching to index.");
          common_vendor.index.reLaunch({ url: "/pages/index/index" });
        }
      }
    },
    onChooseAvatar(e) {
      common_vendor.index.__f__("log", "at pages/login/login.vue:275", "Avatar chosen:", e.detail.avatarUrl);
      if (e.detail.avatarUrl) {
        this.tempAvatarFile = {
          path: e.detail.avatarUrl,
          name: `avatar_${Date.now()}.${e.detail.avatarUrl.split(".").pop() || "png"}`,
          newUpload: true
          // Mark that this is a new file to be uploaded
        };
      }
    },
    onNicknameInputBlur(e) {
      this.tempNickname = e.detail.value.trim();
      common_vendor.index.__f__("log", "at pages/login/login.vue:287", "Nickname input:", this.tempNickname);
    },
    async submitUserProfile() {
      const nicknameUnchanged = !this.tempNickname || this.tempNickname === this.userStore.nickname;
      const avatarUnchanged = !this.tempAvatarFile || !this.tempAvatarFile.newUpload;
      if (nicknameUnchanged && avatarUnchanged) {
        this.navigateToNextPage();
        return;
      }
      let loadingShown = false;
      const updateData = {};
      const updatedFieldsForStore = {};
      try {
        common_vendor.index.showLoading({ title: "正在保存..." });
        loadingShown = true;
        if (this.tempAvatarFile && this.tempAvatarFile.newUpload) {
          const uploadResult = await common_vendor.nr.uploadFile({
            filePath: this.tempAvatarFile.path,
            cloudPath: `user_avatars/${this.userStore.userId}/${this.tempAvatarFile.name}`,
            onProgressUpdate: (progressEvent) => {
            }
          });
          common_vendor.index.__f__("log", "at pages/login/login.vue:315", "Upload result:", uploadResult);
          if (uploadResult.fileID) {
            updateData.avatar_file = { url: uploadResult.fileID };
            updatedFieldsForStore.avatarUrl = uploadResult.fileID;
          } else {
            let errMsg = "头像上传失败";
            try {
              const parsedError = JSON.parse(uploadResult.data);
              if (parsedError && parsedError.message)
                errMsg = parsedError.message;
            } catch (e) {
            }
            throw new Error(errMsg);
          }
        }
        if (this.tempNickname && this.tempNickname !== this.userStore.nickname) {
          updateData.nickname = this.tempNickname;
          updatedFieldsForStore.nickname = this.tempNickname;
        }
        if (Object.keys(updateData).length > 0) {
          common_vendor.index.__f__("log", "at pages/login/login.vue:337", "Updating user data in DB:", JSON.stringify(updateData));
          const usersTable = common_vendor.nr.database().collection("uni-id-users");
          const dbResult = await usersTable.where("_id==$env.uid").update(updateData);
          common_vendor.index.__f__("log", "at pages/login/login.vue:340", "DB update result:", dbResult);
          if (dbResult.result && dbResult.result.updated > 0) {
          } else if (dbResult.result && dbResult.result.updated === 0 && Object.keys(updateData).length > 0) {
            common_vendor.index.__f__("log", "at pages/login/login.vue:346", "Data not changed in DB, but operation considered complete.");
          } else {
            let errMsg = "用户信息更新失败";
            if (dbResult.result && dbResult.result.errCode)
              errMsg = `错误码: ${dbResult.result.errCode}, ${dbResult.result.errMsg}`;
            else if (dbResult.errMsg)
              errMsg = dbResult.errMsg;
            throw new Error(errMsg);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/login/login.vue:356", "No data to update after processing.");
        }
        if (Object.keys(updatedFieldsForStore).length > 0) {
          this.userStore.updateUserInfo(updatedFieldsForStore);
        }
        if (loadingShown) {
          common_vendor.index.hideLoading();
          loadingShown = false;
        }
        common_vendor.index.showToast({ title: "信息保存成功", icon: "success" });
        this.navigateToNextPage();
      } catch (error) {
        if (loadingShown) {
          common_vendor.index.hideLoading();
          loadingShown = false;
        }
        common_vendor.index.__f__("error", "at pages/login/login.vue:378", "Submit user profile error:", error);
        common_vendor.index.showToast({ icon: "none", title: error.message || "信息保存失败，请重试", duration: 3e3 });
      }
    },
    submitLogin() {
      this.$refs.form.validate().then(async (res) => {
        common_vendor.index.__f__("log", "at pages/login/login.vue:385", "表单数据", res);
        let loadingShown = false;
        try {
          common_vendor.index.showLoading({ title: "登录中..." });
          loadingShown = true;
          const loginRes = await common_vendor.nr.callFunction({
            name: "uni-id-co",
            data: {
              action: "login",
              username: this.formData.username,
              password: this.formData.password
              // type: 'password' // login方法内部会根据参数判断
            }
          });
          common_vendor.index.__f__("log", "at pages/login/login.vue:400", "[Login Page] uni-id-co login response:", loginRes);
          if (loginRes.errCode === 0 && loginRes.token) {
            if (loadingShown) {
              common_vendor.index.hideLoading().catch(() => {
              });
              loadingShown = false;
            }
            common_vendor.index.showToast({ title: "登录成功", icon: "success" });
            await this.userStore.loginSuccess({
              token: loginRes.token,
              userInfo: loginRes.userInfo,
              // login接口通常会返回userInfo
              tokenExpired: loginRes.tokenExpired
            }, true);
            await new Promise((resolve) => setTimeout(resolve, 100));
            const redirectUrl = decodeURIComponent(this.options.redirect || "") || "/pages/index/index";
            this.handleRedirect(redirectUrl);
          } else {
            if (loadingShown) {
              common_vendor.index.hideLoading().catch(() => {
              });
              loadingShown = false;
            }
            common_vendor.index.showToast({ title: loginRes.errMsg || "登录失败，请检查您的账号密码", icon: "none", duration: 3e3 });
          }
        } catch (error) {
          if (loadingShown) {
            common_vendor.index.hideLoading().catch(() => {
            });
            loadingShown = false;
          }
          common_vendor.index.__f__("error", "at pages/login/login.vue:443", "[Login Page] Login error:", error);
          let errMsg = "登录失败，请稍后再试";
          if (error.errMsg)
            errMsg = error.errMsg;
          if (error.message)
            errMsg = error.message;
          common_vendor.index.showToast({ title: errMsg, icon: "none", duration: 3e3 });
        }
      }).catch((err) => {
        common_vendor.index.__f__("log", "at pages/login/login.vue:450", "表单错误", err);
      });
    },
    handleRedirect(url) {
      const switchTabPages = [
        "/pages/index/index",
        "/pages/profile/profile",
        "/pages/message/message"
      ];
      const targetPath = url.split("?")[0];
      if (switchTabPages.includes(targetPath)) {
        common_vendor.index.__f__("log", "at pages/login/login.vue:463", `[Login Page] Redirecting to Tab: ${url}`);
        common_vendor.index.switchTab({ url: targetPath });
      } else {
        common_vendor.index.__f__("log", "at pages/login/login.vue:466", `[Login Page] Redirecting to Page: ${url}`);
        common_vendor.index.reLaunch({ url });
      }
    },
    navigateToRegister() {
      common_vendor.index.navigateTo({ url: "/pages/login/register" });
    },
    navigateToForgotPassword() {
      common_vendor.index.showToast({ title: "功能待开发", icon: "none" });
    }
    // loginByWeixin() { ... }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0,
    b: !$data.showProfileCompletionForm
  }, !$data.showProfileCompletionForm ? {
    c: common_vendor.p({
      type: "weixin",
      size: "26",
      color: "#FFF"
    }),
    d: common_vendor.o(($event) => $options.thirdPartyLogin("wechat")),
    e: common_assets._imports_1,
    f: common_vendor.o(($event) => $options.thirdPartyLogin("alipay"))
  } : {}, {
    g: $data.showProfileCompletionForm
  }, $data.showProfileCompletionForm ? {
    h: $data.tempAvatarFile ? $data.tempAvatarFile.path : $setup.userStore.avatarUrl || "/static/default_avatar.png",
    i: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args)),
    j: common_vendor.o((...args) => $options.onNicknameInputBlur && $options.onNicknameInputBlur(...args)),
    k: $data.tempNickname,
    l: common_vendor.o(($event) => $data.tempNickname = $event.detail.value),
    m: common_vendor.o((...args) => $options.submitUserProfile && $options.submitUserProfile(...args))
  } : {}, {
    n: !$data.showProfileCompletionForm
  }, !$data.showProfileCompletionForm ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map

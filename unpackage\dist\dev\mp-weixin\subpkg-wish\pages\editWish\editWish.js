"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_wish = require("../../../store/wish.js");
const store_group = require("../../../store/group.js");
const store_user = require("../../../store/user.js");
const services_authService = require("../../../services/authService.js");
const mixins_groupTagOperations = require("../../../mixins/groupTagOperations.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  mixins: [mixins_groupTagOperations.groupTagOperations],
  data() {
    return {
      wishId: null,
      isEdit: false,
      wishForm: {
        title: "",
        description: "",
        image: [],
        video: [],
        audio: [],
        isCompleted: false,
        startDate: "",
        completeDate: null,
        groupIds: ["all"],
        permission: "private"
      },
      currentMediaType: "image",
      // image, video, audio
      uploadedImages: [],
      uploadedVideos: [],
      uploadedAudios: [],
      groupStore: null,
      wishStore: null,
      userStore: null,
      newGroupName: "",
      // 标题冲突警告
      titleConflictWarning: false
    };
  },
  computed: {
    availableGroups() {
      return this.groupStore ? this.groupStore.getAllGroups : [];
    }
  },
  created() {
    common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:222", "Component created, initializing stores");
    this.initStores();
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:228", "onLoad triggered, initializing stores");
    this.initStores();
    if (options && options.id) {
      this.wishId = options.id;
      this.isEdit = true;
      this.loadWishData();
    }
    this.restoreTempDataIfExists();
    if (options && options.groupId && options.groupId !== "all") {
      common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:243", "接收到分组ID:", options.groupId);
      if (!this.wishForm.groupIds) {
        this.wishForm.groupIds = ["all"];
      }
      if (!this.wishForm.groupIds.includes(options.groupId)) {
        this.wishForm.groupIds.push(options.groupId);
        common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:253", "已将心愿分配到分组:", options.groupId);
      }
      if (options.groupId === "friend-visible") {
        this.wishForm.permission = "friends";
        common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:259", "自动设置权限为朋友可见");
      }
    }
  },
  onShow() {
    if (this.userStore && this.userStore.isLogin) {
      if (this.groupStore && (!this.groupStore.getAllGroups || this.groupStore.getAllGroups.length === 0)) {
        common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:268", "[editWish] User logged in, reinitializing group store...");
        this.groupStore.initGroups();
      }
      this.restoreTempDataIfExists();
    }
  },
  onReady() {
    if (!this.groupStore) {
      common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:279", "Group store not initialized, trying again...");
      this.initStores();
    }
  },
  methods: {
    // 切换媒体类型
    changeMediaType(type) {
      this.currentMediaType = type;
    },
    // 🔧 新增：上传文件到云存储
    async uploadFilesToCloud(files, fileType = "image") {
      if (!files || files.length === 0) {
        return [];
      }
      const uploadedUrls = [];
      const userId = this.userStore.userId;
      if (!userId) {
        throw new Error("用户未登录，无法上传文件");
      }
      for (let i = 0; i < files.length; i++) {
        const filePath = files[i];
        try {
          const timestamp = Date.now();
          const randomStr = Math.random().toString(36).substring(2, 8);
          const fileExtension = filePath.split(".").pop() || (fileType === "image" ? "jpg" : "mp4");
          const cloudPath = `wish_files/${userId}/${fileType}s/${timestamp}_${randomStr}.${fileExtension}`;
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:312", `[editWish.vue] 开始上传文件 ${i + 1}/${files.length}:`, {
            filePath,
            cloudPath,
            fileType
          });
          const uploadResult = await common_vendor.nr.uploadFile({
            filePath,
            cloudPath,
            onUploadProgress: (progressEvent) => {
              const progress = Math.round(progressEvent.loaded / progressEvent.total * 100);
              common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:324", `[editWish.vue] 文件上传进度: ${progress}%`);
              common_vendor.index.showLoading({
                title: `上传中 ${i + 1}/${files.length} (${progress}%)`
              });
            }
          });
          if (uploadResult.fileID) {
            uploadedUrls.push(uploadResult.fileID);
            common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:335", `[editWish.vue] 文件上传成功:`, uploadResult.fileID);
          } else {
            common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:337", `[editWish.vue] 文件上传失败，未返回fileID:`, uploadResult);
            throw new Error(`第${i + 1}个文件上传失败`);
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:342", `[editWish.vue] 文件上传失败:`, error);
          throw new Error(`第${i + 1}个文件上传失败: ${error.message}`);
        }
      }
      common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:347", `[editWish.vue] 所有文件上传完成:`, uploadedUrls);
      return uploadedUrls;
    },
    // 初始化stores
    initStores() {
      try {
        this.wishStore = store_wish.useWishStore();
        this.groupStore = store_group.useGroupStore();
        this.userStore = store_user.useUserStore();
        if (this.groupStore && !this.groupStore.getAllGroups.length && this.userStore.isLogin) {
          this.groupStore.initGroups();
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:363", "Failed to initialize stores:", e);
        common_vendor.index.showToast({
          title: "初始化失败",
          icon: "none"
        });
      }
    },
    // 恢复临时保存的编辑数据
    restoreTempDataIfExists() {
      try {
        const tempData = common_vendor.index.getStorageSync("editWish_tempData");
        if (tempData) {
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:378", "[editWish] 发现备份的编辑数据:", tempData);
          const timeDiff = Date.now() - (tempData.timestamp || 0);
          if (timeDiff > 24 * 60 * 60 * 1e3) {
            common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:383", "[editWish] 备份数据已过期，清除");
            common_vendor.index.removeStorageSync("editWish_tempData");
            return;
          }
          this.wishForm = tempData.wishForm;
          this.uploadedImages = tempData.uploadedImages || [];
          this.uploadedVideos = tempData.uploadedVideos || [];
          this.uploadedAudios = tempData.uploadedAudios || [];
          this.currentMediaType = tempData.currentMediaType || "image";
          this.ensurePermissionGroupConsistency();
          if (tempData.isEdit && tempData.wishId) {
            this.isEdit = tempData.isEdit;
            this.wishId = tempData.wishId;
          }
          common_vendor.index.removeStorageSync("editWish_tempData");
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:407", "[editWish] 编辑数据恢复成功");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:410", "[editWish] 恢复编辑数据失败:", error);
        common_vendor.index.removeStorageSync("editWish_tempData");
      }
    },
    // 标题输入事件处理
    onTitleInput(e) {
      this.titleConflictWarning = this.checkTitleConflictQuiet();
    },
    // 检测标题冲突的核心逻辑（提取公共部分）
    _findConflictWish(title) {
      if (!this.wishStore || !title) {
        return null;
      }
      const currentTitle = title.trim();
      if (!currentTitle) {
        return null;
      }
      const allWishes = this.wishStore.wishList || [];
      return allWishes.find((wish) => {
        if (wish._deleted) {
          return false;
        }
        if (this.isEdit && (wish._id === this.wishId || wish.id === this.wishId)) {
          return false;
        }
        const wishTitle = (wish.title || "").trim();
        return wishTitle.toLowerCase() === currentTitle.toLowerCase();
      });
    },
    // 静默检测标题冲突（不显示弹窗）
    checkTitleConflictQuiet() {
      const conflictWish = this._findConflictWish(this.wishForm.title);
      return !!conflictWish;
    },
    // 检测标题冲突（显示弹窗提示）
    checkTitleConflict() {
      const conflictWish = this._findConflictWish(this.wishForm.title);
      if (conflictWish) {
        common_vendor.index.showModal({
          title: "标题冲突",
          content: `已存在相同标题的心愿："${conflictWish.title}"，请使用不同的标题。`,
          showCancel: false,
          confirmText: "知道了",
          success: () => {
            common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:473", "[editWish] 标题冲突，用户已确认");
          }
        });
        common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:477", "[editWish] 检测到标题冲突:", {
          newTitle: this.wishForm.title.trim(),
          conflictWish
        });
        return true;
      }
      return false;
    },
    // 加载心愿数据
    loadWishData() {
      if (!this.wishStore)
        return;
      const wish = this.wishStore.getWishById(this.wishId);
      if (wish) {
        this.wishForm = {
          title: wish.title || "",
          description: wish.description || "",
          image: Array.isArray(wish.image) ? [...wish.image] : wish.image ? [wish.image] : [],
          video: Array.isArray(wish.video) ? [...wish.video] : wish.video ? [wish.video] : [],
          audio: Array.isArray(wish.audio) ? [...wish.audio] : wish.audio ? [wish.audio] : [],
          isCompleted: !!wish.isCompleted,
          startDate: wish.startDate || "",
          completeDate: wish.completeDate || null,
          groupIds: wish.groupIds ? [...wish.groupIds] : ["all"],
          permission: wish.permission || "private"
        };
        this.ensurePermissionGroupConsistency();
        this.uploadedImages = [...this.wishForm.image];
        this.uploadedVideos = [...this.wishForm.video];
        this.uploadedAudios = [...this.wishForm.audio];
      } else {
        common_vendor.index.showToast({
          title: "心愿不存在",
          icon: "none"
        });
        setTimeout(() => {
          this.safeNavigateBack();
        }, 1500);
      }
    },
    // 验证文件
    validateFile(file, type) {
      const maxSizes = {
        image: 5,
        // 5MB
        video: 50,
        // 50MB  
        audio: 20
        // 20MB
      };
      const allowedTypes = {
        image: ["jpg", "jpeg", "png", "webp", "gif"],
        video: ["mp4", "avi", "mov", "3gp"],
        audio: ["mp3", "wav", "aac", "m4a"]
      };
      const getFileExt = (path) => {
        const parts = path.split(".");
        return parts.length > 1 ? parts.pop().toLowerCase() : "";
      };
      if (file.size && file.size > maxSizes[type] * 1024 * 1024) {
        common_vendor.index.showToast({
          title: `${type === "image" ? "图片" : type === "video" ? "视频" : "音频"}大小不能超过${maxSizes[type]}MB`,
          icon: "none",
          duration: 2e3
        });
        return false;
      }
      const ext = getFileExt(file.path || file.tempFilePath || "");
      if (ext && !allowedTypes[type].includes(ext)) {
        common_vendor.index.showToast({
          title: `不支持的${type === "image" ? "图片" : type === "video" ? "视频" : "音频"}格式`,
          icon: "none",
          duration: 2e3
        });
        return false;
      }
      return true;
    },
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 9 - this.uploadedImages.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          if (res && res.tempFilePaths && res.tempFilePaths.length > 0) {
            const validFiles = res.tempFilePaths.filter((path, index) => {
              const file = res.tempFiles ? res.tempFiles[index] : { path, tempFilePath: path };
              return this.validateFile(file, "image");
            });
            if (validFiles.length > 0) {
              this.uploadedImages = [...this.uploadedImages, ...validFiles];
              this.wishForm.image = [...this.uploadedImages];
              if (validFiles.length < res.tempFilePaths.length) {
                common_vendor.index.showToast({
                  title: `已添加${validFiles.length}张图片，${res.tempFilePaths.length - validFiles.length}张图片验证失败`,
                  icon: "none"
                });
              }
            }
          }
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:600", "选择图片失败:", error);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 选择视频
    chooseVideo() {
      common_vendor.index.chooseVideo({
        count: 1,
        sourceType: ["album", "camera"],
        maxDuration: 60,
        camera: "back",
        success: (res) => {
          if (res && res.tempFilePath) {
            const file = {
              path: res.tempFilePath,
              tempFilePath: res.tempFilePath,
              size: res.size
            };
            if (this.validateFile(file, "video")) {
              this.uploadedVideos = [...this.uploadedVideos, res.tempFilePath];
              this.wishForm.video = [...this.uploadedVideos];
            }
          }
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:633", "选择视频失败:", error);
          common_vendor.index.showToast({
            title: "选择视频失败",
            icon: "none"
          });
        }
      });
    },
    // 选择音频
    chooseAudio() {
      common_vendor.index.chooseFile({
        count: 1,
        type: "all",
        extension: [".mp3", ".wav", ".aac"],
        success: (res) => {
          if (res && res.tempFilePaths && res.tempFilePaths.length > 0) {
            const file = {
              path: res.tempFilePaths[0],
              tempFilePath: res.tempFilePaths[0],
              size: res.tempFiles ? res.tempFiles[0].size : 0
            };
            if (this.validateFile(file, "audio")) {
              this.uploadedAudios = [...this.uploadedAudios, res.tempFilePaths[0]];
              this.wishForm.audio = [...this.uploadedAudios];
            }
          }
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:666", "选择音频失败:", error);
          common_vendor.index.showToast({
            title: "当前平台不支持音频选择",
            icon: "none"
          });
        }
      });
    },
    // 播放音频
    playAudio(src) {
      const audioContext = common_vendor.index.createInnerAudioContext();
      audioContext.src = src;
      audioContext.play();
    },
    // 预览图片
    previewImage(index) {
      if (this.uploadedImages.length > 0) {
        common_vendor.index.previewImage({
          current: this.uploadedImages[index],
          urls: this.uploadedImages
        });
      }
    },
    // 删除图片
    deleteImage(index) {
      this.uploadedImages.splice(index, 1);
      this.wishForm.image = [...this.uploadedImages];
    },
    // 删除视频
    deleteVideo(index) {
      this.uploadedVideos.splice(index, 1);
      this.wishForm.video = [...this.uploadedVideos];
    },
    // 删除音频
    deleteAudio(index) {
      this.uploadedAudios.splice(index, 1);
      this.wishForm.audio = [...this.uploadedAudios];
    },
    // 检查分组是否被选中
    isGroupSelected(groupId) {
      if (!this.wishForm.groupIds) {
        return groupId === "all";
      }
      return this.wishForm.groupIds.indexOf(groupId) > -1;
    },
    // 确保朋友可见权限和分组标签的一致性
    ensurePermissionGroupConsistency() {
      if (!this.wishForm.groupIds) {
        this.wishForm.groupIds = ["all"];
      }
      const hasFriendVisibleGroup = this.wishForm.groupIds.includes("friend-visible");
      const hasFriendPermission = this.wishForm.permission === "friends";
      if (hasFriendPermission && !hasFriendVisibleGroup) {
        this.wishForm.groupIds.push("friend-visible");
        common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:735", "自动添加到朋友可见分组（一致性修复）");
      }
      if (hasFriendVisibleGroup && !hasFriendPermission) {
        this.wishForm.permission = "friends";
        common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:741", "自动设置权限为朋友可见（一致性修复）");
      }
    },
    // 切换分组选择状态
    toggleGroup(groupId) {
      if (!this.wishForm.groupIds) {
        this.wishForm.groupIds = ["all"];
      }
      if (groupId === "all") {
        return;
      }
      const index = this.wishForm.groupIds.indexOf(groupId);
      if (index > -1) {
        this.wishForm.groupIds.splice(index, 1);
        if (groupId === "friend-visible" && this.wishForm.permission === "friends") {
          this.wishForm.permission = "private";
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:765", "取消朋友可见分组，权限自动设置为私密");
        }
      } else {
        this.wishForm.groupIds.push(groupId);
        if (groupId === "friend-visible") {
          this.wishForm.permission = "friends";
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:774", "选择朋友可见分组，权限自动设置为朋友可见");
        }
      }
      common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:778", "当前选中的分组:", this.wishForm.groupIds, "，当前权限:", this.wishForm.permission);
    },
    // 表单验证
    validateForm() {
      if (!this.wishForm.title || !this.wishForm.title.trim()) {
        common_vendor.index.showToast({
          title: "请输入心愿标题",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    // 处理开始日期选择
    onStartDateChange(e) {
      try {
        const dateStr = e.detail.value;
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          this.wishForm.startDate = date.toISOString();
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:800", "开始时间设置为:", this.wishForm.startDate);
        } else {
          common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:802", "无法解析日期:", dateStr);
          this.wishForm.startDate = dateStr;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:806", "日期处理错误:", error);
        this.wishForm.startDate = e.detail.value;
      }
    },
    // 处理完成日期选择
    onCompleteDateChange(e) {
      try {
        const dateStr = e.detail.value;
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          this.wishForm.completeDate = date.toISOString();
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:818", "完成时间设置为:", this.wishForm.completeDate);
        } else {
          common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:820", "无法解析日期:", dateStr);
          this.wishForm.completeDate = dateStr;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:824", "日期处理错误:", error);
        this.wishForm.completeDate = e.detail.value;
      }
    },
    // 保存心愿
    async saveWish() {
      if (this.userStore.isLogin) {
        await this._actualSaveWish();
        return;
      }
      const tempFormData = {
        wishForm: JSON.parse(JSON.stringify(this.wishForm)),
        uploadedImages: [...this.uploadedImages],
        uploadedVideos: [...this.uploadedVideos],
        uploadedAudios: [...this.uploadedAudios],
        currentMediaType: this.currentMediaType,
        isEdit: this.isEdit,
        wishId: this.wishId,
        timestamp: Date.now()
      };
      common_vendor.index.setStorageSync("editWish_tempData", tempFormData);
      common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:849", "[editWish] 备份编辑数据到本地存储:", tempFormData);
      await services_authService.authService.ensureAuthenticated({
        // 不传递 onAuthenticated，让登录后回到当前页面
        promptMessage: "保存心愿需要登录，是否继续？",
        onCancelled: () => {
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:855", "[editWish.vue saveWish] 用户取消登录，未保存。");
          common_vendor.index.removeStorageSync("editWish_tempData");
        }
      });
    },
    async _actualSaveWish() {
      if (!this.wishForm.title.trim()) {
        common_vendor.index.showToast({
          title: "标题不能为空",
          icon: "none"
        });
        return;
      }
      if (this.checkTitleConflict()) {
        return;
      }
      let processedImages = [];
      let processedVideos = [];
      let processedAudios = [];
      try {
        common_vendor.index.showLoading({ title: "正在保存..." });
        if (this.uploadedImages.length > 0) {
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:887", "[editWish.vue] 开始上传图片到云存储，数量:", this.uploadedImages.length);
          try {
            const localImages = this.uploadedImages.filter((path) => {
              return typeof path === "string" && (path.startsWith("wxfile://") || path.startsWith("file://") || path.includes("tmp_") || path.includes("temp"));
            });
            const cloudImages = this.uploadedImages.filter((path) => {
              return typeof path === "string" && (path.startsWith("cloud://") || path.startsWith("https://"));
            });
            common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:908", "[editWish.vue] 本地图片:", localImages);
            common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:909", "[editWish.vue] 云端图片:", cloudImages);
            if (localImages.length > 0) {
              const uploadedUrls = await this.uploadFilesToCloud(localImages, "image");
              processedImages = [...cloudImages, ...uploadedUrls];
            } else {
              processedImages = cloudImages;
            }
            common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:919", "[editWish.vue] 图片处理完成:", processedImages);
          } catch (error) {
            common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:922", "[editWish.vue] 图片上传失败:", error);
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: `图片上传失败: ${error.message}`,
              icon: "none",
              duration: 3e3
            });
            return;
          }
        }
        if (this.uploadedVideos.length > 0) {
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:935", "[editWish.vue] 开始上传视频到云存储，数量:", this.uploadedVideos.length);
          try {
            const localVideos = this.uploadedVideos.filter((path) => {
              return typeof path === "string" && (path.startsWith("wxfile://") || path.startsWith("file://") || path.includes("tmp_") || path.includes("temp"));
            });
            const cloudVideos = this.uploadedVideos.filter((path) => {
              return typeof path === "string" && (path.startsWith("cloud://") || path.startsWith("https://"));
            });
            if (localVideos.length > 0) {
              const uploadedUrls = await this.uploadFilesToCloud(localVideos, "video");
              processedVideos = [...cloudVideos, ...uploadedUrls];
            } else {
              processedVideos = cloudVideos;
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:962", "[editWish.vue] 视频上传失败:", error);
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: `视频上传失败: ${error.message}`,
              icon: "none",
              duration: 3e3
            });
            return;
          }
        }
        if (this.uploadedAudios.length > 0) {
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:975", "[editWish.vue] 开始上传音频到云存储，数量:", this.uploadedAudios.length);
          try {
            const localAudios = this.uploadedAudios.filter((path) => {
              return typeof path === "string" && (path.startsWith("wxfile://") || path.startsWith("file://") || path.includes("tmp_") || path.includes("temp"));
            });
            const cloudAudios = this.uploadedAudios.filter((path) => {
              return typeof path === "string" && (path.startsWith("cloud://") || path.startsWith("https://"));
            });
            if (localAudios.length > 0) {
              const uploadedUrls = await this.uploadFilesToCloud(localAudios, "audio");
              processedAudios = [...cloudAudios, ...uploadedUrls];
            } else {
              processedAudios = cloudAudios;
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:1002", "[editWish.vue] 音频上传失败:", error);
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: `音频上传失败: ${error.message}`,
              icon: "none",
              duration: 3e3
            });
            return;
          }
        }
        const wishData = {
          ...this.wishForm,
          // 🔧 修复：确保保存的是字符串数组，与WishCard组件期望的格式一致
          image: processedImages.length > 0 ? processedImages : this.wishForm.image || [],
          video: processedVideos.length > 0 ? processedVideos : this.wishForm.video || [],
          audio: processedAudios.length > 0 ? processedAudios : this.wishForm.audio || [],
          updateDate: (/* @__PURE__ */ new Date()).toISOString()
        };
        if (!this.isEdit) {
          wishData.createDate = (/* @__PURE__ */ new Date()).toISOString();
        }
        if (this.isEdit) {
          await this.wishStore.updateWish({ ...wishData, _id: this.wishId });
        } else {
          await this.wishStore.addWish(wishData);
        }
        common_vendor.index.hideLoading().catch(() => {
        });
        common_vendor.index.removeStorageSync("editWish_tempData");
        common_vendor.index.showToast({ title: this.isEdit ? "更新成功" : "保存成功", icon: "success" });
        setTimeout(() => {
          this.safeNavigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.hideLoading().catch(() => {
        });
        common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:1045", "保存心愿失败 (actualSaveWish):", error);
        common_vendor.index.showToast({ title: error.message || (this.isEdit ? "更新失败" : "保存失败"), icon: "none" });
      }
    },
    // 安全的返回导航
    safeNavigateBack() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.reLaunch({
          url: "/pages/index/index"
        });
      }
    },
    // 返回上一页
    goBack() {
      this.safeNavigateBack();
    },
    // 显示添加分组弹窗
    showAddGroupDialog() {
      this.newGroupName = "";
      common_vendor.index.showModal({
        title: "添加标签",
        placeholderText: "请输入标签名称",
        editable: true,
        success: (res) => {
          if (res.confirm && res.content) {
            this.newGroupName = res.content.trim();
            this.confirmAddGroup();
          }
        }
      });
    },
    // 确认添加分组
    async confirmAddGroup() {
      common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:1112", "添加标签按钮点击，准备调用ensureAuthenticated");
      if (this.userStore.isLogin) {
        await this._actualConfirmAddGroup();
        return;
      }
      await services_authService.authService.ensureAuthenticated({
        // 不传递 onAuthenticated，让登录后回到当前页面，用户可以重新添加标签
        promptMessage: "添加标签需要登录，是否继续？",
        onCancelled: () => {
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:1124", "[editWish.vue confirmAddGroup] 用户取消登录，未添加标签。");
        }
      });
    },
    async _actualConfirmAddGroup() {
      const name = this.newGroupName.trim();
      if (!name) {
        common_vendor.index.showToast({ title: "标签名称不能为空", icon: "none" });
        return;
      }
      if (!this.groupStore || this.groupStore.groups.length === 0 && this.userStore.isLogin) {
        await this.groupStore.initGroups();
      }
      if (this.groupStore.groups.some((group) => group.name.toLowerCase() === name.toLowerCase())) {
        common_vendor.index.showToast({ title: "已存在相同名称的标签", icon: "none" });
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "正在添加标签..." });
        const newGroupId = await this.groupStore.addGroup(name);
        common_vendor.index.hideLoading();
        if (newGroupId) {
          if (!this.wishForm.groupIds) {
            this.wishForm.groupIds = ["all"];
          }
          if (!this.wishForm.groupIds.includes(newGroupId)) {
            this.wishForm.groupIds.push(newGroupId);
          }
        } else {
          common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:1159", "创建分组标签失败，newGroupId 为 null");
          common_vendor.index.showToast({ title: "添加失败，请重试", icon: "none" });
        }
      } catch (e) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:1164", "添加标签错误 (_actualConfirmAddGroup catch):", e);
        common_vendor.index.showToast({ title: e.message || "添加失败，请重试", icon: "none" });
      }
    },
    // 设置权限
    setPermission(permission) {
      this.wishForm.permission = permission;
      if (!this.wishForm.groupIds) {
        this.wishForm.groupIds = ["all"];
      }
      if (permission === "friends") {
        if (!this.wishForm.groupIds.includes("friend-visible")) {
          this.wishForm.groupIds.push("friend-visible");
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:1183", "自动添加到朋友可见分组");
        }
      } else {
        const index = this.wishForm.groupIds.indexOf("friend-visible");
        if (index > -1) {
          this.wishForm.groupIds.splice(index, 1);
          common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:1190", "自动从朋友可见分组移除");
        }
      }
      common_vendor.index.__f__("log", "at subpkg-wish/pages/editWish/editWish.vue:1194", "权限设置为:", permission, "，当前分组:", this.wishForm.groupIds);
    },
    // 格式化日期用于显示
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime()))
          return dateStr;
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:1207", "日期格式化错误:", error);
        return dateStr;
      }
    },
    // 格式化日期用于picker组件
    formatPickerDate(dateStr) {
      if (!dateStr)
        return "";
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime()))
          return "";
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/editWish/editWish.vue:1222", "Picker日期格式化错误:", error);
        return "";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.titleConflictWarning ? 1 : "",
    b: common_vendor.o([($event) => $data.wishForm.title = $event.detail.value, (...args) => $options.onTitleInput && $options.onTitleInput(...args)]),
    c: $data.wishForm.title,
    d: $data.titleConflictWarning
  }, $data.titleConflictWarning ? {} : {}, {
    e: $data.wishForm.description,
    f: common_vendor.o(($event) => $data.wishForm.description = $event.detail.value),
    g: $data.currentMediaType === "image" ? 1 : "",
    h: common_vendor.o(($event) => $options.changeMediaType("image")),
    i: $data.currentMediaType === "video" ? 1 : "",
    j: common_vendor.o(($event) => $options.changeMediaType("video")),
    k: $data.currentMediaType === "audio" ? 1 : "",
    l: common_vendor.o(($event) => $options.changeMediaType("audio")),
    m: $data.currentMediaType === "image"
  }, $data.currentMediaType === "image" ? common_vendor.e({
    n: common_vendor.f($data.uploadedImages, (img, index, i0) => {
      return {
        a: img,
        b: common_vendor.o(($event) => $options.deleteImage(index), index),
        c: index,
        d: common_vendor.o(($event) => $options.previewImage(index), index)
      };
    }),
    o: $data.uploadedImages.length < 9
  }, $data.uploadedImages.length < 9 ? {
    p: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}) : {}, {
    q: $data.currentMediaType === "video"
  }, $data.currentMediaType === "video" ? common_vendor.e({
    r: common_vendor.f($data.uploadedVideos, (video, index, i0) => {
      return {
        a: video,
        b: common_vendor.o(($event) => $options.deleteVideo(index), index),
        c: index
      };
    }),
    s: common_assets._imports_0$1,
    t: $data.uploadedVideos.length < 2
  }, $data.uploadedVideos.length < 2 ? {
    v: common_vendor.o((...args) => $options.chooseVideo && $options.chooseVideo(...args))
  } : {}) : {}, {
    w: $data.currentMediaType === "audio"
  }, $data.currentMediaType === "audio" ? common_vendor.e({
    x: common_vendor.f($data.uploadedAudios, (audio, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.o(($event) => $options.playAudio(audio), index),
        c: common_vendor.o(($event) => $options.deleteAudio(index), index),
        d: index
      };
    }),
    y: $data.uploadedAudios.length < 3
  }, $data.uploadedAudios.length < 3 ? {
    z: common_vendor.o((...args) => $options.chooseAudio && $options.chooseAudio(...args))
  } : {}) : {}, {
    A: !$data.wishForm.startDate
  }, !$data.wishForm.startDate ? {} : {
    B: common_vendor.t($options.formatDate($data.wishForm.startDate))
  }, {
    C: $options.formatPickerDate($data.wishForm.startDate),
    D: common_vendor.o((...args) => $options.onStartDateChange && $options.onStartDateChange(...args)),
    E: !$data.wishForm.completeDate
  }, !$data.wishForm.completeDate ? {} : {
    F: common_vendor.t($options.formatDate($data.wishForm.completeDate))
  }, {
    G: $options.formatPickerDate($data.wishForm.completeDate),
    H: common_vendor.o((...args) => $options.onCompleteDateChange && $options.onCompleteDateChange(...args)),
    I: $data.wishForm.permission === "private" ? 1 : "",
    J: common_vendor.o(($event) => $options.setPermission("private")),
    K: $data.wishForm.permission === "friends" ? 1 : "",
    L: common_vendor.o(($event) => $options.setPermission("friends")),
    M: $data.wishForm.permission === "public" ? 1 : "",
    N: common_vendor.o(($event) => $options.setPermission("public")),
    O: !$options.availableGroups || $options.availableGroups.length === 0
  }, !$options.availableGroups || $options.availableGroups.length === 0 ? {} : {}, {
    P: common_vendor.f($options.availableGroups, (group, index, i0) => {
      return {
        a: common_vendor.t(group.name),
        b: group.id,
        c: $options.isGroupSelected(group.id) ? 1 : "",
        d: common_vendor.o(($event) => $options.toggleGroup(group.id), group.id),
        e: common_vendor.o(($event) => _ctx.showGroupActionSheet(group), group.id)
      };
    }),
    Q: common_vendor.o((...args) => $options.showAddGroupDialog && $options.showAddGroupDialog(...args)),
    R: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    S: common_vendor.o((...args) => $options.saveWish && $options.saveWish(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpkg-wish/pages/editWish/editWish.js.map

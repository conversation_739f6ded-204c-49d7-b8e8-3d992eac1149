<template>
	<view class="group-selector">
		<!-- 分享按钮固定在右侧 - 改为button元素并添加open-type="share" -->
		<button 
			class="share-group-btn" 
			open-type="share"
			share-type="1"
			@click="handleShareClick" 
			@longpress="onShareButtonLongPress"
			:class="{'active': isShareButtonActive, 'show-tooltip': showTooltip}"
			:data-group-id="currentGroupId"
			:data-group-name="currentGroup ? currentGroup.name : ''"
			:data-share-type="'group'"
		>
			<image src="/static/tabbar/share.png" class="share-icon"></image>
			<view class="share-btn-ripple"></view>
			<view class="share-tooltip" v-if="showTooltip">
				分享此分组及心愿
			</view>
		</button>
		

		

		
		<!-- 确保滚动区域不与分享按钮重叠 -->
		<scroll-view class="group-scroll-view" scroll-x="true" show-scrollbar="false">
			<view class="group-list">
				<view 
					v-for="(group, index) in groups" 
					:key="group.id"
					class="group-item"
					:class="{ 
						active: currentGroupId === group.id, 
						'is-default': group.isDefault 
					}"
					@click="selectGroup(group.id)"
					@longpress="handleGroupLongPress(group)"
					:data-id="group.id"
				>
					<text class="group-text">{{ group.name }}</text>
				</view>
				<view class="group-item add-group" @click="showAddGroupModal">
					<view class="add-icon-wrapper">
						<text class="plus-icon">+</text>
					</view>
				</view>
				<!-- 添加占位元素，防止最右侧标签被分享按钮遮挡 -->
				<view class="spacing-placeholder"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { useGroupStore } from '@/store/group.js'
	import { useWishStore } from '@/store/wish.js'
	import { useUserStore } from '@/store/user.js'
	import groupTagOperations from '@/mixins/groupTagOperations.js'
	import { markRaw, nextTick } from 'vue'
	
	export default {
		name: 'GroupSelector',
		mixins: [groupTagOperations],
		setup() {
			const groupStore = useGroupStore()
			const wishStore = useWishStore()
			const userStore = useUserStore()
			
			return {
				groupStore,
				wishStore,
				userStore
			}
		},
		// 添加生命周期钩子
		created() {
			// 确保分组数据被加载
			if (this.groupStore && !this.groupStore.getAllGroups.length) {
				this.groupStore.initGroups()
			}
			
			// 监听页面显示事件，用于处理登录返回后的状态恢复
			uni.$on('page-show', this.onPageShow);
			
			// 监听登录成功事件，用于立即处理待处理的分组创建
			uni.$on('login-success', () => {
				console.log('接收到登录成功事件');
				// 只有在登录中状态时才处理
				if (this.isLoggingIn) {
					// 标记登录成功，但不立即处理
					// 等待页面重新显示时再处理
					this.isLoggingIn = false;
					
					// 避免页面显示事件没有触发的情况，
					// 设置一个足够长的延迟后检查待处理内容，确保不与页面显示事件冲突
					setTimeout(() => {
						const pendingName = uni.getStorageSync('pending_group_name');
						if (pendingName && !this.isProcessingPendingName) {
							console.log('登录成功延迟检查: 发现待处理分组', pendingName);
							this.checkPendingGroupName();
						}
					}, 1000); // 延长延迟时间，确保不与页面显示事件重叠
				}
			});
		},
		async mounted() {
			console.log('[GroupSelector] Component mounted, checking group data...');
			
			// 二次确认分组数据被正确加载
			if (this.groupStore && !this.groupStore.getAllGroups.length) {
				console.log("[GroupSelector] Groups empty, initializing in mounted hook");
				try {
					await this.groupStore.initGroups();
					console.log("[GroupSelector] Groups initialized successfully");
				} catch (error) {
					console.error("[GroupSelector] Group initialization failed:", error);
				}
			}
			
			// 检查是否有未完成的分组创建（从登录页面返回）
			this.checkPendingGroupName();
		},
		// 组件销毁时清理事件监听
		beforeDestroy() {
			uni.$off('page-show', this.onPageShow);
			// 移除登录成功事件监听
			uni.$off('login-success');
		},
		computed: {
			groups() {
				// 使用 shallowRef 或者只监听分组数据的变化，不受心愿数据影响
				const allGroups = this.groupStore.getAllGroups;
				
				// 生成更可靠的版本标识，包含分组数量和分组ID组合
				const groupIds = allGroups.map(g => g.id || g._id).join(',');
				const currentVersion = `${allGroups.length}_${groupIds}`;
				
				// 检查是否需要重新计算
				if (this.groupsVersion === currentVersion && this.cachedGroups.length > 0) {
					return this.cachedGroups;
				}
				
				// 对分组进行排序：默认分组在前，用户分组在后
				const sortedGroups = [...allGroups].sort((a, b) => {
					// 全部永远排在最前面
					if (a.id === 'all') return -1;
					if (b.id === 'all') return 1;
					
					// 默认分组优先于用户分组
					if (a.isDefault && !b.isDefault) return -1;
					if (!a.isDefault && b.isDefault) return 1;
					
					// 同类型分组按照order属性排序
					return (a.order || 0) - (b.order || 0);
				});
				
				// 更新缓存
				this.cachedGroups = sortedGroups;
				this.groupsVersion = currentVersion;
				
				console.log('[GroupSelector] Groups computed, version:', currentVersion);
				console.log('[GroupSelector] Sorted groups:', sortedGroups.map(g => ({id: g.id, name: g.name, order: g.order})));
				
				return sortedGroups;
			},
			currentGroupId() {
				return this.wishStore.currentGroupId
			},
			// 获取当前分组对象 - 独立计算，避免不必要的依赖
			currentGroup() {
				return this.groupStore.getGroupById(this.currentGroupId)
			}
		},
		watch: {
			// 只监听分组store的变化，不监听心愿store的变化
			'groupStore.groups': {
				handler(newGroups, oldGroups) {
					console.log('[GroupSelector] Groups changed in store, resetting cache');
					
					// 安全处理数组变化
					try {
						// 使用nextTick确保DOM更新在合适时机
						this.$nextTick(() => {
					// 重置缓存，触发重新计算
					this.groupsVersion = 0;
					this.cachedGroups = [];
							console.log('[GroupSelector] Cache reset completed');
						});
					} catch (error) {
						console.error('[GroupSelector] Error handling groups change:', error);
						// 降级处理
						this.groupsVersion = 0;
						this.cachedGroups = [];
					}
				},
				deep: true,
				immediate: false
			},
			// 监听当前分组ID变化
			currentGroupId: {
				handler(newGroupId, oldGroupId) {
					console.log('[GroupSelector] Current group ID changed:', oldGroupId, '->', newGroupId);
				},
				immediate: false
			}
		},
		data() {
			return {
				newGroupName: '',
				isShareButtonActive: false,
				showTooltip: false,
				tooltipTimer: null,
				// 跟踪是否正在登录中
				isLoggingIn: false,
				// 添加标志，避免重复处理待创建分组
				isProcessingPendingName: false,
				// 上次处理的分组名称，避免重复处理
				lastProcessedGroupName: '',
				// 上次处理时间戳
				lastProcessTimestamp: 0,
				// 缓存分组数据，避免不必要的重新计算
				cachedGroups: [],
				// 缓存的分组数据版本号
				groupsVersion: 0
			}
		},
		methods: {
			// 页面显示时检查状态
			onPageShow() {
				// 检查登录状态变化
				if (this.isLoggingIn && this.userStore.isLoggedIn) {
					console.log('登录状态变更：未登录 -> 已登录');
					// 已登录成功，重置登录状态
					this.isLoggingIn = false;
				}
				
				// 检查是否有待处理的分组创建（不依赖于登录状态）
				const pendingName = uni.getStorageSync('pending_group_name');
				// 只有当登录成功且存在待处理分组名称，且当前未处理同一请求时，才处理
				if (pendingName && this.userStore.isLoggedIn && !this.isProcessingPendingName) {
					// 检查是否短时间内重复处理同一分组名
					const currentTime = Date.now();
					const isSameGroup = pendingName === this.lastProcessedGroupName;
					const isRecentProcess = (currentTime - this.lastProcessTimestamp) < 3000; // 3秒内的重复请求

					if (isSameGroup && isRecentProcess) {
						// 直接清除存储数据，避免后续重复处理
						uni.removeStorageSync('pending_group_name');
						return;
					}

					this.checkPendingGroupName();
				}
			},
			
			// 检查是否有未完成的分组创建
			checkPendingGroupName() {
				try {
					// 如果已在处理中，避免重复处理
					if (this.isProcessingPendingName) {
						console.log('已有待处理分组正在处理中，跳过');
						return;
					}
					
					// 标记为处理中
					this.isProcessingPendingName = true;
					
					// 检查存储中是否有暂存的分组名称
					const pendingName = uni.getStorageSync('pending_group_name');
					if (pendingName) {
						console.log('检测到未完成的分组创建:', pendingName);
						
						// 检查登录状态
						if (this.userStore.isLoggedIn) {
							// 记录处理信息，用于防重复
							this.lastProcessedGroupName = pendingName;
							this.lastProcessTimestamp = Date.now();
							
							// 已登录状态，重新显示弹窗并填入之前输入的名称
							setTimeout(() => {
								// #ifdef MP-WEIXIN
								uni.showModal({
									title: '添加分组',
									placeholderText: '请输入分组名称',
									editable: true,
									content: pendingName,
									success: (res) => {
										// 无论成功或取消，都清除暂存数据
										uni.removeStorageSync('pending_group_name');
										
										if (res.confirm && res.content) {
											// 执行添加分组
											this.confirmAddGroup(res.content, true);
										}
										
										// 处理完成，重置状态
										this.isProcessingPendingName = false;
									},
									fail: () => {
										// 处理失败也清除数据和重置状态
										uni.removeStorageSync('pending_group_name');
										this.isProcessingPendingName = false;
									}
								});
								// #endif
								
								// #ifndef MP-WEIXIN
								uni.showModal({
									title: '添加分组',
									content: pendingName,
									editable: true,
									success: (res) => {
										// 无论成功或取消，都清除暂存数据
										uni.removeStorageSync('pending_group_name');
										
										if (res.confirm && res.content) {
											// 执行添加分组
											this.confirmAddGroup(res.content, true);
										}
										
										// 处理完成，重置状态
										this.isProcessingPendingName = false;
									},
									fail: () => {
										// 处理失败也清除数据和重置状态
										uni.removeStorageSync('pending_group_name');
										this.isProcessingPendingName = false;
									}
								});
								// #endif
							}, 500); // 延迟显示，确保页面已完全加载
						} else {
							// 如果未登录，也重置处理状态
							this.isProcessingPendingName = false;
						}
					} else {
						// 没有待处理内容，重置处理状态
						this.isProcessingPendingName = false;
					}
				} catch (e) {
					console.error('检查未完成的分组创建失败:', e);
					// 出错时也重置处理状态并清除可能的数据
					this.isProcessingPendingName = false;
					uni.removeStorageSync('pending_group_name');
				}
			},
			
			// 处理分享按钮点击
			handleShareClick() {
				// 记录下来当前分享的分组信息，给父组件使用
				this.prepareShareData();
				
				// 添加按钮动画效果
				this.animateShareButton();
				
				// 触发振动反馈
				try {
					uni.vibrateShort({
						success: () => {
							console.log('振动反馈成功');
						}
					});
				} catch (e) {
					console.error('振动触发失败:', e);
				}
				
				// 显示分享菜单（针对非微信小程序环境）
				if (uni.getPlatform && uni.getPlatform() !== 'mp-weixin') {
					try {
						uni.showShareMenu({
							withShareTicket: true,
							menus: ['shareAppMessage', 'shareTimeline'],
							success() {
								console.log('显示分享菜单成功');
							},
							fail(e) {
								console.log('显示分享菜单失败', e);
							}
						});
					} catch (err) {
						console.error('分享操作失败:', err);
					}
				}
			},
			
			// 准备分享数据
			prepareShareData() {
				const currentGroup = this.currentGroup;
				if (!currentGroup) return;
				
				// 获取当前分组下的所有心愿
				const groupWishes = this.wishStore.currentGroupWishes;
				const wishCount = groupWishes.length;
				
				const shareData = {
					groupId: currentGroup.id,
					groupName: currentGroup.name,
					wishCount: wishCount,
					timestamp: Date.now()
				};
				
				// 设置全局数据以便在页面的onShareAppMessage中获取
				uni.setStorageSync('current_share_group', shareData);
				
				// 也通过事件通知
				uni.$emit('share-group-trigger', {
					groupId: currentGroup.id,
					groupName: currentGroup.name,
					wishCount: wishCount,
					shareType: 'group'
				});
				
				console.log('分享数据已准备:', {
					groupId: currentGroup.id,
					groupName: currentGroup.name,
					shareType: 'group'
				});
				
				// 试图触发小程序的分享
				try {
					if (uni.canIUse && uni.canIUse('button.open-type.share')) {
						console.log('当前环境支持 open-type="share"');
					}
				} catch (e) {
					console.error('检查分享能力失败:', e);
				}
			},
			
			// 显示分享提示
			showShareTooltip() {
				// 触发振动反馈
				try {
					uni.vibrateShort();
				} catch (e) {
					console.error('振动触发失败:', e);
				}
				
				// 显示提示
				this.showTooltip = true;
				
				// 设置定时器关闭提示
				if (this.tooltipTimer) {
					clearTimeout(this.tooltipTimer);
				}
				
				this.tooltipTimer = setTimeout(() => {
					this.showTooltip = false;
				}, 2000);
			},
			
			// 选择分组
			selectGroup(groupId) {
				this.wishStore.setCurrentGroup(groupId)
				this.$emit('group-change', groupId)
			},
			
			// 显示添加分组弹窗
			showAddGroupModal() {
				// #ifdef MP-WEIXIN
				uni.showModal({
					title: '添加分组',
					placeholderText: '请输入分组名称',
					editable: true,
					success: (res) => {
						if (res.confirm && res.content) {
							// 用户点击确定，并且输入了内容
							// 保存输入的分组名称
							uni.setStorageSync('pending_group_name', res.content);
							this.confirmAddGroup(res.content)
						}
					}
				})
				// #endif
				
				// #ifndef MP-WEIXIN
				// 其他平台使用普通输入框
				uni.showModal({
					title: '添加分组',
					content: '',
					showCancel: true,
					editable: true,
					success: (res) => {
						if (res.confirm && res.content) {
							// 保存输入的分组名称
							uni.setStorageSync('pending_group_name', res.content);
							this.confirmAddGroup(res.content)
						}
					}
				})
				// #endif
			},
			
			// 确认添加分组
			async confirmAddGroup(value, skipStorageSave = false) {
				const name = value.trim()
				if (!name) {
					uni.showToast({
						title: '分组名称不能为空',
						icon: 'none'
					})
					// 如果名称为空，也应该清除暂存
					uni.removeStorageSync('pending_group_name');
					return
				}
				
				// 检查用户是否已登录
				if (!this.userStore.isLoggedIn) {
					console.log('用户未登录，跳转到登录页面');
					
					// 如果没有跳过存储保存，且我们需要重定向到登录
					if (!skipStorageSave) {
						// 已经在showAddGroupModal中保存了名称，但为了安全再次保存
						uni.setStorageSync('pending_group_name', name);
						
						// 设置登录中状态
						this.isLoggingIn = true;
						
						// 重定向到登录页面
						this.userStore.checkLoginAndRedirect();
					}
					return;
				}
				
				// 检查是否已存在同名分组
				if (this.groupStore._checkGroupNameExists && this.groupStore._checkGroupNameExists(name)) {
					uni.showToast({
						title: '分组名称已存在',
						icon: 'none'
					})
					// 名称已存在，清除暂存
					uni.removeStorageSync('pending_group_name');
					return
				}
				
				try {
					console.log('[GroupSelector] Starting to add group:', name);
					
					// 添加分组（异步操作）
					const groupId = await this.groupStore.addGroup(name)
					
					console.log('[GroupSelector] addGroup returned:', groupId);
					
					if (groupId) {
						// 立即清除临时保存的分组名称
						uni.removeStorageSync('pending_group_name');
						
						console.log('[GroupSelector] Before setting current group - wishStore.currentGroupId:', this.wishStore.currentGroupId);
						console.log('[GroupSelector] Available groups:', this.groupStore.getAllGroups.map(g => ({id: g.id, name: g.name})));
						
						// 立即切换到新分组
						this.wishStore.setCurrentGroup(groupId)
						this.$emit('group-change', groupId)
						
						console.log('[GroupSelector] After setting current group - wishStore.currentGroupId:', this.wishStore.currentGroupId);
						console.log('[GroupSelector] Computed currentGroupId:', this.currentGroupId);
						
						// 强制刷新分组缓存和组件
						this.groupsVersion = 0;
						this.cachedGroups = [];
						this.$forceUpdate();
						
						// 再次检查状态
						this.$nextTick(() => {
							console.log('[GroupSelector] After $nextTick - currentGroupId:', this.currentGroupId);
							console.log('[GroupSelector] Groups in computed:', this.groups.map(g => ({id: g.id, name: g.name, active: this.currentGroupId === g.id})));
						});
						
						uni.showToast({
							title: '创建成功',
							icon: 'success'
						})
					} else {
						// 创建失败，也清除暂存
						uni.removeStorageSync('pending_group_name');
						
						uni.showToast({
							title: '创建失败',
							icon: 'none'
						})
					}
				} catch (e) {
					// 发生异常，也清除暂存
					uni.removeStorageSync('pending_group_name');
					
					console.error('添加分组失败:', e)
					uni.showToast({
						title: '创建失败',
						icon: 'none'
					})
				}
				
				// 无论成功与否，都重置处理状态
				this.isProcessingPendingName = false;
			},
			
			// 按钮动画效果
			animateShareButton() {
				this.isShareButtonActive = true;
				
				// 设置定时器移除动画类
				setTimeout(() => {
					this.isShareButtonActive = false;
				}, 600); // 与动画时长匹配
				
				// 成功反馈
				setTimeout(() => {
					try {
						uni.showToast({
							title: '已准备分享',
							icon: 'success',
							duration: 1500
						});
					} catch(e) {
						console.error('显示成功提示失败:', e);
					}
				}, 200);
			},
			
			// 调试：手动触发数据修复（长按分享按钮5秒）
			onShareButtonLongPress() {
				console.log('[GroupSelector] Long press detected on share button');
				
				uni.showModal({
					title: '调试功能',
					content: '是否要修复分组数据？这会清理重复分组并修复排序。',
					success: async (res) => {
						if (res.confirm) {
							try {
								const result = await this.groupStore.repairGroupData();
								console.log('[GroupSelector] Group data repair result:', result);
								
								// 强制刷新当前页面的数据
								this.groupsVersion = 0; // 重置缓存版本
								
								uni.showToast({
									title: '数据修复完成',
									icon: 'success',
									duration: 2000
								});
							} catch (error) {
								console.error('[GroupSelector] Group data repair failed:', error);
								uni.showToast({
									title: '修复失败',
									icon: 'none',
									duration: 2000
								});
							}
						}
					}
				});
			},
			
			// 处理分组长按事件（带调试信息）
			handleGroupLongPress(group) {
				console.log('[GroupSelector] Group long press triggered');
				console.log('[GroupSelector] Group object:', JSON.stringify(group));
				
				// 验证分组对象
				if (!group) {
					console.error('[GroupSelector] 分组对象为空');
					uni.showToast({
						title: '分组数据错误',
						icon: 'none'
					});
					return;
				}
				
				// 确保分组有 id 字段（优先使用 id，其次使用 _id）
				const groupId = group.id || group._id;
				
				console.log('[GroupSelector] Group id:', groupId);
				console.log('[GroupSelector] Group name:', group?.name);
				console.log('[GroupSelector] Group isDefault:', group?.isDefault);
				
				if (!groupId) {
					console.error('[GroupSelector] 分组ID为空, group:', group);
					uni.showToast({
						title: '分组ID缺失',
						icon: 'none'
					});
					return;
				}
				
				// 创建带有正确 id 的分组对象
				const normalizedGroup = {
					...group,
					id: groupId
				};
				
				// 调用 mixin 的方法
				this.showGroupActionSheet(normalizedGroup);
			},
			

			

		}
	}
</script>

<style lang="scss">
	.group-selector {
		padding: 20rpx 0;
		background-color: #fff;
		position: sticky;
		top: 0;
		z-index: 100;
		position: relative; /* 为分享按钮绝对定位提供参考 */
		
		.group-scroll-view {
			width: 100%;
			white-space: nowrap;
			padding-right: 54rpx; /* 为固定在右侧的分享按钮预留空间 */
		}
		
		.group-list {
			padding: 0 20rpx;
			display: inline-flex;
			
			.group-item {
				height: 56rpx;
				padding: 0 22rpx;
				margin-right: 8rpx;
				background-color: #f0f0f0;
				border-radius: 28rpx;
				font-size: 28rpx;
				color: #666;
				display: flex;
				align-items: center;
				justify-content: center;
				text-align: center;
				box-sizing: border-box;
				line-height: 1; /* 确保文字垂直居中 */
				
				.group-text {
					display: inline-block;
					line-height: 1;
				}
				
				&.is-default {
					background-color: rgba(138, 43, 226, 0.1);
					border: 1px solid rgba(138, 43, 226, 0.2);
					color: #8a2be2;
				}
				
				&.active {
					background-color: #8a2be2;
					color: #fff;
				}
				
				&.add-group {
					background-color: #f0e6ff;
					border: 1px dashed #8a2be2;
					color: #8a2be2;
					position: relative;
					
					.add-icon-wrapper {
						position: absolute;
						top: 0;
						left: 0;
						width: 100%;
						height: 100%;
						display: flex;
						align-items: center;
						justify-content: center;
					}
					
					.plus-icon {
						font-size: 40rpx;
						font-weight: bold;
						color: #8a2be2;
						height: 40rpx;
						line-height: 40rpx;
						text-align: center;
						padding-bottom: 4rpx; /* 微调加号位置 */
					}
				}
			}
			
			/* 添加占位元素样式 */
			.spacing-placeholder {
				width: 54rpx; /* 与右侧预留空间一致 */
				height: 1rpx;
				display: inline-block;
			}
		}
		

		

		
		/* 分享按钮样式 - 固定在右侧 */
		.share-group-btn {
			width: 54rpx;
			min-width: 50rpx; /* 确保最小宽度 */
			height: 70rpx;
			border-radius: 50% 0 0 50%; /* 左侧保持圆形，右侧平直 */
			background-color: #8a2be2;
			display: flex;
			align-items: center;
			justify-content: center;
			padding-left: 8rpx; /* 减小左边距 */
			padding-right: 0;
			margin: 0;
			box-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.3);
			position: absolute;
			top: 50%;
			right: 0; /* 紧贴右边框 */
			transform: translateY(-50%);
			z-index: 101; /* 确保在滚动内容之上 */
			transition: all 0.2s ease;
			overflow: hidden; /* 用于容纳波纹效果 */
			line-height: 1; /* 重置line-height */
			
			/* 重置button的默认样式 */
			&::after {
				border: none;
			}
			
			.share-icon {
				width: 34rpx; /* 缩小图标尺寸 */
				height: 34rpx;
				filter: brightness(5); /* 使图标变白 */
				position: relative;
				z-index: 2; /* 确保图标在波纹之上 */
			}
			
			/* 波纹效果 */
			.share-btn-ripple {
				position: absolute;
				top: 50%;
				left: 30%; /* 更加偏左，适应更窄的半圆形 */
				width: 0;
				height: 0;
				background-color: rgba(255, 255, 255, 0.4);
				border-radius: 50%;
				transform: translate(-50%, -50%);
				z-index: 1;
				opacity: 0;
			}
			
			/* 悬浮提示 */
			.share-tooltip {
				position: absolute;
				right: 65rpx;
				padding: 10rpx 16rpx;
				background-color: rgba(0, 0, 0, 0.7);
				color: #fff;
				border-radius: 6rpx;
				font-size: 24rpx;
				white-space: nowrap;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
				opacity: 0;
				transform: translateX(10rpx);
				transition: all 0.3s ease;
				
				/* 添加小三角形 */
				&:after {
					content: '';
					position: absolute;
					right: -8rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 0;
					height: 0;
					border-style: solid;
					border-width: 8rpx 0 8rpx 8rpx;
					border-color: transparent transparent transparent rgba(0, 0, 0, 0.7);
				}
			}
			
			&.show-tooltip .share-tooltip {
				opacity: 1;
				transform: translateX(0);
			}
			
			&.active .share-btn-ripple {
				animation: ripple 0.6s ease-out;
			}
			
			@keyframes ripple {
				0% {
					width: 0;
					height: 0;
					opacity: 0.5;
				}
				100% {
					width: 100rpx;
					height: 100rpx;
					opacity: 0;
				}
			}
			
			&:active {
				transform: translateY(-50%) scale(0.92);
				box-shadow: 0 2rpx 8rpx rgba(138, 43, 226, 0.2);
				opacity: 0.9;
				padding-left: 4rpx; /* 点击时左内边距减小，视觉效果更好 */
			}
		}
	}
</style> 
<template>
	<view class="wish-detail-container">
		<!-- 心愿详情 -->
		<view class="wish-detail card">
			<view class="wish-header">
				<view class="wish-title-wrap">
					<view class="wish-title">{{ wish?.title || '心愿详情' }}</view>
				</view>
				<view class="wish-status">
					<view v-if="wish?.permission === 'private'" class="tag private-tag">
						<uni-icons type="lock-filled" size="12" color="#fff"></uni-icons>
						<text>私密</text>
					</view>
					<view v-else-if="wish?.permission === 'friends'" class="tag friends-tag">
						<uni-icons type="people-filled" size="12" color="#fff"></uni-icons>
						<text>朋友可见</text>
					</view>
					<view v-else-if="wish?.permission === 'public'" class="tag public-tag">
						<uni-icons type="eye-filled" size="12" color="#fff"></uni-icons>
						<text>公开</text>
					</view>
					<view v-if="wish?.isCompleted" class="tag completed-tag">
						<uni-icons type="checkmarkempty" size="12" color="#fff"></uni-icons>
						<text>已完成</text>
					</view>
				</view>
			</view>
			
			<view class="wish-content">
				<view class="wish-desc" v-if="wish?.description">{{ wish.description }}</view>
				
				<!-- 单图情况 -->
				<image v-if="singleImageUrl"
					   class="wish-image"
					   :src="singleImageUrl"
					   mode="widthFix"
					   @click="previewImages(0)"
					   @error="onImageError"></image>

				<!-- 多图情况，使用轮播 -->
				<swiper v-else-if="convertedImages.length > 0"
					class="image-swiper"
					:indicator-dots="true"
					:autoplay="false"
					:duration="500"
					indicator-active-color="#8a2be2">
					<swiper-item v-for="(img, index) in convertedImages" :key="index">
						<image class="swiper-image"
							   :src="img"
							   mode="widthFix"
							   @click="previewImages(index)"
							   @error="onImageError"></image>
					</swiper-item>
				</swiper>
				
				<view class="wish-time-info">
					<view class="time-item">
						<text class="time-label">创建时间：</text>
						<text class="time-value">{{ formatTime(wish?.createDate) }}</text>
					</view>
					<view class="time-item" v-if="wish?.startDate">
						<text class="time-label">开始时间：</text>
						<text class="time-value">{{ formatTime(wish?.startDate) }}</text>
					</view>
					<view class="time-item" v-if="wish?.completeDate || wish?.lastCompleteDate">
						<text class="time-label">完成时间：</text>
						<text class="time-value">{{ getCompleteTime() }}</text>
					</view>
				</view>
			</view>
			
			<view class="wish-actions">
				<view class="action-btn" @click="editWish">
					<uni-icons type="compose" size="18" color="#8a2be2"></uni-icons>
					<text>编辑</text>
				</view>
				<button open-type="share" class="action-btn share-btn" @click="shareWish">
					<uni-icons type="redo-filled" size="18" color="#8a2be2"></uni-icons>
					<text>分享</text>
				</button>
				<view class="action-btn" v-if="!wish?.isCompleted" @click="completeWish">
					<uni-icons type="checkbox-filled" size="18" color="#8a2be2"></uni-icons>
					<text>完成</text>
				</view>
				<view class="action-btn delete" @click="showDeleteConfirm">
					<uni-icons type="trash-filled" size="18" color="#f56c6c"></uni-icons>
					<text>删除</text>
				</view>
			</view>
		</view>
		
		<!-- 分组信息 -->
		<view class="wish-groups card">
			<view class="section-title">
				<text>所属分组</text>
			</view>
			<view class="group-tags">
				<view 
					v-for="groupId in wish?.groupIds" 
					:key="groupId"
					class="group-tag"
					@click="navigateToGroup(groupId)"
				>
					{{ getGroupName(groupId) }}
				</view>
			</view>
		</view>
		
		<!-- 评论区 -->
		<view class="wish-comments card" @click="hideDeleteButton">
			<view class="section-title">
				<text>评论区</text>
				<text class="comment-count">{{ comments.length || 0 }}条评论</text>

			</view>
			
			<view v-if="!comments || comments.length === 0" class="empty-comment">
				<text>暂无评论，快来添加第一条评论吧</text>
			</view>
			
			<view v-else class="comment-list">
				<view 
					v-for="(comment, index) in comments" 
					:key="comment._id"
					class="comment-item"
					@longpress="handleCommentLongPress(comment, $event)"
					@touchstart="handleTouchStart"
					@touchend="handleTouchEnd"
					@click.stop
				>
					<image class="comment-avatar" :src="comment.user_info?.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
					<view class="comment-content">
						<view class="comment-user">
							<text class="comment-nickname">{{ comment.user_info?.nickname || '匿名用户' }}</text>
							<text class="comment-time">{{ formatTime(comment.createDate) }}</text>
						</view>
						<view class="comment-text">{{ comment.content }}</view>
						<image v-if="comment.image && comment.image.length > 0" class="comment-image" :src="comment.image[0]" mode="widthFix"></image>
					</view>
				</view>
			</view>
			
			<!-- 悬浮删除按钮 -->
			<view 
				v-if="showDeleteBtn && canDeleteComment(selectedComment)"
				class="floating-delete-btn"
				:style="deleteButtonStyle"
				@click.stop="showDeleteCommentConfirm(selectedComment)"
			>
				<uni-icons type="trash-filled" size="16" color="#fff"></uni-icons>
				<text>删除</text>
			</view>
			
			<!-- 评论框 -->
			<view class="comment-form">
				<input 
					class="comment-input"
					v-model="commentText"
					placeholder="添加评论..."
					confirm-type="send"
					@confirm="submitComment"
				/>
				<view class="comment-btn" @click="submitComment">
					<uni-icons type="paperplane-filled" size="20" color="#8a2be2"></uni-icons>
				</view>
			</view>
		</view>
		
		<!-- 删除心愿确认弹窗 -->
		<uni-popup ref="deletePopup" type="dialog">
			<uni-popup-dialog
				title="删除心愿"
				content="确定要删除这个心愿吗？删除后无法恢复。"
				:before-close="true"
				@confirm="confirmDelete"
				@close="closeDeleteConfirm"
			></uni-popup-dialog>
		</uni-popup>
		
		<!-- 删除评论确认弹窗 -->
		<uni-popup ref="deleteCommentPopup" type="dialog">
			<uni-popup-dialog
				title="删除评论"
				content="确定要删除这条评论吗？删除后无法恢复。"
				:before-close="true"
				@confirm="confirmDeleteComment"
				@close="closeDeleteCommentConfirm"
			></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import { useWishStore } from '@/store/wish.js'
	import { useGroupStore } from '@/store/group.js'
	import { useCommentStore } from '@/store/comment.js'
	import { useUserStore } from '@/store/user.js'
	import loadingManager from '@/utils/loadingManager.js'
	import groupTagOperations from '@/mixins/groupTagOperations.js'
	import { convertCloudUrl } from '@/utils/imageUtils.js'
	
	export default {
		mixins: [groupTagOperations],
		// 微信小程序分享给朋友
		onShareAppMessage(res) {
			// 获取当前心愿详情
			const wish = this.wish || {}
			return {
				title: wish.title || '分享我的心愿',
				path: `/pages/wishDetail/wishDetail?id=${this.wishId}`,
				imageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? 
					wish.image[0] : (wish.image || '/static/images/share-image.png')
			}
		},
		// 微信小程序分享到朋友圈
		onShareTimeline() {
			// 获取当前心愿详情
			const wish = this.wish || {}
			return {
				title: wish.title || '分享我的心愿',
				query: `id=${this.wishId}`,
				imageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? 
					wish.image[0] : (wish.image || '/static/images/share-image.png')
			}
		},
		setup() {
			const wishStore = useWishStore()
			const groupStore = useGroupStore()
			const commentStore = useCommentStore()
			const userStore = useUserStore()
			
			return {
				wishStore,
				groupStore,
				commentStore,
				userStore
			}
		},
		data() {
			return {
				wishId: '',
				wish: null,
				commentText: '',
				comments: [],
				newComment: '',
				commentImages: [],
				showCommentDialog: false,
				isLoadingComments: false,
				isPageLoading: true, // 页面整体加载状态
				commentToDelete: null, // 待删除的评论
				showDeleteBtn: false, // 是否显示悬浮删除按钮
				selectedComment: null, // 当前选中的评论
				// 图片URL转换
				convertedImages: [], // 转换后的图片URL数组
				singleImageUrl: '' // 单图转换后的URL
				deleteButtonPosition: { x: 0, y: 0 }, // 删除按钮位置
				longPressTimeout: null // 长按定时器
			}
		},
		computed: {
			// 获取分组名称映射（计算属性，避免重复调用）
			groupNameMap() {
				if (!this.groupStore || !this.groupStore.getAllGroups) {
					return {};
				}

				const map = {};
				this.groupStore.getAllGroups.forEach(group => {
					map[group._id || group.id] = group.name;
				});
				return map;
			},

			// 格式化心愿状态
			wishStatus() {
				if (!this.wish) return ''
				return this.wish.isCompleted ? '已完成' : '进行中'
			},

			// 格式化权限显示
			permissionText() {
				if (!this.wish) return ''
				const permissionMap = {
					'private': '私密',
					'friends': '朋友可见', 
					'public': '公开'
				}
				return permissionMap[this.wish.permission] || '未知'
			},
			
			// 删除按钮样式
			deleteButtonStyle() {
				return {
					left: this.deleteButtonPosition.x + 'px',
					top: this.deleteButtonPosition.y + 'px'
				}
			},
			

		},
		async onLoad(options) {
			if (options.id) {
				this.wishId = options.id
				this.isPageLoading = true

				// 清理任何可能残留的加载状态
				this.clearAllLoadingStates()

				try {
					console.log('[wishDetail] 开始初始化页面...')

					// 设置一个总体超时，确保页面不会无限加载
					const pageTimeout = setTimeout(() => {
						console.warn('[wishDetail] 页面加载超时，强制结束加载状态')
						this.isPageLoading = false
						this.isLoadingComments = false
						this.clearAllLoadingStates()
					}, 12000) // 12秒总超时

					await this.initStores()
					this.loadWishDetail()

					// 强制刷新评论，确保获取最新数据
					await this.loadComments(true)

					clearTimeout(pageTimeout)
					console.log('[wishDetail] 页面初始化完成')
				} catch (error) {
					console.error('页面初始化失败:', error)
					this.clearAllLoadingStates()
				} finally {
					this.isPageLoading = false
					this.clearAllLoadingStates()
				}
			}
		},
		

		// 页面每次显示时执行，确保数据最新
		onShow() {
			// 避免在页面初始加载时重复执行
			if (this.isPageLoading) {
				return
			}

			// 清理可能残留的加载状态
			this.clearAllLoadingStates()

			// 从本地存储刷新心愿列表数据
			this.wishStore.refreshWishList();

			// 重新加载当前心愿详情，确保显示最新数据
			if (this.wishId) {
				this.loadWishDetail();

				// 每次显示都强制同步评论数据
				this.loadComments(true);
			}
		},

		// 页面隐藏时清理加载状态
		onHide() {
			this.clearAllLoadingStates()
		},
		methods: {
			// 转换图片URL（处理云存储）
			async convertImageUrls() {
				if (!this.wish || !this.wish.image) {
					this.convertedImages = [];
					this.singleImageUrl = '';
					return;
				}

				try {
					if (typeof this.wish.image === 'string') {
						// 单图情况
						this.singleImageUrl = await convertCloudUrl(this.wish.image);
						this.convertedImages = [];
					} else if (Array.isArray(this.wish.image) && this.wish.image.length > 0) {
						// 多图情况
						this.singleImageUrl = '';
						this.convertedImages = await Promise.all(
							this.wish.image.map(img => convertCloudUrl(img))
						);
					}
				} catch (error) {
					console.error('[wishDetail] 图片URL转换失败:', error);
					// 转换失败时使用原始URL
					if (typeof this.wish.image === 'string') {
						this.singleImageUrl = this.wish.image;
					} else if (Array.isArray(this.wish.image)) {
						this.convertedImages = this.wish.image;
					}
				}
			},

			// 初始化stores
			async initStores() {
				this.wishStore = useWishStore()
				this.groupStore = useGroupStore()
				this.commentStore = useCommentStore()
				this.userStore = useUserStore()

				// 初始化分组store，添加超时处理
				try {
					const timeoutPromise = new Promise((_, reject) => {
						setTimeout(() => reject(new Error('分组初始化超时')), 8000) // 8秒超时
					})

					await Promise.race([this.groupStore.initGroups(), timeoutPromise])
				} catch (error) {
					console.error('分组Store初始化失败:', error)
					// 确保有默认分组可用
					this.groupStore.ensureDefaultGroups()
				}

				// 🚀 使用绑定同步检查替代传统的评论初始化
				await this.checkAndSyncWishAndComments()

				// 设置同步监听
				this._setupSyncListeners()
			},

			// 🚀 检查并同步心愿和评论数据（同步合并：同时调用但不合并内容）
			async checkAndSyncWishAndComments() {
				if (!this.wishId) return;

				console.log('[wishDetail] 检查心愿和评论数据同步状态...');

				try {
					// 🚀 分别检查：减少不必要的数据传输
					const wishNeedsSync = await this.checkWishNeedsSync();
					const commentsNeedSync = await this.checkCommentsNeedSync();

					if (wishNeedsSync || commentsNeedSync) {
						console.log('[wishDetail] 需要同步数据:', {
							wishNeedsSync,
							commentsNeedSync
						});

						// 🚀 同步合并：同时调用但只同步需要的数据，减少传输
						const syncPromises = [];

						if (wishNeedsSync) {
							console.log('[wishDetail] 添加心愿数据同步');
							syncPromises.push(this.wishStore.syncFromCloud({ silent: true }));
						}

						if (commentsNeedSync) {
							console.log('[wishDetail] 添加评论数据同步');
							syncPromises.push(this.commentStore.syncCommentsForWish(this.wishId, true, true));
						}

						// 同时执行需要的同步操作
						await Promise.allSettled(syncPromises);
						console.log('[wishDetail] 同步合并完成');
					} else {
						console.log('[wishDetail] 数据无需同步，使用本地缓存');
					}
				} catch (error) {
					console.error('[wishDetail] 同步合并失败:', error);
				}
			},

			// 检查心愿数据是否需要同步
			async checkWishNeedsSync() {
				if (!this.wishStore || !this.wishId) return true;

				const hasLocalWish = this.wishStore.getWishById(this.wishId);
				const lastSyncTime = this.wishStore.lastSyncTime;
				const now = Date.now();

				// 如果没有本地数据，或者超过5分钟未同步
				return !hasLocalWish || !lastSyncTime || (now - lastSyncTime > 5 * 60 * 1000);
			},

			// 检查评论数据是否需要同步
			async checkCommentsNeedSync() {
				if (!this.commentStore || !this.wishId) return true;

				const hasLocalComments = this.commentStore.commentsByWish[this.wishId] &&
										this.commentStore.commentsByWish[this.wishId].length >= 0; // 允许空数组
				const lastSyncTime = this.commentStore.syncTimestamps[this.wishId];
				const now = Date.now();

				// 如果没有本地评论数据，或者超过5分钟未同步
				return !hasLocalComments || !lastSyncTime || (now - lastSyncTime > 5 * 60 * 1000);
			},


			
			// 设置同步监听器
			_setupSyncListeners() {
				// 监听评论同步状态
				this.commentStore.on && this.commentStore.on('sync_completed', () => {
					console.log('评论数据同步完成')
					// 刷新当前页面的评论列表
					if (this.wishId) {
						this.loadComments()
					}
				})
			},
			

			
			// 加载心愿详情
			async loadWishDetail() {
				this.wish = this.wishStore.getWishById(this.wishId)

				if (!this.wish) {
					uni.showToast({
						title: '心愿不存在',
						icon: 'none'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
					return;
				}

				// 转换图片URL
				await this.convertImageUrls();
			},

			// 图片加载错误处理
			onImageError(e) {
				console.error('[wishDetail] 图片加载失败:', {
					src: e?.target?.src || e?.detail?.src,
					wishId: this.wishId,
					error: e
				});
			},
			
			// 加载评论列表
			async loadComments(refresh = false) {
				if (!this.wishId) return

				this.isLoadingComments = true

				try {
					// 使用统一加载管理器，静默加载（不显示弹窗）
					this.comments = await loadingManager.wrap(
						() => this.commentStore.loadComments(this.wishId, refresh),
						{
							title: '加载评论中...',
							id: `load_comments_${this.wishId}`,
							timeout: 8000,
							silent: true, // 静默加载，不显示弹窗
							showError: false // 不显示错误提示，由页面处理
						}
					)
				} catch (error) {
					console.error('加载评论失败:', error)
					// 失败时，尝试使用本地缓存数据
					this.comments = this.commentStore.commentsByWish[this.wishId] || []
				} finally {
					this.isLoadingComments = false
				}
			},
			
			// 显示评论输入框
			showCommentInput() {
				// 检查登录状态
				if (!this.userStore.checkLoginAndRedirect()) {
					return
				}
				
				this.showCommentDialog = true
			},
			
			// 发布评论
			async submitComment() {
				if (!this.commentText.trim()) {
					uni.showToast({
						title: '请输入评论内容',
						icon: 'none'
					})
					return
				}

				let loadingShown = false

				try {
					uni.showLoading({ title: '发布中...' })
					loadingShown = true

					const commentData = {
						content: this.commentText.trim(),
						image: []
					}

					await this.commentStore.addComment(this.wishId, commentData)

					// 立即强制刷新评论列表，确保数据同步
					await this.loadComments(true)

					// 清空输入
					this.commentText = ''

					uni.showToast({
						title: '评论成功',
						icon: 'success'
					})
				} catch (error) {
					console.error('发布评论失败:', error)
					uni.showToast({
						title: error.message || '评论发布失败',
						icon: 'none'
					})
				} finally {
					if (loadingShown) {
						uni.hideLoading().catch(() => {})
					}
					// 额外清理，确保没有残留的加载状态
					this.clearAllLoadingStates()
				}
			},
			
			// 检查是否可以删除评论（评论作者或心愿作者）
			canDeleteComment(comment) {
				if (!this.userStore.isLoggedIn || !comment) return false
				
				const currentUserId = this.userStore.userId || this.userStore.userInfo?.uid
				const isCommentAuthor = comment.userId === currentUserId
				const isWishAuthor = this.wish?.userId === currentUserId
				
				return isCommentAuthor || isWishAuthor
			},
			
			// 处理评论长按事件
			handleCommentLongPress(comment, event) {
				if (!this.canDeleteComment(comment)) {
					uni.showToast({
						title: '无权限删除',
						icon: 'none'
					})
					return
				}
				
				// 获取触摸位置
				let touchX = 100
				let touchY = 200
				
				// 尝试多种方式获取触摸位置
				if (event && event.detail && (event.detail.x !== undefined || event.detail.y !== undefined)) {
					// 小程序长按事件的位置信息在 detail 中
					touchX = event.detail.x || touchX
					touchY = event.detail.y || touchY
				} else if (event && event.touches && event.touches.length > 0) {
					// 触摸事件
					const touch = event.touches[0]
					touchX = touch.clientX || touch.pageX || touchX
					touchY = touch.clientY || touch.pageY || touchY
				} else if (event && event.changedTouches && event.changedTouches.length > 0) {
					// 触摸结束事件
					const touch = event.changedTouches[0]
					touchX = touch.clientX || touch.pageX || touchX
					touchY = touch.clientY || touch.pageY || touchY
				}
				
				// 获取屏幕信息来调整位置
				const systemInfo = uni.getSystemInfoSync()
				const screenWidth = systemInfo.screenWidth || 375
				const screenHeight = systemInfo.screenHeight || 667
				
				// 调整删除按钮位置，确保不超出屏幕
				const buttonWidth = 80   // 删除按钮宽度（大约等于padding + 图标 + 文字）
				const buttonHeight = 50  // 删除按钮高度
				
				// 计算最终位置（在触摸点上方偏左显示）
				let finalX = touchX - buttonWidth / 2  // 水平居中在触摸点
				let finalY = touchY - buttonHeight - 10  // 在触摸点上方10px
				
				// 边界检查
				if (finalX < 10) {
					finalX = 10  // 距离左边界至少10px
				}
				if (finalX + buttonWidth > screenWidth - 10) {
					finalX = screenWidth - buttonWidth - 10  // 距离右边界至少10px
				}
				if (finalY < 10) {
					finalY = touchY + 10  // 如果上方空间不够，显示在下方
				}
				if (finalY + buttonHeight > screenHeight - 50) {
					finalY = screenHeight - buttonHeight - 50  // 留出底部空间
				}
				
				this.deleteButtonPosition = {
					x: finalX,
					y: finalY
				}
				
				this.selectedComment = comment
				this.showDeleteBtn = true
				
				// 3秒后自动隐藏删除按钮
				setTimeout(() => {
					this.hideDeleteButton()
				}, 3000)
				
				// 震动反馈
				try {
					uni.vibrateShort()
				} catch (error) {
					// 震动失败不影响功能
				}
			},
			
			// 处理触摸开始
			handleTouchStart(event) {
				// 不要立即隐藏删除按钮，让长按事件有机会触发
			},
			
			// 处理触摸结束
			handleTouchEnd() {
				// 可以在这里添加其他逻辑
			},
			
			// 隐藏删除按钮
			hideDeleteButton() {
				this.showDeleteBtn = false
				this.selectedComment = null
			},
			

			
			// 显示删除评论确认弹窗
			showDeleteCommentConfirm(comment) {
				this.commentToDelete = comment
				this.hideDeleteButton() // 隐藏悬浮按钮
				this.$refs.deleteCommentPopup.open()
			},
			
			// 确认删除评论
			async confirmDeleteComment() {
				if (!this.commentToDelete) return

				let loadingShown = false

				try {
					uni.showLoading({ title: '删除中...' })
					loadingShown = true

					await this.commentStore.deleteComment(this.wishId, this.commentToDelete._id)

					// 立即强制刷新评论列表，确保数据同步
					await this.loadComments(true)

					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				} catch (error) {
					console.error('删除评论失败:', error)
					uni.showToast({
						title: error.message || '删除失败',
						icon: 'none'
					})
				} finally {
					if (loadingShown) {
						uni.hideLoading().catch(() => {})
					}
					this.commentToDelete = null
					this.$refs.deleteCommentPopup.close()
					// 额外清理，确保没有残留的加载状态
					this.clearAllLoadingStates()
				}
			},
			
			// 关闭删除评论确认弹窗
			closeDeleteCommentConfirm() {
				this.commentToDelete = null
				this.$refs.deleteCommentPopup.close()
			},
			
			// 删除评论（原有方法保留，可能其他地方调用）
			async deleteComment(commentId) {
				try {
					await this.commentStore.deleteComment(this.wishId, commentId)
					
					// 立即强制刷新评论列表，确保数据同步
					await this.loadComments(true)
					
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				} catch (error) {
					console.error('删除评论失败:', error)
				}
			},
			
			// 预览图片
			previewImages(current = 0) {
				let urls = [];

				if (this.singleImageUrl) {
					// 单图情况
					urls = [this.singleImageUrl];
				} else if (this.convertedImages.length > 0) {
					// 多图情况
					urls = this.convertedImages;
				} else {
					return;
				}

				uni.previewImage({
					current: current,
					urls: urls
				})
			},
			
			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return ''
				
				try {
					const date = new Date(dateStr)
					if (isNaN(date.getTime())) return dateStr
					
					const now = new Date()
					const diff = now - date
					
					// 小于1分钟
					if (diff < 60000) {
						return '刚刚'
					}
					// 小于1小时
					if (diff < 3600000) {
						return `${Math.floor(diff / 60000)}分钟前`
					}
					// 小于1天
					if (diff < 86400000) {
						return `${Math.floor(diff / 3600000)}小时前`
					}
					// 小于7天
					if (diff < 604800000) {
						return `${Math.floor(diff / 86400000)}天前`
					}
					
					// 超过7天显示具体日期
					return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
				} catch (error) {
					console.error('日期格式化错误:', error)
					return dateStr
				}
			},
			
			// 编辑心愿
			editWish() {
				uni.navigateTo({
					url: `/subpkg-wish/pages/editWish/editWish?id=${this.wishId}`
				})
			},
			
			// 完成心愿
			async completeWish() {
				try {
					await this.wishStore.completeWish(this.wishId)
					this.wish.isCompleted = true
					this.wish.completeDate = new Date().toISOString()

					uni.showToast({
						title: '心愿已完成',
						icon: 'success'
					})
				} catch (error) {
					console.error('完成心愿失败:', error)
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				}
			},
			
			// 恢复心愿
			async restoreWish() {
				try {
					await this.wishStore.restoreWish(this.wishId)
					this.wish.isCompleted = false
					this.wish.completeDate = null

					uni.showToast({
						title: '心愿已恢复',
						icon: 'success'
					})
				} catch (error) {
					console.error('恢复心愿失败:', error)
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				}
			},
			
			// 获取分组名称（优化版本，使用缓存）
			getGroupName(groupId) {
				if (!groupId) return '未知分组';

				// 使用缓存的分组名称映射
				const name = this.groupNameMap[groupId];
				return name || '未知分组';
			},

			// 清理所有加载状态
			clearAllLoadingStates() {
				// 使用统一加载管理器强制清理所有加载状态
				loadingManager.forceHideAll()

				// 重置组件内部的加载状态
				this.isLoadingComments = false
				this.isPageLoading = false

				console.log('[wishDetail] 已清理所有加载状态')
			},
			
			// 格式化时间
			formatTime(timeString) {
				if (!timeString) return '无';
				
				// 如果原始字符串中已经包含"(已还原)"，移除它再处理
				let cleanTimeString = timeString;
				if (typeof cleanTimeString === 'string' && cleanTimeString.includes('(已还原)')) {
					cleanTimeString = cleanTimeString.replace('(已还原)', '').trim();
				}
				
				try {
					// 尝试创建日期对象
					const date = new Date(cleanTimeString);
					
					// 检查日期是否有效
					if (isNaN(date.getTime())) {
						console.error('无效的日期字符串:', cleanTimeString);
						// 确保返回的字符串不包含"(已还原)"
						return typeof cleanTimeString === 'string' 
							? cleanTimeString.replace(/\s*\(已还原\)\s*/g, '') 
							: '无';
					}
					
					return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
				} catch (error) {
					console.error('日期格式化错误:', error);
					// 确保返回的字符串不包含"(已还原)"
					return typeof cleanTimeString === 'string' 
						? cleanTimeString.replace(/\s*\(已还原\)\s*/g, '') 
						: '无';
				}
			},
			
			// 获取完成时间（显示completeDate或lastCompleteDate）
			getCompleteTime() {
				// 如果是已完成状态，显示完成时间
				if (this.wish?.isCompleted && this.wish?.completeDate) {
					return this.formatTime(this.wish.completeDate);
				} 
				
				// 检查未完成但曾经完成过的心愿
				// 1. 心愿未完成
				// 2. 拥有lastCompleteDate字段(表示曾经被完成过)
				if (!this.wish?.isCompleted && this.wish?.lastCompleteDate) {
					// 直接返回格式化后的时间，不添加任何标记
					return this.formatTime(this.wish.lastCompleteDate);
				}
				
				// 兼容旧数据：有completeDate但isCompleted为false，且没有lastCompleteDate字段
				if (!this.wish?.isCompleted && this.wish?.completeDate && !this.wish?.lastCompleteDate) {
					// 直接返回格式化后的时间，不添加任何标记
					return this.formatTime(this.wish.completeDate);
				}
				
				// 其他情况（如：从未完成过的心愿）
				return '无';
			},
			
			// 分享心愿
			shareWish() {
				try {
					// 显示分享菜单
					uni.showShareMenu({
						withShareTicket: true,
						menus: ['shareAppMessage', 'shareTimeline'],
						success() {
							console.log('显示分享菜单成功')
							// 触发振动反馈
							uni.vibrateShort()
						},
						fail(err) {
							console.log('显示分享菜单失败', err)
							// 回退到自定义分享方案
							uni.showActionSheet({
								itemList: ['分享给微信好友', '分享到朋友圈'],
								success: res => {
									// 这里只是模拟，实际需要调用分享API
									uni.showToast({
										title: '分享成功',
										icon: 'success'
									})
								}
							})
						}
					})
				} catch (err) {
					console.error('分享操作失败:', err)
					// 回退到自定义分享方案
					uni.showActionSheet({
						itemList: ['分享给微信好友', '分享到朋友圈'],
						success: res => {
							// 这里只是模拟，实际需要调用分享API
							uni.showToast({
								title: '分享成功',
								icon: 'success'
							})
						}
					})
				}
			},
			
			// 显示删除确认
			showDeleteConfirm() {
				this.$refs.deletePopup.open()
			},
			
			// 关闭删除确认
			closeDeleteConfirm() {
				this.$refs.deletePopup.close()
			},
			
			// 确认删除
			confirmDelete() {
				if (!this.wishId) return
				
				this.wishStore.deleteWish(this.wishId)
				uni.showToast({
					title: '已删除',
					icon: 'success'
				})
				
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			},
			
			// 导航到分组页面
			navigateToGroup(groupId) {
				// 设置当前分组
				this.wishStore.setCurrentGroup(groupId)
				
				// 返回到首页
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			

		}
	}
</script>

<style lang="scss">
	.wish-detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}
	
	.card {
		margin-bottom: 20rpx;
		border-radius: 16rpx;
	}
	
	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 30rpx;
		font-weight: 500;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		margin-bottom: 20rpx;
		
		.comment-count {
			font-size: 24rpx;
			color: #999;
			font-weight: normal;
		}
	}
	
	.wish-detail {
		padding: 30rpx;
		
		.wish-header {
			margin-bottom: 30rpx;
			
			.wish-title-wrap {
				display: flex;
				align-items: center;
				margin-bottom: 16rpx;
				
				.wish-title {
					font-size: 36rpx;
					font-weight: 600;
				}
			}
			
			.wish-status {
				display: flex;
				
				.tag {
					display: flex;
					align-items: center;
					padding: 6rpx 16rpx;
					border-radius: 20rpx;
					font-size: 22rpx;
					color: #fff;
					margin-right: 16rpx;
					
					.uni-icons {
						margin-right: 6rpx;
					}
					
					&.private-tag {
						background-color: #ff9800;
					}
					
					&.friends-tag {
						background-color: #2196f3;
					}
					
					&.public-tag {
						background-color: #4caf50;
					}
					
					&.completed-tag {
						background-color: #409eff;
					}
				}
			}
		}
		
		.wish-content {
			padding: 20rpx 0;
			
			.wish-desc {
				font-size: 30rpx;
				line-height: 1.6;
				color: #333;
				margin-bottom: 20rpx;
			}
			
			.image-swiper {
				width: 100%;
				height: 500rpx;
				margin-bottom: 20rpx;
				border-radius: 12rpx;
				overflow: hidden;
			}
			
			.swiper-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			
			.wish-image {
				width: 100%;
				max-height: 500rpx;
				border-radius: 12rpx;
				margin-bottom: 20rpx;
			}
			
			.wish-time-info {
				margin-top: 20rpx;
				.time-item {
					display: flex;
					margin-bottom: 10rpx;
					
					.time-label {
						color: #666;
						font-size: 26rpx;
						margin-right: 10rpx;
					}
					
					.time-value {
						color: #333;
						font-size: 26rpx;
					}
				}
			}
		}
		
		.wish-actions {
			display: flex;
			justify-content: space-around;
			padding: 20rpx 0;
			border-top: 1rpx solid #f0f0f0;
			
			.action-btn, .share-btn {
				display: flex;
				flex-direction: column;
				align-items: center;
				font-size: 24rpx;
				color: #666;
				background: none;
				margin: 0;
				padding: 10rpx 20rpx;
				line-height: 1.5;
				
				&::after {
					border: none;
				}
				
				&.delete {
					color: #f56c6c;
				}
			}
		}
	}
	
	.wish-groups {
		padding: 30rpx;
		
		.group-tags {
			display: flex;
			flex-wrap: wrap;
			
			.group-tag {
				padding: 6rpx 16rpx;
				background-color: #f0e6ff;
				color: #8a2be2;
				border-radius: 24rpx;
				font-size: 26rpx;
				margin-right: 8rpx;
				margin-bottom: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				text-align: center;
			}
		}
	}
	
	.wish-comments {
		padding: 30rpx;
		
		.empty-comment {
			text-align: center;
			color: #999;
			font-size: 28rpx;
			padding: 40rpx 0;
		}
		
		.comment-list {
			margin-bottom: 30rpx;
			
			.comment-item {
				display: flex;
				margin-bottom: 30rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.comment-avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 40rpx;
					margin-right: 20rpx;
				}
				
				.comment-content {
					flex: 1;
					
					.comment-user {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 10rpx;
						
						.comment-nickname {
							font-size: 28rpx;
							font-weight: 500;
							color: #333;
						}
						
						.comment-time {
							font-size: 24rpx;
							color: #999;
						}
					}
					
					.comment-text {
						font-size: 28rpx;
						color: #333;
						line-height: 1.5;
						margin-bottom: 10rpx;
					}
					
					.comment-image {
						width: 100%;
						max-width: 450rpx;
						border-radius: 12rpx;
					}
				}
			}
		}
		
		// 悬浮删除按钮
		.floating-delete-btn {
			position: fixed;
			z-index: 1000;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #f56c6c;
			color: #fff;
			border-radius: 20rpx;
			padding: 8rpx 16rpx;
			font-size: 22rpx;
			box-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.4);
			animation: fadeInScale 0.2s ease-out;
			white-space: nowrap;
			
			text {
				margin-left: 6rpx;
			}
			
			&:active {
				transform: scale(0.9);
				background-color: #e85a5a;
			}
		}
		
		@keyframes fadeInScale {
			0% {
				opacity: 0;
				transform: scale(0.5);
			}
			100% {
				opacity: 1;
				transform: scale(1);
			}
		}
		
		.comment-form {
			display: flex;
			align-items: center;
			padding: 20rpx;
			background-color: #f8f8f8;
			border-radius: 35rpx;
			
			.comment-input {
				flex: 1;
				height: 70rpx;
				font-size: 28rpx;
			}
			
			.comment-btn {
				padding: 0 10rpx;
			}
		}
		


	}
</style> 
<template>
	<view class="profile-container">
		<!-- 用户信息区 -->
		<view class="user-card card glass">
			<view v-if="!isLogin" class="login-btn" @click="goToLogin">
				<text>登录/注册</text>
			</view>
			<view v-else class="user-info">
				<image class="avatar" :src="userStore.avatarUrl" mode="aspectFill"></image>
				<view class="user-detail">
					<view class="username">{{ userStore.nickname }}</view>
				</view>
				<view class="edit-btn" @click="goToUserEdit">
					<uni-icons type="gear" size="24" color="#fff"></uni-icons>
				</view>
			</view>
		</view>
		
		<!-- 功能区 -->
		<view class="function-list card">
			<view class="function-item" @click="goToPage('/subpkg-profile/pages/addressManage/addressManage')">
				<view class="function-icon primary-bg">
					<uni-icons type="location" size="20" color="#fff"></uni-icons>
				</view>
				<view class="function-title">地址管理</view>
				<uni-icons type="right" size="16" color="#ccc"></uni-icons>
			</view>

			<view class="function-item" @click="goToPage('/subpkg-wish/pages/groupManage/groupManage')">
				<view class="function-icon primary-bg">
					<uni-icons type="bars" size="20" color="#fff"></uni-icons>
				</view>
				<view class="function-title">分组管理</view>
				<uni-icons type="right" size="16" color="#ccc"></uni-icons>
			</view>

		<view class="function-item" @click="goToPage('/subpkg-profile/pages/historyWish/historyWish')">
				<view class="function-icon primary-bg">
					<uni-icons type="checkbox-filled" size="20" color="#fff"></uni-icons>
				</view>
				<view class="function-title">历史心愿</view>
				<uni-icons type="right" size="16" color="#ccc"></uni-icons>
			</view>

			<view class="function-item" @click="showShareOptions">
				<view class="function-icon primary-bg">
					<uni-icons type="redo-filled" size="20" color="#fff"></uni-icons>
				</view>
				<view class="function-title">分享应用</view>
				<uni-icons type="right" size="16" color="#ccc"></uni-icons>
			</view>

			<view class="function-item" @click="goToPage('/subpkg-profile/pages/about/about')">
				<view class="function-icon primary-bg">
					<uni-icons type="info-filled" size="20" color="#fff"></uni-icons>
				</view>
				<view class="function-title">关于我们</view>
				<uni-icons type="right" size="16" color="#ccc"></uni-icons>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view v-if="isLogin" class="logout-btn" @click="showLogoutConfirm">
			退出登录
		</view>
		

	</view>
</template>

<script>
	import { useUserStore } from '@/store/user.js'
	import { useGroupStore } from '@/store/group.js'
	import { useWishStore } from '@/store/wish.js'
	import { useMessageStore } from '@/store/message.js'
	
	export default {
		setup() {
			const userStore = useUserStore()
			const groupStore = useGroupStore()
			const wishStore = useWishStore()
			const messageStore = useMessageStore()
			
			return {
				userStore,
				groupStore,
				wishStore,
				messageStore
			}
		},
		computed: {
			isLogin() {
				return this.userStore.hasLogin
			},
			userInfo() {
				return this.userStore.getUserInfo || {}
			}
		},
		onShow() {
			// 页面显示时的处理
		},
		methods: {
			
			// 跳转到登录页
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			},
			
			// 通用页面跳转
			goToPage(url) {
				uni.navigateTo({
					url
				})
			},
			
			// 显示分享选项
			showShareOptions() {
				uni.showActionSheet({
					itemList: ['分享给微信好友', '分享到朋友圈'],
					success: res => {
						// 这里只是模拟，实际需要调用分享API
						uni.showToast({
							title: '分享成功',
							icon: 'success'
						})
					}
				})
			},
			
			// 显示退出确认
			showLogoutConfirm() {
				uni.showModal({
					title: '退出登录',
					content: '确定要退出当前账号吗？',
					showCancel: true,
					cancelText: '取消',
					confirmText: '确定',
					confirmColor: '#e60012',
					success: (res) => {
						if (res.confirm) {
							console.log('用户点击确定退出登录');
							this.logout();
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					},
					fail: (err) => {
						console.error('显示退出确认弹窗失败:', err);
					}
				});
			},
			
			// 退出登录
			logout() {
				this.userStore.logout()
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				})
			},
			
			// 跳转到用户编辑页
			goToUserEdit() {
				uni.navigateTo({
					url: '/subpkg-profile/pages/userEdit/userEdit'
				})
			}
		}
	}
</script>

<style lang="scss">
	.profile-container {
		/* min-height: 100vh; */
		background-color: #f5f5f5;
		padding: 30rpx;
	}
	
	.user-card {
		height: 200rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 20rpx;
		background-image: linear-gradient(135deg, rgba(138, 43, 226, 0.8), rgba(186, 85, 211, 0.8));
		position: relative;
		
		.login-btn {
			color: #fff;
			font-size: 36rpx;
			font-weight: 500;
			padding: 20rpx 60rpx;
			border: 2rpx solid rgba(255, 255, 255, 0.6);
			border-radius: 40rpx;
		}
		
		.user-info {
			width: 100%;
			display: flex;
			align-items: center;
			padding: 0 40rpx;
			
			.avatar {
				width: 120rpx;
				height: 120rpx;
				border-radius: 60rpx;
				margin-right: 30rpx;
				border: 4rpx solid rgba(255, 255, 255, 0.6);
			}
			
			.user-detail {
				flex: 1;
				
				.username {
					font-size: 36rpx;
					color: #fff;
					font-weight: 500;
					margin-bottom: 10rpx;
				}
				
				.user-id {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.8);
				}
			}
			
			.edit-btn {
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				width: 60rpx;
				height: 60rpx;
				border-radius: 30rpx;
				background-color: rgba(255, 255, 255, 0.3);
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 10;
			}
		}
	}
	
	.function-list {
		border-radius: 20rpx;
		margin-bottom: 30rpx;
		
		.function-item {
			display: flex;
			align-items: center;
			padding: 30rpx 20rpx;
			border-bottom: 1rpx solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.function-icon {
				width: 70rpx;
				height: 70rpx;
				border-radius: 35rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 20rpx;
				
				&.primary-bg {
					background-color: #8a2be2;
				}
			}
			
			.function-title {
				flex: 1;
				font-size: 30rpx;
				color: #333;
			}
		}
	}
	
	.logout-btn {
		margin-top: 60rpx;
		background-color: #fff;
		color: #ff5a5f;
		font-size: 32rpx;
		text-align: center;
		padding: 24rpx 0;
		border-radius: 12rpx;
	}
</style> 
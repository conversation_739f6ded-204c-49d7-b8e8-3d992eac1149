/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.history-card-container {
  margin: 10rpx 2%;
  width: 96%;
  box-sizing: border-box;
}
.swipe-action-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 12rpx;
  display: flex;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;
}
.swipe-content {
  flex: 1;
  width: 100%;
  z-index: 2;
  background-color: #fff;
  transition: transform 0.3s ease;
  border-radius: 12rpx;
}
.swipe-buttons {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  height: 100%;
}
.swipe-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  color: white;
  font-size: 26rpx;
  font-weight: 500;
  height: 100%;
  flex-direction: column;
}
.swipe-button:active {
  opacity: 0.85;
}
.restore {
  background-color: #19ad19;
}
.delete {
  background-color: #fa5151;
}
.wish-card {
  padding: 24rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 12rpx;
  width: 100%;
}
.wish-card .wish-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  position: relative;
}
.wish-card .wish-card-header .wish-card-order {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: #8a2be2;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 16rpx;
}
.wish-card .wish-card-header .wish-card-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 16rpx;
}
.wish-card .wish-card-header .wish-card-status {
  display: flex;
  align-items: center;
}
.wish-card .wish-card-header .wish-card-status .completed-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #52c41a;
  display: flex;
  align-items: center;
  justify-content: center;
}
.wish-card .wish-card-content {
  margin-bottom: 16rpx;
  position: relative;
}
.wish-card .wish-card-content .wish-card-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* 🔧 修改为最多显示3行 */
  overflow: hidden;
}
.wish-card .wish-card-content .image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  margin-top: 16rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.wish-card .wish-card-content .wish-card-image {
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
  /* 🔧 移除 object-fit: cover，使用 mode="aspectFit" 完整显示图片 */
}
.wish-card .wish-card-content .multi-image-badge {
  position: absolute;
  right: 12rpx;
  bottom: 12rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  z-index: 5;
}
.wish-card .wish-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.wish-card .wish-card-footer .wish-card-date {
  font-size: 24rpx;
  color: #999;
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
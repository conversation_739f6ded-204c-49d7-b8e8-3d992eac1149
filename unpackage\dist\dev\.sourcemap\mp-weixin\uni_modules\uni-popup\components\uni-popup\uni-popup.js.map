{"version": 3, "file": "uni-popup.js", "sources": ["uni_modules/uni-popup/components/uni-popup/uni-popup.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQ3lrMTYvRGVza3RvcC93aXNobGlzdC11bmlhcHAvdW5pX21vZHVsZXMvdW5pLXBvcHVwL2NvbXBvbmVudHMvdW5pLXBvcHVwL3VuaS1wb3B1cC52dWU"], "sourcesContent": ["<template>\r\n\t<view v-if=\"showPopup\" class=\"uni-popup\" :class=\"[popupstyle, isDesktop ? 'fixforpc-z-index' : '']\">\r\n\t\t<view @touchstart=\"touchstart\">\r\n\t\t\t<uni-transition key=\"1\" v-if=\"maskShow\" name=\"mask\" mode-class=\"fade\" :styles=\"maskClass\"\r\n\t\t\t\t:duration=\"duration\" :show=\"showTrans\" @click=\"onTap\" />\r\n\t\t\t<uni-transition key=\"2\" :mode-class=\"ani\" name=\"content\" :styles=\"transClass\" :duration=\"duration\"\r\n\t\t\t\t:show=\"showTrans\" @click=\"onTap\">\r\n\t\t\t\t<view class=\"uni-popup__wrapper\" :style=\"getStyles\" :class=\"[popupstyle]\" @click=\"clear\">\r\n\t\t\t\t\t<slot />\r\n\t\t\t\t</view>\r\n\t\t\t</uni-transition>\r\n\t\t</view>\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t<keypress v-if=\"maskShow\" @esc=\"onTap\" />\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef H5\r\n\timport keypress from './keypress.js'\r\n\t// #endif\r\n\r\n\t/**\r\n\t * PopUp 弹出层\r\n\t * @description 弹出层组件，为了解决遮罩弹层的问题\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=329\r\n\t * @property {String} type = [top|center|bottom|left|right|message|dialog|share] 弹出方式\r\n\t * \t@value top 顶部弹出\r\n\t * \t@value center 中间弹出\r\n\t * \t@value bottom 底部弹出\r\n\t * \t@value left\t\t左侧弹出\r\n\t * \t@value right  右侧弹出\r\n\t * \t@value message 消息提示\r\n\t * \t@value dialog 对话框\r\n\t * \t@value share 底部分享示例\r\n\t * @property {Boolean} animation = [true|false] 是否开启动画\r\n\t * @property {Boolean} maskClick = [true|false] 蒙版点击是否关闭弹窗(废弃)\r\n\t * @property {Boolean} isMaskClick = [true|false] 蒙版点击是否关闭弹窗\r\n\t * @property {String}  backgroundColor 主窗口背景色\r\n\t * @property {String}  maskBackgroundColor 蒙版颜色\r\n\t * @property {String}  borderRadius 设置圆角(左上、右上、右下和左下) 示例:\"10px 10px 10px 10px\"\r\n\t * @property {Boolean} safeArea\t\t   是否适配底部安全区\r\n\t * @event {Function} change 打开关闭弹窗触发，e={show: false}\r\n\t * @event {Function} maskClick 点击遮罩触发\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'uniPopup',\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef H5\r\n\t\t\tkeypress\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\temits: ['change', 'maskClick'],\r\n\t\tprops: {\r\n\t\t\t// 开启动画\r\n\t\t\tanimation: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 弹出层类型，可选值，top: 顶部弹出层；bottom：底部弹出层；center：全屏弹出层\r\n\t\t\t// message: 消息提示 ; dialog : 对话框\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'center'\r\n\t\t\t},\r\n\t\t\t// maskClick\r\n\t\t\tisMaskClick: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\t// TODO 2 个版本后废弃属性 ，使用 isMaskClick\r\n\t\t\tmaskClick: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'none'\r\n\t\t\t},\r\n\t\t\tsafeArea: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tmaskBackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'rgba(0, 0, 0, 0.4)'\r\n\t\t\t},\r\n\t\t\tborderRadius:{\r\n\t\t\t\ttype: String,\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\twatch: {\r\n\t\t\t/**\r\n\t\t\t * 监听type类型\r\n\t\t\t */\r\n\t\t\ttype: {\r\n\t\t\t\thandler: function(type) {\r\n\t\t\t\t\tif (!this.config[type]) return\r\n\t\t\t\t\tthis[this.config[type]](true)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\tisDesktop: {\r\n\t\t\t\thandler: function(newVal) {\r\n\t\t\t\t\tif (!this.config[newVal]) return\r\n\t\t\t\t\tthis[this.config[this.type]](true)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 监听遮罩是否可点击\r\n\t\t\t * @param {Object} val\r\n\t\t\t */\r\n\t\t\tmaskClick: {\r\n\t\t\t\thandler: function(val) {\r\n\t\t\t\t\tthis.mkclick = val\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\tisMaskClick: {\r\n\t\t\t\thandler: function(val) {\r\n\t\t\t\t\tthis.mkclick = val\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\t// H5 下禁止底部滚动\r\n\t\t\tshowPopup(show) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\r\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow = show ? 'hidden' : 'visible'\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tduration: 300,\r\n\t\t\t\tani: [],\r\n\t\t\t\tshowPopup: false,\r\n\t\t\t\tshowTrans: false,\r\n\t\t\t\tpopupWidth: 0,\r\n\t\t\t\tpopupHeight: 0,\r\n\t\t\t\tconfig: {\r\n\t\t\t\t\ttop: 'top',\r\n\t\t\t\t\tbottom: 'bottom',\r\n\t\t\t\t\tcenter: 'center',\r\n\t\t\t\t\tleft: 'left',\r\n\t\t\t\t\tright: 'right',\r\n\t\t\t\t\tmessage: 'top',\r\n\t\t\t\t\tdialog: 'center',\r\n\t\t\t\t\tshare: 'bottom'\r\n\t\t\t\t},\r\n\t\t\t\tmaskClass: {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tbackgroundColor: 'rgba(0, 0, 0, 0.4)'\r\n\t\t\t\t},\r\n\t\t\t\ttransClass: {\r\n\t\t\t\t\tbackgroundColor: 'transparent',\r\n\t\t\t\t\tborderRadius: this.borderRadius || \"0\",\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0\r\n\t\t\t\t},\r\n\t\t\t\tmaskShow: true,\r\n\t\t\t\tmkclick: true,\r\n\t\t\t\tpopupstyle: 'top'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tgetStyles() {\r\n\t\t\t\tlet res = { backgroundColor: this.bg };\r\n\t\t\t\tif (this.borderRadius || \"0\") {\r\n\t\t\t\t\tres = Object.assign(res, { borderRadius: this.borderRadius })\r\n\t\t\t\t}\r\n\t\t\t\treturn res;\r\n\t\t\t},\r\n\t\t\tisDesktop() {\r\n\t\t\t\treturn this.popupWidth >= 500 && this.popupHeight >= 500\r\n\t\t\t},\r\n\t\t\tbg() {\r\n\t\t\t\tif (this.backgroundColor === '' || this.backgroundColor === 'none') {\r\n\t\t\t\t\treturn 'transparent'\r\n\t\t\t\t}\r\n\t\t\t\treturn this.backgroundColor\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tconst fixSize = () => {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tconst {\r\n\t\t\t\t\twindowWidth,\r\n\t\t\t\t\twindowHeight,\r\n\t\t\t\t\twindowTop,\r\n\t\t\t\t\tsafeArea,\r\n\t\t\t\t\tscreenHeight,\r\n\t\t\t\t\tsafeAreaInsets\r\n\t\t\t\t} = uni.getWindowInfo()\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\tconst {\r\n\t\t\t\t\twindowWidth,\r\n\t\t\t\t\twindowHeight,\r\n\t\t\t\t\twindowTop,\r\n\t\t\t\t\tsafeArea,\r\n\t\t\t\t\tscreenHeight,\r\n\t\t\t\t\tsafeAreaInsets\r\n\t\t\t\t} = uni.getSystemInfoSync()\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.popupWidth = windowWidth\r\n\t\t\t\tthis.popupHeight = windowHeight + (windowTop || 0)\r\n\t\t\t\t// TODO fix by mehaotian 是否适配底部安全区 ,目前微信ios 、和 app ios 计算有差异，需要框架修复\r\n\t\t\t\tif (safeArea && this.safeArea) {\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tthis.safeAreaInsets = screenHeight - safeArea.bottom\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\tthis.safeAreaInsets = safeAreaInsets.bottom\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.safeAreaInsets = 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tfixSize()\r\n\t\t\t// #ifdef H5\r\n\t\t\t// window.addEventListener('resize', fixSize)\r\n\t\t\t// this.$once('hook:beforeDestroy', () => {\r\n\t\t\t// \twindow.removeEventListener('resize', fixSize)\r\n\t\t\t// })\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// #ifndef VUE3\r\n\t\t// TODO vue2\r\n\t\tdestroyed() {\r\n\t\t\tthis.setH5Visible()\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE3\r\n\t\t// TODO vue3\r\n\t\tunmounted() {\r\n\t\t\tthis.setH5Visible()\r\n\t\t},\r\n\t\t// #endif\r\n\t\tactivated() {\r\n   \t  this.setH5Visible(!this.showPopup);\r\n    },\r\n    deactivated() {\r\n      this.setH5Visible(true);\r\n    },\r\n\t\tcreated() {\r\n\t\t\t// this.mkclick =  this.isMaskClick || this.maskClick\r\n\t\t\tif (this.isMaskClick === null && this.maskClick === null) {\r\n\t\t\t\tthis.mkclick = true\r\n\t\t\t} else {\r\n\t\t\t\tthis.mkclick = this.isMaskClick !== null ? this.isMaskClick : this.maskClick\r\n\t\t\t}\r\n\t\t\tif (this.animation) {\r\n\t\t\t\tthis.duration = 300\r\n\t\t\t} else {\r\n\t\t\t\tthis.duration = 0\r\n\t\t\t}\r\n\t\t\t// TODO 处理 message 组件生命周期异常的问题\r\n\t\t\tthis.messageChild = null\r\n\t\t\t// TODO 解决头条冒泡的问题\r\n\t\t\tthis.clearPropagation = false\r\n\t\t\tthis.maskClass.backgroundColor = this.maskBackgroundColor\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetH5Visible(visible = true) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\r\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow =  visible ? \"visible\" : \"hidden\";\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 公用方法，不显示遮罩层\r\n\t\t\t */\r\n\t\t\tcloseMask() {\r\n\t\t\t\tthis.maskShow = false\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 公用方法，遮罩层禁止点击\r\n\t\t\t */\r\n\t\t\tdisableMask() {\r\n\t\t\t\tthis.mkclick = false\r\n\t\t\t},\r\n\t\t\t// TODO nvue 取消冒泡\r\n\t\t\tclear(e) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\te.stopPropagation()\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.clearPropagation = true\r\n\t\t\t},\r\n\r\n\t\t\topen(direction) {\r\n\t\t\t\t// fix by mehaotian 处理快速打开关闭的情况\r\n\t\t\t\tif (this.showPopup) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet innerType = ['top', 'center', 'bottom', 'left', 'right', 'message', 'dialog', 'share']\r\n\t\t\t\tif (!(direction && innerType.indexOf(direction) !== -1)) {\r\n\t\t\t\t\tdirection = this.type\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.config[direction]) {\r\n\t\t\t\t\tconsole.error('缺少类型：', direction)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis[this.config[direction]]()\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tshow: true,\r\n\t\t\t\t\ttype: direction\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose(type) {\r\n\t\t\t\tthis.showTrans = false\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tshow: false,\r\n\t\t\t\t\ttype: this.type\r\n\t\t\t\t})\r\n\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\t// // 自定义关闭事件\r\n\t\t\t\t// this.customOpen && this.customClose()\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.showPopup = false\r\n\t\t\t\t}, 300)\r\n\t\t\t},\r\n\t\t\t// TODO 处理冒泡事件，头条的冒泡事件有问题 ，先这样兼容\r\n\t\t\ttouchstart() {\r\n\t\t\t\tthis.clearPropagation = false\r\n\t\t\t},\r\n\r\n\t\t\tonTap() {\r\n\t\t\t\tif (this.clearPropagation) {\r\n\t\t\t\t\t// fix by mehaotian 兼容 nvue\r\n\t\t\t\t\tthis.clearPropagation = false\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('maskClick')\r\n\t\t\t\tif (!this.mkclick) return\r\n\t\t\t\tthis.close()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 顶部弹出样式处理\r\n\t\t\t */\r\n\t\t\ttop(type) {\r\n\t\t\t\tthis.popupstyle = this.isDesktop ? 'fixforpc-top' : 'top'\r\n\t\t\t\tthis.ani = ['slide-top']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg,\r\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\"\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.showPoptrans()\r\n\t\t\t\t\tif (this.messageChild && this.type === 'message') {\r\n\t\t\t\t\t\tthis.messageChild.timerClose()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 底部弹出样式处理\r\n\t\t\t */\r\n\t\t\tbottom(type) {\r\n\t\t\t\tthis.popupstyle = 'bottom'\r\n\t\t\t\tthis.ani = ['slide-bottom']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tpaddingBottom: this.safeAreaInsets + 'px',\r\n\t\t\t\t\tbackgroundColor: this.bg,\r\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\",\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPoptrans()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 中间弹出样式处理\r\n\t\t\t */\r\n\t\t\tcenter(type) {\r\n\t\t\t\tthis.popupstyle = 'center'\r\n\t\t\t\t//微信小程序下，组合动画会出现文字向上闪动问题，再此做特殊处理\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tthis.ani = ['fade']\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\tthis.ani = ['zoom-out', 'fade']\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column',\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tjustifyContent: 'center',\r\n\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\"\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPoptrans()\r\n\t\t\t},\r\n\t\t\tleft(type) {\r\n\t\t\t\tthis.popupstyle = 'left'\r\n\t\t\t\tthis.ani = ['slide-left']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg,\r\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\",\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column'\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPoptrans()\r\n\t\t\t},\r\n\t\t\tright(type) {\r\n\t\t\t\tthis.popupstyle = 'right'\r\n\t\t\t\tthis.ani = ['slide-right']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg,\r\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\",\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column'\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPoptrans()\r\n\t\t\t},\r\n\t\t\tshowPoptrans(){\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\tthis.showPopup = true\r\n\t\t\t\t\tthis.showTrans = true\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.uni-popup {\r\n\t\tposition: fixed;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 99;\r\n\r\n\t\t/* #endif */\r\n\t\t&.top,\r\n\t\t&.left,\r\n\t\t&.right {\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\ttop: var(--window-top);\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifndef H5 */\r\n\t\t\ttop: 0;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\r\n\t\t.uni-popup__wrapper {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tdisplay: block;\r\n\t\t\t/* #endif */\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t/* iphonex 等安全区设置，底部安全区适配 */\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t// padding-bottom: constant(safe-area-inset-bottom);\r\n\t\t\t// padding-bottom: env(safe-area-inset-bottom);\r\n\t\t\t/* #endif */\r\n\t\t\t&.left,\r\n\t\t\t&.right {\r\n\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\tpadding-top: var(--window-top);\r\n\t\t\t\t/* #endif */\r\n\t\t\t\t/* #ifndef H5 */\r\n\t\t\t\tpadding-top: 0;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.fixforpc-z-index {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 999;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.fixforpc-top {\r\n\t\ttop: 0;\r\n\t}\r\n</style>\r\n", "import Component from 'C:/Users/<USER>/Desktop/wishlist-uniapp/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AA+CC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,YAAY,CAIX;AAAA,EACD,OAAO,CAAC,UAAU,WAAW;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEN,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA;AAAA,IAGD,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,cAAa;AAAA,MACZ,MAAM;AAAA,IACP;AAAA,EACA;AAAA,EAED,OAAO;AAAA;AAAA;AAAA;AAAA,IAIN,MAAM;AAAA,MACL,SAAS,SAAS,MAAM;AACvB,YAAI,CAAC,KAAK,OAAO,IAAI;AAAG;AACxB,aAAK,KAAK,OAAO,IAAI,CAAC,EAAE,IAAI;AAAA,MAC5B;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IACD,WAAW;AAAA,MACV,SAAS,SAAS,QAAQ;AACzB,YAAI,CAAC,KAAK,OAAO,MAAM;AAAG;AAC1B,aAAK,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE,IAAI;AAAA,MACjC;AAAA,MACD,WAAW;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW;AAAA,MACV,SAAS,SAAS,KAAK;AACtB,aAAK,UAAU;AAAA,MACf;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IACD,aAAa;AAAA,MACZ,SAAS,SAAS,KAAK;AACtB,aAAK,UAAU;AAAA,MACf;AAAA,MACD,WAAW;AAAA,IACX;AAAA;AAAA,IAED,UAAU,MAAM;AAAA,IAKhB;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,KAAK,CAAE;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,MACP;AAAA,MACD,WAAW;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,iBAAiB;AAAA,MACjB;AAAA,MACD,YAAY;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc,KAAK,gBAAgB;AAAA,QACnC,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,MACP;AAAA,MACD,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,UAAI,MAAM,EAAE,iBAAiB,KAAK;AAClC,UAAI,KAAK,gBAAgB,KAAK;AAC7B,cAAM,OAAO,OAAO,KAAK,EAAE,cAAc,KAAK,cAAc;AAAA,MAC7D;AACA,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,aAAO,KAAK,cAAc,OAAO,KAAK,eAAe;AAAA,IACrD;AAAA,IACD,KAAK;AACJ,UAAI,KAAK,oBAAoB,MAAM,KAAK,oBAAoB,QAAQ;AACnE,eAAO;AAAA,MACR;AACA,aAAO,KAAK;AAAA,IACb;AAAA,EACA;AAAA,EACD,UAAU;AACT,UAAM,UAAU,MAAM;AAErB,YAAM;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,IAAIA,cAAAA,MAAI,cAAc;AAYtB,WAAK,aAAa;AAClB,WAAK,cAAc,gBAAgB,aAAa;AAEhD,UAAI,YAAY,KAAK,UAAU;AAE9B,aAAK,iBAAiB,eAAe,SAAS;AAAA,aAKxC;AACN,aAAK,iBAAiB;AAAA,MACvB;AAAA,IACD;AACA,YAAQ;AAAA,EAOR;AAAA;AAAA,EASD,YAAY;AACX,SAAK,aAAa;AAAA,EAClB;AAAA,EAED,YAAY;AACR,SAAK,aAAa,CAAC,KAAK,SAAS;AAAA,EAClC;AAAA,EACD,cAAc;AACZ,SAAK,aAAa,IAAI;AAAA,EACvB;AAAA,EACH,UAAU;AAET,QAAI,KAAK,gBAAgB,QAAQ,KAAK,cAAc,MAAM;AACzD,WAAK,UAAU;AAAA,WACT;AACN,WAAK,UAAU,KAAK,gBAAgB,OAAO,KAAK,cAAc,KAAK;AAAA,IACpE;AACA,QAAI,KAAK,WAAW;AACnB,WAAK,WAAW;AAAA,WACV;AACN,WAAK,WAAW;AAAA,IACjB;AAEA,SAAK,eAAe;AAEpB,SAAK,mBAAmB;AACxB,SAAK,UAAU,kBAAkB,KAAK;AAAA,EACtC;AAAA,EACD,SAAS;AAAA,IACR,aAAa,UAAU,MAAM;AAAA,IAK5B;AAAA;AAAA;AAAA;AAAA,IAID,YAAY;AACX,WAAK,WAAW;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA,IAID,cAAc;AACb,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAED,MAAM,GAAG;AAER,QAAE,gBAAgB;AAElB,WAAK,mBAAmB;AAAA,IACxB;AAAA,IAED,KAAK,WAAW;AAEf,UAAI,KAAK,WAAW;AACnB;AAAA,MACD;AACA,UAAI,YAAY,CAAC,OAAO,UAAU,UAAU,QAAQ,SAAS,WAAW,UAAU,OAAO;AACzF,UAAI,EAAE,aAAa,UAAU,QAAQ,SAAS,MAAM,KAAK;AACxD,oBAAY,KAAK;AAAA,MAClB;AACA,UAAI,CAAC,KAAK,OAAO,SAAS,GAAG;AAC5BA,sBAAAA,MAAA,MAAA,SAAA,mEAAc,SAAS,SAAS;AAChC;AAAA,MACD;AACA,WAAK,KAAK,OAAO,SAAS,CAAC,EAAE;AAC7B,WAAK,MAAM,UAAU;AAAA,QACpB,MAAM;AAAA,QACN,MAAM;AAAA,OACN;AAAA,IACD;AAAA,IACD,MAAM,MAAM;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AAAA,QACpB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,OACX;AACD,mBAAa,KAAK,KAAK;AAGvB,WAAK,QAAQ,WAAW,MAAM;AAC7B,aAAK,YAAY;AAAA,MACjB,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAED,aAAa;AACZ,WAAK,mBAAmB;AAAA,IACxB;AAAA,IAED,QAAQ;AACP,UAAI,KAAK,kBAAkB;AAE1B,aAAK,mBAAmB;AACxB;AAAA,MACD;AACA,WAAK,MAAM,WAAW;AACtB,UAAI,CAAC,KAAK;AAAS;AACnB,WAAK,MAAM;AAAA,IACX;AAAA;AAAA;AAAA;AAAA,IAID,IAAI,MAAM;AACT,WAAK,aAAa,KAAK,YAAY,iBAAiB;AACpD,WAAK,MAAM,CAAC,WAAW;AACvB,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,iBAAiB,KAAK;AAAA,QACtB,cAAa,KAAK,gBAAgB;AAAA,MACnC;AAEA,UAAI;AAAM;AACV,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,UAAU,MAAM;AACpB,aAAK,aAAa;AAClB,YAAI,KAAK,gBAAgB,KAAK,SAAS,WAAW;AACjD,eAAK,aAAa,WAAW;AAAA,QAC9B;AAAA,OACA;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,OAAO,MAAM;AACZ,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,cAAc;AAC1B,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,eAAe,KAAK,iBAAiB;AAAA,QACrC,iBAAiB,KAAK;AAAA,QACtB,cAAa,KAAK,gBAAgB;AAAA,MACnC;AAEA,UAAI;AAAM;AACV,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA;AAAA;AAAA,IAID,OAAO,MAAM;AACZ,WAAK,aAAa;AAGjB,WAAK,MAAM,CAAC,MAAM;AAKnB,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QAEV,SAAS;AAAA,QACT,eAAe;AAAA,QAEf,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,cAAa,KAAK,gBAAgB;AAAA,MACnC;AAEA,UAAI;AAAM;AACV,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,KAAK,MAAM;AACV,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,YAAY;AACxB,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,iBAAiB,KAAK;AAAA,QACtB,cAAa,KAAK,gBAAgB;AAAA,QAElC,SAAS;AAAA,QACT,eAAe;AAAA,MAEhB;AAEA,UAAI;AAAM;AACV,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,MAAM,MAAM;AACX,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,aAAa;AACzB,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,KAAK;AAAA,QACL,iBAAiB,KAAK;AAAA,QACtB,cAAa,KAAK,gBAAgB;AAAA,QAElC,SAAS;AAAA,QACT,eAAe;AAAA,MAEhB;AAEA,UAAI;AAAM;AACV,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,eAAc;AACb,WAAK,UAAU,MAAI;AAClB,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA,OACjB;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/cD,GAAG,gBAAgB,SAAS;"}
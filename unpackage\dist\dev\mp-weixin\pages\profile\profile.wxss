/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.profile-container {
  /* min-height: 100vh; */
  background-color: #f5f5f5;
  padding: 30rpx;
}
.user-card {
  height: 200rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
  background-image: linear-gradient(135deg, rgba(138, 43, 226, 0.8), rgba(186, 85, 211, 0.8));
  position: relative;
}
.user-card .login-btn {
  color: #fff;
  font-size: 36rpx;
  font-weight: 500;
  padding: 20rpx 60rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  border-radius: 40rpx;
}
.user-card .user-info {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 40rpx;
}
.user-card .user-info .avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.6);
}
.user-card .user-info .user-detail {
  flex: 1;
}
.user-card .user-info .user-detail .username {
  font-size: 36rpx;
  color: #fff;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.user-card .user-info .user-detail .user-id {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.user-card .user-info .edit-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.function-list {
  border-radius: 20rpx;
  margin-bottom: 30rpx;
}
.function-list .function-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.function-list .function-item:last-child {
  border-bottom: none;
}
.function-list .function-item .function-icon {
  width: 70rpx;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.function-list .function-item .function-icon.primary-bg {
  background-color: #8a2be2;
}
.function-list .function-item .function-title {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}
.logout-btn {
  margin-top: 60rpx;
  background-color: #fff;
  color: #ff5a5f;
  font-size: 32rpx;
  text-align: center;
  padding: 24rpx 0;
  border-radius: 12rpx;
}
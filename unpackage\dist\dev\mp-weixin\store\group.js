"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const store_user = require("./user.js");
const utils_envUtils = require("../utils/envUtils.js");
const useGroupStore = common_vendor.defineStore("group", {
  state: () => ({
    groups: [],
    isLoading: false,
    lastSyncTime: null,
    isOnline: true,
    // 网络状态
    // 🚀 增强同步状态管理
    _syncOperations: /* @__PURE__ */ new Set(),
    // 正在进行的同步操作
    _lastSyncTime: 0
    // 上次同步时间
  }),
  getters: {
    // 获取所有分组
    getAllGroups: (state) => state.groups,
    // 根据ID获取分组
    getGroupById: (state) => (id) => {
      return state.groups.find((group) => group.id === id) || null;
    },
    // 检查是否有待同步的数据
    hasPendingSync: (state) => {
      return state.groups.some((group) => group._needSync);
    },
    // 获取待同步的分组数量
    pendingSyncCount: (state) => {
      return state.groups.filter((group) => group._needSync).length;
    },
    // 获取待同步的分组列表
    pendingSyncGroups: (state) => {
      return state.groups.filter((group) => group._needSync);
    }
  },
  actions: {
    // 初始化分组数据
    async initGroups() {
      const storedGroups = common_vendor.index.getStorageSync("groups");
      if (storedGroups) {
        const parsed = JSON.parse(storedGroups);
        this.groups = parsed.map((group) => ({
          ...group,
          id: group.id || group._id
          // 如果没有 id 字段，使用 _id
        }));
      }
      this.initNetworkMonitor();
      await this.syncFromCloud();
      this.ensureDefaultGroups();
    },
    // 初始化网络监听
    initNetworkMonitor() {
      this.checkNetworkStatus();
      common_vendor.index.onNetworkStatusChange((res) => {
        const wasOnline = this.isOnline;
        this.isOnline = res.isConnected;
        utils_envUtils.devLog.log(`分组Store - 网络状态变化: ${wasOnline ? "在线" : "离线"} -> ${this.isOnline ? "在线" : "离线"}`);
        if (!wasOnline && this.isOnline) {
          utils_envUtils.devLog.log("分组Store - 网络恢复，但不启用自动同步");
        }
      });
    },
    // 检查网络状态
    async checkNetworkStatus() {
      try {
        const networkType = await common_vendor.index.getNetworkType();
        this.isOnline = networkType.networkType !== "none";
        utils_envUtils.devLog.log("分组Store - 当前网络状态:", this.isOnline ? "在线" : "离线");
      } catch (error) {
        utils_envUtils.devLog.error("分组Store - 检查网络状态失败:", error);
        this.isOnline = false;
      }
    },
    // 🚀 新增：检查同步操作是否可以执行
    _canStartSync(operation) {
      const now = Date.now();
      if (this._syncOperations.has(operation)) {
        utils_envUtils.devLog.log(`[groupStore] 同步操作 ${operation} 已在进行中`);
        return false;
      }
      if (now - this._lastSyncTime < 1e3) {
        utils_envUtils.devLog.log(`[groupStore] 同步操作过于频繁，跳过`);
        return false;
      }
      return true;
    },
    // 🚀 新增：开始同步操作
    _startSyncOperation(operation) {
      this._syncOperations.add(operation);
      this._lastSyncTime = Date.now();
      utils_envUtils.devLog.log(`[groupStore] 开始同步操作: ${operation}`);
    },
    // 🚀 新增：结束同步操作
    _endSyncOperation(operation) {
      this._syncOperations.delete(operation);
      utils_envUtils.devLog.log(`[groupStore] 结束同步操作: ${operation}`);
    },
    // 统一的同步方法 - 简化版本
    async syncFromCloud() {
      utils_envUtils.devLog.log("[groupStore] 🔄 开始从云端同步分组数据...");
      const userStore = store_user.useUserStore();
      if (!userStore.isLogin) {
        utils_envUtils.devLog.log("[groupStore] 用户未登录，跳过云端同步");
        if (this.groups.length === 0) {
          this._initDefaultGroups();
        }
        return { success: false, reason: "not_logged_in" };
      }
      try {
        this.isLoading = true;
        const groupCenter = common_vendor.nr.importObject("group-center");
        utils_envUtils.devLog.log("[groupStore] 调用云函数 getGroupList...");
        const result = await groupCenter.getGroupList();
        utils_envUtils.devLog.log("[groupStore] 云函数返回结果:", result);
        if (result.errCode === 0) {
          const cloudGroups = result.data || [];
          utils_envUtils.devLog.log("[groupStore] ✅ 从云端获取到", cloudGroups.length, "个分组");
          this.groups = cloudGroups.map((group) => ({
            ...group,
            id: group._id || group.id
          }));
          this.ensureDefaultGroups();
          this._saveToStorage();
          this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
          common_vendor.index.setStorageSync("groupsLastSyncTime", this.lastSyncTime);
          utils_envUtils.devLog.log("[groupStore] ✅ 分组同步完成，本地现有", this.groups.length, "个分组");
          return { success: true, count: cloudGroups.length };
        } else {
          const errorMsg = result.errMsg || "获取分组数据失败";
          utils_envUtils.devLog.error("[groupStore] ❌ 云函数错误:", errorMsg);
          throw new Error(errorMsg);
        }
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] ❌ 分组同步失败:", error);
        if (this.groups.length === 0) {
          this._initDefaultGroups();
        }
        throw error;
      } finally {
        this.isLoading = false;
      }
    },
    // 兼容方法：smartSync 调用 syncFromCloud
    async smartSync() {
      utils_envUtils.devLog.log("[groupStore] smartSync 调用 syncFromCloud");
      try {
        const result = await this.syncFromCloud();
        return {
          hasUpdates: result.success,
          updatedCount: result.count || 0,
          reason: result.success ? "success" : "error"
        };
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] smartSync 失败:", error);
        return {
          hasUpdates: false,
          updatedCount: 0,
          reason: "error",
          error: error.message
        };
      }
    },
    // 检查分组是否需要同步（轻量级调用）
    async _checkGroupSyncNeeded() {
      {
        utils_envUtils.devLog.log("[groupStore] Lightweight group sync check disabled, performing full sync");
        return true;
      }
    },
    // 获取本地分组数据的最后修改时间
    _getLocalGroupLastModified() {
      const nonDefaultGroups = this.groups.filter((group) => !group.isDefault);
      if (nonDefaultGroups.length === 0)
        return null;
      const latestGroup = nonDefaultGroups.reduce((latest, current) => {
        const currentTime = new Date(current.updateDate || current.createDate || 0);
        const latestTime = new Date(latest.updateDate || latest.createDate || 0);
        return currentTime > latestTime ? current : latest;
      });
      return latestGroup.updateDate || latestGroup.createDate;
    },
    // 执行智能分组同步（合并数据而不是覆盖）
    async _performIntelligentGroupSync() {
      utils_envUtils.devLog.log("[groupStore] Performing intelligent group data merge...");
      const groupCenter = common_vendor.nr.importObject("group-center");
      const result = await groupCenter.getGroupList({
        includeDeleted: true
        // 包含已删除的数据用于冲突解决
      });
      if (result.errCode === 0) {
        const cloudGroups = result.data || [];
        utils_envUtils.devLog.log("[groupStore] Cloud group data received:", cloudGroups.length, "items");
        const mergedData = this._mergeGroupData(this.groups, cloudGroups);
        utils_envUtils.devLog.log("[groupStore] 开始数据合并:", {
          localCount: this.groups.length,
          cloudCount: cloudGroups.length
        });
        try {
          await new Promise((resolve) => {
            if (this.$nextTick) {
              this.$nextTick(() => {
                this.groups = mergedData;
                resolve();
              });
            } else {
              setTimeout(() => {
                this.groups = mergedData;
                resolve();
              }, 0);
            }
          });
          utils_envUtils.devLog.log("[groupStore] 数据合并完成:", {
            mergedCount: mergedData.length
          });
          this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
          this._saveToStorage();
          this.ensureDefaultGroups();
          utils_envUtils.devLog.log("[groupStore] Intelligent group sync completed, merged data:", this.groups.length, "items");
        } catch (error) {
          utils_envUtils.devLog.error("[groupStore] Error updating groups data:", error);
          this.groups = mergedData;
          this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
          this._saveToStorage();
          this.ensureDefaultGroups();
        }
      } else {
        throw new Error(result.errMsg || "获取云端分组数据失败");
      }
    },
    // 智能合并本地和云端分组数据
    _mergeGroupData(localGroups, cloudGroups) {
      utils_envUtils.devLog.log("[groupStore] Merging local and cloud group data...");
      const safeLocalGroups = Array.isArray(localGroups) ? localGroups : [];
      const safeCloudGroups = Array.isArray(cloudGroups) ? cloudGroups : [];
      utils_envUtils.devLog.log("[groupStore] Local groups before merge:", safeLocalGroups.map((g) => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order })));
      utils_envUtils.devLog.log("[groupStore] Cloud groups before merge:", safeCloudGroups.map((g) => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order })));
      if (safeLocalGroups.length === 0 && safeCloudGroups.length === 0) {
        utils_envUtils.devLog.log("[groupStore] Both local and cloud groups are empty, returning empty array");
        return [];
      }
      const mergedMap = /* @__PURE__ */ new Map();
      const defaultGroups = safeLocalGroups.filter((group) => group.isDefault);
      defaultGroups.forEach((group) => {
        if (group.id) {
          mergedMap.set(group.id, {
            ...group,
            _source: "local-default"
          });
        }
      });
      safeCloudGroups.forEach((cloudGroup) => {
        if (!cloudGroup.isDefault && cloudGroup.id) {
          const groupWithId = {
            ...cloudGroup,
            id: cloudGroup.id || cloudGroup._id,
            // 确保id字段存在
            _source: "cloud"
          };
          mergedMap.set(groupWithId.id, groupWithId);
        }
      });
      const localNonDefaultGroups = safeLocalGroups.filter((group) => !group.isDefault);
      localNonDefaultGroups.forEach((localGroup) => {
        const id = localGroup.id;
        const existingGroup = mergedMap.get(id);
        if (!existingGroup) {
          utils_envUtils.devLog.log(`[groupStore] Preserving local-only group: ${localGroup.name}`);
          mergedMap.set(id, {
            ...localGroup,
            _source: "local-only",
            _needSync: true
            // 标记需要同步到云端
          });
        } else {
          const localTimeStr = localGroup.updateDate || localGroup.createDate;
          const cloudTimeStr = existingGroup.updateDate || existingGroup.createDate;
          const localTime = localTimeStr ? new Date(localTimeStr) : /* @__PURE__ */ new Date(0);
          const cloudTime = cloudTimeStr ? new Date(cloudTimeStr) : /* @__PURE__ */ new Date(0);
          const localTimeValid = !isNaN(localTime.getTime());
          const cloudTimeValid = !isNaN(cloudTime.getTime());
          if (!localTimeValid && !cloudTimeValid) {
            utils_envUtils.devLog.log(`[groupStore] 两个时间戳都无效，使用云端数据: ${existingGroup.name}`);
          } else if (!localTimeValid) {
            utils_envUtils.devLog.log(`[groupStore] 本地时间戳无效，使用云端数据: ${existingGroup.name}`);
          } else if (!cloudTimeValid) {
            utils_envUtils.devLog.log(`[groupStore] 云端时间戳无效，使用本地数据: ${localGroup.name}`);
            mergedMap.set(id, {
              ...localGroup,
              _source: "local-newer",
              _needSync: true
            });
          } else if (localTime > cloudTime) {
            utils_envUtils.devLog.log(`[groupStore] Local group data newer for ${localGroup.name}, using local version`);
            mergedMap.set(id, {
              ...localGroup,
              _source: "local-newer",
              _needSync: true
              // 需要同步到云端
            });
          } else if (localTime < cloudTime) {
            utils_envUtils.devLog.log(`[groupStore] Cloud group data newer for ${existingGroup.name}, using cloud version`);
          } else {
            if (this._hasGroupContentDifference(localGroup, existingGroup)) {
              utils_envUtils.devLog.log(`[groupStore] Group content difference detected for ${localGroup.name}, preferring cloud data`);
            } else {
              utils_envUtils.devLog.log(`[groupStore] Groups are identical for ${localGroup.name}, keeping cloud version`);
            }
          }
        }
      });
      let mergedArray = Array.from(mergedMap.values()).filter((group) => !group._deleted);
      mergedArray = this._fixGroupOrder(mergedArray);
      utils_envUtils.devLog.log("[groupStore] Group data merge completed:", {
        localCount: safeLocalGroups.length,
        cloudCount: safeCloudGroups.length,
        mergedCount: mergedArray.length
      });
      utils_envUtils.devLog.log("[groupStore] Merged groups:", mergedArray.map((g) => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order, source: g._source })));
      return mergedArray;
    },
    // 修复分组排序
    _fixGroupOrder(groups) {
      let maxOrder = Math.max(0, ...groups.filter((g) => g.order !== void 0).map((g) => g.order));
      groups.forEach((group) => {
        if (group.order === void 0 || group.order === null) {
          maxOrder += 1;
          group.order = maxOrder;
          utils_envUtils.devLog.log(`[groupStore] Assigned order ${maxOrder} to group: ${group.name}`);
        }
      });
      return groups.sort((a, b) => (a.order || 0) - (b.order || 0));
    },
    // 检查两个分组对象是否有内容差异
    _hasGroupContentDifference(group1, group2) {
      const keys = ["name", "icon", "color", "description"];
      return keys.some((key) => {
        return group1[key] !== group2[key];
      });
    },
    // 检查是否为网络错误
    _isNetworkError(error) {
      if (error.code === "NETWORK_ERROR")
        return true;
      if (error.message && (error.message.includes("网络") || error.message.includes("network") || error.message.includes("timeout")))
        return true;
      return false;
    },
    // 初始化默认分组
    _initDefaultGroups() {
      const defaultGroups = [
        {
          id: "all",
          name: "全部",
          isDefault: true,
          order: 0
        },
        {
          id: "gift",
          name: "礼物",
          isDefault: true,
          order: 1
        },
        {
          id: "friend-visible",
          name: "朋友可见",
          isDefault: true,
          order: 2
        }
      ];
      try {
        this.groups.splice(0, this.groups.length);
        this.groups.push(...defaultGroups);
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] Error updating default groups safely, falling back to direct assignment:", error);
        this.groups = defaultGroups;
      }
      this._saveToStorage();
    },
    // 确保默认分组存在
    ensureDefaultGroups() {
      const allGroupExists = this.groups.some((group) => group.id === "all");
      if (!allGroupExists) {
        this.groups.push({
          id: "all",
          name: "全部",
          isDefault: true,
          order: 0
          // 确保始终在第一位
        });
      } else {
        const allGroupIndex = this.groups.findIndex((group) => group.id === "all");
        if (allGroupIndex > -1) {
          this.groups[allGroupIndex].order = 0;
        }
      }
      const giftGroupExists = this.groups.some((group) => group.id === "gift");
      if (!giftGroupExists) {
        this.groups.push({
          id: "gift",
          name: "礼物",
          isDefault: true,
          order: 1
        });
      }
      const friendVisibleGroupExists = this.groups.some((group) => group.id === "friend-visible");
      if (!friendVisibleGroupExists) {
        this.groups.push({
          id: "friend-visible",
          name: "朋友可见",
          isDefault: true,
          order: 2
        });
      }
      this._saveToStorage();
    },
    // 添加新分组
    async addGroup(name) {
      const userStore = store_user.useUserStore();
      if (!userStore.isLogin) {
        utils_envUtils.devLog.error("[store/group.js addGroup] Critical: User not logged in before attempting to create group.");
        common_vendor.index.showToast({
          title: "请先登录以创建分组",
          icon: "none",
          duration: 3e3
        });
        return null;
      }
      if (!name || typeof name !== "string") {
        utils_envUtils.devLog.error("分组名称不合法");
        common_vendor.index.showToast({ title: "分组名称不合法", icon: "none" });
        return null;
      }
      if (this._checkGroupNameExists(name)) {
        utils_envUtils.devLog.warn("前端检查：分组名称已存在 - ", name);
        common_vendor.index.showToast({ title: "分组名称已存在", icon: "none" });
        return null;
      }
      try {
        const timestamp = Date.now();
        const groupCenter = common_vendor.nr.importObject("group-center");
        const result = await groupCenter.createGroup({
          name,
          icon: "",
          color: "#8a2be2",
          // 时间戳支持
          timestamp,
          deviceId: common_vendor.index.getSystemInfoSync().deviceId || "unknown"
        });
        if (result.errCode === 0) {
          const nonDefaultGroups = this.groups.filter((g) => !g.isDefault);
          const defaultGroupsMaxOrder = 2;
          const maxOrder = Math.max(defaultGroupsMaxOrder, ...nonDefaultGroups.map((g) => g.order || 0));
          const newGroup = {
            ...result.data,
            id: result.data.id || result.data._id,
            // 确保id字段存在
            order: result.data.order || maxOrder + 1
            // 确保新分组排在所有分组之后
          };
          this.groups.push(newGroup);
          this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
          this._saveToStorage();
          utils_envUtils.devLog.log("[groupStore] 分组添加成功:", {
            groupName: newGroup.name,
            groupId: result.data.id,
            groupOrder: newGroup.order
          });
          this._triggerReactiveUpdate();
          common_vendor.index.showToast({ title: "分组创建成功", icon: "success" });
          utils_envUtils.devLog.log("[groupStore] New group added:", newGroup.name, "with order:", newGroup.order);
          utils_envUtils.devLog.log("[groupStore] Current groups after adding:", this.groups.map((g) => ({ id: g.id, name: g.name, order: g.order })));
          return result.data.id;
        } else {
          utils_envUtils.devLog.error("云函数创建分组失败:", result.errMsg, "(errCode:", result.errCode, ")");
          common_vendor.index.showToast({
            title: result.errMsg || "创建分组失败",
            icon: "none",
            duration: 3e3
          });
          return null;
        }
      } catch (error) {
        utils_envUtils.devLog.error("调用云函数添加分组时发生异常:", error);
        const isNetworkIssue = error.message && (error.message.includes("network") || error.message.includes("超时"));
        if (isNetworkIssue) {
          utils_envUtils.devLog.log("网络问题，尝试离线保存分组:", name);
          const id = "group_" + Date.now();
          const nonDefaultGroups = this.groups.filter((g) => !g.isDefault);
          const defaultGroupsMaxOrder = 2;
          const maxOrder = Math.max(defaultGroupsMaxOrder, ...nonDefaultGroups.map((g) => g.order || 0));
          const newGroup = {
            id,
            name,
            isDefault: false,
            order: maxOrder + 1,
            _needSync: true
          };
          this.groups.push(newGroup);
          this._saveToStorage();
          this._triggerReactiveUpdate();
          common_vendor.index.showToast({
            title: "网络似乎有问题，已保存到本地",
            icon: "none"
          });
          utils_envUtils.devLog.log("[groupStore] Offline group added:", newGroup.name, "with order:", newGroup.order);
          utils_envUtils.devLog.log("[groupStore] Current groups after offline adding:", this.groups.map((g) => ({ id: g.id, name: g.name, order: g.order })));
          return id;
        } else {
          common_vendor.index.showToast({
            title: "创建分组时发生未知错误",
            icon: "none"
          });
          return null;
        }
      }
    },
    // 更新分组名称
    async updateGroup(id, name) {
      const group = this.groups.find((g) => g.id === id);
      if (group && !group.isDefault) {
        if (this._checkGroupNameExists(name, id)) {
          utils_envUtils.devLog.warn("[groupStore] updateGroup: 分组名称已存在 -", name);
          common_vendor.index.showToast({
            title: "分组名称已存在",
            icon: "none"
          });
          return;
        }
        try {
          const groupCenter = common_vendor.nr.importObject("group-center");
          const result = await groupCenter.updateGroup(id, { name });
          if (result.errCode === 0) {
            group.name = name;
            this._saveToStorage();
            this._triggerReactiveUpdate();
          } else {
            throw new Error(result.errMsg || "更新分组失败");
          }
        } catch (error) {
          utils_envUtils.devLog.error("更新分组失败:", error);
          group.name = name;
          group._needSync = true;
          this._saveToStorage();
          this._triggerReactiveUpdate();
          common_vendor.index.showToast({
            title: "已保存到本地，稍后将同步到云端",
            icon: "none"
          });
        }
      }
    },
    // 删除分组（优化版本：先本地删除，后台同步）
    // 
    // 优化后的删除流程：
    // 1. 立即删除本地数据，提供即时响应（用户体验好）
    // 2. 后台异步删除云端数据，不阻塞用户操作
    // 3. 避免调用loadUserFromStorage()造成的重复登录验证
    // 4. 避免触发完整的数据重新同步
    async deleteGroup(id) {
      if (!id) {
        utils_envUtils.devLog.error("[groupStore] deleteGroup: 分组ID不能为空");
        common_vendor.index.showToast({
          title: "分组ID不能为空",
          icon: "none"
        });
        return false;
      }
      utils_envUtils.devLog.log("[groupStore] deleteGroup called with id:", id);
      const group = this.groups.find((g) => g.id === id);
      if (!group) {
        utils_envUtils.devLog.error("[groupStore] deleteGroup: 找不到指定的分组, id:", id);
        common_vendor.index.showToast({
          title: "找不到指定的分组",
          icon: "none"
        });
        return false;
      }
      if (group.isDefault) {
        utils_envUtils.devLog.error("[groupStore] deleteGroup: 不能删除默认分组, id:", id);
        common_vendor.index.showToast({
          title: "不能删除默认分组",
          icon: "none"
        });
        return false;
      }
      if (group && !group.isDefault) {
        this.groups = this.groups.filter((g) => g.id !== id);
        this._triggerReactiveUpdate();
        this._saveToStorage();
        utils_envUtils.devLog.log("[groupStore] deleteGroup: 本地删除成功, id:", id);
        this._deleteGroupFromCloud(id);
        return true;
      }
      utils_envUtils.devLog.log("[groupStore] deleteGroup: 不符合删除条件, id:", id);
      return false;
    },
    // 后台删除云端分组数据
    async _deleteGroupFromCloud(id) {
      try {
        utils_envUtils.devLog.log("[groupStore] 后台删除云端分组, id:", id);
        const groupCenter = common_vendor.nr.importObject("group-center");
        const result = await groupCenter.deleteGroup(id);
        if (result.errCode === 0) {
          utils_envUtils.devLog.log("[groupStore] 云端分组删除成功, id:", id);
        } else {
          utils_envUtils.devLog.warn("[groupStore] 云端分组删除失败, id:", id, "error:", result.errMsg);
        }
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] 后台删除云端分组失败, id:", id, "error:", error);
      }
    },
    // 更新分组顺序
    async updateGroupOrder(id, order) {
      const groupIndex = this.groups.findIndex((group) => group.id === id);
      if (groupIndex !== -1) {
        try {
          const groupCenter = common_vendor.nr.importObject("group-center");
          const result = await groupCenter.updateGroup(id, { order });
          if (result.errCode === 0) {
            this.groups[groupIndex] = {
              ...this.groups[groupIndex],
              order
            };
            this._saveToStorage();
            return true;
          } else {
            throw new Error(result.errMsg || "更新排序失败");
          }
        } catch (error) {
          utils_envUtils.devLog.error("更新分组排序失败:", error);
          this.groups[groupIndex] = {
            ...this.groups[groupIndex],
            order,
            _needSync: true
          };
          this._saveToStorage();
          return true;
        }
      }
      return false;
    },
    // 重置分组数据
    resetGroups() {
      common_vendor.index.removeStorageSync("groups");
      this._initDefaultGroups();
      return {
        success: true,
        message: "分组数据已重置"
      };
    },
    // 手动同步待上传的本地数据到云端（仅在用户主动操作时调用）
    async syncPendingData() {
      const pendingGroups = this.groups.filter((group) => group._needSync);
      if (pendingGroups.length === 0) {
        utils_envUtils.devLog.log("[groupStore] No pending groups to sync");
        return;
      }
      utils_envUtils.devLog.log(`[groupStore] Manual sync triggered for ${pendingGroups.length} pending groups`);
      for (const group of pendingGroups) {
        try {
          const groupCenter = common_vendor.nr.importObject("group-center");
          if (group.id.startsWith("group_")) {
            const result = await groupCenter.createGroup({
              name: group.name,
              icon: group.icon || "",
              color: group.color || "#8a2be2"
            });
            if (result.errCode === 0) {
              group.id = result.data.id;
              delete group._needSync;
              utils_envUtils.devLog.log(`[groupStore] Successfully synced new group: ${group.name}`);
            }
          } else {
            const result = await groupCenter.updateGroup(group.id, {
              name: group.name,
              order: group.order
            });
            if (result.errCode === 0) {
              delete group._needSync;
              utils_envUtils.devLog.log(`[groupStore] Successfully synced updated group: ${group.name}`);
            }
          }
        } catch (error) {
          if (error.message && (error.message.includes("分组不存在") || error.message.includes("无权限") || error.message.includes("not found") || error.message.includes("permission"))) {
            utils_envUtils.devLog.log(`[groupStore] 多设备同步冲突处理: 分组 "${group.name}" 在云端不存在，可能已被其他设备删除`);
            delete group._needSync;
            if (!group.isDefault) {
              const localIndex = this.groups.findIndex((g) => g.id === group.id);
              if (localIndex !== -1) {
                utils_envUtils.devLog.log(`[groupStore] 清理本地已删除的分组: ${group.name}`);
                this.groups.splice(localIndex, 1);
              }
            }
          } else {
            utils_envUtils.devLog.error("同步分组失败:", group.name, error);
          }
        }
      }
      this._saveToStorage();
    },
    // 智能同步 - 只同步有差异的数据（用于下拉刷新）
    async smartSync() {
      utils_envUtils.devLog.log("[groupStore] Starting smart sync...");
      if (!this.isOnline) {
        utils_envUtils.devLog.warn("[groupStore] Network unavailable, skipping smart sync");
        return { hasUpdates: false, updatedCount: 0, reason: "offline" };
      }
      try {
        const pendingGroups = this.groups.filter((group) => group._needSync);
        if (pendingGroups.length > 0) {
          utils_envUtils.devLog.log(`[groupStore] Uploading ${pendingGroups.length} pending groups...`);
          await this.syncPendingData();
        }
        const groupCenter = common_vendor.nr.importObject("group-center");
        const summaryResult = await groupCenter.getGroupSyncSummary();
        if (summaryResult.errCode !== 0) {
          throw new Error(summaryResult.errMsg || "获取云端分组摘要失败");
        }
        const cloudSummary = summaryResult.data || {};
        utils_envUtils.devLog.log("[groupStore] Cloud summary received:", cloudSummary);
        const localSummary = this._generateLocalSummary();
        utils_envUtils.devLog.log("[groupStore] Local summary:", localSummary);
        const needsSync = this._compareDataSummaries(localSummary, cloudSummary);
        if (!needsSync) {
          utils_envUtils.devLog.log("[groupStore] Data is up to date, no sync needed");
          return { hasUpdates: false, updatedCount: 0, reason: "up_to_date" };
        }
        utils_envUtils.devLog.log("[groupStore] Data differences detected, performing incremental sync...");
        const syncResult = await this._performIncrementalSync(cloudSummary);
        this.cleanupInvalidSyncMarkers();
        utils_envUtils.devLog.log("[groupStore] Smart sync completed:", syncResult);
        return syncResult;
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] Smart sync failed:", error);
        throw error;
      }
    },
    // 生成本地数据摘要
    _generateLocalSummary() {
      const nonDefaultGroups = this.groups.filter((g) => !g.isDefault);
      return {
        count: nonDefaultGroups.length,
        lastModified: this._getLocalGroupLastModified(),
        ids: nonDefaultGroups.map((g) => g.id).sort(),
        checksum: this._calculateGroupsChecksum(nonDefaultGroups)
      };
    },
    // 计算分组数据校验和
    _calculateGroupsChecksum(groups) {
      const dataStr = groups.map((g) => `${g._id || g.id}:${g.name}:${g.updateDate || g.createDate}:${g.order}`).sort().join("|");
      let hash = 0;
      for (let i = 0; i < dataStr.length; i++) {
        const char = dataStr.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash;
      }
      return hash.toString(36);
    },
    // 对比数据摘要
    _compareDataSummaries(localSummary, cloudSummary) {
      if (localSummary.count !== cloudSummary.count) {
        utils_envUtils.devLog.log("[groupStore] Count difference detected:", localSummary.count, "vs", cloudSummary.count);
        return true;
      }
      if (localSummary.checksum !== cloudSummary.checksum) {
        utils_envUtils.devLog.log("[groupStore] Checksum difference detected:", localSummary.checksum, "vs", cloudSummary.checksum);
        return true;
      }
      if (cloudSummary.lastModified && localSummary.lastModified) {
        const cloudTime = new Date(cloudSummary.lastModified).getTime();
        const localTime = new Date(localSummary.lastModified).getTime();
        if (cloudTime > localTime) {
          utils_envUtils.devLog.log("[groupStore] Cloud data is newer:", cloudSummary.lastModified, "vs", localSummary.lastModified);
          return true;
        }
      }
      return false;
    },
    // 执行增量同步
    async _performIncrementalSync(cloudSummary) {
      const groupCenter = common_vendor.nr.importObject("group-center");
      const result = await groupCenter.getGroupList({ includeDeleted: true });
      if (result.errCode !== 0) {
        throw new Error(result.errMsg || "获取云端分组数据失败");
      }
      const cloudGroups = result.data || [];
      const beforeCount = this.groups.length;
      const mergedData = this._mergeGroupData(this.groups, cloudGroups);
      await this._safeUpdateGroups(mergedData);
      const afterCount = this.groups.length;
      const updatedCount = Math.abs(afterCount - beforeCount);
      this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
      this._saveToStorage();
      return {
        hasUpdates: true,
        updatedCount,
        beforeCount,
        afterCount,
        reason: "incremental_sync"
      };
    },
    // 安全更新分组数据
    async _safeUpdateGroups(newData) {
      try {
        await new Promise((resolve) => {
          if (this.$nextTick) {
            this.$nextTick(() => {
              this.groups = newData;
              resolve();
            });
          } else {
            setTimeout(() => {
              this.groups = newData;
              resolve();
            }, 0);
          }
        });
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] Error in safe update, falling back to direct assignment:", error);
        this.groups = newData;
      }
    },
    // 清理无效的同步标记（多设备冲突后的清理）
    cleanupInvalidSyncMarkers() {
      let cleanedCount = 0;
      this.groups.forEach((group) => {
        if (group._needSync && (!group.id || !group.name)) {
          utils_envUtils.devLog.log(`[groupStore] 清理无效的同步标记: ${group.name || "未知分组"}`);
          delete group._needSync;
          cleanedCount++;
        }
      });
      if (cleanedCount > 0) {
        utils_envUtils.devLog.log(`[groupStore] 已清理 ${cleanedCount} 个无效的同步标记`);
        this._saveToStorage();
      }
      return cleanedCount;
    },
    // 手动触发完整同步（供用户主动调用）
    async manualSync() {
      if (!this.isOnline) {
        common_vendor.index.__f__("log", "at store/group.js:1230", "[groupStore] 当前网络不可用，跳过同步");
        return;
      }
      try {
        await this.syncPendingData();
        await this.syncFromCloud();
        utils_envUtils.devLog.log("[groupStore] Manual sync completed");
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] Manual sync failed:", error);
        common_vendor.index.__f__("log", "at store/group.js:1248", "[groupStore] 同步失败");
      }
    },
    // 保存到本地存储
    _saveToStorage() {
      common_vendor.index.setStorageSync("groups", JSON.stringify(this.groups));
      common_vendor.index.setStorageSync("groupsLastSyncTime", this.lastSyncTime);
    },
    // 清理本地数据（用于重新登录时清除旧用户数据）
    clearLocalData() {
      utils_envUtils.devLog.log("[groupStore] Clearing user-specific data...");
      this.groups = this.groups.filter((group) => group.isDefault === true);
      this.ensureDefaultGroups();
      this.lastSyncTime = null;
      this.isLoading = false;
      common_vendor.index.removeStorageSync("groups");
      common_vendor.index.removeStorageSync("groupsLastSyncTime");
      this._saveToStorage();
      utils_envUtils.devLog.log("[groupStore] User-specific data cleared, default groups preserved");
    },
    // 强制重新初始化（用于登录后重新同步）
    async forceInit() {
      utils_envUtils.devLog.log("[groupStore] Force initialization...");
      this.groups = [];
      this.lastSyncTime = null;
      this.isLoading = false;
      common_vendor.index.removeStorageSync("groups");
      common_vendor.index.removeStorageSync("groupsLastSyncTime");
      await this.syncFromCloud();
      this.ensureDefaultGroups();
      utils_envUtils.devLog.log("[groupStore] Force initialization completed");
    },
    // 清理重复分组和修复排序
    async cleanupAndFixGroups() {
      utils_envUtils.devLog.log("[groupStore] Starting group cleanup and fix...");
      try {
        const groupNames = /* @__PURE__ */ new Map();
        const duplicates = [];
        this.groups.forEach((group, index) => {
          if (!group.isDefault) {
            const lowerName = group.name.toLowerCase();
            if (groupNames.has(lowerName)) {
              duplicates.push({
                index,
                group,
                originalIndex: groupNames.get(lowerName).index
              });
            } else {
              groupNames.set(lowerName, { group, index });
            }
          }
        });
        if (duplicates.length > 0) {
          utils_envUtils.devLog.log("[groupStore] Found and removing", duplicates.length, "duplicate groups");
          duplicates.sort((a, b) => b.index - a.index);
          duplicates.forEach((duplicate) => {
            this.groups.splice(duplicate.index, 1);
          });
        }
        const fixedGroups = this._fixGroupOrder(this.groups);
        this.groups = fixedGroups;
        this._saveToStorage();
        utils_envUtils.devLog.log("[groupStore] Group cleanup completed");
        if (duplicates.length > 0) {
          common_vendor.index.showToast({
            title: `已清理${duplicates.length}个重复分组`,
            icon: "success",
            duration: 2e3
          });
        }
        return {
          duplicatesRemoved: duplicates.length,
          totalGroups: this.groups.length
        };
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] Group cleanup failed:", error);
        return { error: error.message };
      }
    },
    // 手动触发数据修复（供用户或开发者调用）
    async repairGroupData() {
      try {
        const result = await this.cleanupAndFixGroups();
        if (this.isOnline) {
          await this.syncFromCloud();
        }
        utils_envUtils.devLog.log("[groupStore] Data repair completed");
        return result;
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] Repair failed:", error);
        common_vendor.index.__f__("log", "at store/group.js:1388", "[groupStore] 数据修复失败");
        throw error;
      }
    },
    // 触发响应式更新（确保UI立即响应）
    _triggerReactiveUpdate() {
      try {
        this.$nextTick && this.$nextTick(() => {
          utils_envUtils.devLog.log("[groupStore] Reactive update triggered via nextTick, groups count:", this.groups.length);
        });
        if (this.groups && this.groups.length > 0) {
          const updatedGroups = this.groups.map((group) => ({ ...group }));
          this.groups = updatedGroups;
        }
        utils_envUtils.devLog.log("[groupStore] Gentle reactive update completed, groups count:", this.groups.length);
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] Reactive update failed:", error);
        this.groups = [...this.groups || []];
      }
    },
    // 调试方法：检查分组数据完整性（仅开发环境）
    debugGroupData() {
      utils_envUtils.devLog.log("[groupStore] === 分组数据调试信息 ===");
      utils_envUtils.devLog.log("[groupStore] 总分组数量:", this.groups.length);
      this.groups.forEach((group, index) => {
        utils_envUtils.devLog.log(`[groupStore] 分组 ${index}:`, {
          id: group.id,
          _id: group._id,
          name: group.name,
          isDefault: group.isDefault,
          _needSync: group._needSync,
          _source: group._source
        });
      });
      const pendingGroups = this.groups.filter((g) => g._needSync);
      utils_envUtils.devLog.log("[groupStore] 待同步分组数量:", pendingGroups.length);
      if (pendingGroups.length > 0) {
        utils_envUtils.devLog.log("[groupStore] 待同步分组详情:", pendingGroups.map((g) => ({
          id: g.id,
          name: g.name
        })));
      }
      return {
        totalCount: this.groups.length,
        pendingCount: pendingGroups.length,
        groups: this.groups.map((g) => ({
          id: g.id,
          _id: g._id,
          name: g.name,
          isDefault: g.isDefault,
          _needSync: g._needSync
        }))
      };
    },
    /**
     * 🚀 处理实时数据更新
     * 当收到实时推送时调用此方法
     */
    async _updateLocalFromRealtimeData(realtimeData) {
      try {
        utils_envUtils.devLog.log("[groupStore] 📂 收到实时分组数据更新:", realtimeData.length, "个分组");
        const mergedData = this._mergeGroupData(this.groups, realtimeData);
        this.groups = mergedData;
        this._saveToStorage();
        this._triggerReactiveUpdate();
        utils_envUtils.devLog.log("[groupStore] ✅ 实时分组数据更新完成");
      } catch (error) {
        utils_envUtils.devLog.error("[groupStore] 处理实时分组数据更新失败:", error);
      }
    },
    // 重新整理分组顺序，确保正确的排序
    reorganizeGroupOrder() {
      utils_envUtils.devLog.log("[groupStore] Reorganizing group order...");
      const defaultGroups = this.groups.filter((g) => g.isDefault);
      const userGroups = this.groups.filter((g) => !g.isDefault);
      defaultGroups.forEach((group, index) => {
        if (group.id === "all") {
          group.order = 0;
        } else if (group.id === "gift") {
          group.order = 1;
        } else if (group.id === "friend-visible") {
          group.order = 2;
        } else {
          group.order = index;
        }
      });
      userGroups.sort((a, b) => (a.order || 999) - (b.order || 999));
      userGroups.forEach((group, index) => {
        group.order = 3 + index;
      });
      this.groups = [...defaultGroups, ...userGroups];
      this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
      this._saveToStorage();
      utils_envUtils.devLog.log("[groupStore] Group order reorganized:", this.groups.map((g) => ({
        id: g.id,
        name: g.name,
        order: g.order,
        isDefault: g.isDefault
      })));
      this._triggerReactiveUpdate();
    },
    // 🚀 新增：支持新同步架构的方法
    /**
     * 添加分组到列表（供实时同步调用）
     */
    addGroupToList(group) {
      const existingIndex = this.groups.findIndex((g) => g.id === group.id || g._id === group._id);
      if (existingIndex === -1) {
        this.groups.push({
          ...group,
          id: group.id || group._id
          // 确保有 id 字段
        });
        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
        this._saveToStorage();
        utils_envUtils.devLog.log("[groupStore] 🚀 添加分组到列表:", group.name);
      }
    },
    /**
     * 更新列表中的分组（供实时同步调用）
     */
    updateGroupInList(group) {
      const index = this.groups.findIndex((g) => g.id === group.id || g._id === group._id);
      if (index !== -1) {
        this.groups[index] = {
          ...group,
          id: group.id || group._id
          // 确保有 id 字段
        };
        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
        this._saveToStorage();
        utils_envUtils.devLog.log("[groupStore] 🚀 更新列表中的分组:", group.name);
      }
    },
    /**
     * 从列表中移除分组（供实时同步调用）
     */
    removeGroupFromList(groupId) {
      const index = this.groups.findIndex((g) => g.id === groupId || g._id === groupId);
      if (index !== -1) {
        const groupName = this.groups[index].name;
        this.groups.splice(index, 1);
        this._saveToStorage();
        utils_envUtils.devLog.log("[groupStore] 🚀 从列表中移除分组:", groupName);
      }
    },
    // 注意：syncFromCloud 方法已在上面定义，这里移除重复定义
    // ==================== 新增同步优化方法 ====================
    /**
     * 通用分组名称检查方法（避免重复逻辑）
     */
    _checkGroupNameExists(name, excludeId = null) {
      if (!name)
        return false;
      const lowerName = name.toLowerCase();
      return this.groups.some((group) => {
        if (excludeId && (group.id === excludeId || group._id === excludeId)) {
          return false;
        }
        return group.name.toLowerCase() === lowerName;
      });
    },
    /**
     * 从同步中添加分组（避免触发推送）
     */
    addGroupFromSync(group) {
      const existingIndex = this.groups.findIndex((g) => g.id === group.id || g._id === group._id);
      if (existingIndex === -1) {
        this.groups.push({
          ...group,
          id: group._id || group.id
        });
        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
        this._saveToStorage();
        utils_envUtils.devLog.log(`[groupStore] 从同步添加分组: ${group.name}`);
      }
    },
    /**
     * 从同步中更新分组（避免触发推送）
     */
    updateGroupFromSync(group) {
      const index = this.groups.findIndex((g) => g.id === group.id || g._id === group._id);
      if (index !== -1) {
        this.groups[index] = {
          ...group,
          id: group._id || group.id
        };
        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
        this._saveToStorage();
        utils_envUtils.devLog.log(`[groupStore] 从同步更新分组: ${group.name}`);
      }
    },
    /**
     * 从同步中删除分组（避免触发推送）
     */
    removeGroupById(groupId) {
      const index = this.groups.findIndex((g) => g.id === groupId || g._id === groupId);
      if (index !== -1) {
        const removedGroup = this.groups.splice(index, 1)[0];
        this._saveToStorage();
        utils_envUtils.devLog.log(`[groupStore] 从同步删除分组: ${removedGroup.name}`);
      }
    }
  }
});
exports.useGroupStore = useGroupStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/group.js.map

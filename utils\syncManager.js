/**
 * uni-push 2.0 多设备同步管理器
 * 基于事件驱动的真正实时推送同步，无需轮询，资源高效
 */

import { devLog } from './envUtils.js'
import { BatchManager } from './debouncer.js'

// 动态导入Store模块
let useWishStore = null
let useGroupStore = null
let useUserStore = null

// 动态导入函数
async function importStores() {
  if (!useWishStore) {
    try {
      const wishModule = await import('@/store/wish.js')
      useWishStore = wishModule.useWishStore

      const groupModule = await import('@/store/group.js')
      useGroupStore = groupModule.useGroupStore

      const userModule = await import('@/store/user.js')
      useUserStore = userModule.useUserStore
    } catch (error) {
      devLog.error('[SyncManager] 导入Store模块失败:', error)
    }
  }
}

class SyncManager {
  constructor() {
    this.userId = null
    this.isInitialized = false
    this.isOnline = true
    this.pushClientId = null

    // 推送同步状态
    this.pushEnabled = false
    this.syncStatus = {
      issyncing: false,
      lastSyncResult: null,
      needSync: false
    }

    // 事件监听器
    this.listeners = {}

    // 同步队列（用于离线时缓存）
    this.syncQueue = []
    this.isProcessingQueue = false

    // 🚀 增强防重复机制
    this._processedPushes = new Map() // 已处理的推送记录
    this._activeSyncOperations = new Map() // 正在进行的同步操作
    this._syncLocks = new Map() // 同步锁，防止并发同步

    // 使用批量管理器优化推送逻辑
    this.batchManager = new BatchManager(
      (batch) => this._processBatchPush(batch),
      { delay: 500, maxSize: 20 }
    )

    devLog.log('[SyncManager] 🚀 初始化 uni-push 2.0 多设备同步管理器')
  }

  /**
   * 初始化同步管理器
   */
  async init() {
    if (this.isInitialized) {
      devLog.log('[SyncManager] 已初始化，跳过')
      return
    }

    devLog.log('[SyncManager] 🚀 启动 uni-push 2.0 多设备同步系统...')

    try {
      // 首先导入Store模块
      await importStores()

      // 获取用户信息
      this.userId = await this._getCurrentUserId()
      devLog.log('[SyncManager] 获取到用户ID:', this.userId)

      if (!this.userId) {
        devLog.warn('[SyncManager] 用户未登录，跳过同步初始化')
        return
      }

      // 初始化 uni-push 推送（允许失败）
      await this._initUniPush()

      // 设置网络监听
      this._setupNetworkListeners()

      this.isInitialized = true

      if (this.pushEnabled) {
        devLog.log('[SyncManager] ✅ uni-push 2.0 多设备同步系统初始化完成')
      } else {
        devLog.log('[SyncManager] ✅ 同步系统初始化完成（无推送模式）')
      }

    } catch (error) {
      devLog.error('[SyncManager] 初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化 uni-push 推送
   */
  async _initUniPush() {
    try {
      devLog.log('[SyncManager] 🔄 初始化 uni-push 推送...')

      // 检查是否在支持 uni-push 的环境中
      if (!uni.getPushClientId) {
        devLog.warn('[SyncManager] 当前环境不支持 uni-push，跳过推送初始化')
        this.pushEnabled = false
        return
      }

      // 获取推送客户端标识
      const pushResult = await new Promise((resolve, reject) => {
        uni.getPushClientId({
          success: (res) => {
            devLog.log('[SyncManager] 获取推送客户端ID成功:', res.cid)
            resolve(res)
          },
          fail: (err) => {
            devLog.warn('[SyncManager] 获取推送客户端ID失败:', err)
            // 在开发环境中，uni-push 可能不可用，这是正常的
            if (err.errMsg && err.errMsg.includes('uniPush is not enabled')) {
              devLog.log('[SyncManager] uni-push 未启用，可能在开发环境中，将使用降级模式')
              resolve({ cid: null, devMode: true })
            } else {
              reject(err)
            }
          }
        })
      })

      if (pushResult.devMode || !pushResult.cid) {
        devLog.log('[SyncManager] ⚠️ 推送功能在当前环境不可用，使用降级模式')
        this.pushEnabled = false
        this.pushClientId = null
      } else {
        this.pushClientId = pushResult.cid
        this.pushEnabled = true
        devLog.log('[SyncManager] ✅ uni-push 推送初始化完成')
      }

      // 监听推送消息
      this._setupPushListener()

    } catch (error) {
      devLog.error('[SyncManager] uni-push 初始化失败:', error)
      this.pushEnabled = false
      // 不再抛出错误，允许同步管理器在没有推送的情况下继续工作
      devLog.log('[SyncManager] 将在无推送模式下继续运行')
    }
  }

  /**
   * 设置推送消息监听
   */
  _setupPushListener() {
    devLog.log('[SyncManager] 🔔 设置推送消息监听')

    // 监听推送消息（需要在 App.vue 中调用 uni.onPushMessage）
    // 这里提供处理推送消息的方法
  }

  /**
   * 处理推送消息（由 App.vue 调用）
   */
  async handlePushMessage(message) {
    try {
      devLog.log('[SyncManager] 📨 收到推送消息:', JSON.stringify(message))

      if (!message || !message.payload) {
        devLog.warn('[SyncManager] 推送消息格式无效')
        return
      }

      let payload
      try {
        payload = typeof message.payload === 'string'
          ? JSON.parse(message.payload)
          : message.payload
      } catch (parseError) {
        devLog.error('[SyncManager] 推送消息解析失败:', parseError)
        return
      }

      devLog.log('[SyncManager] 📋 解析后的推送载荷:', payload)

      // 处理不同类型的推送
      if (payload.type === 'data_sync') {
        await this._handleDataSyncPush(payload)
      } else if (payload.type === 'batch_data_sync') {
        await this._handleBatchDataSyncPush(payload)
      } else if (payload.type === 'user_status') {
        await this._handleUserStatusPush(payload)
      } else {
        devLog.warn('[SyncManager] 未知的推送消息类型:', payload.type)
      }

    } catch (error) {
      devLog.error('[SyncManager] 处理推送消息失败:', error)
    }
  }

  /**
   * 处理数据同步推送
   */
  async _handleDataSyncPush(payload) {
    try {
      const { dataType, action, dataId, timestamp } = payload
      devLog.log(`[SyncManager] 🔄 处理数据同步推送: ${dataType}.${action}`)

      // 防重复处理：检查是否已经处理过这个推送
      const pushKey = `${dataType}_${action}_${dataId}_${timestamp}`
      if (this._isRecentlyProcessed(pushKey)) {
        devLog.log(`[SyncManager] ⏭️ 跳过重复推送: ${pushKey}`)
        return
      }

      // 记录已处理的推送
      this._markAsProcessed(pushKey)

      // 确保Store模块已导入
      await importStores()

      // 🚀 同步合并：独立同步，减少数据传输
      if (dataType === 'wish' && useWishStore) {
        const wishStore = useWishStore()
        await this._syncSpecificWishData(action, dataId, wishStore)
      } else if (dataType === 'group' && useGroupStore) {
        const groupStore = useGroupStore()
        await this._syncSpecificGroupData(action, dataId, groupStore)
      } else if (dataType === 'comment') {
        const { useCommentStore } = await import('@/store/comment.js')
        const commentStore = useCommentStore()
        await this._syncSpecificCommentData(action, dataId, commentStore)
      }

      devLog.log(`[SyncManager] ✅ ${dataType}数据同步完成`)

      // 触发同步完成事件
      this._emit('data_synced', { dataType, action, dataId, timestamp })

    } catch (error) {
      devLog.error('[SyncManager] 处理数据同步推送失败:', error)
    }
  }

  /**
   * 处理批量数据同步推送
   */
  async _handleBatchDataSyncPush(payload) {
    try {
      const { changes, timestamp } = payload
      devLog.log(`[SyncManager] 🔄 处理批量数据同步推送: ${changes.length}个变化`)

      // 防重复处理
      const batchKey = `batch_${timestamp}_${changes.length}`
      if (this._isRecentlyProcessed(batchKey)) {
        devLog.log(`[SyncManager] ⏭️ 跳过重复批量推送: ${batchKey}`)
        return
      }
      this._markAsProcessed(batchKey)

      // 确保Store模块已导入
      await importStores()

      // 🚀 优化：按数据类型分组处理，减少云函数调用
      const changesByType = this._groupChangesByType(changes)

      // 🚀 同步合并：同时调用但不合并内容，减少数据传输
      const syncPromises = []

      // 独立处理心愿数据
      if (changesByType.wish && useWishStore) {
        const wishStore = useWishStore()
        syncPromises.push(this._batchSyncWishData(changesByType.wish, wishStore))
      }

      // 独立处理评论数据
      if (changesByType.comment && useCommentStore) {
        const commentStore = useCommentStore()
        syncPromises.push(this._batchSyncCommentData(changesByType.comment, commentStore))
      }

      // 独立处理分组数据
      if (changesByType.group && useGroupStore) {
        const groupStore = useGroupStore()
        syncPromises.push(this._batchSyncGroupData(changesByType.group, groupStore))
      }

      await Promise.allSettled(syncPromises)

      devLog.log(`[SyncManager] ✅ 批量数据同步完成`)

      // 触发批量同步完成事件
      this._emit('batch_data_synced', { changes, timestamp })

    } catch (error) {
      devLog.error('[SyncManager] 处理批量数据同步推送失败:', error)
    }
  }

  /**
   * 处理用户状态推送
   */
  async _handleUserStatusPush(payload) {
    try {
      const { action, deviceInfo, timestamp } = payload
      devLog.log(`[SyncManager] 👤 处理用户状态推送: ${action}`)

      // 触发用户状态变化事件
      this._emit('user_status_changed', { action, deviceInfo, timestamp })

    } catch (error) {
      devLog.error('[SyncManager] 处理用户状态推送失败:', error)
    }
  }

  /**
   * 数据变化通知（Store调用）- 支持批量推送优化
   */
  async notifyDataChange(dataType, action, data, options = {}) {
    devLog.log(`[SyncManager] 📝 数据变化通知: ${dataType}.${action}`)

    if (!this.pushEnabled || !this.userId) {
      devLog.warn('[SyncManager] 推送未启用或用户未登录，跳过推送')
      return
    }

    // 🚀 优化：支持批量推送，减少云函数调用
    if (options.batch) {
      this._addToBatchQueue(dataType, action, data)
      return
    }

    try {
      // 调用推送云对象，通知其他设备
      const syncPush = uniCloud.importObject('sync-push')
      const result = await syncPush.pushDataSync({
        userId: this.userId,
        dataType,
        action,
        dataId: data?.id || data?._id,
        data: data
      })

      if (result.errCode === 0) {
        devLog.log(`[SyncManager] ✅ 推送通知发送成功: ${dataType}.${action}`)
      } else {
        devLog.error('[SyncManager] 推送通知发送失败:', result.errMsg)
      }

    } catch (error) {
      devLog.error('[SyncManager] 发送推送通知失败:', error)
      // 推送失败不影响主要业务逻辑
    }
  }

  /**
   * 手动同步数据（用于用户主动刷新）- 🚀 增强防重复机制
   */
  async manualSync() {
    const syncKey = 'manual_sync'

    // 🚀 检查是否已有同步操作在进行
    if (this._isSyncInProgress(syncKey)) {
      devLog.log('[SyncManager] ⏭️ 手动同步已在进行中，跳过重复请求')
      return { success: true, skipped: true }
    }

    // 🚀 获取同步锁
    if (!(await this._acquireSyncLock(syncKey))) {
      devLog.warn('[SyncManager] ⚠️ 无法获取同步锁，跳过同步')
      return { success: false, error: '同步锁获取失败' }
    }

    try {
      this._markSyncStart(syncKey)
      devLog.log('[SyncManager] 🔄 手动同步数据...')

      // 确保Store模块已导入
      await importStores()
      devLog.log('[SyncManager] Store模块导入状态:', {
        useWishStore: !!useWishStore,
        useGroupStore: !!useGroupStore,
        useUserStore: !!useUserStore
      })

      const syncPromises = []

      if (useWishStore) {
        const wishStore = useWishStore()
        devLog.log('[SyncManager] 开始同步心愿数据...')
        syncPromises.push(
          wishStore.syncFromCloud().then(result => {
            devLog.log('[SyncManager] 心愿数据同步成功:', result)
            return result
          }).catch(err => {
            devLog.error('[SyncManager] 心愿数据同步失败:', err)
            return { error: err, type: 'wish' }
          })
        )
      } else {
        devLog.warn('[SyncManager] useWishStore 未导入')
      }

      if (useGroupStore) {
        const groupStore = useGroupStore()
        devLog.log('[SyncManager] 开始同步分组数据...')
        syncPromises.push(
          groupStore.syncFromCloud().then(result => {
            devLog.log('[SyncManager] 分组数据同步成功:', result)
            return result
          }).catch(err => {
            devLog.error('[SyncManager] 分组数据同步失败:', err)
            return { error: err, type: 'group' }
          })
        )
      } else {
        devLog.warn('[SyncManager] useGroupStore 未导入')
      }

      const results = await Promise.allSettled(syncPromises)

      // 🚀 统计同步结果
      const errors = results
        .filter(result => result.status === 'rejected' || result.value?.error)
        .map(result => result.reason || result.value?.error)

      const success = errors.length === 0

      devLog.log(`[SyncManager] ${success ? '✅' : '⚠️'} 手动同步完成，错误数: ${errors.length}`)

      this._emit('manual_sync_completed', {
        success,
        errors: errors.length > 0 ? errors : undefined
      })

      return { success, errors }

    } catch (error) {
      devLog.error('[SyncManager] 手动同步失败:', error)
      this._emit('manual_sync_completed', { success: false, error })
      return { success: false, error }
    } finally {
      this._markSyncEnd(syncKey)
      this._releaseSyncLock(syncKey)
    }
  }

  /**
   * 获取当前用户ID
   */
  async _getCurrentUserId() {
    try {
      // 从存储获取
      const userInfo = uni.getStorageSync('userInfo')
      if (userInfo) {
        // 解析存储的用户信息（可能是字符串）
        let parsedUserInfo = userInfo
        if (typeof userInfo === 'string') {
          try {
            parsedUserInfo = JSON.parse(userInfo)
          } catch (e) {
            devLog.warn('[SyncManager] 用户信息解析失败:', e)
          }
        }
        
        if (parsedUserInfo && parsedUserInfo.uid) {
          return parsedUserInfo.uid
        }
      }
      
      // 从Store获取
      try {
        await importStores()
        if (useUserStore) {
          const userStore = useUserStore()
          if (userStore && userStore.userInfo && userStore.userInfo.uid) {
            return userStore.userInfo.uid
          }
        }
      } catch (storeError) {
        devLog.warn('[SyncManager] Store获取用户信息失败:', storeError)
      }
      
      return null
    } catch (error) {
      devLog.error('[SyncManager] 获取用户ID失败:', error)
      return null
    }
  }

  /**
   * 设置网络监听
   */
  _setupNetworkListeners() {
    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      const wasOnline = this.isOnline
      this.isOnline = res.isConnected

      devLog.log(`[SyncManager] 网络状态: ${this.isOnline ? '在线' : '离线'}`)

      // 网络恢复时触发手动同步
      if (!wasOnline && this.isOnline) {
        devLog.log('[SyncManager] 🌐 网络恢复，触发同步')
        setTimeout(() => {
          devLog.log('[SyncManager] 执行网络恢复同步...')
          this.manualSync().then(result => {
            devLog.log('[SyncManager] 网络恢复同步完成:', result)
          }).catch(error => {
            devLog.error('[SyncManager] 网络恢复同步失败:', error)
          })
        }, 1000)
      }
    })
  }

  /**
   * 应用前台显示时的处理（由 App.vue 调用）
   */
  onAppShow() {
    devLog.log('[SyncManager] 📱 应用前台显示')

    // 应用回到前台时，主动同步一次数据
    if (this.isInitialized && this.isOnline) {
      devLog.log('[SyncManager] 应用前台显示，准备同步数据...')
      setTimeout(() => {
        devLog.log('[SyncManager] 执行应用前台同步...')
        this.manualSync().then(result => {
          devLog.log('[SyncManager] 应用前台同步完成:', result)
        }).catch(error => {
          devLog.error('[SyncManager] 应用前台同步失败:', error)
        })
      }, 1000)
    } else {
      devLog.warn('[SyncManager] 应用前台显示，但同步条件不满足:', {
        isInitialized: this.isInitialized,
        isOnline: this.isOnline
      })
    }
  }

  /**
   * 应用进入后台时的处理（由 App.vue 调用）
   */
  onAppHide() {
    devLog.log('[SyncManager] 📱 应用进入后台')
    // uni-push 2.0 模式下，后台也能接收推送，无需特殊处理
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isInitialized: this.isInitialized,
      isOnline: this.isOnline,
      pushEnabled: this.pushEnabled,
      pushClientId: this.pushClientId,
      issyncing: this.syncStatus.issyncing,
      lastSyncResult: this.syncStatus.lastSyncResult,
      userId: this.userId,
      syncMode: 'uni-push-2.0'
    }
  }

  /**
   * 获取推送状态信息
   */
  getPushStatus() {
    return {
      supported: !!uni.getPushClientId,
      enabled: this.pushEnabled,
      clientId: this.pushClientId,
      initialized: this.isInitialized,
      userId: this.userId
    }
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }
    this.listeners[event].push(callback)
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (!this.listeners[event]) return
    
    const index = this.listeners[event].indexOf(callback)
    if (index > -1) {
      this.listeners[event].splice(index, 1)
    }
  }

  /**
   * 触发事件
   */
  _emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          devLog.error('[SyncManager] 事件回调执行失败:', error)
        }
      })
    }
  }

  // ==================== 新增优化方法 ====================

  /**
   * 检查推送是否最近已处理（防重复）
   */
  _isRecentlyProcessed(key) {
    const now = Date.now()
    const processed = this._processedPushes.get(key)

    // 🚀 优化：缩短重复检测时间窗口到2分钟，提高响应性
    if (processed && (now - processed) < 2 * 60 * 1000) {
      devLog.log(`[SyncManager] 🔄 跳过重复处理: ${key}`)
      return true
    }

    return false
  }

  /**
   * 标记推送为已处理
   */
  _markAsProcessed(key) {
    const now = Date.now()
    this._processedPushes.set(key, now)

    // 🚀 优化：定期清理过期记录，避免内存泄漏
    if (this._processedPushes.size > 100) {
      this._cleanupProcessedRecords()
    }
  }

  /**
   * 🚀 新增：清理过期的处理记录
   */
  _cleanupProcessedRecords() {
    const now = Date.now()
    const tenMinutesAgo = now - 10 * 60 * 1000

    for (const [key, timestamp] of this._processedPushes.entries()) {
      if (timestamp < tenMinutesAgo) {
        this._processedPushes.delete(key)
      }
    }

    devLog.log(`[SyncManager] 🧹 清理过期记录，当前记录数: ${this._processedPushes.size}`)
  }

  /**
   * 🚀 新增：检查同步操作是否正在进行
   */
  _isSyncInProgress(syncKey) {
    return this._activeSyncOperations.has(syncKey)
  }

  /**
   * 🚀 新增：标记同步操作开始
   */
  _markSyncStart(syncKey) {
    this._activeSyncOperations.set(syncKey, Date.now())
    devLog.log(`[SyncManager] 🔄 开始同步操作: ${syncKey}`)
  }

  /**
   * 🚀 新增：标记同步操作结束
   */
  _markSyncEnd(syncKey) {
    this._activeSyncOperations.delete(syncKey)
    devLog.log(`[SyncManager] ✅ 完成同步操作: ${syncKey}`)
  }

  /**
   * 🚀 新增：获取同步锁
   */
  async _acquireSyncLock(lockKey, timeout = 30000) {
    const startTime = Date.now()

    while (this._syncLocks.has(lockKey)) {
      if (Date.now() - startTime > timeout) {
        devLog.warn(`[SyncManager] ⏰ 获取同步锁超时: ${lockKey}`)
        return false
      }
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    this._syncLocks.set(lockKey, Date.now())
    devLog.log(`[SyncManager] 🔒 获取同步锁: ${lockKey}`)
    return true
  }

  /**
   * 🚀 新增：释放同步锁
   */
  _releaseSyncLock(lockKey) {
    this._syncLocks.delete(lockKey)
    devLog.log(`[SyncManager] 🔓 释放同步锁: ${lockKey}`)
  }

  /**
   * 按数据类型分组变化
   */
  _groupChangesByType(changes) {
    const grouped = {}
    changes.forEach(change => {
      if (!grouped[change.dataType]) {
        grouped[change.dataType] = []
      }
      grouped[change.dataType].push(change)
    })
    return grouped
  }

  /**
   * 增量同步特定心愿数据（基础方法）
   */
  async _syncSpecificWishData(action, dataId, wishStore) {
    try {
      if (action === 'delete') {
        // 删除操作：直接从本地移除
        wishStore.removeWishById(dataId)
        devLog.log(`[SyncManager] 🗑️ 本地删除心愿: ${dataId}`)
      } else {
        // 创建/更新操作：获取特定数据
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.getWishById(dataId)

        if (result.errCode === 0 && result.data) {
          if (action === 'create') {
            wishStore.addWishFromSync(result.data)
            devLog.log(`[SyncManager] ➕ 同步新增心愿: ${result.data.title}`)
          } else if (action === 'update') {
            wishStore.updateWishFromSync(result.data)
            devLog.log(`[SyncManager] 🔄 同步更新心愿: ${result.data.title}`)
          }
        }
      }
    } catch (error) {
      devLog.error(`[SyncManager] 同步特定心愿数据失败:`, error)
      // 失败时回退到全量同步
      await wishStore.syncFromCloud()
    }
  }

  /**
   * 增量同步特定分组数据
   */
  async _syncSpecificGroupData(action, dataId, groupStore) {
    try {
      if (action === 'delete') {
        groupStore.removeGroupById(dataId)
        devLog.log(`[SyncManager] 🗑️ 本地删除分组: ${dataId}`)
      } else {
        const groupCenter = uniCloud.importObject('group-center')
        const result = await groupCenter.getGroupById(dataId)

        if (result.errCode === 0 && result.data) {
          if (action === 'create') {
            groupStore.addGroupFromSync(result.data)
            devLog.log(`[SyncManager] ➕ 同步新增分组: ${result.data.name}`)
          } else if (action === 'update') {
            groupStore.updateGroupFromSync(result.data)
            devLog.log(`[SyncManager] 🔄 同步更新分组: ${result.data.name}`)
          }
        }
      }
    } catch (error) {
      devLog.error(`[SyncManager] 同步特定分组数据失败:`, error)
      // 失败时回退到全量同步
      await groupStore.syncFromCloud()
    }
  }

  /**
   * 增量同步特定评论数据
   */
  async _syncSpecificCommentData(action, dataId, commentStore) {
    try {
      if (action === 'delete') {
        // 删除操作：直接从本地移除
        commentStore.removeCommentById(dataId)
        devLog.log(`[SyncManager] 🗑️ 本地删除评论: ${dataId}`)
      } else {
        // 创建/更新操作：获取特定数据
        const commentCenter = uniCloud.importObject('comment-center')
        const result = await commentCenter.getCommentById(dataId)

        if (result.errCode === 0 && result.data) {
          if (action === 'create') {
            commentStore.addCommentFromSync(result.data)
            devLog.log(`[SyncManager] ➕ 同步新增评论: ${dataId}`)
          } else if (action === 'update') {
            commentStore.updateCommentFromSync(result.data)
            devLog.log(`[SyncManager] 🔄 同步更新评论: ${dataId}`)
          }
        }
      }
    } catch (error) {
      devLog.error(`[SyncManager] 评论增量同步失败:`, error)
      // 增量同步失败时降级为相关心愿的评论全量同步
      if (action !== 'delete' && dataId) {
        try {
          const commentCenter = uniCloud.importObject('comment-center')
          const result = await commentCenter.getCommentById(dataId)
          if (result.errCode === 0 && result.data && result.data.wishId) {
            await commentStore.syncCommentsForWish(result.data.wishId, true, true)
          }
        } catch (fallbackError) {
          devLog.error(`[SyncManager] 评论同步降级失败:`, fallbackError)
        }
      }
    }
  }

  /**
   * 批量同步评论数据
   */
  async _batchSyncCommentData(changes, commentStore) {
    try {
      devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个评论变化`)

      // 按心愿ID分组处理
      const changesByWish = {}
      changes.forEach(change => {
        const wishId = change.data?.wishId || change.wishId
        if (wishId) {
          if (!changesByWish[wishId]) {
            changesByWish[wishId] = []
          }
          changesByWish[wishId].push(change)
        }
      })

      // 并行处理不同心愿的评论变化
      const syncPromises = Object.entries(changesByWish).map(async ([wishId, wishChanges]) => {
        // 对于每个心愿，重新同步其所有评论
        await commentStore.syncCommentsForWish(wishId, true, true)
      })

      await Promise.allSettled(syncPromises)
      devLog.log(`[SyncManager] ✅ 批量评论同步完成`)

    } catch (error) {
      devLog.error('[SyncManager] 批量评论同步失败:', error)
    }
  }

  /**
   * 批量同步心愿数据（基础方法）
   */
  async _batchSyncWishData(changes, wishStore) {
    try {
      devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个心愿变化`)

      // 分类处理不同操作
      const deletes = changes.filter(c => c.action === 'delete')
      const updates = changes.filter(c => c.action !== 'delete')

      // 处理删除操作
      deletes.forEach(change => {
        wishStore.removeWishById(change.dataId)
      })

      // 批量获取需要更新的数据
      if (updates.length > 0) {
        const wishCenter = uniCloud.importObject('wish-center')
        const dataIds = updates.map(c => c.dataId)
        const result = await wishCenter.getWishesByIds(dataIds)

        if (result.errCode === 0 && result.data) {
          result.data.forEach(wish => {
            const change = updates.find(c => c.dataId === wish._id)
            if (change.action === 'create') {
              wishStore.addWishFromSync(wish)
            } else if (change.action === 'update') {
              wishStore.updateWishFromSync(wish)
            }
          })
        }
      }

      devLog.log(`[SyncManager] ✅ 批量心愿同步完成`)
    } catch (error) {
      devLog.error(`[SyncManager] 批量心愿同步失败:`, error)
      // 失败时回退到全量同步
      await wishStore.syncFromCloud()
    }
  }

  /**
   * 批量同步分组数据
   */
  async _batchSyncGroupData(changes, groupStore) {
    try {
      devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个分组变化`)

      const deletes = changes.filter(c => c.action === 'delete')
      const updates = changes.filter(c => c.action !== 'delete')

      deletes.forEach(change => {
        groupStore.removeGroupById(change.dataId)
      })

      if (updates.length > 0) {
        const groupCenter = uniCloud.importObject('group-center')
        const dataIds = updates.map(c => c.dataId)
        const result = await groupCenter.getGroupsByIds(dataIds)

        if (result.errCode === 0 && result.data) {
          result.data.forEach(group => {
            const change = updates.find(c => c.dataId === group._id)
            if (change.action === 'create') {
              groupStore.addGroupFromSync(group)
            } else if (change.action === 'update') {
              groupStore.updateGroupFromSync(group)
            }
          })
        }
      }

      devLog.log(`[SyncManager] ✅ 批量分组同步完成`)
    } catch (error) {
      devLog.error(`[SyncManager] 批量分组同步失败:`, error)
      await groupStore.syncFromCloud()
    }
  }

  /**
   * 添加到批量推送队列（使用优化的批量管理器）
   */
  _addToBatchQueue(dataType, action, data) {
    this.batchManager.add({
      dataType,
      action,
      dataId: data?.id || data?._id,
      data
    })
  }

  /**
   * 处理批量推送（由 BatchManager 调用）
   */
  async _processBatchPush(batch) {
    try {
      devLog.log(`[SyncManager] 🚀 批量推送 ${batch.length} 个数据变化`)
      
      // 转换格式以兼容现有 API
      const changes = batch.map(item => ({
        dataType: item.dataType,
        action: item.action,
        dataId: item.dataId,
        data: item.data,
        timestamp: item.timestamp
      }))
      
      const syncPush = uniCloud.importObject('sync-push')
      const result = await syncPush.pushBatchDataSync({
        userId: this.userId,
        changes
      })

      if (result.errCode === 0) {
        devLog.log(`[SyncManager] ✅ 批量推送成功: ${changes.length}个变化`)
      } else {
        devLog.error('[SyncManager] 批量推送失败:', result.errMsg)
      }
    } catch (error) {
      devLog.error('[SyncManager] 批量推送失败:', error)
    }
  }

  /**
   * 销毁同步管理器
   */
  destroy() {
    devLog.log('[SyncManager] 🗑️ 销毁 uni-push 2.0 同步管理器')

    // 清理批量管理器
    if (this.batchManager) {
      this.batchManager.clear()
    }

    // 重置状态
    this.isInitialized = false
    this.pushEnabled = false
    this.pushClientId = null
    this.listeners = {}
    this.syncQueue = []
    this._processedPushes = null
  }
}

// 创建全局实例
const syncManager = new SyncManager()

export default syncManager 

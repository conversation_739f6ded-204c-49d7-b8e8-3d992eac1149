<template>
	<view class="wish-container"
		  ref="containerRef"
		  @click="closeAllSwipeActions"
		  @touchstart="handleContainerTouchStart"
		  @touchmove="handleContainerTouchMove"
		  @touchend="handleContainerTouchEnd"
		  :class="{'drag-mode': isDragging, 'pull-refresh-disabled': isDragging || isPullRefreshDisabled, 'gesture-disabled': isPullRefreshDisabled}">
		<!-- 分组选择器 -->
		<GroupSelector @group-change="onGroupChange" />

		<!-- 心愿列表 -->
		<view class="wish-list" id="wishListScrollView" @touchmove="onCardListTouch">
			<view class="wish-list-content">
				<!-- 心愿卡片 -->
				<transition-group name="list-item" tag="view" class="list-transition-group" :key="forceUpdateKey">
					<WishCard
						v-for="(wish, index) in wishStore.currentGroupWishes"
						:key="`${wish.id || wish._id}-${forceUpdateKey}`"
						:wish="wish"
						:index="index"
						:active-card-id="activeCardId"
						:is-draggable="true"
						:is-dragging-element="isDragging && dragCurrentIndex === index"
						@card-swipe-start="handleCardSwipeStart"
						@card-open="handleCardOpen"
						@card-close="handleCardClose"
						@card-scroll-detected="handleCardScrollDetected"
						@complete="completeWish"
						@delete="deleteWish"
						@share="shareWish"
						@drag-start="handleDragStart"
						@drag-move="handleDragMove"
						@drag-end="handleDragEnd"
						ref="wishCards"
					/>
				</transition-group>
				
				<!-- 空列表状态 -->
				<view class="empty-list" v-if="wishStore.currentGroupWishes.length === 0">
					<view class="empty-text">暂无心愿，点击下方按钮添加</view>
				</view>
			</view>
		</view>
		
		<!-- 添加按钮 -->
		<view class="add-wish-btn" @click.stop="goToAddWish">
			<view class="plus-icon">
				<view class="h-line"></view>
				<view class="v-line"></view>
			</view>
		</view>

		<!-- 同步调试按钮 - 仅开发环境 -->
		<view class="sync-debug-btn" @click="testSyncSystem" v-if="isDevelopment">
			<text class="debug-text">🔄</text>
		</view>
		

	</view>
</template>

<script>
	import { useWishStore } from '@/store/wish.js'
	import { useGroupStore } from '@/store/group.js'
	import { useUserStore } from '@/store/user.js'
	import { useMessageStore } from '@/store/message.js'
	import { useCommentStore } from '@/store/comment.js'  // 确保主包使用 comment store
	import { ref, reactive, onMounted, nextTick, onUnmounted, watchEffect, computed, getCurrentInstance } from 'vue'
	import syncManager from '@/utils/syncManager.js'
	import animationMonitor, { AnimationUtils } from '@/utils/animationPerformance.js'
	import gestureManager from '@/utils/gestureManager.js'
	import { devLog } from '@/utils/envUtils.js'

	import WishCard from '@/components/WishCard/WishCard.vue'
	import GroupSelector from '@/components/GroupSelector/GroupSelector.vue'
	
	export default {
		components: {
			WishCard,
			GroupSelector
		},
		// 添加页面滚动处理函数
		onPageScroll(e) {
			// 页面滚动时关闭打开的卡片
			if (e && e.scrollTop > 10) {
				// 发送事件给组件内部处理
				uni.$emit('page-scroll-close-cards', {
					scrollTop: e.scrollTop,
					timestamp: Date.now()
				});
			}
		},
		// 新增的方法：强制重置卡片状态
		forceResetCard(card) {
			try {
				if (card.moveX !== undefined && card.startX !== undefined) {
					card.moveX = card.startX;
				}
				if (card.isOpen !== undefined) {
					card.isOpen = false;
				}
				if (card.resetSwipe) {
					card.resetSwipe();
				}
				if (card.swipeRef && card.swipeRef.style) {
					card.swipeRef.style.transform = "translateX(0px)";
				}
			} catch (e) {
				devLog.error("重置卡片状态失败", e);
			}
		},
		// 新增的方法：通知所有卡片组件滚动事件发生
		notifyCardsOfScroll() {
			uni.$emit('page-scroll-event', {
				timestamp: Date.now(),
				shouldClose: true
			});
		},
		// 添加页面触底处理
		onReachBottom() {
			// 发送事件关闭左滑状态
			uni.$emit('page-reach-bottom-close-cards', {
				timestamp: Date.now()
			});
		},
		// 添加页面下拉刷新 - 改为组合式API外部版本
		async onPullDownRefresh() {
			// 通过事件通知 setup() 函数内的逻辑处理
			uni.$emit('page-pull-down-refresh-start', {
				timestamp: Date.now()
			});
		},
		// 微信小程序分享给朋友
		onShareAppMessage(res) {
			// 判断是否是从分享按钮触发 - WishCard组件
			if (res.from === 'button' && res.target && res.target.dataset && res.target.dataset.index !== undefined) {
				// 获取当前分享的心愿对象
				const index = Number(res.target.dataset.index || 0);
				const wish = this.wishList[index] || {};
				
				return {
					title: wish.title || '分享我的心愿',
					path: `/pages/wishDetail/wishDetail?id=${wish.id}`,
					imageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? 
						wish.image[0] : (wish.image || '/static/images/share-image.png')
				}
			}
			
			// 判断是否是从分组分享按钮触发 - GroupSelector组件
			if (res.from === 'button' && res.target && res.target.dataset && 
			   (res.target.dataset.groupId || res.target.dataset.shareType === 'group')) {
				const groupId = res.target.dataset.groupId;
				const groupName = res.target.dataset.groupName || '分组';
				
				return {
					title: `分享"${groupName}"分组的心愿清单`,
					path: `/pages/index/index?groupId=${groupId}`,
					imageUrl: '/static/images/share-image.png'
				}
			}
			
			// 尝试从本地存储获取分享信息
			const shareGroupInfo = uni.getStorageSync('current_share_group');
			if (shareGroupInfo && Date.now() - shareGroupInfo.timestamp < 3000) { // 3秒内有效
				const groupId = shareGroupInfo.groupId;
				const groupName = shareGroupInfo.groupName;
				
				// 清除临时存储
				uni.removeStorageSync('current_share_group');
				
				return {
					title: `分享"${groupName}"分组的心愿清单`,
					path: `/pages/index/index?groupId=${groupId}`,
					imageUrl: '/static/images/share-image.png'
				}
			}
			
			// 检查是否有从分组分享按钮触发的分享
			const currentGroupId = this.wishStore.currentGroupId;
			const groupStore = this.groupStore;
			const currentGroup = groupStore.getGroupById(currentGroupId);
			
			// 如果当前在特定分组且不是全部分组，分享该分组
			if (currentGroup && currentGroupId !== 'all') {
				return {
					title: `分享"${currentGroup.name}"分组的心愿清单`,
					path: `/pages/index/index?groupId=${currentGroupId}`,
					imageUrl: '/static/images/share-image.png'
				}
			}
			
			// 默认分享内容
			return {
				title: '心愿清单 - 记录你的美好心愿',
				path: '/pages/index/index',
				imageUrl: '/static/images/share-image.png'
			}
		},
		// 微信小程序分享到朋友圈
		onShareTimeline() {
			// 尝试从本地存储获取分享信息
			const shareGroupInfo = uni.getStorageSync('current_share_group');
			if (shareGroupInfo && Date.now() - shareGroupInfo.timestamp < 3000) { // 3秒内有效
				const groupId = shareGroupInfo.groupId;
				const groupName = shareGroupInfo.groupName;
				
				// 不清除临时存储，因为可能还需要在onShareAppMessage中使用
				
				return {
					title: `分享"${groupName}"分组的心愿清单`,
					query: `groupId=${groupId}`,
					imageUrl: '/static/images/share-group-image.png'
				}
			}
			
			// 检查当前分组
			const currentGroupId = this.wishStore.currentGroupId;
			const groupStore = this.groupStore;
			const currentGroup = groupStore.getGroupById(currentGroupId);
			
			// 如果当前在特定分组且不是全部分组，分享该分组
			if (currentGroup && currentGroupId !== 'all') {
				return {
					title: `分享"${currentGroup.name}"分组的心愿清单`,
					query: `groupId=${currentGroupId}`,
					imageUrl: '/static/images/share-group-image.png'
				}
			}
			
			// 默认分享内容
			return {
				title: '心愿清单 - 记录你的美好心愿',
				query: '',
				imageUrl: '/static/images/share-image.png'
			}
		},
		setup() {
			const wishStore = useWishStore()
			const groupStore = useGroupStore()
			const userStore = useUserStore()
			const messageStore = useMessageStore()
			const commentStore = useCommentStore()  // 确保主包使用 comment store
			const wishCards = ref([])
			const containerRef = ref(null)
			const contentHeight = ref(0)
			const windowHeight = ref(0)
			const activeCardId = ref(null)

			// 动画性能监控初始化
			onMounted(() => {
				// 启动FPS监控（仅开发环境）
				if (process.env.NODE_ENV === 'development') {
					animationMonitor.startFPSMonitoring();
				}
				
				// 设置手势管理器的页面实例
				gestureManager.setPageInstance(getCurrentInstance());
			})
			const lastTouchY = ref(undefined)
			const isSyncing = ref(false)
			const isPullRefreshDisabled = ref(false)
			
			// 拖拽状态
			const isDragging = ref(false)
			const dragStartIndex = ref(-1)
			const dragCurrentIndex = ref(-1)
			const initialOrder = ref([])
			const dragStartY = ref(0)
			const dragThrottled = ref(false)
			const cardHeight = ref(160) // 默认估计高度，单位rpx转px
			
			// 计算属性：当前分组的心愿列表
			const wishList = computed(() => wishStore.currentGroupWishes)
			
			// 添加强制更新标记
			const forceUpdateKey = ref(0)
			
			// 是否已登录
			const isLogin = computed(() => userStore.isLoggedIn)

			// 开发环境检测
			const isDevelopment = ref(process.env.NODE_ENV === 'development')
			
			// 获取窗口高度
			uni.getSystemInfo({
				success: (res) => {
					windowHeight.value = res.windowHeight
				}
			})
			
			// 监听页面显示事件
			uni.$on('page-show', () => {
				// 页面显示时的处理
			})

			// 监听页面滚动关闭卡片事件
			uni.$on('page-scroll-close-cards', (data) => {
				devLog.log('[Index] 收到页面滚动关闭卡片事件:', data);
				if (activeCardId.value !== null) {
					closeAllSwipeActions();
				}
			})

			// 监听页面触底关闭卡片事件
			uni.$on('page-reach-bottom-close-cards', (data) => {
				devLog.log('[Index] 收到页面触底关闭卡片事件:', data);
				closeAllSwipeActions();
			})

			// 监听下拉刷新开始事件（从外部onPullDownRefresh触发）
			uni.$on('page-pull-down-refresh-start', async (data) => {
				devLog.log('[Index] 收到下拉刷新开始事件:', data);

				// 多重检查：手势管理器 + 本地状态
				if (!gestureManager.canStartGesture('pullRefresh') || isPullRefreshDisabled.value) {
					devLog.log('[Index] 拖拽进行中或已被禁用，忽略下拉刷新');
					uni.stopPullDownRefresh();
					return;
				}
				
				// 开始下拉刷新
				if (!gestureManager.startPullRefresh()) {
					uni.stopPullDownRefresh();
					return;
				}
				
				// 关闭左滑状态
				closeAllSwipeActions();

				// 执行智能同步
				try {
					await forceSyncData();
				} catch (error) {
					console.error('[Index] 下拉刷新同步失败:', error);
				} finally {
					// 结束下拉刷新状态
					gestureManager.endPullRefresh();
					uni.stopPullDownRefresh();
				}
			})

			// 监听下拉刷新事件（保留兼容性）
			uni.$on('page-pull-down-refresh', async (data) => {
				devLog.log('[Index] 收到下拉刷新事件:', data);
				// 关闭左滑状态
				closeAllSwipeActions();

				// 执行手动同步
				try {
					await forceSyncData();
				} catch (error) {
					console.error('[Index] 下拉刷新同步失败:', error);
				} finally {
					// 结束下拉刷新状态
					gestureManager.endPullRefresh();
					uni.stopPullDownRefresh();
				}
			})
			
			// 监听手势管理器的下拉刷新禁用事件
			uni.$on('gesture-disable-pull-refresh', (data) => {
				devLog.log('[Index] 手势管理器禁用下拉刷新:', data);
				// 这里可以添加额外的禁用逻辑
				isPullRefreshDisabled.value = true;
			})

			// 监听手势管理器的下拉刷新启用事件
			uni.$on('gesture-enable-pull-refresh', (data) => {
				devLog.log('[Index] 手势管理器启用下拉刷新:', data);
				// 延迟恢复，确保拖拽完全结束
				setTimeout(() => {
					isPullRefreshDisabled.value = false;
				}, 200);
			})
			
			// 监听心愿列表更新事件（统一处理所有操作）
			uni.$on('wish-list-updated', async (data) => {
				console.log('[Index] 收到心愿列表更新事件:', data);

				// 强制刷新页面显示
				await nextTick();

				// 统一处理所有操作类型：delete, complete, restore, add, update
				console.log(`[Index] 处理${data.action}操作，强制刷新界面`);

				// 确保计数器同步
				if (data.updateCounter && data.updateCounter > wishStore.listUpdateCounter) {
					wishStore.listUpdateCounter = data.updateCounter;
				}

				// 强制组件重新渲染
				forceUpdateKey.value++;

				// 再次确保响应式更新
				await nextTick();

				console.log(`[Index] ${data.action}操作界面更新完成，forceUpdateKey: ${forceUpdateKey.value}`);
			})

			// 监听用户登录成功事件
			uni.$on('user-login-success', async (data) => {
				console.log('[Index] User login success event received:', data);
				
				// 确保在重新初始化期间不触发手势冲突
				const wasDisabled = isPullRefreshDisabled.value;
				isPullRefreshDisabled.value = true;
				
				// 重新初始化stores
				try {
					console.log('[Index] Reinitializing stores after login...');
					
					// 温和地清空数据，避免DOM错误
					await new Promise(resolve => {
						setTimeout(() => {
							// 使用store提供的清空方法而不是直接赋值
							groupStore.resetGroups();
							wishStore.clearLocalData();
							resolve();
						}, 100);
					});
					
					// 按顺序重新初始化
					// 1. 先初始化分组数据
					await groupStore.initGroups();
					console.log('[Index] Group store reinitialized');
					
					// 2. 再初始化心愿数据
					await wishStore.initWishList();
					console.log('[Index] Wish store reinitialized');
					
					// 3. 最后初始化消息数据
					await messageStore.initMessages();
					console.log('[Index] Message store reinitialized');
					
					console.log('[Index] Stores reinitialized successfully');
					
					// 刷新页面数据完成
					console.log('[Index] Page data refreshed after login');
				} catch (error) {
					console.error('[Index] Error reinitializing stores:', error);
				} finally {
					// 恢复下拉刷新状态，延迟确保数据初始化完成
					setTimeout(() => {
						if (!isDragging.value) {
							isPullRefreshDisabled.value = wasDisabled;
						}
					}, 500);
				}
			})

			// 监听页面加载事件
			uni.$on('page-load', async (data) => {
				console.log('[Index] 收到页面加载事件:', data);
				const options = data.options;

				// 如果有groupId参数，设置当前分组
				if (options && options.groupId) {
					console.log('[Index] Setting group from URL params:', options.groupId);
					wishStore.setCurrentGroup(options.groupId);
				}

				// 初始化数据（按顺序）
				try {
					// 1. 先初始化分组数据
					console.log('[Index] Initializing groups...');
					await groupStore.initGroups();

					// 2. 再初始化心愿数据
					console.log('[Index] Initializing wishes...');
					await wishStore.initWishList();

					console.log('[Index] Data initialization completed');
				} catch (error) {
					console.error('[Index] Data initialization failed:', error);
				}
			})

			// 监听页面显示生命周期事件
			uni.$on('page-show-lifecycle', (data) => {
				console.log('[Index] 收到页面显示生命周期事件:', data);

				// 广播页面显示事件，用于通知子组件（例如GroupSelector）
				uni.$emit('page-show');

				// 如果用户已登录，检查并刷新数据
				if (userStore.isLoggedIn) {
					console.log('[Index] User is logged in, refreshing data...');

					// 从本地存储刷新最新的心愿数据
					wishStore.refreshWishList();

					// 检查未读消息并更新徽标
					checkUnreadMessages();
				} else {
					console.log('[Index] User not logged in');

					// 用户未登录时，确保有默认分组可用
					if (groupStore.getAllGroups.length === 0) {
						console.log('[Index] Ensuring default groups are available');
						groupStore.ensureDefaultGroups();
					}
				}

				// 页面显示完成
				console.log('[Index] Page shown');
			})

			// 监听页面隐藏生命周期事件
			uni.$on('page-hide-lifecycle', (data) => {
				console.log('[Index] 收到页面隐藏生命周期事件:', data);
				onPageHide();
			})

			// 🚀 监听静默刷新事件（多设备冲突时触发）
			uni.$on('silent-refresh-data', async (data) => {
				console.log('[Index] 收到静默刷新事件:', data);

				try {
					// 静默刷新数据，不显示任何加载提示
					await forceSyncData();
					console.log('[Index] 静默刷新完成');
				} catch (error) {
					console.error('[Index] 静默刷新失败:', error);
				}
			})

			// 简化的拖拽状态管理
			uni.$on('lock-page-scroll', (data) => {
				console.log('锁定页面滚动', data);
				// 发送事件给其他组件
				uni.$emit('page-scroll-locked', {
					timestamp: Date.now(),
					source: data.source || 'unknown'
				});
			})
			
			// 在组件卸载时取消事件监听
			onUnmounted(() => {
				uni.$off('page-show')
				uni.$off('page-scroll-close-cards')
				uni.$off('page-reach-bottom-close-cards')
				uni.$off('page-pull-down-refresh')
				uni.$off('page-pull-down-refresh-start')
				uni.$off('page-load')
				uni.$off('page-show-lifecycle')
				uni.$off('page-hide-lifecycle')
				uni.$off('lock-page-scroll')
				uni.$off('user-login-success')
				uni.$off('gesture-disable-pull-refresh')
				uni.$off('gesture-enable-pull-refresh')
				uni.$off('wish-list-updated')
			})

			// 页面隐藏时执行
			const onPageHide = () => {
				console.log('[onPageHide] 页面隐藏');
				// 关闭任何可能打开的卡片
				closeAllSwipeActions();
				// 重置手势状态，确保页面隐藏时清理所有手势状态
				gestureManager.resetAll();
			}
			
			// 简化的页面初始化
			const initializePage = () => {
				// 页面初始化逻辑
				console.log('页面初始化完成')
			}
			
			// 处理开始拖拽
			const handleDragStart = (data) => {
				console.log('开始拖拽，数据:', data);
				
				// 检查手势管理器是否允许拖拽
				if (!gestureManager.canStartGesture('drag')) {
					console.log('[Index] 下拉刷新进行中，忽略拖拽开始');
					return;
				}
				
				// 立即禁用下拉刷新
				isPullRefreshDisabled.value = true;
				
				// 开始拖拽
				if (!gestureManager.startDrag({ cardId: data.wishId, index: data.index })) {
					// 如果拖拽开始失败，恢复下拉刷新
					isPullRefreshDisabled.value = false;
					return;
				}
				
				// 关闭所有已打开的卡片
				closeAllSwipeActions();
				
				// 设置拖拽状态
				isDragging.value = true;
				dragStartIndex.value = data.index;
				dragCurrentIndex.value = data.index;
				dragStartY.value = data.clientY;
				
				// 阻止页面滚动
				uni.$emit('lock-page-scroll', { source: 'drag' });
				
				// 记录当前心愿列表顺序，用于重排序
				initialOrder.value = wishList.value.map(wish => wish.id);
				console.log('初始心愿顺序:', initialOrder.value);
				console.log('当前心愿列表数量:', wishList.value.length);
				
				// 获取卡片实际高度，用于后续拖拽计算
				try {
					const query = uni.createSelectorQuery();
					query.select('.wish-card-container').boundingClientRect(data => {
						if (data && data.height) {
							// 保存卡片实际高度以供拖拽计算使用
							cardHeight.value = data.height;
							console.log('获取到卡片实际高度:', cardHeight.value);
						}
					}).exec();
				} catch (e) {
					console.error('获取卡片高度失败:', e);
					// 使用默认高度
					cardHeight.value = 160; // 默认估计高度，单位rpx转px
				}
				
				// 静默振动反馈
				try {
					uni.vibrateShort();
				} catch (e) {
					console.error('振动失败:', e);
				}
				
				console.log('拖拽模式初始化完成，当前拖拽索引:', dragCurrentIndex.value);
			}
			
			// 处理拖拽移动
			const handleDragMove = (data) => {
				if (!isDragging.value) {
					console.log('非拖拽状态，忽略移动事件');
					return;
				}
				
				// 计算移动距离
				const moveDistance = data.clientY - dragStartY.value;
				console.log('拖拽移动:', {
					currentY: data.clientY,
					startY: dragStartY.value,
					moveDistance: moveDistance,
					currentIndex: dragCurrentIndex.value
				});
				
				// 如果移动距离太小，忽略这次移动
				if (Math.abs(moveDistance) < 5) { // 降低阈值，提高灵敏度
					return;
				}
				
				// 防止短时间内多次触发
				if (dragThrottled.value) {
					return;
				}
				
				// 设置节流标志
				dragThrottled.value = true;
				setTimeout(() => {
					dragThrottled.value = false;
				}, 50); // 降低节流时间，提高响应性
				
				// 确定移动方向
				const direction = moveDistance < 0 ? 'up' : 'down';
				const currentIndex = dragCurrentIndex.value;
				let targetIndex = -1;
				
				// 计算移动距离与卡片高度的比例
				// 估算卡片高度为100px，也可以从DOM获取实际高度
				const moveRatio = Math.abs(moveDistance) / cardHeight.value;
				
				// 根据移动比例计算应该移动的卡片数量
				// 移动超过半个卡片高度时交换位置
				const shouldMove = moveRatio >= 0.5;
				
				if (direction === 'up' && currentIndex > 0 && shouldMove) {
					// 向上拖动
					targetIndex = currentIndex - 1;
					console.log('向上拖动，目标索引:', targetIndex);
					
					// 检查目标卡片是否在可视区域内，如果不在则滚动到可见
					checkTargetVisibility(targetIndex);
				} else if (direction === 'down' && currentIndex < wishList.value.length - 1 && shouldMove) {
					// 向下拖动
					targetIndex = currentIndex + 1;
					console.log('向下拖动，目标索引:', targetIndex);
					
					// 检查目标卡片是否在可视区域内，如果不在则滚动到可见
					checkTargetVisibility(targetIndex);
				} else {
					// 如果移动比例不足，可以考虑使用CSS transform实现跟随手指的视觉效果
					// 此处不做位置交换，但可以在WishCard组件中添加视觉反馈
					return;
				}
				
				// 执行交换操作
				console.log('交换位置:', currentIndex, '->', targetIndex);
				
				// 执行交换
				swapItems(currentIndex, targetIndex);
				
				// 更新当前拖拽索引
				dragCurrentIndex.value = targetIndex;
				
				// 更新开始拖拽位置，但保留部分位移差距，使移动更连贯
				// 保留30%的位移差距，避免完全重置造成的不连贯感
				dragStartY.value = data.clientY - (moveDistance * 0.3);
				
				// 触发振动反馈
				try {
					uni.vibrateShort({
						success: function() {
							console.log('交换位置振动触发');
						}
					});
				} catch (e) {
					console.error('震动API调用失败:', e);
				}
			}
			
			// 检查目标卡片是否在可视区域内，如果不在则自动滚动到可见
			const checkTargetVisibility = (targetIndex) => {
				if (!wishCards.value || wishCards.value.length <= targetIndex) {
					return;
				}
				
				try {
					// 创建查询选择器
					const query = uni.createSelectorQuery();
					
					// 先获取列表的滚动位置和边界
					query.select('.wish-list').boundingRect(listRect => {
						if (!listRect) return;
						
						// 获取所有卡片位置信息
						query.selectAll('.wish-card-container').boundingRect(cardsRect => {
							if (!cardsRect || !cardsRect[targetIndex]) return;
							
							// 获取目标卡片信息
							const cardRect = cardsRect[targetIndex];
							const listTop = listRect.top;
							const listBottom = listRect.bottom;
							const cardTop = cardRect.top;
							const cardBottom = cardRect.bottom;
							
							console.log('卡片可见性检查:', {
								targetIndex,
								listTop,
								listBottom,
								cardTop,
								cardBottom
							});
							
							// 获取当前滚动位置
							query.select('.wish-list').scrollOffset(scrollData => {
								if (!scrollData) return;
								
								const currentScrollTop = scrollData.scrollTop;
								let newScrollTop = currentScrollTop;
								
								// 如果卡片顶部在可视区域上方且不在页面顶部，向上滚动
								if (cardTop < listTop + 100 && currentScrollTop > 0) {
									// 向上滚动一个卡片高度的距离
									newScrollTop = Math.max(0, currentScrollTop - (cardRect.height + 20));
									console.log('向上滚动到:', newScrollTop);
								}
								// 如果卡片底部在可视区域下方，向下滚动
								else if (cardBottom > listBottom - 100) {
									// 向下滚动一个卡片高度的距离
									newScrollTop = currentScrollTop + (cardRect.height + 20);
									console.log('向下滚动到:', newScrollTop);
								} else {
									// 卡片在可视区域内，不需要滚动
									return;
								}
								
								// 使用uni.pageScrollTo滚动到指定位置
								try {
									uni.pageScrollTo({
										scrollTop: newScrollTop,
										duration: 300
									});
									console.log('已滚动到位置:', newScrollTop);
								} catch (e) {
									console.warn('滚动失败:', e);
								}
							}).exec();
						}).exec();
					}).exec();
				} catch (e) {
					console.error('检查卡片可见性失败:', e);
				}
			}
			
			// 滚动列表到指定位置的函数（小程序兼容版本）
			const scrollListTo = (targetScrollTop) => {
				try {
					// 在微信小程序环境中使用选择器获取ScrollView组件的上下文
					const scrollViewId = 'wishListScrollView'; // 确保在模板中给scroll-view添加这个id

					// 使用uni.pageScrollTo滚动
					uni.pageScrollTo({
						scrollTop: targetScrollTop,
						duration: 300
					});
					console.log('设置scrollTop值为:', targetScrollTop);
					
					// 尝试使用小程序原生方法
					if (uni.createSelectorQuery) {
						uni.createSelectorQuery()
							.select(`#${scrollViewId}`)
							.context((res) => {
								// 如果成功获取了上下文
								if (res && res.context && typeof res.context.scrollTo === 'function') {
									try {
										res.context.scrollTo({
											top: targetScrollTop,
											duration: 300
										});
										console.log('成功使用context.scrollTo方法滚动');
										return true;
									} catch (e) {
										console.error('scrollTo方法调用失败:', e);
									}
								}
							})
							.exec();
					}
					
					return false;
				} catch (e) {
					console.error('滚动列表失败:', e);
					return false;
				}
			}
			
			// 处理拖拽结束
			const handleDragEnd = () => {
				console.log('结束拖拽');
				
				if (isDragging.value) {
					// 结束拖拽状态
					isDragging.value = false;
					
					// 允许所有卡片添加过渡效果，使恢复更平滑
					// 可以通过CSS类来实现
					
					// 恢复页面滚动
					setTimeout(() => {
						// 发送滚动恢复通知
						uni.$emit('page-scroll-enabled', { timestamp: Date.now() });
					}, 50);
					
					// 保存新的排序
					const newOrder = wishList.value.map(wish => wish.id);
					
					// 检查顺序是否发生变化
					const hasOrderChanged = initialOrder.value.some((id, index) => id !== newOrder[index]);
					
					if (hasOrderChanged) {
						// 更新心愿排序
						wishStore.updateWishOrder(newOrder);
						
						// 静默振动反馈表示排序完成
						try {
							uni.vibrateShort();
						} catch (e) {
							console.error('振动失败:', e);
						}
					}
					
									// 通知手势管理器拖拽结束
				gestureManager.endDrag({ hasOrderChanged });
				
				// 延迟恢复下拉刷新，确保拖拽完全结束
				setTimeout(() => {
					isPullRefreshDisabled.value = false;
				}, 300);
					
					// 重置拖拽相关状态
					dragStartIndex.value = -1;
					dragCurrentIndex.value = -1;
					initialOrder.value = [];
				}
			}
			

			
			// 交换两个项目的位置
			const swapItems = (fromIndex, toIndex) => {
				if (fromIndex === toIndex) return;
				
				console.log('执行交换:', fromIndex, '->', toIndex);
				
				// 创建新数组用于重排序
				const newWishList = [...wishList.value];
				
				// 从数组中取出要移动的元素
				const itemToMove = newWishList[fromIndex];
				
				// 从数组中删除该元素
				newWishList.splice(fromIndex, 1);
				
				// 在目标位置插入该元素
				newWishList.splice(toIndex, 0, itemToMove);
				
				// 更新wishList
				wishStore.setCurrentList(newWishList);
				
				// 静默振动反馈 - 轻微振动表示位置交换
				try {
					uni.vibrateShort({
						success: function() {
							console.log('交换位置振动触发');
						}
					});
				} catch (e) {
					console.error('震动API调用失败:', e);
				}
			}
			
			// 添加或提取之前缺少的方法
			const closeAllSwipeActions = () => {
				// 关闭所有打开的左滑卡片
				if (wishCards.value && wishCards.value.length) {
					wishCards.value.forEach(card => {
						if (card && card.resetSwipe) {
							card.resetSwipe();
						}
					});
				}
				// 重置活动卡片ID
				activeCardId.value = null;
			}
			
			// 前往添加心愿页面
			const goToAddWish = () => {
				// 获取当前选中的分组ID
				const currentGroupId = wishStore.currentGroupId;
				
				// 如果当前选中的是"全部"分组，则不传递分组ID
				const url = currentGroupId !== 'all' ?
					`/subpkg-wish/pages/editWish/editWish?groupId=${currentGroupId}` :
					'/subpkg-wish/pages/editWish/editWish';
				
				uni.navigateTo({
					url: url
				});
			}
			
			// 处理分组变化
			const onGroupChange = (groupId) => {
				console.log('[onGroupChange] 开始处理分组变化:', groupId);
				try {
					wishStore.setCurrentGroup(groupId);
					console.log('[onGroupChange] 分组设置完成');

					// 关闭所有卡片
					closeAllSwipeActions();
					console.log('[onGroupChange] 卡片关闭完成');

					// 分组变化后滚动到顶部
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 300
					});
					console.log('[onGroupChange] 滚动到顶部完成');
				} catch (e) {
					console.error('[onGroupChange] 处理分组变化时发生错误:', e);
					console.error('[onGroupChange] 错误堆栈:', e.stack);
				}
			}
			
			// 处理卡片打开
			const handleCardOpen = (cardId) => {
				activeCardId.value = cardId;
			}
			
			// 处理卡片关闭
			const handleCardClose = () => {
				activeCardId.value = null;
			}
			
			// 处理卡片开始滑动
			const handleCardSwipeStart = (cardId) => {
				// 如果有其他卡片已打开，先关闭它
				if (activeCardId.value && activeCardId.value !== cardId) {
					closeAllSwipeActions();
				}
			}
			
			// 处理卡片滚动检测
			const handleCardScrollDetected = (data) => {
				// 当检测到卡片滚动时的处理
				console.log('检测到卡片滚动', data);
				// 大幅提高阈值到50px，避免轻微滚动就关闭左滑状态
				// 让用户可以在左滑状态下进行一定的垂直操作
				if (data && data.deltaY > 50) {
					closeAllSwipeActions();
				}
			}
			
			// 处理页面触摸
			const handlePageTouch = (e) => {
				
				// 如果处于拖拽状态，完全阻止页面滚动
				if (isDragging.value) {
					// 使用更强力的方式阻止事件传播和默认行为
					e.preventDefault && e.preventDefault();
					e.stopPropagation && e.stopPropagation();
					
					// 阻止事件传播
					if (e.cancelable) {
						e.cancelable && e.preventDefault();
					}
					
					// 立即返回，不处理任何触摸逻辑
					return false;
				}
				
				// 页面触摸时的处理逻辑
				const currentY = e.touches[0].clientY;
				
				// 如果有上一次触摸位置记录，计算移动距离
				if (lastTouchY.value !== undefined) {
					const deltaY = Math.abs(currentY - lastTouchY.value);
					
					// 大幅提高阈值到60px，只有非常明显的滑动才关闭左滑卡片
					// 避免用户在左滑卡片上的正常操作被误判为页面滑动
					if (activeCardId.value !== null && deltaY > 60) {
						closeAllSwipeActions();
					}
				}
				
				lastTouchY.value = currentY;
			}
			
			// 处理页面触摸结束
			const handlePageTouchEnd = (e) => {
				// 页面触摸结束时的处理逻辑
				lastTouchY.value = undefined;
			}
			
			// 页面滚动处理 - 简化版本
			const onPageScroll = (e) => {
				// 页面滚动时关闭打开的卡片
				if (activeCardId.value && e && e.scrollTop > 10) {
					closeAllSwipeActions();
				}
			}
			
			// 卡片列表触摸处理
			const onCardListTouch = (e) => {

				// 卡片列表触摸时处理逻辑
				// 如果有卡片处于左滑状态，且不是在卡片内部触摸（触摸了空白区域），关闭左滑状态
				if (activeCardId.value !== null) {
					const currentTouch = e.touches[0];

					// 尝试判断触摸是否在卡片区域外
					// 由于无法直接确定触摸点是否在卡片区域内，我们使用移动距离判断
					const currentY = currentTouch.clientY;

					if (lastTouchY.value !== undefined) {
						const deltaY = Math.abs(currentY - lastTouchY.value);

						// 大幅提高阈值到50px，减少列表滚动时的误触发
						// 让用户可以在卡片周围进行一定的触摸操作而不会立即关闭左滑状态
						if (deltaY > 50) {
							closeAllSwipeActions();
						}
					}

					lastTouchY.value = currentY;
				}
			}

			// 容器触摸开始 - 阻止水平滑动
			const containerTouchStartX = ref(0);
			const containerTouchStartY = ref(0);

			const handleContainerTouchStart = (e) => {
				if (e.touches && e.touches.length > 0) {
					containerTouchStartX.value = e.touches[0].clientX;
					containerTouchStartY.value = e.touches[0].clientY;
				}
				
				// 如果下拉刷新被禁用，阻止所有可能的触摸行为
				if (isPullRefreshDisabled.value || isDragging.value) {
					e.preventDefault && e.preventDefault();
					e.stopPropagation && e.stopPropagation();
					return false;
				}
			}

			// 容器触摸移动 - 阻止水平滑动和下拉刷新冲突
			const handleContainerTouchMove = (e) => {
				// 优先检查：如果下拉刷新被禁用或正在拖拽，直接阻止
				if (isPullRefreshDisabled.value || isDragging.value) {
					e.preventDefault && e.preventDefault();
					e.stopPropagation && e.stopPropagation();
					return false;
				}
				
				if (e.touches && e.touches.length > 0) {
					const currentX = e.touches[0].clientX;
					const currentY = e.touches[0].clientY;

					const deltaX = Math.abs(currentX - containerTouchStartX.value);
					const deltaY = Math.abs(currentY - containerTouchStartY.value);

					// 如果水平移动大于垂直移动，阻止默认行为（阻止水平滑动）
					if (deltaX > deltaY && deltaX > 10) {
						e.preventDefault();
						e.stopPropagation();
						return false;
					}
				}
			}

			// 容器触摸结束 - 重置状态
			const handleContainerTouchEnd = (e) => {
				containerTouchStartX.value = 0;
				containerTouchStartY.value = 0;
			}
			
			// 完成心愿
			const completeWish = async (id) => {
				// 检查用户是否已登录
				if (!userStore.checkLoginAndRedirect()) {
					console.log('用户未登录，跳转到登录页面')
					return
				}

				try {
					await wishStore.completeWish(id);

					uni.showToast({
						title: '心愿已完成',
						icon: 'success',
						duration: 1500
					});
				} catch (error) {
					console.error('完成心愿失败:', error);
					uni.showToast({
						title: '操作失败',
						icon: 'none',
						duration: 1500
					});
				}
			}
			
			// 删除心愿
			const deleteWish = async (id) => {
				// 检查用户是否已登录
				if (!userStore.checkLoginAndRedirect()) {
					console.log('用户未登录，跳转到登录页面')
					return
				}
				
				try {
					// 先关闭所有左滑菜单
					closeAllSwipeActions();
					
					// 记录当前分组ID，避免删除后分组选择器不必要的刷新
					const currentGroupBeforeDelete = wishStore.currentGroupId;
					
					// 执行删除操作
					await wishStore.deleteWish(id);
					
					// 强制刷新当前页面数据，确保界面立即更新
					await nextTick();
					
					// 手机端特殊处理：强制触发组件重新渲染
					uni.$emit('wish-list-updated', {
						timestamp: Date.now(),
						action: 'delete',
						deletedId: id
					});
					
					// 确保分组选择器状态保持一致，避免不必要的重新渲染
					if (wishStore.currentGroupId !== currentGroupBeforeDelete) {
						// 只有当分组真的改变时才更新
						console.log('分组已变更，触发必要的更新');
					}
					
					// 显示成功提示
					uni.showToast({
						title: '心愿已删除',
						icon: 'success',
						duration: 1500
					});
					
					console.log('心愿删除成功，界面已更新');
				} catch (error) {
					// 删除失败时的处理已在store中完成
					console.error('删除心愿失败:', error);
				}
			}
			
			// 分享心愿
			const shareWish = (id) => {
				// 检查用户是否已登录
				if (!userStore.checkLoginAndRedirect()) {
					console.log('用户未登录，跳转到登录页面')
					return
				}
				
				// 显示加载提示
				uni.showLoading({
					title: '生成海报中...',
					mask: true
				})
				
				// 模拟生成海报或分享操作的延迟
				setTimeout(() => {
					// 隐藏加载提示
					uni.hideLoading().catch(() => {})

					// 显示分享成功提示
					uni.showToast({
						title: '分享成功',
						icon: 'success',
						duration: 1500
					})

					// 触发振动反馈
					uni.vibrateShort()
				}, 1500)
			}
			
			// 检查登录状态
			const checkLoginStatus = async () => {
				const isLoggedIn = userStore.isLoggedIn;
				
				// 不再自动跳转到登录页面，无论登录状态如何都加载数据
				// 已登录或跳过登录检查，按顺序初始化数据
				try {
					// 1. 先初始化分组数据
					await groupStore.initGroups();
					
					// 2. 再初始化心愿数据
					await wishStore.initWishList();
				} catch (error) {
					console.error('[checkLoginStatus] Data initialization error:', error);
				}
				
				// 初始化完成
				console.log('[checkLoginStatus] Data initialization completed');
			}
			
			// 强制禁用页面滚动的函数
			const forceDisableScroll = () => {
				console.log('尝试禁用页面滚动');

				// 使用各种兼容方法禁止滚动
				try {
					// 针对不同环境采取不同措施
					if (typeof uni.disablePageScroll === 'function') {
						// 如果uni-app提供了原生的禁用滚动方法，使用它
						uni.disablePageScroll();
					} else if (typeof uni.pageScrollTo === 'function') {
						// 尝试使用pageScrollTo固定当前位置
						// 获取当前滚动位置并固定
						uni.createSelectorQuery().selectViewport().scrollOffset((res) => {
							if (res) {
								uni.pageScrollTo({
									scrollTop: res.scrollTop,
									duration: 0
								});
							}
						}).exec();
					}
				} catch (e) {
					console.error('强制禁用滚动失败:', e);
				}
			}
			
			// 检查未读消息
			const checkUnreadMessages = () => {
				// 确保消息store已初始化
				messageStore.initMessages();

				// 获取未读消息数量
				const unreadCount = messageStore.getUnreadCount;

				// 更新tabbar徽标
				if (unreadCount > 0) {
					uni.setTabBarBadge({
						index: 1, // 消息选项卡索引
						text: unreadCount.toString()
					});
				} else {
					// 尝试移除徽标
					try {
						uni.removeTabBarBadge({
							index: 1
						});
					} catch (e) {
						console.log('移除徽标失败，可能没有显示徽标');
					}
				}
			}
			

			

			

			
			// 手动同步数据（下拉刷新时调用）
			const forceSyncData = async () => {
				console.log('[Index] Manual sync data triggered');

				if (isSyncing.value) {
					console.log('[Index] Sync already in progress');
					return;
				}

				try {
					isSyncing.value = true;
					console.log('[Index] 开始同步，用户登录状态:', userStore.isLoggedIn);

					// 检查用户登录状态
					if (!userStore.isLoggedIn) {
						console.warn('[Index] 用户未登录，跳过同步');
						uni.showToast({
							title: '请先登录',
							icon: 'none',
							duration: 2000
						});
						return;
					}

					// 使用 SyncManager 的统一同步方法
					console.log('[Index] 调用 syncManager.manualSync...');
					const result = await syncManager.manualSync();
					console.log('[Index] 同步结果:', result);

				} catch (error) {
					console.error('[Index] Manual sync failed:', error);
					uni.showToast({
						title: '同步失败: ' + (error.message || '未知错误'),
						icon: 'none',
						duration: 3000
					});
				} finally {
					isSyncing.value = false;
					console.log('[Index] 同步完成，重置状态');
				}
			}

			// 测试同步系统
			const testSyncSystem = async () => {
				try {
					console.log('[Index] 开始测试同步系统...')

					// 1. 检查 SyncManager 状态
					const syncManagerStatus = {
						isInitialized: syncManager.isInitialized,
						userId: syncManager.userId,
						pushEnabled: syncManager.pushEnabled,
						isOnline: syncManager.isOnline
					}
					console.log('[Index] SyncManager 状态:', syncManagerStatus)

					// 2. 检查用户登录状态
					const userStatus = {
						isLoggedIn: userStore.isLoggedIn,
						userId: userStore.userId,
						userInfo: userStore.userInfo
					}
					console.log('[Index] 用户状态:', userStatus)

					// 3. 检查 Store 数据
					const storeStatus = {
						wishCount: wishStore.wishList.length,
						groupCount: groupStore.groups.length,
						wishSyncStatus: wishStore.syncStatus,
						groupSyncStatus: groupStore.syncStatus
					}
					console.log('[Index] Store 状态:', storeStatus)

					// 4. 测试手动同步
					console.log('[Index] 测试手动同步...')
					const syncResult = await syncManager.manualSync()
					console.log('[Index] 手动同步结果:', syncResult)

					// 显示测试结果
					const summary = {
						syncManager: syncManagerStatus,
						user: userStatus,
						stores: storeStatus,
						syncResult: syncResult
					}

					uni.showModal({
						title: '同步系统测试结果',
						content: JSON.stringify(summary, null, 2),
						showCancel: false,
						confirmText: '确定'
					})

				} catch (error) {
					console.error('[Index] 同步系统测试失败:', error)
					uni.showModal({
						title: '测试失败',
						content: error.message || '未知错误',
						showCancel: false,
						confirmText: '确定'
					})
				}
			}

			return {
				wishStore,
				groupStore,
				userStore,
				messageStore,
				wishCards,
				containerRef,
				activeCardId,
				lastTouchY,
				isPullRefreshDisabled,
				closeAllSwipeActions,
				handleCardOpen,
				handleCardClose,
				handleCardSwipeStart,
				handleCardScrollDetected,
				handlePageTouch,
				handlePageTouchEnd,
				onPageScroll,
				onPageHide,
				handleContainerTouchStart,
				handleContainerTouchMove,
				handleContainerTouchEnd,
				// 添加拖拽相关方法
				isDragging,
				dragStartIndex,
				dragCurrentIndex,
				dragStartY,
				dragThrottled,
				cardHeight,
				handleDragStart,
				handleDragMove,
				handleDragEnd,
				swapItems,
				// 其他方法
				onGroupChange,
				completeWish,
				deleteWish,
				shareWish,
				goToAddWish,
				wishList,
				isLogin,
				checkLoginStatus,
				checkUnreadMessages,
				forceSyncData,
				isSyncing,
				// 开发调试
				isDevelopment,
				testSyncSystem,
				// 强制更新key
				forceUpdateKey
			}
		},
		data() {
			return {
				isSyncing: false
			}
		},
		computed: {
			wishList() {
				return this.wishStore.currentGroupWishes
			},
			isLogin() {
				return this.userStore.isLoggedIn
			}
		},

		// 页面加载时执行
		async onLoad(options) {
			console.log('[Index onLoad] 页面加载，初始化数据...');

			// 发送事件到 setup() 函数处理
			uni.$emit('page-load', {
				options: options,
				timestamp: Date.now()
			});
		},

		// 页面每次显示时执行
		onShow() {
			console.log('[Index onShow] 页面显示，检查数据状态...');

			// 发送事件到 setup() 函数处理
			uni.$emit('page-show-lifecycle', {
				timestamp: Date.now()
			});
		},
		// 页面隐藏时执行
		onHide() {
			console.log('[Index onHide] 页面隐藏');
			// 发送事件到 setup() 函数处理
			uni.$emit('page-hide-lifecycle', {
				timestamp: Date.now()
			});
		},
		// 监听数据变化
		watch: {
			wishList: {
				handler() {
					// 心愿列表变化时的处理
					console.log('心愿列表已更新')
				},
				deep: true
			}
		},
		methods: {
			// 清理所有心愿中的"已还原"标记按钮已移除
		}
	}
</script>

<style lang="scss">
	/* 全局禁用水平滚动 - 最强规则 */
	page {
		overflow-x: hidden !important;
		overflow-y: auto !important;
		width: 100vw !important;
		max-width: 100vw !important;
		min-width: 100vw !important;
		box-sizing: border-box !important;
		position: relative !important;
	}

	/* 确保body也不会水平滚动 */
	body {
		overflow-x: hidden !important;
		overflow-y: auto !important;
		width: 100vw !important;
		max-width: 100vw !important;
		min-width: 100vw !important;
		box-sizing: border-box !important;
		position: relative !important;
	}

	/* 确保主要元素都不会导致水平滚动 */
	view, text, image, button, input, textarea {
		box-sizing: border-box;
	}

	/* 主容器样式 - 禁用水平滚动 */
	.wish-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		position: relative;
		padding-bottom: 120rpx; /* 调整底部填充，为tabbar预留空间 */
		box-sizing: border-box;
		overflow-x: hidden !important; /* 强制禁止水平滚动 */
		overflow-y: auto !important; /* 允许垂直滚动 */
		width: 100vw !important; /* 确保容器宽度等于视口 */
		max-width: 100vw !important; /* 防止内容溢出 */
		min-width: 100vw !important; /* 确保最小宽度 */
		left: 0 !important; /* 确保容器从左边开始 */
		right: 0 !important; /* 确保容器到右边结束 */
		touch-action: pan-y; /* 只允许垂直滑动 */
		-webkit-user-select: none;
		user-select: none;
		
		/* 全局下拉刷新禁用样式 */
		&.pull-refresh-disabled {
			/* 完全禁用下拉刷新的各种可能触发方式 */
			overflow-y: hidden !important;
			-webkit-overflow-scrolling: touch !important;
			overscroll-behavior: contain !important;
			overscroll-behavior-y: contain !important;
			
			/* 禁用所有可能的滚动反弹效果 */
			-webkit-overflow-scrolling: auto !important;
			
			/* 在某些平台上阻止下拉刷新的额外CSS */
			position: relative !important;
			
			&::before {
				content: '';
				position: absolute;
				top: -100px;
				left: 0;
				right: 0;
				height: 100px;
				background: transparent;
				pointer-events: none;
				z-index: 9999;
			}
		}
		
		/* 手势禁用状态的强化保护 */
		&.gesture-disabled {
			/* 最强的下拉刷新禁用策略 */
			position: fixed !important;
			top: 0 !important;
			left: 0 !important;
			right: 0 !important;
			bottom: 0 !important;
			overflow: hidden !important;
			touch-action: none !important;
			-webkit-overflow-scrolling: auto !important;
			overscroll-behavior: none !important;
			overscroll-behavior-y: none !important;
			-webkit-touch-callout: none !important;
			-webkit-user-select: none !important;
			user-select: none !important;
			
			/* 阻止所有可能的下拉触发 */
			&::before {
				content: '';
				position: absolute;
				top: -200px;
				left: 0;
				right: 0;
				height: 200px;
				background: transparent;
				pointer-events: none !important;
				z-index: 99999;
			}
		}
		
		&.drag-mode {
			/* 拖拽模式样式 */
			/* 完全禁止滚动 */
			overflow: hidden !important;
			touch-action: none !important; /* 禁止所有触摸操作 */
			overscroll-behavior: none !important; /* 禁止所有过度滚动行为 */
			
			/* 额外的下拉刷新禁用 */
			-webkit-overflow-scrolling: auto !important; /* 禁用iOS弹性滚动 */
			overscroll-behavior-y: contain !important; /* 防止Y轴过度滚动 */
			
			/* 防止任何可能触发下拉刷新的行为 */
			&::before, &::after {
				pointer-events: none !important;
			}

			.add-wish-btn {
				opacity: 0.5;
			}

			.wish-list {
				scroll-behavior: smooth;
				transition: scroll-behavior 0.3s;
				pointer-events: none; /* 在拖拽时禁用滚动容器的指针事件 */
				
				/* 强化禁用下拉刷新 */
				overflow-y: hidden !important;
				touch-action: none !important;
				overscroll-behavior-y: none !important;

				/* 但允许卡片本身的指针事件 */
				.wish-card-container {
					pointer-events: auto;
				}
			}

			.wish-card-container {
				/* 性能优化：避免使用 transition: all，只对需要的属性设置过渡 */
				transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
				           opacity 0.3s ease-out;

				/* 启用硬件加速 */
				transform: translateZ(0);
				will-change: auto;
				backface-visibility: hidden;

				&:not(.is-dragging) {
					opacity: 0.8;
					transform: scale(0.98) translateZ(0);
				}

				&.is-dragging {
					transform: scale(1.05) translateZ(0);
					opacity: 1;
					z-index: 100;
					background-color: #fff;
					will-change: transform, opacity;

					/* 阴影单独处理，避免影响主动画 */
					box-shadow: 0 10rpx 30rpx rgba(138, 43, 226, 0.5);
					transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
					           opacity 0.2s ease-out,
					           box-shadow 0.3s ease-out;
					border-radius: 12rpx;
					position: relative;
				}
			}
		}
	}
	
	/* 隐藏滚动条但保留滚动功能 */
	::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}
	
	/* 心愿列表容器 */
	.wish-list {
		padding: 0; /* 移除内边距，让卡片控制自己的边距 */
		position: relative;
		padding-bottom: 150rpx; /* 增加底部内边距，确保最后一个卡片不被tabbar遮挡 */
		overflow-x: hidden; /* 禁止水平滚动 */
		width: 100%; /* 确保宽度不超过父容器 */
		max-width: 100%; /* 防止内容溢出 */
	}
	
	/* 卡片列表内容容器 */
	.wish-list-content {
		width: 100%;
		max-width: 100%; /* 防止内容溢出 */
		display: flex;
		flex-direction: column;
		overflow-x: hidden; /* 禁止水平滚动 */
		/* 移除居中对齐，让卡片根据自己的边距自然排列 */
	}
	
	.empty-list {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 100rpx;
		padding-bottom: 100rpx;
		
		.empty-text {
			font-size: 28rpx;
			color: #999;
			text-align: center;
		}
	}
	
	.add-wish-btn {
		position: fixed;
		right: 40rpx;
		bottom: 160rpx; /* 调整底部位置，确保不被tabbar遮挡 */
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background-color: #8a2be2;
		box-shadow: 0 6rpx 16rpx rgba(138, 43, 226, 0.4);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 99;
		
		.plus-icon {
			width: 50rpx;
			height: 50rpx;
			position: relative;
			
			.h-line, .v-line {
				background-color: #fff;
				position: absolute;
				border-radius: 4rpx;
			}
			
			.h-line {
				width: 50rpx;
				height: 6rpx;
				top: 22rpx; /* 居中位置 (50-6)/2 */
				left: 0;
			}
			
			.v-line {
				height: 50rpx;
				width: 6rpx;
				left: 22rpx; /* 居中位置 (50-6)/2 */
				top: 0;
			}
		}
	}

	.sync-debug-btn {
		position: fixed;
		right: 40rpx;
		bottom: 280rpx;
		width: 80rpx;
		height: 80rpx;
		background: rgba(0, 150, 255, 0.8);
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 98;

		.debug-text {
			color: white;
			font-size: 32rpx;
		}
	}

	/* Vue transition-group 动画样式 */
	.list-transition-group {
		position: relative;
	}

	/* 优化后的删除动画 - 分阶段执行 */
	.list-item-leave-active {
		/* 第一阶段：快速淡出和移动 */
		transition: opacity 0.2s ease-out,
		           transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
		transform-origin: center;
		will-change: opacity, transform;
	}

	.list-item-leave-to {
		opacity: 0;
		/* 使用 translate3d 启用硬件加速 */
		transform: translate3d(-30px, 0, 0) scale(0.9);
	}

	/* 高度收缩动画 - 延迟执行 */
	.list-item-height-collapse {
		transition: height 0.2s ease-out 0.2s,
		           margin 0.2s ease-out 0.2s,
		           padding 0.2s ease-out 0.2s;
		height: 0;
		margin-bottom: 0;
		padding-top: 0;
		padding-bottom: 0;
		overflow: hidden;
	}

	/* 其他元素位置调整动画 - 优化性能 */
	.list-item-move {
		transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		will-change: transform;
	}

	/* 优化后的进入动画 */
	.list-item-enter-active {
		transition: opacity 0.3s ease-out,
		           transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		will-change: opacity, transform;
	}

	.list-item-enter-from {
		opacity: 0;
		/* 使用 translate3d 启用硬件加速 */
		transform: translate3d(30px, 0, 0) scale(0.9);
	}

	/* 响应式动画优化 */

	/* 尊重用户的动画偏好设置 */
	@media (prefers-reduced-motion: reduce) {
		.wish-card-container,
		.list-item-leave-active,
		.list-item-enter-active,
		.list-item-move {
			transition: none !important;
			animation: none !important;
		}

		/* 保持基本的视觉反馈，但移除动画 */
		.wish-card-container.is-dragging {
			transform: scale(1.02) translateZ(0);
			box-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.3);
		}
	}

	/* 低端设备优化 */
	@media (max-device-width: 768px) and (-webkit-max-device-pixel-ratio: 1) {
		.wish-card-container {
			transition-duration: 0.2s; /* 缩短动画时间 */
		}

		.list-item-leave-active,
		.list-item-enter-active {
			transition-duration: 0.25s; /* 缩短列表动画时间 */
		}

		/* 移除复杂的阴影效果 */
		.wish-card-container.is-dragging {
			box-shadow: 0 4rpx 8rpx rgba(138, 43, 226, 0.2);
		}
	}

	/* 高刷新率屏幕优化 */
	@media (min-resolution: 120dpi) {
		.wish-card-container,
		.list-item-move {
			transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
		}
	}

	/* 按钮点击反馈动画优化 */
	.add-wish-btn {
		transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1),
		           box-shadow 0.1s ease-out;
		transform: translateZ(0);
		will-change: auto;
		backface-visibility: hidden;
	}

	.add-wish-btn:active {
		transform: translate3d(0, 2px, 0) scale(0.98);
		box-shadow: 0 2px 8px rgba(138, 43, 226, 0.3);
		will-change: transform, box-shadow;
	}

	/* 支持 hover 的设备上的悬停效果 */
	@media (hover: hover) and (pointer: fine) {
		.add-wish-btn:hover {
			transform: translate3d(0, -1px, 0);
			box-shadow: 0 4px 12px rgba(138, 43, 226, 0.2);
			transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
		}
	}




</style>

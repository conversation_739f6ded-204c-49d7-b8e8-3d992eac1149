<template>
	<view class="history-card-container">
		<view class="swipe-action-wrapper" ref="swipeRef"
			@touchstart="touchStart"
			@touchmove="touchMove"
			@touchend="touchEnd">
			<!-- 主卡片内容 -->
			<view class="swipe-content" :style="contentStyle">
				<view class="wish-card">
					<view class="wish-card-header">
						<view class="wish-card-order">{{ index + 1 }}</view>
						<view class="wish-card-title text-ellipsis">{{ wish.title }}</view>
						<view class="wish-card-status">
							<view class="completed-icon">
								<uni-icons type="checkmarkempty" size="16" color="#fff"></uni-icons>
							</view>
						</view>
					</view>
					
					<view class="wish-card-content">
						<view class="wish-card-desc" v-if="wish.description">{{ wish.description }}</view>
						
						<!-- 🔧 图片展示 - 支持多种数据格式 -->
						<view class="image-container" v-if="convertedImageUrl && hasValidImage(wish.image)">
							<image
								class="wish-card-image"
								:src="convertedImageUrl"
								mode="aspectFit"
								@error="onImageError"
								@load="onImageLoad"
							></image>
							<view v-if="getImageCount(wish.image) > 1" class="multi-image-badge">+{{getImageCount(wish.image)}}</view>
						</view>
					</view>
					
					<view class="wish-card-footer">
						<view class="wish-card-date">完成于 {{ formatDate(wish.completeDate || wish.lastCompleteDate) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 滑动操作按钮 -->
			<view class="swipe-buttons">
				<view class="swipe-button restore" @click="handleButtonClick(0)">还原</view>
				<view class="swipe-button delete" @click="handleButtonClick(1)">删除</view>
			</view>
		</view>
	</view>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { convertCloudUrl } from '@/utils/imageUtils.js';

export default {
	name: 'HistoryWishCard',
	props: {
		wish: {
			type: Object,
			required: true
		},
		index: {
			type: Number,
			default: 0
		},
		activeCardId: {
			type: String,
			default: ''
		}
	},
	setup(props, { emit }) {
		// 滑动状态
		const swiping = ref(false);
		const startX = ref(0);
		const moveX = ref(0);
		const startY = ref(0);
		const swipeRef = ref(null);
		
		// 记录当前卡片的滑动状态
		const isOpen = ref(false);
		
		// 按钮总宽度 (还原按钮宽度 + 删除按钮宽度)
		const buttonsWidth = 120;  // 2个按钮共120px宽度，与主页面一致
		
		// 🔧 检查是否有有效图片 - 支持多种数据格式
		const hasValidImage = (image) => {
			if (!image) return false;

			if (typeof image === 'string') {
				return image.trim() !== '';
			}

			if (Array.isArray(image)) {
				return image.length > 0 && image.some(img => {
					if (typeof img === 'string') {
						return img.trim() !== '';
					} else if (img && typeof img === 'object' && img.url) {
						return img.url.trim() !== '';
					}
					return false;
				});
			}

			if (image && typeof image === 'object' && image.url) {
				return image.url.trim() !== '';
			}

			return false;
		};

		// 🔧 获取图片URL - 支持多种数据格式和云存储转换
		const convertedImageUrl = ref('');

		const getImageUrl = async (image) => {
			if (!image) return '';

			let rawUrl = '';
			if (typeof image === 'string') {
				rawUrl = image;
			} else if (Array.isArray(image) && image.length > 0) {
				const firstImage = image[0];
				if (typeof firstImage === 'string') {
					rawUrl = firstImage;
				} else if (firstImage && typeof firstImage === 'object' && firstImage.url) {
					rawUrl = firstImage.url;
				}
			} else if (image && typeof image === 'object' && image.url) {
				rawUrl = image.url;
			}

			if (!rawUrl) return '';

			try {
				return await convertCloudUrl(rawUrl);
			} catch (error) {
				console.error('[HistoryWishCard] 图片URL转换失败:', error);
				return rawUrl;
			}
		};

		// 监听图片变化并转换URL
		watch(() => props.wish?.image, async (newImage) => {
			convertedImageUrl.value = await getImageUrl(newImage);
		}, { immediate: true });

		// 🔧 获取图片数量
		const getImageCount = (image) => {
			if (!image) return 0;

			if (typeof image === 'string' && image.trim() !== '') {
				return 1;
			}

			if (Array.isArray(image)) {
				return image.filter(img => {
					if (typeof img === 'string') {
						return img.trim() !== '';
					} else if (img && typeof img === 'object' && img.url) {
						return img.url.trim() !== '';
					}
					return false;
				}).length;
			}

			if (image && typeof image === 'object' && image.url && image.url.trim() !== '') {
				return 1;
			}

			return 0;
		};

		// 🔧 图片加载成功处理 - 添加安全检查
		const onImageLoad = (e) => {
			const src = e?.target?.src || e?.detail?.src || '未知图片源';
			console.log('[HistoryWishCard] 图片加载成功:', src);
		};

		// 🔧 图片加载失败处理 - 添加安全检查
		const onImageError = (e) => {
			const src = e?.target?.src || e?.detail?.src || '未知图片源';
			console.error('[HistoryWishCard] 图片加载失败:', {
				src: src,
				error: e
			});
		};
		
		// 在组件挂载时添加滚动监听
		onMounted(() => {
			uni.$on('page-scroll-event', (data) => {
				if (isOpen.value && data && data.shouldClose) {
					resetSwipeWithClass();
				}
			});
			
			uni.$on('onPageScroll', () => {
				if (isOpen.value) {
					resetSwipeWithClass();
				}
			});
			
			uni.$on('force-close-cards', (data) => {
				if (isOpen.value) {
					resetSwipeWithClass();
				}
			});
		});
		
		// 在组件卸载时移除事件监听
		onUnmounted(() => {
			uni.$off('onPageScroll');
			uni.$off('page-scroll-event');
			uni.$off('force-close-cards');
		});
		
		// 内容样式计算
		const contentStyle = computed(() => {
			let x = moveX.value - startX.value;
			
			if (x > 0) {
				x = 0;
			}
			
			if (x < 0) {
				if (x < -buttonsWidth) {
					x = -buttonsWidth + (x + buttonsWidth) * 0.2;
				}
			}
			
			return {
				// 使用 translate3d 启用硬件加速
				transform: `translate3d(${x}px, 0, 0)`,
				transition: swiping.value ? 'none' : 'transform 0.25s cubic-bezier(0.3, 0.9, 0.3, 1)',
				// 性能优化
				willChange: swiping.value ? 'transform' : 'auto',
				backfaceVisibility: 'hidden'
			};
		});
		
		// 触摸开始
		const touchStart = (e) => {
			swiping.value = true;
			startX.value = e.touches[0].clientX;
			moveX.value = startX.value;
			startY.value = e.touches[0].clientY;
			
			if (props.activeCardId && props.activeCardId !== props.wish.id) {
				emit('card-swipe-start', props.wish.id);
			}
		};
		
		// 触摸移动
		const touchMove = (e) => {
			if (!swiping.value) return;
			
			const currentX = e.touches[0].clientX;
			const currentY = e.touches[0].clientY;
			
			const deltaY = Math.abs(currentY - startY.value);
			const deltaX = Math.abs(currentX - startX.value);
			
			if (deltaY > deltaX * 2 && deltaY > 15) {
				swiping.value = false;
				moveX.value = startX.value;
				
				if (isOpen.value) {
					resetSwipeWithClass();
					emit('card-scroll-detected', {
						cardId: props.wish.id,
						deltaY: deltaY
					});
				}
				return;
			}
			
			if (currentX === moveX.value) return;
			moveX.value = currentX;
		};
		
		// 触摸结束
		const touchEnd = (e) => {
			swiping.value = false;
			
			const distance = moveX.value - startX.value;
			
			if (distance < -buttonsWidth / 5) {
				moveX.value = startX.value - buttonsWidth;
				isOpen.value = true;
				emit('card-open', props.wish.id);
			} else {
				moveX.value = startX.value;
				isOpen.value = false;
				
				if (props.activeCardId === props.wish.id) {
					emit('card-close');
				}
			}
		};
		
		// 重置滑动状态
		const resetSwipe = () => {
			moveX.value = startX.value;
			isOpen.value = false;
		};
		
		// 强制重置滑动状态
		const resetSwipeWithClass = () => {
			moveX.value = startX.value;
			isOpen.value = false;
		};
		
		// 按钮点击处理
		const handleButtonClick = (index) => {
			if (index === 0) {
				emit('restore', props.wish.id);
			} else if (index === 1) {
				emit('delete', props.wish.id);
			}
			
			moveX.value = startX.value;
			isOpen.value = false;
		};
		
		// 格式化日期
		const formatDate = (date) => {
			if (!date) return '未知';
			
			try {
				let cleanDate = date;
				if (typeof cleanDate === 'string' && cleanDate.includes('(已还原)')) {
					cleanDate = cleanDate.replace(/\s*\(已还原\)\s*/g, '').trim();
				}
				
				const d = new Date(cleanDate);
				
				if (isNaN(d.getTime())) {
					return typeof cleanDate === 'string' ? 
						cleanDate.replace(/\s*\(已还原\)\s*/g, '') : '未知';
				}
				
				const now = new Date();
				const diffTime = now.getTime() - d.getTime();
				const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
				
				if (diffDays === 0) {
					return '今天';
				} else if (diffDays === 1) {
					return '昨天';
				} else if (diffDays < 7) {
					return `${diffDays}天前`;
				} else {
					return `${d.getMonth() + 1}月${d.getDate()}日`;
				}
			} catch (error) {
				console.error('日期格式化错误:', error);
				return typeof date === 'string' ? 
					date.replace(/\s*\(已还原\)\s*/g, '') : '未知';
			}
		};
		
		return {
			swipeRef,
			contentStyle,
			touchStart,
			touchMove,
			touchEnd,
			handleButtonClick,
			resetSwipe,
			isOpen,
			formatDate,
			hasValidImage,
			convertedImageUrl,
			getImageUrl,
			getImageCount,
			onImageError,
			onImageLoad
		};
	}
}
</script>

<style lang="scss">
.history-card-container {
	margin: 10rpx 2%;
	width: 96%;
	box-sizing: border-box;
}

.swipe-action-wrapper {
	position: relative;
	overflow: hidden;
	border-radius: 12rpx;
	display: flex;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	width: 100%;
}

.swipe-content {
	flex: 1;
	width: 100%;
	z-index: 2;
	background-color: #fff;
	transition: transform 0.3s ease;
	border-radius: 12rpx;
}

.swipe-buttons {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	display: flex;
	height: 100%;
}

.swipe-button {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60px;
	color: white;
	font-size: 26rpx;
	font-weight: 500;
	height: 100%;
	flex-direction: column;
	
	&:active {
		opacity: 0.85;
	}
}

.restore {
	background-color: #19ad19;
}

.delete {
	background-color: #fa5151;
}

.wish-card {
	padding: 24rpx;
	box-sizing: border-box;
	background-color: #ffffff;
	border-radius: 12rpx;
	width: 100%;
	
	.wish-card-header {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
		position: relative;
		
		.wish-card-order {
			width: 40rpx;
			height: 40rpx;
			border-radius: 20rpx;
			background-color: #8a2be2;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			margin-right: 16rpx;
		}
		
		.wish-card-title {
			flex: 1;
			font-size: 32rpx;
			font-weight: 500;
			margin-right: 16rpx;
		}
		
		.wish-card-status {
			display: flex;
			align-items: center;
			
			.completed-icon {
				width: 32rpx;
				height: 32rpx;
				border-radius: 16rpx;
				background-color: #52c41a;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
	
	.wish-card-content {
		margin-bottom: 16rpx;
		position: relative;
		
		.wish-card-desc {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 16rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3; /* 🔧 修改为最多显示3行 */
			overflow: hidden;
		}
		
		.image-container {
			position: relative;
			width: 100%;
			height: 300rpx;
			margin-top: 16rpx;
			border-radius: 8rpx;
			overflow: hidden;
		}
		
		.wish-card-image {
			width: 100%;
			height: 300rpx;
			border-radius: 8rpx;
			/* 🔧 移除 object-fit: cover，使用 mode="aspectFit" 完整显示图片 */
		}
		
		.multi-image-badge {
			position: absolute;
			right: 12rpx;
			bottom: 12rpx;
			background-color: rgba(0, 0, 0, 0.6);
			color: white;
			padding: 4rpx 12rpx;
			border-radius: 16rpx;
			font-size: 20rpx;
			z-index: 5;
		}
	}
	
	.wish-card-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.wish-card-date {
			font-size: 24rpx;
			color: #999;
		}
	}
}

.text-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
</style> 
"use strict";
const common_vendor = require("../common/vendor.js");
const utils_envUtils = require("./envUtils.js");
const utils_debouncer = require("./debouncer.js");
let useWishStore = null;
let useGroupStore = null;
let useUserStore = null;
async function importStores() {
  if (!useWishStore) {
    try {
      const wishModule = await "../store/wish.js";
      useWishStore = wishModule.useWishStore;
      const groupModule = await "../store/group.js";
      useGroupStore = groupModule.useGroupStore;
      const userModule = await "../store/user.js";
      useUserStore = userModule.useUserStore;
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 导入Store模块失败:", error);
    }
  }
}
class SyncManager {
  constructor() {
    this.userId = null;
    this.isInitialized = false;
    this.isOnline = true;
    this.pushClientId = null;
    this.pushEnabled = false;
    this.syncStatus = {
      issyncing: false,
      lastSyncResult: null,
      needSync: false
    };
    this.listeners = {};
    this.syncQueue = [];
    this.isProcessingQueue = false;
    this._processedPushes = /* @__PURE__ */ new Map();
    this._activeSyncOperations = /* @__PURE__ */ new Map();
    this._syncLocks = /* @__PURE__ */ new Map();
    this.batchManager = new utils_debouncer.BatchManager(
      (batch) => this._processBatchPush(batch),
      { delay: 500, maxSize: 20 }
    );
    utils_envUtils.devLog.log("[SyncManager] 🚀 初始化 uni-push 2.0 多设备同步管理器");
  }
  /**
   * 初始化同步管理器
   */
  async init() {
    if (this.isInitialized) {
      utils_envUtils.devLog.log("[SyncManager] 已初始化，跳过");
      return;
    }
    utils_envUtils.devLog.log("[SyncManager] 🚀 启动 uni-push 2.0 多设备同步系统...");
    try {
      await importStores();
      this.userId = await this._getCurrentUserId();
      utils_envUtils.devLog.log("[SyncManager] 获取到用户ID:", this.userId);
      if (!this.userId) {
        utils_envUtils.devLog.warn("[SyncManager] 用户未登录，跳过同步初始化");
        return;
      }
      await this._initUniPush();
      this._setupNetworkListeners();
      this.isInitialized = true;
      if (this.pushEnabled) {
        utils_envUtils.devLog.log("[SyncManager] ✅ uni-push 2.0 多设备同步系统初始化完成");
      } else {
        utils_envUtils.devLog.log("[SyncManager] ✅ 同步系统初始化完成（无推送模式）");
      }
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 初始化失败:", error);
      throw error;
    }
  }
  /**
   * 初始化 uni-push 推送
   */
  async _initUniPush() {
    try {
      utils_envUtils.devLog.log("[SyncManager] 🔄 初始化 uni-push 推送...");
      if (!common_vendor.index.getPushClientId) {
        utils_envUtils.devLog.warn("[SyncManager] 当前环境不支持 uni-push，使用降级模式");
        this.pushEnabled = false;
        return;
      }
      const pushResult = await new Promise((resolve, reject) => {
        common_vendor.index.getPushClientId({
          success: (res) => {
            utils_envUtils.devLog.log("[SyncManager] 获取推送客户端ID成功:", res.cid);
            resolve(res);
          },
          fail: (err) => {
            utils_envUtils.devLog.warn("[SyncManager] 获取推送客户端ID失败:", err);
            if (err.errMsg && err.errMsg.includes("uniPush is not enabled")) {
              utils_envUtils.devLog.log("[SyncManager] uni-push 未启用，可能在开发环境中，将使用降级模式");
              resolve({ cid: null, devMode: true });
            } else {
              reject(err);
            }
          }
        });
      });
      if (pushResult.devMode || !pushResult.cid) {
        utils_envUtils.devLog.log("[SyncManager] ⚠️ 推送功能在当前环境不可用，使用降级模式");
        this.pushEnabled = false;
        this.pushClientId = null;
      } else {
        this.pushClientId = pushResult.cid;
        this.pushEnabled = true;
        utils_envUtils.devLog.log("[SyncManager] ✅ uni-push 推送初始化完成");
      }
      this._setupPushListener();
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] uni-push 初始化失败:", error);
      this.pushEnabled = false;
      utils_envUtils.devLog.log("[SyncManager] 将在无推送模式下继续运行");
    }
  }
  /**
   * 设置推送消息监听
   */
  _setupPushListener() {
    utils_envUtils.devLog.log("[SyncManager] 🔔 设置推送消息监听");
  }
  /**
   * 处理推送消息（由 App.vue 调用）
   */
  async handlePushMessage(message) {
    try {
      utils_envUtils.devLog.log("[SyncManager] 📨 收到推送消息:", JSON.stringify(message));
      if (!message || !message.payload) {
        utils_envUtils.devLog.warn("[SyncManager] 推送消息格式无效");
        return;
      }
      let payload;
      try {
        payload = typeof message.payload === "string" ? JSON.parse(message.payload) : message.payload;
      } catch (parseError) {
        utils_envUtils.devLog.error("[SyncManager] 推送消息解析失败:", parseError);
        return;
      }
      utils_envUtils.devLog.log("[SyncManager] 📋 解析后的推送载荷:", payload);
      if (payload.type === "data_sync") {
        await this._handleDataSyncPush(payload);
      } else if (payload.type === "batch_data_sync") {
        await this._handleBatchDataSyncPush(payload);
      } else if (payload.type === "user_status") {
        await this._handleUserStatusPush(payload);
      } else {
        utils_envUtils.devLog.warn("[SyncManager] 未知的推送消息类型:", payload.type);
      }
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 处理推送消息失败:", error);
    }
  }
  /**
   * 处理数据同步推送
   */
  async _handleDataSyncPush(payload) {
    try {
      const { dataType, action, dataId, timestamp } = payload;
      utils_envUtils.devLog.log(`[SyncManager] 🔄 处理数据同步推送: ${dataType}.${action}`);
      const pushKey = `${dataType}_${action}_${dataId}_${timestamp}`;
      if (this._isRecentlyProcessed(pushKey)) {
        utils_envUtils.devLog.log(`[SyncManager] ⏭️ 跳过重复推送: ${pushKey}`);
        return;
      }
      this._markAsProcessed(pushKey);
      await importStores();
      if (dataType === "wish" && useWishStore) {
        const wishStore = useWishStore();
        await this._syncSpecificWishData(action, dataId, wishStore);
      } else if (dataType === "group" && useGroupStore) {
        const groupStore = useGroupStore();
        await this._syncSpecificGroupData(action, dataId, groupStore);
      } else if (dataType === "comment") {
        const { useCommentStore: useCommentStore2 } = await "../store/comment.js";
        const commentStore = useCommentStore2();
        await this._syncSpecificCommentData(action, dataId, commentStore);
      }
      utils_envUtils.devLog.log(`[SyncManager] ✅ ${dataType}数据同步完成`);
      this._emit("data_synced", { dataType, action, dataId, timestamp });
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 处理数据同步推送失败:", error);
    }
  }
  /**
   * 处理批量数据同步推送
   */
  async _handleBatchDataSyncPush(payload) {
    try {
      const { changes, timestamp } = payload;
      utils_envUtils.devLog.log(`[SyncManager] 🔄 处理批量数据同步推送: ${changes.length}个变化`);
      const batchKey = `batch_${timestamp}_${changes.length}`;
      if (this._isRecentlyProcessed(batchKey)) {
        utils_envUtils.devLog.log(`[SyncManager] ⏭️ 跳过重复批量推送: ${batchKey}`);
        return;
      }
      this._markAsProcessed(batchKey);
      await importStores();
      const changesByType = this._groupChangesByType(changes);
      const syncPromises = [];
      if (changesByType.wish && useWishStore) {
        const wishStore = useWishStore();
        syncPromises.push(this._batchSyncWishData(changesByType.wish, wishStore));
      }
      if (changesByType.comment && useCommentStore) {
        const commentStore = useCommentStore();
        syncPromises.push(this._batchSyncCommentData(changesByType.comment, commentStore));
      }
      if (changesByType.group && useGroupStore) {
        const groupStore = useGroupStore();
        syncPromises.push(this._batchSyncGroupData(changesByType.group, groupStore));
      }
      await Promise.allSettled(syncPromises);
      utils_envUtils.devLog.log(`[SyncManager] ✅ 批量数据同步完成`);
      this._emit("batch_data_synced", { changes, timestamp });
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 处理批量数据同步推送失败:", error);
    }
  }
  /**
   * 处理用户状态推送
   */
  async _handleUserStatusPush(payload) {
    try {
      const { action, deviceInfo, timestamp } = payload;
      utils_envUtils.devLog.log(`[SyncManager] 👤 处理用户状态推送: ${action}`);
      this._emit("user_status_changed", { action, deviceInfo, timestamp });
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 处理用户状态推送失败:", error);
    }
  }
  /**
   * 数据变化通知（Store调用）- 支持批量推送优化
   */
  async notifyDataChange(dataType, action, data, options = {}) {
    utils_envUtils.devLog.log(`[SyncManager] 📝 数据变化通知: ${dataType}.${action}`);
    if (!this.pushEnabled || !this.userId) {
      utils_envUtils.devLog.warn("[SyncManager] 推送未启用或用户未登录，跳过推送");
      return;
    }
    if (options.batch) {
      this._addToBatchQueue(dataType, action, data);
      return;
    }
    try {
      const syncPush = common_vendor.nr.importObject("sync-push");
      const result = await syncPush.pushDataSync({
        userId: this.userId,
        dataType,
        action,
        dataId: (data == null ? void 0 : data.id) || (data == null ? void 0 : data._id),
        data
      });
      if (result.errCode === 0) {
        utils_envUtils.devLog.log(`[SyncManager] ✅ 推送通知发送成功: ${dataType}.${action}`);
      } else {
        utils_envUtils.devLog.error("[SyncManager] 推送通知发送失败:", result.errMsg);
      }
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 发送推送通知失败:", error);
    }
  }
  /**
   * 手动同步数据（用于用户主动刷新）- 🚀 增强防重复机制
   */
  async manualSync() {
    const syncKey = "manual_sync";
    if (this._isSyncInProgress(syncKey)) {
      utils_envUtils.devLog.log("[SyncManager] ⏭️ 手动同步已在进行中，跳过重复请求");
      return { success: true, skipped: true };
    }
    if (!await this._acquireSyncLock(syncKey)) {
      utils_envUtils.devLog.warn("[SyncManager] ⚠️ 无法获取同步锁，跳过同步");
      return { success: false, error: "同步锁获取失败" };
    }
    try {
      this._markSyncStart(syncKey);
      utils_envUtils.devLog.log("[SyncManager] 🔄 手动同步数据...");
      await importStores();
      utils_envUtils.devLog.log("[SyncManager] Store模块导入状态:", {
        useWishStore: !!useWishStore,
        useGroupStore: !!useGroupStore,
        useUserStore: !!useUserStore
      });
      const syncPromises = [];
      if (useWishStore) {
        const wishStore = useWishStore();
        utils_envUtils.devLog.log("[SyncManager] 开始同步心愿数据...");
        syncPromises.push(
          wishStore.syncFromCloud().then((result) => {
            utils_envUtils.devLog.log("[SyncManager] 心愿数据同步成功:", result);
            return result;
          }).catch((err) => {
            utils_envUtils.devLog.error("[SyncManager] 心愿数据同步失败:", err);
            return { error: err, type: "wish" };
          })
        );
      } else {
        utils_envUtils.devLog.warn("[SyncManager] useWishStore 未导入");
      }
      if (useGroupStore) {
        const groupStore = useGroupStore();
        utils_envUtils.devLog.log("[SyncManager] 开始同步分组数据...");
        syncPromises.push(
          groupStore.syncFromCloud().then((result) => {
            utils_envUtils.devLog.log("[SyncManager] 分组数据同步成功:", result);
            return result;
          }).catch((err) => {
            utils_envUtils.devLog.error("[SyncManager] 分组数据同步失败:", err);
            return { error: err, type: "group" };
          })
        );
      } else {
        utils_envUtils.devLog.warn("[SyncManager] useGroupStore 未导入");
      }
      const results = await Promise.allSettled(syncPromises);
      const errors = results.filter((result) => {
        var _a;
        return result.status === "rejected" || ((_a = result.value) == null ? void 0 : _a.error);
      }).map((result) => {
        var _a;
        return result.reason || ((_a = result.value) == null ? void 0 : _a.error);
      });
      const success = errors.length === 0;
      utils_envUtils.devLog.log(`[SyncManager] ${success ? "✅" : "⚠️"} 手动同步完成，错误数: ${errors.length}`);
      this._emit("manual_sync_completed", {
        success,
        errors: errors.length > 0 ? errors : void 0
      });
      return { success, errors };
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 手动同步失败:", error);
      this._emit("manual_sync_completed", { success: false, error });
      return { success: false, error };
    } finally {
      this._markSyncEnd(syncKey);
      this._releaseSyncLock(syncKey);
    }
  }
  /**
   * 获取当前用户ID
   */
  async _getCurrentUserId() {
    try {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo) {
        let parsedUserInfo = userInfo;
        if (typeof userInfo === "string") {
          try {
            parsedUserInfo = JSON.parse(userInfo);
          } catch (e) {
            utils_envUtils.devLog.warn("[SyncManager] 用户信息解析失败:", e);
          }
        }
        if (parsedUserInfo && parsedUserInfo.uid) {
          return parsedUserInfo.uid;
        }
      }
      try {
        await importStores();
        if (useUserStore) {
          const userStore = useUserStore();
          if (userStore && userStore.userInfo && userStore.userInfo.uid) {
            return userStore.userInfo.uid;
          }
        }
      } catch (storeError) {
        utils_envUtils.devLog.warn("[SyncManager] Store获取用户信息失败:", storeError);
      }
      return null;
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 获取用户ID失败:", error);
      return null;
    }
  }
  /**
   * 设置网络监听
   */
  _setupNetworkListeners() {
    common_vendor.index.onNetworkStatusChange((res) => {
      const wasOnline = this.isOnline;
      this.isOnline = res.isConnected;
      utils_envUtils.devLog.log(`[SyncManager] 网络状态: ${this.isOnline ? "在线" : "离线"}`);
      if (!wasOnline && this.isOnline) {
        utils_envUtils.devLog.log("[SyncManager] 🌐 网络恢复，触发同步");
        setTimeout(() => {
          utils_envUtils.devLog.log("[SyncManager] 执行网络恢复同步...");
          this.manualSync().then((result) => {
            utils_envUtils.devLog.log("[SyncManager] 网络恢复同步完成:", result);
          }).catch((error) => {
            utils_envUtils.devLog.error("[SyncManager] 网络恢复同步失败:", error);
          });
        }, 1e3);
      }
    });
  }
  /**
   * 应用前台显示时的处理（由 App.vue 调用）
   */
  onAppShow() {
    utils_envUtils.devLog.log("[SyncManager] 📱 应用前台显示");
    if (this.isInitialized && this.isOnline) {
      utils_envUtils.devLog.log("[SyncManager] 应用前台显示，准备同步数据...");
      setTimeout(() => {
        utils_envUtils.devLog.log("[SyncManager] 执行应用前台同步...");
        this.manualSync().then((result) => {
          utils_envUtils.devLog.log("[SyncManager] 应用前台同步完成:", result);
        }).catch((error) => {
          utils_envUtils.devLog.error("[SyncManager] 应用前台同步失败:", error);
        });
      }, 1e3);
    } else {
      utils_envUtils.devLog.warn("[SyncManager] 应用前台显示，但同步条件不满足:", {
        isInitialized: this.isInitialized,
        isOnline: this.isOnline
      });
    }
  }
  /**
   * 应用进入后台时的处理（由 App.vue 调用）
   */
  onAppHide() {
    utils_envUtils.devLog.log("[SyncManager] 📱 应用进入后台");
  }
  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isInitialized: this.isInitialized,
      isOnline: this.isOnline,
      pushEnabled: this.pushEnabled,
      pushClientId: this.pushClientId,
      issyncing: this.syncStatus.issyncing,
      lastSyncResult: this.syncStatus.lastSyncResult,
      userId: this.userId,
      syncMode: "uni-push-2.0"
    };
  }
  /**
   * 获取推送状态信息
   */
  getPushStatus() {
    return {
      supported: !!common_vendor.index.getPushClientId,
      enabled: this.pushEnabled,
      clientId: this.pushClientId,
      initialized: this.isInitialized,
      userId: this.userId
    };
  }
  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }
  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (!this.listeners[event])
      return;
    const index = this.listeners[event].indexOf(callback);
    if (index > -1) {
      this.listeners[event].splice(index, 1);
    }
  }
  /**
   * 触发事件
   */
  _emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          utils_envUtils.devLog.error("[SyncManager] 事件回调执行失败:", error);
        }
      });
    }
  }
  // ==================== 新增优化方法 ====================
  /**
   * 检查推送是否最近已处理（防重复）
   */
  _isRecentlyProcessed(key) {
    const now = Date.now();
    const processed = this._processedPushes.get(key);
    if (processed && now - processed < 2 * 60 * 1e3) {
      utils_envUtils.devLog.log(`[SyncManager] 🔄 跳过重复处理: ${key}`);
      return true;
    }
    return false;
  }
  /**
   * 标记推送为已处理
   */
  _markAsProcessed(key) {
    const now = Date.now();
    this._processedPushes.set(key, now);
    if (this._processedPushes.size > 100) {
      this._cleanupProcessedRecords();
    }
  }
  /**
   * 🚀 新增：清理过期的处理记录
   */
  _cleanupProcessedRecords() {
    const now = Date.now();
    const tenMinutesAgo = now - 10 * 60 * 1e3;
    for (const [key, timestamp] of this._processedPushes.entries()) {
      if (timestamp < tenMinutesAgo) {
        this._processedPushes.delete(key);
      }
    }
    utils_envUtils.devLog.log(`[SyncManager] 🧹 清理过期记录，当前记录数: ${this._processedPushes.size}`);
  }
  /**
   * 🚀 新增：检查同步操作是否正在进行
   */
  _isSyncInProgress(syncKey) {
    return this._activeSyncOperations.has(syncKey);
  }
  /**
   * 🚀 新增：标记同步操作开始
   */
  _markSyncStart(syncKey) {
    this._activeSyncOperations.set(syncKey, Date.now());
    utils_envUtils.devLog.log(`[SyncManager] 🔄 开始同步操作: ${syncKey}`);
  }
  /**
   * 🚀 新增：标记同步操作结束
   */
  _markSyncEnd(syncKey) {
    this._activeSyncOperations.delete(syncKey);
    utils_envUtils.devLog.log(`[SyncManager] ✅ 完成同步操作: ${syncKey}`);
  }
  /**
   * 🚀 新增：获取同步锁
   */
  async _acquireSyncLock(lockKey, timeout = 3e4) {
    const startTime = Date.now();
    while (this._syncLocks.has(lockKey)) {
      if (Date.now() - startTime > timeout) {
        utils_envUtils.devLog.warn(`[SyncManager] ⏰ 获取同步锁超时: ${lockKey}`);
        return false;
      }
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
    this._syncLocks.set(lockKey, Date.now());
    utils_envUtils.devLog.log(`[SyncManager] 🔒 获取同步锁: ${lockKey}`);
    return true;
  }
  /**
   * 🚀 新增：释放同步锁
   */
  _releaseSyncLock(lockKey) {
    this._syncLocks.delete(lockKey);
    utils_envUtils.devLog.log(`[SyncManager] 🔓 释放同步锁: ${lockKey}`);
  }
  /**
   * 按数据类型分组变化
   */
  _groupChangesByType(changes) {
    const grouped = {};
    changes.forEach((change) => {
      if (!grouped[change.dataType]) {
        grouped[change.dataType] = [];
      }
      grouped[change.dataType].push(change);
    });
    return grouped;
  }
  /**
   * 增量同步特定心愿数据（基础方法）
   */
  async _syncSpecificWishData(action, dataId, wishStore) {
    try {
      if (action === "delete") {
        wishStore.removeWishById(dataId);
        utils_envUtils.devLog.log(`[SyncManager] 🗑️ 本地删除心愿: ${dataId}`);
      } else {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.getWishById(dataId);
        if (result.errCode === 0 && result.data) {
          if (action === "create") {
            wishStore.addWishFromSync(result.data);
            utils_envUtils.devLog.log(`[SyncManager] ➕ 同步新增心愿: ${result.data.title}`);
          } else if (action === "update") {
            wishStore.updateWishFromSync(result.data);
            utils_envUtils.devLog.log(`[SyncManager] 🔄 同步更新心愿: ${result.data.title}`);
          }
        }
      }
    } catch (error) {
      utils_envUtils.devLog.error(`[SyncManager] 同步特定心愿数据失败:`, error);
      await wishStore.syncFromCloud();
    }
  }
  /**
   * 增量同步特定分组数据
   */
  async _syncSpecificGroupData(action, dataId, groupStore) {
    try {
      if (action === "delete") {
        groupStore.removeGroupById(dataId);
        utils_envUtils.devLog.log(`[SyncManager] 🗑️ 本地删除分组: ${dataId}`);
      } else {
        const groupCenter = common_vendor.nr.importObject("group-center");
        const result = await groupCenter.getGroupById(dataId);
        if (result.errCode === 0 && result.data) {
          if (action === "create") {
            groupStore.addGroupFromSync(result.data);
            utils_envUtils.devLog.log(`[SyncManager] ➕ 同步新增分组: ${result.data.name}`);
          } else if (action === "update") {
            groupStore.updateGroupFromSync(result.data);
            utils_envUtils.devLog.log(`[SyncManager] 🔄 同步更新分组: ${result.data.name}`);
          }
        }
      }
    } catch (error) {
      utils_envUtils.devLog.error(`[SyncManager] 同步特定分组数据失败:`, error);
      await groupStore.syncFromCloud();
    }
  }
  /**
   * 增量同步特定评论数据
   */
  async _syncSpecificCommentData(action, dataId, commentStore) {
    try {
      if (action === "delete") {
        commentStore.removeCommentById(dataId);
        utils_envUtils.devLog.log(`[SyncManager] 🗑️ 本地删除评论: ${dataId}`);
      } else {
        const commentCenter = common_vendor.nr.importObject("comment-center");
        const result = await commentCenter.getCommentById(dataId);
        if (result.errCode === 0 && result.data) {
          if (action === "create") {
            commentStore.addCommentFromSync(result.data);
            utils_envUtils.devLog.log(`[SyncManager] ➕ 同步新增评论: ${dataId}`);
          } else if (action === "update") {
            commentStore.updateCommentFromSync(result.data);
            utils_envUtils.devLog.log(`[SyncManager] 🔄 同步更新评论: ${dataId}`);
          }
        }
      }
    } catch (error) {
      utils_envUtils.devLog.error(`[SyncManager] 评论增量同步失败:`, error);
      if (action !== "delete" && dataId) {
        try {
          const commentCenter = common_vendor.nr.importObject("comment-center");
          const result = await commentCenter.getCommentById(dataId);
          if (result.errCode === 0 && result.data && result.data.wishId) {
            await commentStore.syncCommentsForWish(result.data.wishId, true, true);
          }
        } catch (fallbackError) {
          utils_envUtils.devLog.error(`[SyncManager] 评论同步降级失败:`, fallbackError);
        }
      }
    }
  }
  /**
   * 批量同步评论数据
   */
  async _batchSyncCommentData(changes, commentStore) {
    try {
      utils_envUtils.devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个评论变化`);
      const changesByWish = {};
      changes.forEach((change) => {
        var _a;
        const wishId = ((_a = change.data) == null ? void 0 : _a.wishId) || change.wishId;
        if (wishId) {
          if (!changesByWish[wishId]) {
            changesByWish[wishId] = [];
          }
          changesByWish[wishId].push(change);
        }
      });
      const syncPromises = Object.entries(changesByWish).map(async ([wishId, wishChanges]) => {
        await commentStore.syncCommentsForWish(wishId, true, true);
      });
      await Promise.allSettled(syncPromises);
      utils_envUtils.devLog.log(`[SyncManager] ✅ 批量评论同步完成`);
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 批量评论同步失败:", error);
    }
  }
  /**
   * 批量同步心愿数据（基础方法）
   */
  async _batchSyncWishData(changes, wishStore) {
    try {
      utils_envUtils.devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个心愿变化`);
      const deletes = changes.filter((c) => c.action === "delete");
      const updates = changes.filter((c) => c.action !== "delete");
      deletes.forEach((change) => {
        wishStore.removeWishById(change.dataId);
      });
      if (updates.length > 0) {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const dataIds = updates.map((c) => c.dataId);
        const result = await wishCenter.getWishesByIds(dataIds);
        if (result.errCode === 0 && result.data) {
          result.data.forEach((wish) => {
            const change = updates.find((c) => c.dataId === wish._id);
            if (change.action === "create") {
              wishStore.addWishFromSync(wish);
            } else if (change.action === "update") {
              wishStore.updateWishFromSync(wish);
            }
          });
        }
      }
      utils_envUtils.devLog.log(`[SyncManager] ✅ 批量心愿同步完成`);
    } catch (error) {
      utils_envUtils.devLog.error(`[SyncManager] 批量心愿同步失败:`, error);
      await wishStore.syncFromCloud();
    }
  }
  /**
   * 批量同步分组数据
   */
  async _batchSyncGroupData(changes, groupStore) {
    try {
      utils_envUtils.devLog.log(`[SyncManager] 🔄 批量同步 ${changes.length} 个分组变化`);
      const deletes = changes.filter((c) => c.action === "delete");
      const updates = changes.filter((c) => c.action !== "delete");
      deletes.forEach((change) => {
        groupStore.removeGroupById(change.dataId);
      });
      if (updates.length > 0) {
        const groupCenter = common_vendor.nr.importObject("group-center");
        const dataIds = updates.map((c) => c.dataId);
        const result = await groupCenter.getGroupsByIds(dataIds);
        if (result.errCode === 0 && result.data) {
          result.data.forEach((group) => {
            const change = updates.find((c) => c.dataId === group._id);
            if (change.action === "create") {
              groupStore.addGroupFromSync(group);
            } else if (change.action === "update") {
              groupStore.updateGroupFromSync(group);
            }
          });
        }
      }
      utils_envUtils.devLog.log(`[SyncManager] ✅ 批量分组同步完成`);
    } catch (error) {
      utils_envUtils.devLog.error(`[SyncManager] 批量分组同步失败:`, error);
      await groupStore.syncFromCloud();
    }
  }
  /**
   * 添加到批量推送队列（使用优化的批量管理器）
   */
  _addToBatchQueue(dataType, action, data) {
    this.batchManager.add({
      dataType,
      action,
      dataId: (data == null ? void 0 : data.id) || (data == null ? void 0 : data._id),
      data
    });
  }
  /**
   * 处理批量推送（由 BatchManager 调用）
   */
  async _processBatchPush(batch) {
    try {
      utils_envUtils.devLog.log(`[SyncManager] 🚀 批量推送 ${batch.length} 个数据变化`);
      const changes = batch.map((item) => ({
        dataType: item.dataType,
        action: item.action,
        dataId: item.dataId,
        data: item.data,
        timestamp: item.timestamp
      }));
      const syncPush = common_vendor.nr.importObject("sync-push");
      const result = await syncPush.pushBatchDataSync({
        userId: this.userId,
        changes
      });
      if (result.errCode === 0) {
        utils_envUtils.devLog.log(`[SyncManager] ✅ 批量推送成功: ${changes.length}个变化`);
      } else {
        utils_envUtils.devLog.error("[SyncManager] 批量推送失败:", result.errMsg);
      }
    } catch (error) {
      utils_envUtils.devLog.error("[SyncManager] 批量推送失败:", error);
    }
  }
  /**
   * 销毁同步管理器
   */
  destroy() {
    utils_envUtils.devLog.log("[SyncManager] 🗑️ 销毁 uni-push 2.0 同步管理器");
    if (this.batchManager) {
      this.batchManager.clear();
    }
    this.isInitialized = false;
    this.pushEnabled = false;
    this.pushClientId = null;
    this.listeners = {};
    this.syncQueue = [];
    this._processedPushes = null;
  }
}
const syncManager = new SyncManager();
exports.syncManager = syncManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/syncManager.js.map

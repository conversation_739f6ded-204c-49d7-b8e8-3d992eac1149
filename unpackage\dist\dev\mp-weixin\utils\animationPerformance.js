"use strict";
const utils_envUtils = require("./envUtils.js");
class AnimationPerformanceMonitor {
  constructor() {
    this.isEnabled = utils_envUtils.enablePerformanceMonitoring();
    if (this.isEnabled) {
      this.performanceData = [];
      this.tempData = {};
      this.frameCount = 0;
      this.lastTime = 0;
      this.fps = 0;
    }
  }
  /**
   * 开始监控动画性能
   * @param {string} animationName - 动画名称
   */
  startMonitoring(animationName) {
    if (!this.isEnabled)
      return;
    if (typeof performance !== "undefined" && performance.mark) {
      performance.mark(`${animationName}-start`);
      utils_envUtils.devLog.log(`🎬 开始监控动画: ${animationName}`);
    } else {
      this.tempData[`${animationName}-start`] = Date.now();
      utils_envUtils.devLog.log(`🎬 开始监控动画 (降级模式): ${animationName}`);
    }
  }
  /**
   * 结束监控并输出结果
   * @param {string} animationName - 动画名称
   */
  endMonitoring(animationName) {
    if (!this.isEnabled)
      return;
    let duration = 0;
    if (typeof performance !== "undefined" && performance.mark && performance.measure) {
      performance.mark(`${animationName}-end`);
      performance.measure(`${animationName}-duration`, `${animationName}-start`, `${animationName}-end`);
      const measure = performance.getEntriesByName(`${animationName}-duration`)[0];
      if (measure) {
        duration = measure.duration;
      }
    } else {
      const startTime = this.tempData[`${animationName}-start`];
      if (startTime) {
        duration = Date.now() - startTime;
        delete this.tempData[`${animationName}-start`];
      }
    }
    if (duration > 0) {
      this.performanceData.push({
        name: animationName,
        duration,
        timestamp: Date.now()
      });
      if (duration > 16.67) {
        utils_envUtils.devLog.warn(`⚠️ 动画性能警告: ${animationName} 耗时 ${duration.toFixed(2)}ms (超过16.67ms)`);
        this.suggestOptimization(animationName, duration);
      } else {
        utils_envUtils.devLog.log(`✅ 动画性能良好: ${animationName} 耗时 ${duration.toFixed(2)}ms`);
      }
    }
  }
  /**
   * 提供优化建议
   * @param {string} animationName - 动画名称
   * @param {number} duration - 动画耗时
   */
  suggestOptimization(animationName, duration) {
    const suggestions = [];
    if (duration > 50) {
      suggestions.push("考虑使用 transform 替代 left/top 属性");
      suggestions.push("添加 will-change 属性");
      suggestions.push("使用 translate3d 启用硬件加速");
    }
    if (duration > 100) {
      suggestions.push("考虑分解复杂动画为多个简单动画");
      suggestions.push("减少同时进行的动画数量");
      suggestions.push("使用 requestAnimationFrame 优化");
    }
    utils_envUtils.devLog.log(`💡 ${animationName} 优化建议:`);
    suggestions.forEach((suggestion) => utils_envUtils.devLog.log(`• ${suggestion}`));
  }
  /**
   * 监控FPS
   */
  startFPSMonitoring() {
    if (!this.isEnabled)
      return;
    const raf = typeof requestAnimationFrame !== "undefined" ? requestAnimationFrame : null;
    if (!raf) {
      utils_envUtils.devLog.warn("[AnimationPerformanceMonitor] requestAnimationFrame不可用，跳过FPS监控");
      return;
    }
    const measureFPS = (currentTime) => {
      this.frameCount++;
      if (currentTime - this.lastTime >= 1e3) {
        this.fps = Math.round(this.frameCount * 1e3 / (currentTime - this.lastTime));
        if (this.fps < 50) {
          utils_envUtils.devLog.warn(`⚠️ FPS过低: ${this.fps} fps`);
        }
        this.frameCount = 0;
        this.lastTime = currentTime;
      }
      raf(measureFPS);
    };
    raf(measureFPS);
  }
  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    if (!this.isEnabled)
      return null;
    const totalAnimations = this.performanceData.length;
    const averageDuration = this.performanceData.reduce((sum, data) => sum + data.duration, 0) / totalAnimations;
    const slowAnimations = this.performanceData.filter((data) => data.duration > 16.67);
    return {
      totalAnimations,
      averageDuration: averageDuration.toFixed(2),
      slowAnimations: slowAnimations.length,
      currentFPS: this.fps,
      performanceScore: this.calculatePerformanceScore()
    };
  }
  /**
   * 计算性能评分
   */
  calculatePerformanceScore() {
    if (this.performanceData.length === 0)
      return 100;
    const slowAnimationRatio = this.performanceData.filter((data) => data.duration > 16.67).length / this.performanceData.length;
    const fpsScore = Math.min(this.fps / 60, 1);
    return Math.round((1 - slowAnimationRatio) * fpsScore * 100);
  }
  /**
   * 清理性能数据
   */
  clearData() {
    this.performanceData = [];
    this.tempData = {};
    this.frameCount = 0;
    this.lastTime = 0;
  }
}
const animationMonitor = new AnimationPerformanceMonitor();
const AnimationUtils = {
  /**
   * 优化的 requestAnimationFrame (兼容小程序)
   * @param {Function} callback - 回调函数
   * @returns {number} - 动画帧ID或定时器ID
   */
  optimizedRAF(callback) {
    if (typeof requestAnimationFrame !== "undefined") {
      let rafId;
      let executed = false;
      rafId = requestAnimationFrame(() => {
        if (!executed) {
          executed = true;
          callback();
        }
      });
      return rafId;
    } else {
      return setTimeout(callback, 16);
    }
  },
  /**
   * 取消动画帧 (兼容小程序)
   * @param {number} id - 动画帧ID或定时器ID
   */
  cancelRAF(id) {
    if (typeof cancelAnimationFrame !== "undefined") {
      cancelAnimationFrame(id);
    } else {
      clearTimeout(id);
    }
  },
  /**
   * 批量执行动画
   * @param {Array} animations - 动画数组
   * @returns {Promise} - 所有动画完成的Promise
   */
  batchAnimations(animations) {
    return Promise.all(animations.map((animation) => {
      if (animation.finished) {
        return animation.finished;
      }
      return new Promise((resolve) => {
        animation.addEventListener("finish", resolve, { once: true });
      });
    }));
  },
  /**
   * 创建高性能的CSS动画
   * @param {Element} element - 目标元素
   * @param {Object} keyframes - 关键帧
   * @param {Object} options - 动画选项
   * @returns {Animation} - Web Animation API 动画对象
   */
  createOptimizedAnimation(element, keyframes, options = {}) {
    const defaultOptions = {
      duration: 300,
      easing: "cubic-bezier(0.4, 0, 0.2, 1)",
      fill: "forwards"
    };
    const finalOptions = { ...defaultOptions, ...options };
    if (keyframes.some((frame) => frame.transform)) {
      element.style.willChange = "transform";
    }
    const animation = element.animate(keyframes, finalOptions);
    animation.addEventListener("finish", () => {
      element.style.willChange = "auto";
    }, { once: true });
    return animation;
  },
  /**
   * 检测设备性能并返回适合的动画配置
   */
  getOptimalAnimationConfig() {
    const isLowEndDevice = this.isLowEndDevice();
    const prefersReducedMotion = typeof window !== "undefined" && window.matchMedia ? window.matchMedia("(prefers-reduced-motion: reduce)").matches : false;
    if (prefersReducedMotion) {
      return {
        duration: 0,
        enabled: false
      };
    }
    if (isLowEndDevice) {
      return {
        duration: 200,
        easing: "ease-out",
        complexity: "low"
      };
    }
    return {
      duration: 300,
      easing: "cubic-bezier(0.4, 0, 0.2, 1)",
      complexity: "high"
    };
  },
  /**
   * 检测是否为低端设备
   */
  isLowEndDevice() {
    const hardwareConcurrency = typeof navigator !== "undefined" && navigator.hardwareConcurrency ? navigator.hardwareConcurrency : 4;
    const deviceMemory = typeof navigator !== "undefined" && navigator.deviceMemory ? navigator.deviceMemory : 4;
    const connection = typeof navigator !== "undefined" ? navigator.connection || navigator.mozConnection || navigator.webkitConnection : null;
    const isSlowConnection = connection && (connection.effectiveType === "slow-2g" || connection.effectiveType === "2g");
    return hardwareConcurrency <= 2 || deviceMemory <= 2 || isSlowConnection;
  }
};
exports.AnimationUtils = AnimationUtils;
exports.animationMonitor = animationMonitor;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/animationPerformance.js.map

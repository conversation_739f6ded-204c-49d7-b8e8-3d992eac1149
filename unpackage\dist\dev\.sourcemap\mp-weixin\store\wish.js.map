{"version": 3, "file": "wish.js", "sources": ["store/wish.js"], "sourcesContent": ["/**\n * 心愿数据管理 Store - 简化版本\n */\n\nimport { defineStore } from 'pinia'\n\n// 错误代码常量\nconst ERROR_CODES = {\n  NETWORK_ERROR: 'NETWORK_ERROR',\n  AUTH_ERROR: 'AUTH_ERROR',\n  VALIDATION_ERROR: 'VALIDATION_ERROR',\n  PERMISSION_ERROR: 'PERMISSION_ERROR',\n  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',\n  SERVER_ERROR: 'SERVER_ERROR'\n}\n\n// 错误处理工具类\nconst ErrorHandler = {\n  getUserFriendlyMessage(error) {\n    if (typeof error === 'string') return error\n    return error.message || error.errMsg || '未知错误'\n  },\n  \n  isNetworkError(error) {\n    if (error.errCode === ERROR_CODES.NETWORK_ERROR) return true\n    if (error.code === 'NETWORK_ERROR') return true\n    return false\n  },\n  \n  isVersionConflictError(error) {\n    return error && error.message && (\n      error.message.includes('数据版本冲突') ||\n      error.message.includes('逻辑时钟冲突') ||\n      error.message.includes('version conflict') ||\n      error.message.includes('conflict')\n    )\n  },\n\n  showError(error, title = '操作失败', context = 'unknown') {\n    const message = this.getUserFriendlyMessage(error)\n    uni.showToast({\n      title: message,\n      icon: 'none',\n      duration: 3000\n    })\n    console.error(`${title}:`, error)\n  }\n}\n\nexport const useWishStore = defineStore('wish', {\n  state: () => ({\n    wishList: [],\n    currentGroupId: 'all',\n    isLoading: false,\n    lastSyncTime: null,\n    isOnline: true,\n    listUpdateCounter: 0,  // 🚀 添加列表更新计数器\n\n    // 同步状态管理\n    syncStatus: {\n      issyncing: false,\n      lastSyncResult: null,\n      pendingCount: 0,\n      errorCount: 0,\n      lastError: null\n    },\n\n    // 防重复同步机制\n    _syncOperations: new Set(),\n    _lastSyncTime: 0\n  }),\n\n  getters: {\n    activeWishes: (state) => {\n      return state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)\n    },\n\n    // 🚀 添加当前分组的心愿列表\n    currentGroupWishes: (state) => {\n      const activeWishes = state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)\n\n      if (state.currentGroupId === 'all') {\n        return activeWishes\n      } else if (state.currentGroupId === 'completed') {\n        return state.wishList.filter(wish => !wish._deleted && wish.isCompleted)\n      } else {\n        // 特定分组\n        return activeWishes.filter(wish =>\n          wish.groupIds && wish.groupIds.includes(state.currentGroupId)\n        )\n      }\n    },\n\n    // 🚀 添加其他必要的 getters\n    getWishById: (state) => (id) => {\n      return state.wishList.find(wish =>\n        (wish._id === id || wish.id === id) && !wish._deleted\n      ) || null\n    }\n  },\n\n  actions: {\n    async initWishList() {\n      console.log('[wishStore] Initializing wish list...')\n      this.isLoading = true\n\n      try {\n        // 从本地存储加载数据\n        const storedWishes = uni.getStorageSync('wishList')\n        if (storedWishes) {\n          const parsed = JSON.parse(storedWishes)\n          this.wishList = parsed.map(wish => ({\n            ...wish,\n            id: wish.id || wish._id\n          }))\n        }\n\n        // 尝试从云端同步数据\n        console.log('[wishStore] 开始从云端同步最新数据...')\n        await this.syncFromCloud()\n\n        console.log('[wishStore] Wish list initialization completed')\n      } catch (error) {\n        console.error('[wishStore] Failed to initialize wish list:', error)\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    // 🚀 添加基本的同步方法来测试云函数修改\n    async syncFromCloud() {\n      const operation = 'syncFromCloud'\n\n      // 检查是否可以开始同步\n      if (!this._canStartSync(operation)) {\n        return { success: false, reason: 'sync_in_progress' }\n      }\n\n      console.log('[wishStore] 开始从云端同步数据...')\n\n      try {\n        this._startSyncOperation(operation)\n        this._updateSyncStatus({ issyncing: true })\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.getWishList({\n          page: 1,\n          pageSize: 1000,\n          includeCompleted: true\n        })\n\n        if (result.errCode === 0) {\n          const cloudWishes = result.data || []\n          console.log('[wishStore] 从云端获取到', cloudWishes.length, '个心愿')\n\n          // 执行智能数据合并而不是直接覆盖\n          const mergedData = this._mergeWishData(this.wishList, cloudWishes)\n\n          // 更新本地数据\n          this.wishList = mergedData.map(wish => ({\n            ...wish,\n            id: wish._id || wish.id // 确保有 id 字段\n          }))\n\n          // 保存到本地存储\n          uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          this.lastSyncTime = new Date().toISOString()\n          uni.setStorageSync('wishListLastSyncTime', this.lastSyncTime)\n\n          console.log('[wishStore] 智能同步完成，合并后共', this.wishList.length, '个心愿')\n\n          // 更新同步状态\n          this._updateSyncStatus({\n            lastSyncResult: 'success',\n            errorCount: 0,\n            lastError: null\n          })\n\n          return { success: true, count: cloudWishes.length }\n        } else {\n          const errorMsg = result.errMsg || '云函数返回错误'\n          console.error('[wishStore] 云函数错误:', errorMsg)\n\n          // 更新错误状态\n          this._updateSyncStatus({\n            lastSyncResult: 'error',\n            errorCount: this.syncStatus.errorCount + 1,\n            lastError: errorMsg\n          })\n\n          throw new Error(errorMsg)\n        }\n      } catch (error) {\n        console.error('[wishStore] 同步出错:', error)\n\n        // 更新错误状态\n        this._updateSyncStatus({\n          lastSyncResult: 'error',\n          errorCount: this.syncStatus.errorCount + 1,\n          lastError: error.message\n        })\n\n        throw error\n      } finally {\n        this._updateSyncStatus({ issyncing: false })\n        this._endSyncOperation(operation)\n      }\n    },\n\n    // 🚀 更新心愿\n    async updateWish(updatedWish) {\n      console.log('[wishStore] 更新心愿:', updatedWish)\n\n      try {\n        // 验证更新数据的完整性\n        if (!updatedWish || (!updatedWish._id && !updatedWish.id)) {\n          throw new Error('更新数据缺少必要的ID字段')\n        }\n\n        const wishId = updatedWish._id || updatedWish.id\n\n        // 准备更新数据\n        const updateData = {\n          ...updatedWish,\n          updateDate: new Date().toISOString()\n        }\n\n        // 调用云函数更新\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.updateWish(wishId, updateData)\n\n        if (result.errCode === 0) {\n          // 云端更新成功，更新本地数据\n          const index = this.wishList.findIndex(w => w._id === wishId || w.id === wishId)\n          if (index !== -1) {\n            // 合并数据，确保使用正确的字段名\n            const mergedWish = {\n              ...this.wishList[index],\n              ...updateData,\n              id: wishId,\n              _id: wishId\n            }\n            this.wishList[index] = mergedWish\n            uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          }\n\n          uni.showToast({\n            title: '更新成功',\n            icon: 'success'\n          })\n\n          console.log('[wishStore] 更新成功')\n        } else {\n          throw new Error(result.errMsg || '更新心愿失败')\n        }\n      } catch (error) {\n        console.error('[wishStore] 更新心愿失败:', error)\n        ErrorHandler.showError(error, '更新失败')\n        throw error // 重新抛出错误，让调用方知道失败了\n      }\n    },\n\n    // 🚀 添加删除方法来测试多设备冲突处理\n    async deleteWish(id) {\n      console.log('[wishStore] 删除心愿:', id)\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.deleteWish(id)\n\n        if (result.errCode === 0) {\n          // 从本地列表中移除\n          const index = this.wishList.findIndex(w => w._id === id || w.id === id)\n          if (index !== -1) {\n            this.wishList.splice(index, 1)\n            uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          }\n\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          })\n\n          console.log('[wishStore] 删除成功')\n        } else {\n          console.error('[wishStore] 删除失败:', result.errMsg)\n          ErrorHandler.showError({ message: result.errMsg }, '删除失败')\n        }\n      } catch (error) {\n        console.error('[wishStore] 删除出错:', error)\n        ErrorHandler.showError(error, '删除失败')\n      }\n    },\n\n    // 智能合并本地和云端数据\n    _mergeWishData(localWishes, cloudWishes) {\n      console.log('[wishStore] 智能合并本地和云端数据...')\n      console.log('[wishStore] 本地数据:', localWishes.length, '个心愿')\n      console.log('[wishStore] 云端数据:', cloudWishes.length, '个心愿')\n\n      const mergedMap = new Map()\n\n      // 首先处理云端数据\n      cloudWishes.forEach(cloudWish => {\n        mergedMap.set(cloudWish._id, {\n          ...cloudWish,\n          _source: 'cloud'\n        })\n      })\n\n      // 然后处理本地数据，根据时间戳决定是否覆盖\n      localWishes.forEach(localWish => {\n        const id = localWish._id || localWish.id\n        const existingWish = mergedMap.get(id)\n\n        if (!existingWish) {\n          // 本地独有的数据，保留\n          mergedMap.set(id, {\n            ...localWish,\n            _id: id,\n            _source: 'local'\n          })\n        } else {\n          // 数据冲突，比较时间戳\n          const localTime = new Date(localWish.updatedAt || localWish.createdAt || 0).getTime()\n          const cloudTime = new Date(existingWish.updatedAt || existingWish.createdAt || 0).getTime()\n\n          if (localTime > cloudTime) {\n            // 本地数据更新，使用本地数据\n            mergedMap.set(id, {\n              ...localWish,\n              _id: id,\n              _source: 'local_newer'\n            })\n          }\n          // 否则使用云端数据（已经在map中）\n        }\n      })\n\n      const mergedArray = Array.from(mergedMap.values())\n      console.log('[wishStore] 合并完成，最终数据:', mergedArray.length, '个心愿')\n\n      return mergedArray\n    },\n\n    // 检查同步操作是否可以执行\n    _canStartSync(operation) {\n      const now = Date.now()\n\n      // 检查是否有相同操作正在进行\n      if (this._syncOperations.has(operation)) {\n        console.log(`[wishStore] 同步操作 ${operation} 已在进行中`)\n        return false\n      }\n\n      // 检查是否距离上次同步太近（防抖）\n      if (now - this._lastSyncTime < 1000) {\n        console.log(`[wishStore] 同步操作过于频繁，跳过`)\n        return false\n      }\n\n      return true\n    },\n\n    // 开始同步操作\n    _startSyncOperation(operation) {\n      this._syncOperations.add(operation)\n      this._lastSyncTime = Date.now()\n      console.log(`[wishStore] 开始同步操作: ${operation}`)\n    },\n\n    // 结束同步操作\n    _endSyncOperation(operation) {\n      this._syncOperations.delete(operation)\n      console.log(`[wishStore] 结束同步操作: ${operation}`)\n    },\n\n    // 更新同步状态\n    _updateSyncStatus(updates) {\n      Object.assign(this.syncStatus, updates)\n    },\n\n    // 🚀 添加其他必要的方法\n    setCurrentGroup(groupId) {\n      this.currentGroupId = groupId\n    },\n\n    // 强制同步数据（用于下拉刷新）\n    async forceSyncData() {\n      console.log('[wishStore] 强制同步数据...')\n      await this.syncFromCloud()\n    },\n\n    // 手动同步（兼容原有接口）\n    async manualSync(silent = false) {\n      if (!silent) {\n        uni.showLoading({ title: '同步中...' })\n      }\n\n      try {\n        await this.syncFromCloud()\n\n        if (!silent) {\n          uni.hideLoading()\n          uni.showToast({\n            title: '同步完成',\n            icon: 'success'\n          })\n        }\n      } catch (error) {\n        if (!silent) {\n          uni.hideLoading()\n        }\n        throw error\n      }\n    },\n\n    // 智能同步方法（兼容 userStore 调用）\n    async smartSync() {\n      console.log('[wishStore] Starting smart sync...')\n\n      try {\n        // 检查网络状态\n        const networkInfo = await uni.getNetworkType()\n        if (networkInfo.networkType === 'none') {\n          console.warn('[wishStore] Network unavailable, skipping smart sync')\n          return { hasUpdates: false, updatedCount: 0, reason: 'offline' }\n        }\n\n        // 执行同步\n        const result = await this.syncFromCloud()\n\n        if (result && result.success) {\n          return {\n            hasUpdates: true,\n            updatedCount: result.count || 0,\n            reason: 'success'\n          }\n        } else {\n          return {\n            hasUpdates: false,\n            updatedCount: 0,\n            reason: 'no_updates'\n          }\n        }\n      } catch (error) {\n        console.error('[wishStore] Smart sync failed:', error)\n        return {\n          hasUpdates: false,\n          updatedCount: 0,\n          reason: 'error',\n          error: error.message\n        }\n      }\n    },\n\n    // 完成心愿\n    async completeWish(id) {\n      console.log('[wishStore] 完成心愿:', id)\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.completeWish(id)\n\n        if (result.errCode === 0) {\n          // 更新本地数据\n          const wish = this.wishList.find(w => w._id === id || w.id === id)\n          if (wish) {\n            wish.isCompleted = true\n            wish.completeDate = new Date().toISOString()\n            uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          }\n\n          uni.showToast({\n            title: '心愿已完成',\n            icon: 'success'\n          })\n        } else {\n          ErrorHandler.showError({ message: result.errMsg }, '完成失败')\n        }\n      } catch (error) {\n        console.error('[wishStore] 完成心愿出错:', error)\n        ErrorHandler.showError(error, '完成失败')\n      }\n    },\n\n    // 恢复心愿\n    async restoreWish(id) {\n      console.log('[wishStore] 恢复心愿:', id)\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.restoreWish(id)\n\n        if (result.errCode === 0) {\n          // 更新本地数据\n          const wish = this.wishList.find(w => w._id === id || w.id === id)\n          if (wish) {\n            wish.isCompleted = false\n            wish.completeDate = null\n            uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          }\n\n          uni.showToast({\n            title: '心愿已恢复',\n            icon: 'success'\n          })\n        } else {\n          ErrorHandler.showError({ message: result.errMsg }, '恢复失败')\n        }\n      } catch (error) {\n        console.error('[wishStore] 恢复心愿出错:', error)\n        ErrorHandler.showError(error, '恢复失败')\n      }\n    },\n\n    // 🚀 清理本地数据（登录后重新初始化时使用）\n    clearLocalData() {\n      console.log('[wishStore] 清理本地数据...')\n\n      // 清空当前数据\n      this.wishList = []\n      this.currentGroupId = 'all'\n      this.lastSyncTime = null\n      this.isLoading = false\n\n      // 清理本地存储\n      uni.removeStorageSync('wishList')\n      uni.removeStorageSync('wishLastSyncTime')\n\n      console.log('[wishStore] 本地数据清理完成')\n    },\n\n    // 🚀 强制初始化（登录后使用）\n    async forceInit() {\n      console.log('[wishStore] 强制初始化...')\n\n      // 清空当前数据\n      this.clearLocalData()\n\n      // 强制从云端同步\n      await this.syncFromCloud()\n\n      console.log('[wishStore] 强制初始化完成')\n    },\n\n    // 🚀 用户登出时清理数据\n    clearUserData() {\n      console.log('[wishStore] 清理用户数据...')\n\n      // 清空用户的心愿数据\n      this.wishList = []\n\n      // 重置为默认分组（全部）\n      this.currentGroupId = 'all'\n\n      this.lastSyncTime = null\n      this.isLoading = false\n\n      // 清除本地存储中的用户数据\n      uni.removeStorageSync('wishList')\n      uni.removeStorageSync('wishLastSyncTime')\n\n      console.log('[wishStore] 用户数据清理完成')\n    },\n\n    // 🚀 刷新心愿列表（从本地存储重新加载）\n    refreshWishList() {\n      console.log('[wishStore] 刷新心愿列表...')\n\n      try {\n        const storedWishes = uni.getStorageSync('wishList')\n        if (storedWishes) {\n          const parsed = JSON.parse(storedWishes)\n          this.wishList = parsed.map(wish => ({\n            ...wish,\n            id: wish.id || wish._id\n          }))\n          console.log('[wishStore] 心愿列表已刷新，共', this.wishList.length, '个心愿')\n        }\n      } catch (error) {\n        console.error('[wishStore] 刷新心愿列表失败:', error)\n      }\n    },\n\n    // 🚀 更新心愿排序\n    updateWishOrder(orderedIds) {\n      console.log('[wishStore] 更新心愿排序:', orderedIds)\n\n      // 简单实现：根据新的ID顺序重新排列心愿\n      const reorderedWishes = []\n      orderedIds.forEach(id => {\n        const wish = this.wishList.find(w => w._id === id || w.id === id)\n        if (wish) {\n          reorderedWishes.push(wish)\n        }\n      })\n\n      // 添加不在排序列表中的心愿\n      this.wishList.forEach(wish => {\n        if (!orderedIds.includes(wish._id) && !orderedIds.includes(wish.id)) {\n          reorderedWishes.push(wish)\n        }\n      })\n\n      this.wishList = reorderedWishes\n      uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n\n      console.log('[wishStore] 心愿排序已更新')\n    },\n\n    // 🚀 设置当前心愿列表\n    setCurrentList(newWishList) {\n      console.log('[wishStore] 设置当前心愿列表:', newWishList.length, '个心愿')\n\n      this.wishList = newWishList.map(wish => ({\n        ...wish,\n        id: wish.id || wish._id\n      }))\n\n      uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n    },\n\n    // 🚀 添加心愿\n    async addWish(wishData) {\n      console.log('[wishStore] 添加心愿:', wishData)\n\n      try {\n        // 增强心愿数据\n        const enhancedWishData = {\n          ...wishData,\n          createDate: new Date().toISOString(),\n          updateDate: new Date().toISOString(),\n          isCompleted: false,\n          userId: '', // 会在云函数中设置\n          permission: wishData.permission || 'private',\n          groupIds: wishData.groupIds || ['all'],\n          order: this.wishList.length + 1\n        }\n\n        // 检查网络状态\n        if (!this.isOnline) {\n          console.log('当前离线，心愿将保存到本地')\n          return this._addWishOffline(enhancedWishData)\n        }\n\n        // 先调用云函数创建\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.createWish(enhancedWishData)\n\n        if (result.errCode === 0) {\n          // 云端创建成功，添加到本地列表\n          const wishWithId = {\n            ...result.data,\n            id: result.data._id // 确保有 id 字段供前端组件使用\n          }\n          this.wishList.push(wishWithId)\n          uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n\n          uni.showToast({\n            title: '心愿创建成功',\n            icon: 'success'\n          })\n\n          return wishWithId\n        } else {\n          throw new Error(result.errMsg || '创建心愿失败')\n        }\n      } catch (error) {\n        console.error('添加心愿失败:', error)\n\n        // 网络错误时，保存到本地（离线模式）\n        if (ErrorHandler.isNetworkError(error)) {\n          this.isOnline = false // 更新网络状态\n          return this._addWishOffline(enhancedWishData)\n        }\n\n        // 版本冲突时，使用离线模式\n        if (ErrorHandler.isVersionConflictError(error)) {\n          return this._addWishOffline(enhancedWishData)\n        }\n\n        // 其他错误，显示错误信息\n        ErrorHandler.showError(error, '创建心愿失败')\n        throw error\n      }\n    },\n\n    // 🚀 离线模式添加心愿\n    _addWishOffline(wishData) {\n      console.log('[wishStore] 离线模式添加心愿:', wishData.title)\n\n      // 生成临时ID\n      const tempId = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)\n\n      const wish = {\n        _id: tempId,\n        id: tempId,\n        ...wishData,\n        _needSync: true, // 标记需要同步到云端\n        _isLocal: true   // 标记为本地创建\n      }\n\n      this.wishList.push(wish)\n      uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n\n      // 显示离线提示\n      uni.showToast({\n        title: this.isOnline ? '已保存到本地，稍后将同步到云端' : '当前离线，已保存到本地',\n        icon: 'none',\n        duration: 2500\n      })\n\n      return wish\n    }\n  }\n})\n"], "names": ["uni", "defineStore", "uniCloud", "enhancedWishData"], "mappings": ";;;AAOA,MAAM,cAAc;AAAA,EAClB,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAChB;AAGA,MAAM,eAAe;AAAA,EACnB,uBAAuB,OAAO;AAC5B,QAAI,OAAO,UAAU;AAAU,aAAO;AACtC,WAAO,MAAM,WAAW,MAAM,UAAU;AAAA,EACzC;AAAA,EAED,eAAe,OAAO;AACpB,QAAI,MAAM,YAAY,YAAY;AAAe,aAAO;AACxD,QAAI,MAAM,SAAS;AAAiB,aAAO;AAC3C,WAAO;AAAA,EACR;AAAA,EAED,uBAAuB,OAAO;AAC5B,WAAO,SAAS,MAAM,YACpB,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,kBAAkB,KACzC,MAAM,QAAQ,SAAS,UAAU;AAAA,EAEpC;AAAA,EAED,UAAU,OAAO,QAAQ,QAAQ,UAAU,WAAW;AACpD,UAAM,UAAU,KAAK,uBAAuB,KAAK;AACjDA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAChB,CAAK;AACDA,wBAAA,MAAA,SAAA,uBAAc,GAAG,KAAK,KAAK,KAAK;AAAA,EACjC;AACH;AAEY,MAAC,eAAeC,cAAW,YAAC,QAAQ;AAAA,EAC9C,OAAO,OAAO;AAAA,IACZ,UAAU,CAAE;AAAA,IACZ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,mBAAmB;AAAA;AAAA;AAAA,IAGnB,YAAY;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,IACZ;AAAA;AAAA,IAGD,iBAAiB,oBAAI,IAAK;AAAA,IAC1B,eAAe;AAAA,EACnB;AAAA,EAEE,SAAS;AAAA,IACP,cAAc,CAAC,UAAU;AACvB,aAAO,MAAM,SAAS,OAAO,UAAQ,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AAAA,IACzE;AAAA;AAAA,IAGD,oBAAoB,CAAC,UAAU;AAC7B,YAAM,eAAe,MAAM,SAAS,OAAO,UAAQ,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AAEtF,UAAI,MAAM,mBAAmB,OAAO;AAClC,eAAO;AAAA,MACf,WAAiB,MAAM,mBAAmB,aAAa;AAC/C,eAAO,MAAM,SAAS,OAAO,UAAQ,CAAC,KAAK,YAAY,KAAK,WAAW;AAAA,MAC/E,OAAa;AAEL,eAAO,aAAa;AAAA,UAAO,UACzB,KAAK,YAAY,KAAK,SAAS,SAAS,MAAM,cAAc;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,CAAC,UAAU,CAAC,OAAO;AAC9B,aAAO,MAAM,SAAS;AAAA,QAAK,WACxB,KAAK,QAAQ,MAAM,KAAK,OAAO,OAAO,CAAC,KAAK;AAAA,MACrD,KAAW;AAAA,IACN;AAAA,EACF;AAAA,EAED,SAAS;AAAA,IACP,MAAM,eAAe;AACnBD,oBAAAA,MAAY,MAAA,OAAA,wBAAA,uCAAuC;AACnD,WAAK,YAAY;AAEjB,UAAI;AAEF,cAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAClD,YAAI,cAAc;AAChB,gBAAM,SAAS,KAAK,MAAM,YAAY;AACtC,eAAK,WAAW,OAAO,IAAI,WAAS;AAAA,YAClC,GAAG;AAAA,YACH,IAAI,KAAK,MAAM,KAAK;AAAA,UAChC,EAAY;AAAA,QACH;AAGDA,sBAAAA,MAAA,MAAA,OAAA,wBAAY,4BAA4B;AACxC,cAAM,KAAK,cAAe;AAE1BA,sBAAAA,MAAY,MAAA,OAAA,wBAAA,gDAAgD;AAAA,MAC7D,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,+CAA+C,KAAK;AAAA,MAC1E,UAAgB;AACR,aAAK,YAAY;AAAA,MAClB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,YAAM,YAAY;AAGlB,UAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AAClC,eAAO,EAAE,SAAS,OAAO,QAAQ,mBAAoB;AAAA,MACtD;AAEDA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,0BAA0B;AAEtC,UAAI;AACF,aAAK,oBAAoB,SAAS;AAClC,aAAK,kBAAkB,EAAE,WAAW,KAAI,CAAE;AAC1C,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,YAAY;AAAA,UAC1C,MAAM;AAAA,UACN,UAAU;AAAA,UACV,kBAAkB;AAAA,QAC5B,CAAS;AAED,YAAI,OAAO,YAAY,GAAG;AACxB,gBAAM,cAAc,OAAO,QAAQ,CAAE;AACrCF,wBAAY,MAAA,MAAA,OAAA,wBAAA,sBAAsB,YAAY,QAAQ,KAAK;AAG3D,gBAAM,aAAa,KAAK,eAAe,KAAK,UAAU,WAAW;AAGjE,eAAK,WAAW,WAAW,IAAI,WAAS;AAAA,YACtC,GAAG;AAAA,YACH,IAAI,KAAK,OAAO,KAAK;AAAA;AAAA,UACjC,EAAY;AAGFA,wBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC5D,eAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAC5CA,wBAAAA,MAAI,eAAe,wBAAwB,KAAK,YAAY;AAE5DA,8BAAA,MAAA,OAAA,wBAAY,2BAA2B,KAAK,SAAS,QAAQ,KAAK;AAGlE,eAAK,kBAAkB;AAAA,YACrB,gBAAgB;AAAA,YAChB,YAAY;AAAA,YACZ,WAAW;AAAA,UACvB,CAAW;AAED,iBAAO,EAAE,SAAS,MAAM,OAAO,YAAY,OAAQ;AAAA,QAC7D,OAAe;AACL,gBAAM,WAAW,OAAO,UAAU;AAClCA,wBAAAA,MAAc,MAAA,SAAA,wBAAA,sBAAsB,QAAQ;AAG5C,eAAK,kBAAkB;AAAA,YACrB,gBAAgB;AAAA,YAChB,YAAY,KAAK,WAAW,aAAa;AAAA,YACzC,WAAW;AAAA,UACvB,CAAW;AAED,gBAAM,IAAI,MAAM,QAAQ;AAAA,QACzB;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wBAAA,qBAAqB,KAAK;AAGxC,aAAK,kBAAkB;AAAA,UACrB,gBAAgB;AAAA,UAChB,YAAY,KAAK,WAAW,aAAa;AAAA,UACzC,WAAW,MAAM;AAAA,QAC3B,CAAS;AAED,cAAM;AAAA,MACd,UAAgB;AACR,aAAK,kBAAkB,EAAE,WAAW,MAAK,CAAE;AAC3C,aAAK,kBAAkB,SAAS;AAAA,MACjC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,WAAW,aAAa;AAC5BA,oBAAAA,2CAAY,qBAAqB,WAAW;AAE5C,UAAI;AAEF,YAAI,CAAC,eAAgB,CAAC,YAAY,OAAO,CAAC,YAAY,IAAK;AACzD,gBAAM,IAAI,MAAM,eAAe;AAAA,QAChC;AAED,cAAM,SAAS,YAAY,OAAO,YAAY;AAG9C,cAAM,aAAa;AAAA,UACjB,GAAG;AAAA,UACH,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,QACrC;AAGD,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,WAAW,QAAQ,UAAU;AAE7D,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,UAAU,EAAE,OAAO,MAAM;AAC9E,cAAI,UAAU,IAAI;AAEhB,kBAAM,aAAa;AAAA,cACjB,GAAG,KAAK,SAAS,KAAK;AAAA,cACtB,GAAG;AAAA,cACH,IAAI;AAAA,cACJ,KAAK;AAAA,YACN;AACD,iBAAK,SAAS,KAAK,IAAI;AACvBF,0BAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAEDA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,kBAAkB;AAAA,QACxC,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,uBAAuB,KAAK;AAC1C,qBAAa,UAAU,OAAO,MAAM;AACpC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,WAAW,IAAI;AACnBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,qBAAqB,EAAE;AAEnC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,WAAW,EAAE;AAE7C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AACtE,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,OAAO,OAAO,CAAC;AAC7BF,0BAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAEDA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,kBAAkB;AAAA,QACxC,OAAe;AACLA,wBAAA,MAAA,MAAA,SAAA,wBAAc,qBAAqB,OAAO,MAAM;AAChD,uBAAa,UAAU,EAAE,SAAS,OAAO,OAAQ,GAAE,MAAM;AAAA,QAC1D;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wBAAA,qBAAqB,KAAK;AACxC,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,aAAa,aAAa;AACvCA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,4BAA4B;AACxCA,+DAAY,qBAAqB,YAAY,QAAQ,KAAK;AAC1DA,+DAAY,qBAAqB,YAAY,QAAQ,KAAK;AAE1D,YAAM,YAAY,oBAAI,IAAK;AAG3B,kBAAY,QAAQ,eAAa;AAC/B,kBAAU,IAAI,UAAU,KAAK;AAAA,UAC3B,GAAG;AAAA,UACH,SAAS;AAAA,QACnB,CAAS;AAAA,MACT,CAAO;AAGD,kBAAY,QAAQ,eAAa;AAC/B,cAAM,KAAK,UAAU,OAAO,UAAU;AACtC,cAAM,eAAe,UAAU,IAAI,EAAE;AAErC,YAAI,CAAC,cAAc;AAEjB,oBAAU,IAAI,IAAI;AAAA,YAChB,GAAG;AAAA,YACH,KAAK;AAAA,YACL,SAAS;AAAA,UACrB,CAAW;AAAA,QACX,OAAe;AAEL,gBAAM,YAAY,IAAI,KAAK,UAAU,aAAa,UAAU,aAAa,CAAC,EAAE,QAAS;AACrF,gBAAM,YAAY,IAAI,KAAK,aAAa,aAAa,aAAa,aAAa,CAAC,EAAE,QAAS;AAE3F,cAAI,YAAY,WAAW;AAEzB,sBAAU,IAAI,IAAI;AAAA,cAChB,GAAG;AAAA,cACH,KAAK;AAAA,cACL,SAAS;AAAA,YACvB,CAAa;AAAA,UACF;AAAA,QAEF;AAAA,MACT,CAAO;AAED,YAAM,cAAc,MAAM,KAAK,UAAU,OAAM,CAAE;AACjDA,oBAAY,MAAA,MAAA,OAAA,wBAAA,0BAA0B,YAAY,QAAQ,KAAK;AAE/D,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,cAAc,WAAW;AACvB,YAAM,MAAM,KAAK,IAAK;AAGtB,UAAI,KAAK,gBAAgB,IAAI,SAAS,GAAG;AACvCA,iEAAY,oBAAoB,SAAS,QAAQ;AACjD,eAAO;AAAA,MACR;AAGD,UAAI,MAAM,KAAK,gBAAgB,KAAM;AACnCA,sBAAAA,MAAY,MAAA,OAAA,wBAAA,yBAAyB;AACrC,eAAO;AAAA,MACR;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,oBAAoB,WAAW;AAC7B,WAAK,gBAAgB,IAAI,SAAS;AAClC,WAAK,gBAAgB,KAAK,IAAK;AAC/BA,oBAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB,SAAS,EAAE;AAAA,IAC/C;AAAA;AAAA,IAGD,kBAAkB,WAAW;AAC3B,WAAK,gBAAgB,OAAO,SAAS;AACrCA,oBAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB,SAAS,EAAE;AAAA,IAC/C;AAAA;AAAA,IAGD,kBAAkB,SAAS;AACzB,aAAO,OAAO,KAAK,YAAY,OAAO;AAAA,IACvC;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACvB,WAAK,iBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB;AACnC,YAAM,KAAK,cAAe;AAAA,IAC3B;AAAA;AAAA,IAGD,MAAM,WAAW,SAAS,OAAO;AAC/B,UAAI,CAAC,QAAQ;AACXA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAQ,CAAE;AAAA,MACpC;AAED,UAAI;AACF,cAAM,KAAK,cAAe;AAE1B,YAAI,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAa;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACd,YAAI,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAa;AAAA,QAClB;AACD,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,YAAY;AAChBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,oCAAoC;AAEhD,UAAI;AAEF,cAAM,cAAc,MAAMA,cAAG,MAAC,eAAgB;AAC9C,YAAI,YAAY,gBAAgB,QAAQ;AACtCA,wBAAAA,MAAA,MAAA,QAAA,wBAAa,sDAAsD;AACnE,iBAAO,EAAE,YAAY,OAAO,cAAc,GAAG,QAAQ,UAAW;AAAA,QACjE;AAGD,cAAM,SAAS,MAAM,KAAK,cAAe;AAEzC,YAAI,UAAU,OAAO,SAAS;AAC5B,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,cAAc,OAAO,SAAS;AAAA,YAC9B,QAAQ;AAAA,UACT;AAAA,QACX,OAAe;AACL,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,QAAQ;AAAA,UACT;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,6CAAc,kCAAkC,KAAK;AACrD,eAAO;AAAA,UACL,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,OAAO,MAAM;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,aAAa,IAAI;AACrBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,qBAAqB,EAAE;AAEnC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,aAAa,EAAE;AAE/C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,cAAI,MAAM;AACR,iBAAK,cAAc;AACnB,iBAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAC5CF,0BAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACX,OAAe;AACL,uBAAa,UAAU,EAAE,SAAS,OAAO,OAAQ,GAAE,MAAM;AAAA,QAC1D;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,uBAAuB,KAAK;AAC1C,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,YAAY,IAAI;AACpBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,qBAAqB,EAAE;AAEnC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,YAAY,EAAE;AAE9C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,cAAI,MAAM;AACR,iBAAK,cAAc;AACnB,iBAAK,eAAe;AACpBF,0BAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACX,OAAe;AACL,uBAAa,UAAU,EAAE,SAAS,OAAO,OAAQ,GAAE,MAAM;AAAA,QAC1D;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,uBAAuB,KAAK;AAC1C,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB;AAGnC,WAAK,WAAW,CAAE;AAClB,WAAK,iBAAiB;AACtB,WAAK,eAAe;AACpB,WAAK,YAAY;AAGjBA,oBAAG,MAAC,kBAAkB,UAAU;AAChCA,oBAAG,MAAC,kBAAkB,kBAAkB;AAExCA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,sBAAsB;AAAA,IACnC;AAAA;AAAA,IAGD,MAAM,YAAY;AAChBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,sBAAsB;AAGlC,WAAK,eAAgB;AAGrB,YAAM,KAAK,cAAe;AAE1BA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,qBAAqB;AAAA,IAClC;AAAA;AAAA,IAGD,gBAAgB;AACdA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB;AAGnC,WAAK,WAAW,CAAE;AAGlB,WAAK,iBAAiB;AAEtB,WAAK,eAAe;AACpB,WAAK,YAAY;AAGjBA,oBAAG,MAAC,kBAAkB,UAAU;AAChCA,oBAAG,MAAC,kBAAkB,kBAAkB;AAExCA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,sBAAsB;AAAA,IACnC;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB;AAEnC,UAAI;AACF,cAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAClD,YAAI,cAAc;AAChB,gBAAM,SAAS,KAAK,MAAM,YAAY;AACtC,eAAK,WAAW,OAAO,IAAI,WAAS;AAAA,YAClC,GAAG;AAAA,YACH,IAAI,KAAK,MAAM,KAAK;AAAA,UAChC,EAAY;AACFA,8BAAA,MAAA,OAAA,wBAAY,yBAAyB,KAAK,SAAS,QAAQ,KAAK;AAAA,QACjE;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,yBAAyB,KAAK;AAAA,MAC7C;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,YAAY;AAC1BA,oBAAAA,2CAAY,uBAAuB,UAAU;AAG7C,YAAM,kBAAkB,CAAE;AAC1B,iBAAW,QAAQ,QAAM;AACvB,cAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,YAAI,MAAM;AACR,0BAAgB,KAAK,IAAI;AAAA,QAC1B;AAAA,MACT,CAAO;AAGD,WAAK,SAAS,QAAQ,UAAQ;AAC5B,YAAI,CAAC,WAAW,SAAS,KAAK,GAAG,KAAK,CAAC,WAAW,SAAS,KAAK,EAAE,GAAG;AACnE,0BAAgB,KAAK,IAAI;AAAA,QAC1B;AAAA,MACT,CAAO;AAED,WAAK,WAAW;AAChBA,oBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAE5DA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,qBAAqB;AAAA,IAClC;AAAA;AAAA,IAGD,eAAe,aAAa;AAC1BA,oBAAY,MAAA,MAAA,OAAA,wBAAA,yBAAyB,YAAY,QAAQ,KAAK;AAE9D,WAAK,WAAW,YAAY,IAAI,WAAS;AAAA,QACvC,GAAG;AAAA,QACH,IAAI,KAAK,MAAM,KAAK;AAAA,MAC5B,EAAQ;AAEFA,oBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,IAC7D;AAAA;AAAA,IAGD,MAAM,QAAQ,UAAU;AACtBA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,qBAAqB,QAAQ;AAEzC,UAAI;AAEF,cAAMG,oBAAmB;AAAA,UACvB,GAAG;AAAA,UACH,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,UACpC,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,UACpC,aAAa;AAAA,UACb,QAAQ;AAAA;AAAA,UACR,YAAY,SAAS,cAAc;AAAA,UACnC,UAAU,SAAS,YAAY,CAAC,KAAK;AAAA,UACrC,OAAO,KAAK,SAAS,SAAS;AAAA,QAC/B;AAGD,YAAI,CAAC,KAAK,UAAU;AAClBH,wBAAAA,MAAA,MAAA,OAAA,wBAAY,eAAe;AAC3B,iBAAO,KAAK,gBAAgBG,iBAAgB;AAAA,QAC7C;AAGD,cAAM,aAAaD,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,WAAWC,iBAAgB;AAE3D,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,aAAa;AAAA,YACjB,GAAG,OAAO;AAAA,YACV,IAAI,OAAO,KAAK;AAAA;AAAA,UACjB;AACD,eAAK,SAAS,KAAK,UAAU;AAC7BH,wBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAE5DA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAED,iBAAO;AAAA,QACjB,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,WAAW,KAAK;AAG9B,YAAI,aAAa,eAAe,KAAK,GAAG;AACtC,eAAK,WAAW;AAChB,iBAAO,KAAK,gBAAgB,gBAAgB;AAAA,QAC7C;AAGD,YAAI,aAAa,uBAAuB,KAAK,GAAG;AAC9C,iBAAO,KAAK,gBAAgB,gBAAgB;AAAA,QAC7C;AAGD,qBAAa,UAAU,OAAO,QAAQ;AACtC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,UAAU;AACxBA,oBAAY,MAAA,MAAA,OAAA,wBAAA,yBAAyB,SAAS,KAAK;AAGnD,YAAM,SAAS,UAAU,KAAK,IAAG,IAAK,MAAM,KAAK,OAAQ,EAAC,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC;AAElF,YAAM,OAAO;AAAA,QACX,KAAK;AAAA,QACL,IAAI;AAAA,QACJ,GAAG;AAAA,QACH,WAAW;AAAA;AAAA,QACX,UAAU;AAAA;AAAA,MACX;AAED,WAAK,SAAS,KAAK,IAAI;AACvBA,oBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAG5DA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,WAAW,oBAAoB;AAAA,QAC3C,MAAM;AAAA,QACN,UAAU;AAAA,MAClB,CAAO;AAED,aAAO;AAAA,IACR;AAAA,EACF;AACH,CAAC;;"}
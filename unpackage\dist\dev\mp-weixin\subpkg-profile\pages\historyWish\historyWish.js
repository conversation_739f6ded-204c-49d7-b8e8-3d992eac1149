"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_wish = require("../../../store/wish.js");
const store_user = require("../../../store/user.js");
const HistoryWishCard = () => "../../../components/HistoryWishCard/HistoryWishCard.js";
const _sfc_main = {
  components: {
    HistoryWishCard
  },
  setup() {
    const wishStore = store_wish.useWishStore();
    const userStore = store_user.useUserStore();
    const activeCardId = common_vendor.ref("");
    const completedWishes = common_vendor.computed(() => {
      return wishStore.wishList.filter((wish) => wish.isCompleted).sort((a, b) => {
        const dateA = new Date(a.completeDate || a.lastCompleteDate || a.updateDate);
        const dateB = new Date(b.completeDate || b.lastCompleteDate || b.updateDate);
        return dateB - dateA;
      });
    });
    const handleCardOpen = (cardId) => {
      common_vendor.index.__f__("log", "at subpkg-profile/pages/historyWish/historyWish.vue:78", "卡片打开:", cardId);
      activeCardId.value = cardId;
    };
    const handleCardClose = () => {
      common_vendor.index.__f__("log", "at subpkg-profile/pages/historyWish/historyWish.vue:84", "卡片关闭");
      activeCardId.value = "";
    };
    const handleCardSwipeStart = (cardId) => {
      common_vendor.index.__f__("log", "at subpkg-profile/pages/historyWish/historyWish.vue:90", "卡片开始滑动，强制关闭其他卡片");
      if (activeCardId.value && activeCardId.value !== cardId) {
        common_vendor.index.$emit("force-close-cards", { targetCardId: cardId });
        activeCardId.value = "";
      }
    };
    const handleCardScrollDetected = (data) => {
      common_vendor.index.__f__("log", "at subpkg-profile/pages/historyWish/historyWish.vue:100", "检测到卡片滚动:", data);
    };
    const onListScroll = (e) => {
      common_vendor.index.$emit("page-scroll-event", {
        shouldClose: true,
        scrollTop: e.detail.scrollTop
      });
      activeCardId.value = "";
    };
    const restoreWish = (id) => {
      common_vendor.index.showModal({
        title: "还原心愿",
        content: "确定要将此心愿恢复到待完成状态吗？",
        confirmColor: "#8a2be2",
        success: (res) => {
          if (res.confirm) {
            const result = wishStore.restoreWish(id);
            if (result) {
              common_vendor.index.showToast({
                title: "心愿已还原",
                icon: "success"
              });
            } else {
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const deleteWish = (id) => {
      common_vendor.index.showModal({
        title: "删除心愿",
        content: "确定要永久删除此心愿吗？此操作不可恢复。",
        confirmColor: "#ff4d4f",
        success: (res) => {
          if (res.confirm) {
            wishStore.deleteWish(id);
            common_vendor.index.showToast({
              title: "心愿已删除",
              icon: "success"
            });
          }
        }
      });
    };
    const checkAndSyncWishData = async () => {
      common_vendor.index.__f__("log", "at subpkg-profile/pages/historyWish/historyWish.vue:162", "[HistoryWish] 检查心愿数据同步状态...");
      const hasLocalData = wishStore && wishStore.wishList.length > 0;
      const lastSyncTime = wishStore ? wishStore.lastSyncTime : null;
      const now = Date.now();
      const shouldSync = !hasLocalData || !lastSyncTime || now - lastSyncTime > 5 * 60 * 1e3;
      if (shouldSync) {
        common_vendor.index.__f__(
          "log",
          "at subpkg-profile/pages/historyWish/historyWish.vue:173",
          "[HistoryWish] 需要同步心愿数据，原因:",
          !hasLocalData ? "无本地数据" : !lastSyncTime ? "未记录同步时间" : "超过5分钟未同步"
        );
        await wishStore.syncFromCloud({ silent: true });
      } else {
        common_vendor.index.__f__("log", "at subpkg-profile/pages/historyWish/historyWish.vue:180", "[HistoryWish] 使用本地缓存数据");
      }
    };
    return {
      wishStore,
      completedWishes,
      activeCardId,
      handleCardOpen,
      handleCardClose,
      handleCardSwipeStart,
      handleCardScrollDetected,
      onListScroll,
      restoreWish,
      deleteWish,
      userStore,
      checkAndSyncWishData
    };
  },
  onLoad() {
    if (!this.userStore.isLogin) {
      common_vendor.index.__f__("log", "at subpkg-profile/pages/historyWish/historyWish.vue:203", "[historyWish] 用户未登录，等待导航拦截器处理");
      return;
    }
  },
  // 🔧 页面显示时的处理 - 使用Options API方式
  onShow() {
    if (this.userStore && this.userStore.isLogin) {
      this.checkAndSyncWishData();
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_HistoryWishCard2 = common_vendor.resolveComponent("HistoryWishCard");
  (_easycom_uni_icons2 + _easycom_HistoryWishCard2)();
}
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_HistoryWishCard = () => "../../../components/HistoryWishCard/HistoryWishCard.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_HistoryWishCard)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $setup.completedWishes.length === 0
  }, $setup.completedWishes.length === 0 ? {
    b: common_vendor.p({
      type: "calendar-circle",
      size: "80",
      color: "#d9d9d9"
    }),
    c: common_vendor.p({
      type: "plus-circle",
      size: "16",
      color: "#8a2be2"
    })
  } : {
    d: common_vendor.f($setup.completedWishes, (wish, index, i0) => {
      return {
        a: wish.id || wish._id,
        b: common_vendor.o($setup.restoreWish, wish.id || wish._id),
        c: common_vendor.o($setup.deleteWish, wish.id || wish._id),
        d: common_vendor.o($setup.handleCardOpen, wish.id || wish._id),
        e: common_vendor.o($setup.handleCardClose, wish.id || wish._id),
        f: common_vendor.o($setup.handleCardSwipeStart, wish.id || wish._id),
        g: common_vendor.o($setup.handleCardScrollDetected, wish.id || wish._id),
        h: "1a9ec0d4-2-" + i0,
        i: common_vendor.p({
          wish,
          index,
          activeCardId: $setup.activeCardId
        })
      };
    }),
    e: common_vendor.o((...args) => $setup.onListScroll && $setup.onListScroll(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpkg-profile/pages/historyWish/historyWish.js.map

{"version": 3, "file": "GroupSelector.js", "sources": ["components/GroupSelector/GroupSelector.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQ3lrMTYvRGVza3RvcC93aXNobGlzdC11bmlhcHAvY29tcG9uZW50cy9Hcm91cFNlbGVjdG9yL0dyb3VwU2VsZWN0b3IudnVl"], "sourcesContent": ["<template>\n\t<view class=\"group-selector\">\n\t\t<!-- 分享按钮固定在右侧 - 改为button元素并添加open-type=\"share\" -->\n\t\t<button \n\t\t\tclass=\"share-group-btn\" \n\t\t\topen-type=\"share\"\n\t\t\tshare-type=\"1\"\n\t\t\t@click=\"handleShareClick\" \n\t\t\t@longpress=\"onShareButtonLongPress\"\n\t\t\t:class=\"{'active': isShareButtonActive, 'show-tooltip': showTooltip}\"\n\t\t\t:data-group-id=\"currentGroupId\"\n\t\t\t:data-group-name=\"currentGroup ? currentGroup.name : ''\"\n\t\t\t:data-share-type=\"'group'\"\n\t\t>\n\t\t\t<image src=\"/static/tabbar/share.png\" class=\"share-icon\"></image>\n\t\t\t<view class=\"share-btn-ripple\"></view>\n\t\t\t<view class=\"share-tooltip\" v-if=\"showTooltip\">\n\t\t\t\t分享此分组及心愿\n\t\t\t</view>\n\t\t</button>\n\t\t\n\n\t\t\n\n\t\t\n\t\t<!-- 确保滚动区域不与分享按钮重叠 -->\n\t\t<scroll-view class=\"group-scroll-view\" scroll-x=\"true\" show-scrollbar=\"false\">\n\t\t\t<view class=\"group-list\">\n\t\t\t\t<view \n\t\t\t\t\tv-for=\"(group, index) in groups\" \n\t\t\t\t\t:key=\"group.id\"\n\t\t\t\t\tclass=\"group-item\"\n\t\t\t\t\t:class=\"{ \n\t\t\t\t\t\tactive: currentGroupId === group.id, \n\t\t\t\t\t\t'is-default': group.isDefault \n\t\t\t\t\t}\"\n\t\t\t\t\t@click=\"selectGroup(group.id)\"\n\t\t\t\t\t@longpress=\"handleGroupLongPress(group)\"\n\t\t\t\t\t:data-id=\"group.id\"\n\t\t\t\t>\n\t\t\t\t\t<text class=\"group-text\">{{ group.name }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"group-item add-group\" @click=\"showAddGroupModal\">\n\t\t\t\t\t<view class=\"add-icon-wrapper\">\n\t\t\t\t\t\t<text class=\"plus-icon\">+</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 添加占位元素，防止最右侧标签被分享按钮遮挡 -->\n\t\t\t\t<view class=\"spacing-placeholder\"></view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\n\timport { useGroupStore } from '@/store/group.js'\n\timport { useWishStore } from '@/store/wish.js'\n\timport { useUserStore } from '@/store/user.js'\n\timport groupTagOperations from '@/mixins/groupTagOperations.js'\n\timport { markRaw, nextTick } from 'vue'\n\t\n\texport default {\n\t\tname: 'GroupSelector',\n\t\tmixins: [groupTagOperations],\n\t\tsetup() {\n\t\t\tconst groupStore = useGroupStore()\n\t\t\tconst wishStore = useWishStore()\n\t\t\tconst userStore = useUserStore()\n\t\t\t\n\t\t\treturn {\n\t\t\t\tgroupStore,\n\t\t\t\twishStore,\n\t\t\t\tuserStore\n\t\t\t}\n\t\t},\n\t\t// 添加生命周期钩子\n\t\tcreated() {\n\t\t\t// 确保分组数据被加载\n\t\t\tif (this.groupStore && !this.groupStore.getAllGroups.length) {\n\t\t\t\tthis.groupStore.initGroups()\n\t\t\t}\n\t\t\t\n\t\t\t// 监听页面显示事件，用于处理登录返回后的状态恢复\n\t\t\tuni.$on('page-show', this.onPageShow);\n\t\t\t\n\t\t\t// 监听登录成功事件，用于立即处理待处理的分组创建\n\t\t\tuni.$on('login-success', () => {\n\t\t\t\tconsole.log('接收到登录成功事件');\n\t\t\t\t// 只有在登录中状态时才处理\n\t\t\t\tif (this.isLoggingIn) {\n\t\t\t\t\t// 标记登录成功，但不立即处理\n\t\t\t\t\t// 等待页面重新显示时再处理\n\t\t\t\t\tthis.isLoggingIn = false;\n\t\t\t\t\t\n\t\t\t\t\t// 避免页面显示事件没有触发的情况，\n\t\t\t\t\t// 设置一个足够长的延迟后检查待处理内容，确保不与页面显示事件冲突\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tconst pendingName = uni.getStorageSync('pending_group_name');\n\t\t\t\t\t\tif (pendingName && !this.isProcessingPendingName) {\n\t\t\t\t\t\t\tconsole.log('登录成功延迟检查: 发现待处理分组', pendingName);\n\t\t\t\t\t\t\tthis.checkPendingGroupName();\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 1000); // 延长延迟时间，确保不与页面显示事件重叠\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tasync mounted() {\n\t\t\tconsole.log('[GroupSelector] Component mounted, checking group data...');\n\t\t\t\n\t\t\t// 二次确认分组数据被正确加载\n\t\t\tif (this.groupStore && !this.groupStore.getAllGroups.length) {\n\t\t\t\tconsole.log(\"[GroupSelector] Groups empty, initializing in mounted hook\");\n\t\t\t\ttry {\n\t\t\t\t\tawait this.groupStore.initGroups();\n\t\t\t\t\tconsole.log(\"[GroupSelector] Groups initialized successfully\");\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"[GroupSelector] Group initialization failed:\", error);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否有未完成的分组创建（从登录页面返回）\n\t\t\tthis.checkPendingGroupName();\n\t\t},\n\t\t// 组件销毁时清理事件监听\n\t\tbeforeDestroy() {\n\t\t\tuni.$off('page-show', this.onPageShow);\n\t\t\t// 移除登录成功事件监听\n\t\t\tuni.$off('login-success');\n\t\t},\n\t\tcomputed: {\n\t\t\tgroups() {\n\t\t\t\t// 使用 shallowRef 或者只监听分组数据的变化，不受心愿数据影响\n\t\t\t\tconst allGroups = this.groupStore.getAllGroups;\n\t\t\t\t\n\t\t\t\t// 生成更可靠的版本标识，包含分组数量和分组ID组合\n\t\t\t\tconst groupIds = allGroups.map(g => g.id || g._id).join(',');\n\t\t\t\tconst currentVersion = `${allGroups.length}_${groupIds}`;\n\t\t\t\t\n\t\t\t\t// 检查是否需要重新计算\n\t\t\t\tif (this.groupsVersion === currentVersion && this.cachedGroups.length > 0) {\n\t\t\t\t\treturn this.cachedGroups;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 对分组进行排序：默认分组在前，用户分组在后\n\t\t\t\tconst sortedGroups = [...allGroups].sort((a, b) => {\n\t\t\t\t\t// 全部永远排在最前面\n\t\t\t\t\tif (a.id === 'all') return -1;\n\t\t\t\t\tif (b.id === 'all') return 1;\n\t\t\t\t\t\n\t\t\t\t\t// 默认分组优先于用户分组\n\t\t\t\t\tif (a.isDefault && !b.isDefault) return -1;\n\t\t\t\t\tif (!a.isDefault && b.isDefault) return 1;\n\t\t\t\t\t\n\t\t\t\t\t// 同类型分组按照order属性排序\n\t\t\t\t\treturn (a.order || 0) - (b.order || 0);\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 更新缓存\n\t\t\t\tthis.cachedGroups = sortedGroups;\n\t\t\t\tthis.groupsVersion = currentVersion;\n\t\t\t\t\n\t\t\t\tconsole.log('[GroupSelector] Groups computed, version:', currentVersion);\n\t\t\t\tconsole.log('[GroupSelector] Sorted groups:', sortedGroups.map(g => ({id: g.id, name: g.name, order: g.order})));\n\t\t\t\t\n\t\t\t\treturn sortedGroups;\n\t\t\t},\n\t\t\tcurrentGroupId() {\n\t\t\t\treturn this.wishStore.currentGroupId\n\t\t\t},\n\t\t\t// 获取当前分组对象 - 独立计算，避免不必要的依赖\n\t\t\tcurrentGroup() {\n\t\t\t\treturn this.groupStore.getGroupById(this.currentGroupId)\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 只监听分组store的变化，不监听心愿store的变化\n\t\t\t'groupStore.groups': {\n\t\t\t\thandler(newGroups, oldGroups) {\n\t\t\t\t\tconsole.log('[GroupSelector] Groups changed in store, resetting cache');\n\t\t\t\t\t\n\t\t\t\t\t// 安全处理数组变化\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// 使用nextTick确保DOM更新在合适时机\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t// 重置缓存，触发重新计算\n\t\t\t\t\tthis.groupsVersion = 0;\n\t\t\t\t\tthis.cachedGroups = [];\n\t\t\t\t\t\t\tconsole.log('[GroupSelector] Cache reset completed');\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('[GroupSelector] Error handling groups change:', error);\n\t\t\t\t\t\t// 降级处理\n\t\t\t\t\t\tthis.groupsVersion = 0;\n\t\t\t\t\t\tthis.cachedGroups = [];\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true,\n\t\t\t\timmediate: false\n\t\t\t},\n\t\t\t// 监听当前分组ID变化\n\t\t\tcurrentGroupId: {\n\t\t\t\thandler(newGroupId, oldGroupId) {\n\t\t\t\t\tconsole.log('[GroupSelector] Current group ID changed:', oldGroupId, '->', newGroupId);\n\t\t\t\t},\n\t\t\t\timmediate: false\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tnewGroupName: '',\n\t\t\t\tisShareButtonActive: false,\n\t\t\t\tshowTooltip: false,\n\t\t\t\ttooltipTimer: null,\n\t\t\t\t// 跟踪是否正在登录中\n\t\t\t\tisLoggingIn: false,\n\t\t\t\t// 添加标志，避免重复处理待创建分组\n\t\t\t\tisProcessingPendingName: false,\n\t\t\t\t// 上次处理的分组名称，避免重复处理\n\t\t\t\tlastProcessedGroupName: '',\n\t\t\t\t// 上次处理时间戳\n\t\t\t\tlastProcessTimestamp: 0,\n\t\t\t\t// 缓存分组数据，避免不必要的重新计算\n\t\t\t\tcachedGroups: [],\n\t\t\t\t// 缓存的分组数据版本号\n\t\t\t\tgroupsVersion: 0\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 页面显示时检查状态\n\t\t\tonPageShow() {\n\t\t\t\t// 检查登录状态变化\n\t\t\t\tif (this.isLoggingIn && this.userStore.isLoggedIn) {\n\t\t\t\t\tconsole.log('登录状态变更：未登录 -> 已登录');\n\t\t\t\t\t// 已登录成功，重置登录状态\n\t\t\t\t\tthis.isLoggingIn = false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查是否有待处理的分组创建（不依赖于登录状态）\n\t\t\t\tconst pendingName = uni.getStorageSync('pending_group_name');\n\t\t\t\t// 只有当登录成功且存在待处理分组名称，且当前未处理同一请求时，才处理\n\t\t\t\tif (pendingName && this.userStore.isLoggedIn && !this.isProcessingPendingName) {\n\t\t\t\t\t// 检查是否短时间内重复处理同一分组名\n\t\t\t\t\tconst currentTime = Date.now();\n\t\t\t\t\tconst isSameGroup = pendingName === this.lastProcessedGroupName;\n\t\t\t\t\tconst isRecentProcess = (currentTime - this.lastProcessTimestamp) < 3000; // 3秒内的重复请求\n\n\t\t\t\t\tif (isSameGroup && isRecentProcess) {\n\t\t\t\t\t\t// 直接清除存储数据，避免后续重复处理\n\t\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.checkPendingGroupName();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 检查是否有未完成的分组创建\n\t\t\tcheckPendingGroupName() {\n\t\t\t\ttry {\n\t\t\t\t\t// 如果已在处理中，避免重复处理\n\t\t\t\t\tif (this.isProcessingPendingName) {\n\t\t\t\t\t\tconsole.log('已有待处理分组正在处理中，跳过');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 标记为处理中\n\t\t\t\t\tthis.isProcessingPendingName = true;\n\t\t\t\t\t\n\t\t\t\t\t// 检查存储中是否有暂存的分组名称\n\t\t\t\t\tconst pendingName = uni.getStorageSync('pending_group_name');\n\t\t\t\t\tif (pendingName) {\n\t\t\t\t\t\tconsole.log('检测到未完成的分组创建:', pendingName);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查登录状态\n\t\t\t\t\t\tif (this.userStore.isLoggedIn) {\n\t\t\t\t\t\t\t// 记录处理信息，用于防重复\n\t\t\t\t\t\t\tthis.lastProcessedGroupName = pendingName;\n\t\t\t\t\t\t\tthis.lastProcessTimestamp = Date.now();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 已登录状态，重新显示弹窗并填入之前输入的名称\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '添加分组',\n\t\t\t\t\t\t\t\t\tplaceholderText: '请输入分组名称',\n\t\t\t\t\t\t\t\t\teditable: true,\n\t\t\t\t\t\t\t\t\tcontent: pendingName,\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\t// 无论成功或取消，都清除暂存数据\n\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\tif (res.confirm && res.content) {\n\t\t\t\t\t\t\t\t\t\t\t// 执行添加分组\n\t\t\t\t\t\t\t\t\t\t\tthis.confirmAddGroup(res.content, true);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t// 处理完成，重置状态\n\t\t\t\t\t\t\t\t\t\tthis.isProcessingPendingName = false;\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\t\t\t// 处理失败也清除数据和重置状态\n\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\t\t\t\t\t\tthis.isProcessingPendingName = false;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '添加分组',\n\t\t\t\t\t\t\t\t\tcontent: pendingName,\n\t\t\t\t\t\t\t\t\teditable: true,\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\t// 无论成功或取消，都清除暂存数据\n\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\tif (res.confirm && res.content) {\n\t\t\t\t\t\t\t\t\t\t\t// 执行添加分组\n\t\t\t\t\t\t\t\t\t\t\tthis.confirmAddGroup(res.content, true);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t// 处理完成，重置状态\n\t\t\t\t\t\t\t\t\t\tthis.isProcessingPendingName = false;\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\t\t\t// 处理失败也清除数据和重置状态\n\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\t\t\t\t\t\tthis.isProcessingPendingName = false;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t}, 500); // 延迟显示，确保页面已完全加载\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果未登录，也重置处理状态\n\t\t\t\t\t\t\tthis.isProcessingPendingName = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 没有待处理内容，重置处理状态\n\t\t\t\t\t\tthis.isProcessingPendingName = false;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('检查未完成的分组创建失败:', e);\n\t\t\t\t\t// 出错时也重置处理状态并清除可能的数据\n\t\t\t\t\tthis.isProcessingPendingName = false;\n\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 处理分享按钮点击\n\t\t\thandleShareClick() {\n\t\t\t\t// 记录下来当前分享的分组信息，给父组件使用\n\t\t\t\tthis.prepareShareData();\n\t\t\t\t\n\t\t\t\t// 添加按钮动画效果\n\t\t\t\tthis.animateShareButton();\n\t\t\t\t\n\t\t\t\t// 触发振动反馈\n\t\t\t\ttry {\n\t\t\t\t\tuni.vibrateShort({\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tconsole.log('振动反馈成功');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('振动触发失败:', e);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示分享菜单（针对非微信小程序环境）\n\t\t\t\tif (uni.getPlatform && uni.getPlatform() !== 'mp-weixin') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.showShareMenu({\n\t\t\t\t\t\t\twithShareTicket: true,\n\t\t\t\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline'],\n\t\t\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t\t\tconsole.log('显示分享菜单成功');\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail(e) {\n\t\t\t\t\t\t\t\tconsole.log('显示分享菜单失败', e);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tconsole.error('分享操作失败:', err);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 准备分享数据\n\t\t\tprepareShareData() {\n\t\t\t\tconst currentGroup = this.currentGroup;\n\t\t\t\tif (!currentGroup) return;\n\t\t\t\t\n\t\t\t\t// 获取当前分组下的所有心愿\n\t\t\t\tconst groupWishes = this.wishStore.currentGroupWishes;\n\t\t\t\tconst wishCount = groupWishes.length;\n\t\t\t\t\n\t\t\t\tconst shareData = {\n\t\t\t\t\tgroupId: currentGroup.id,\n\t\t\t\t\tgroupName: currentGroup.name,\n\t\t\t\t\twishCount: wishCount,\n\t\t\t\t\ttimestamp: Date.now()\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 设置全局数据以便在页面的onShareAppMessage中获取\n\t\t\t\tuni.setStorageSync('current_share_group', shareData);\n\t\t\t\t\n\t\t\t\t// 也通过事件通知\n\t\t\t\tuni.$emit('share-group-trigger', {\n\t\t\t\t\tgroupId: currentGroup.id,\n\t\t\t\t\tgroupName: currentGroup.name,\n\t\t\t\t\twishCount: wishCount,\n\t\t\t\t\tshareType: 'group'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconsole.log('分享数据已准备:', {\n\t\t\t\t\tgroupId: currentGroup.id,\n\t\t\t\t\tgroupName: currentGroup.name,\n\t\t\t\t\tshareType: 'group'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 试图触发小程序的分享\n\t\t\t\ttry {\n\t\t\t\t\tif (uni.canIUse && uni.canIUse('button.open-type.share')) {\n\t\t\t\t\t\tconsole.log('当前环境支持 open-type=\"share\"');\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('检查分享能力失败:', e);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 显示分享提示\n\t\t\tshowShareTooltip() {\n\t\t\t\t// 触发振动反馈\n\t\t\t\ttry {\n\t\t\t\t\tuni.vibrateShort();\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('振动触发失败:', e);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示提示\n\t\t\t\tthis.showTooltip = true;\n\t\t\t\t\n\t\t\t\t// 设置定时器关闭提示\n\t\t\t\tif (this.tooltipTimer) {\n\t\t\t\t\tclearTimeout(this.tooltipTimer);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.tooltipTimer = setTimeout(() => {\n\t\t\t\t\tthis.showTooltip = false;\n\t\t\t\t}, 2000);\n\t\t\t},\n\t\t\t\n\t\t\t// 选择分组\n\t\t\tselectGroup(groupId) {\n\t\t\t\tthis.wishStore.setCurrentGroup(groupId)\n\t\t\t\tthis.$emit('group-change', groupId)\n\t\t\t},\n\t\t\t\n\t\t\t// 显示添加分组弹窗\n\t\t\tshowAddGroupModal() {\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '添加分组',\n\t\t\t\t\tplaceholderText: '请输入分组名称',\n\t\t\t\t\teditable: true,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm && res.content) {\n\t\t\t\t\t\t\t// 用户点击确定，并且输入了内容\n\t\t\t\t\t\t\t// 保存输入的分组名称\n\t\t\t\t\t\t\tuni.setStorageSync('pending_group_name', res.content);\n\t\t\t\t\t\t\tthis.confirmAddGroup(res.content)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\t// 其他平台使用普通输入框\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '添加分组',\n\t\t\t\t\tcontent: '',\n\t\t\t\t\tshowCancel: true,\n\t\t\t\t\teditable: true,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm && res.content) {\n\t\t\t\t\t\t\t// 保存输入的分组名称\n\t\t\t\t\t\t\tuni.setStorageSync('pending_group_name', res.content);\n\t\t\t\t\t\t\tthis.confirmAddGroup(res.content)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t\n\t\t\t// 确认添加分组\n\t\t\tasync confirmAddGroup(value, skipStorageSave = false) {\n\t\t\t\tconst name = value.trim()\n\t\t\t\tif (!name) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '分组名称不能为空',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\t// 如果名称为空，也应该清除暂存\n\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查用户是否已登录\n\t\t\t\tif (!this.userStore.isLoggedIn) {\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页面');\n\t\t\t\t\t\n\t\t\t\t\t// 如果没有跳过存储保存，且我们需要重定向到登录\n\t\t\t\t\tif (!skipStorageSave) {\n\t\t\t\t\t\t// 已经在showAddGroupModal中保存了名称，但为了安全再次保存\n\t\t\t\t\t\tuni.setStorageSync('pending_group_name', name);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 设置登录中状态\n\t\t\t\t\t\tthis.isLoggingIn = true;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 重定向到登录页面\n\t\t\t\t\t\tthis.userStore.checkLoginAndRedirect();\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 检查是否已存在同名分组\n\t\t\t\tif (this.groupStore._checkGroupNameExists && this.groupStore._checkGroupNameExists(name)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '分组名称已存在',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\t// 名称已存在，清除暂存\n\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('[GroupSelector] Starting to add group:', name);\n\t\t\t\t\t\n\t\t\t\t\t// 添加分组（异步操作）\n\t\t\t\t\tconst groupId = await this.groupStore.addGroup(name)\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('[GroupSelector] addGroup returned:', groupId);\n\t\t\t\t\t\n\t\t\t\t\tif (groupId) {\n\t\t\t\t\t\t// 立即清除临时保存的分组名称\n\t\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('[GroupSelector] Before setting current group - wishStore.currentGroupId:', this.wishStore.currentGroupId);\n\t\t\t\t\t\tconsole.log('[GroupSelector] Available groups:', this.groupStore.getAllGroups.map(g => ({id: g.id, name: g.name})));\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 立即切换到新分组\n\t\t\t\t\t\tthis.wishStore.setCurrentGroup(groupId)\n\t\t\t\t\t\tthis.$emit('group-change', groupId)\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('[GroupSelector] After setting current group - wishStore.currentGroupId:', this.wishStore.currentGroupId);\n\t\t\t\t\t\tconsole.log('[GroupSelector] Computed currentGroupId:', this.currentGroupId);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 强制刷新分组缓存和组件\n\t\t\t\t\t\tthis.groupsVersion = 0;\n\t\t\t\t\t\tthis.cachedGroups = [];\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 再次检查状态\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tconsole.log('[GroupSelector] After $nextTick - currentGroupId:', this.currentGroupId);\n\t\t\t\t\t\t\tconsole.log('[GroupSelector] Groups in computed:', this.groups.map(g => ({id: g.id, name: g.name, active: this.currentGroupId === g.id})));\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '创建成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 创建失败，也清除暂存\n\t\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\t\t\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '创建失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// 发生异常，也清除暂存\n\t\t\t\t\tuni.removeStorageSync('pending_group_name');\n\t\t\t\t\t\n\t\t\t\t\tconsole.error('添加分组失败:', e)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '创建失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 无论成功与否，都重置处理状态\n\t\t\t\tthis.isProcessingPendingName = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 按钮动画效果\n\t\t\tanimateShareButton() {\n\t\t\t\tthis.isShareButtonActive = true;\n\t\t\t\t\n\t\t\t\t// 设置定时器移除动画类\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.isShareButtonActive = false;\n\t\t\t\t}, 600); // 与动画时长匹配\n\t\t\t\t\n\t\t\t\t// 成功反馈\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已准备分享',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch(e) {\n\t\t\t\t\t\tconsole.error('显示成功提示失败:', e);\n\t\t\t\t\t}\n\t\t\t\t}, 200);\n\t\t\t},\n\t\t\t\n\t\t\t// 调试：手动触发数据修复（长按分享按钮5秒）\n\t\t\tonShareButtonLongPress() {\n\t\t\t\tconsole.log('[GroupSelector] Long press detected on share button');\n\t\t\t\t\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '调试功能',\n\t\t\t\t\tcontent: '是否要修复分组数据？这会清理重复分组并修复排序。',\n\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tconst result = await this.groupStore.repairGroupData();\n\t\t\t\t\t\t\t\tconsole.log('[GroupSelector] Group data repair result:', result);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 强制刷新当前页面的数据\n\t\t\t\t\t\t\t\tthis.groupsVersion = 0; // 重置缓存版本\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '数据修复完成',\n\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\tconsole.error('[GroupSelector] Group data repair failed:', error);\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '修复失败',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 处理分组长按事件（带调试信息）\n\t\t\thandleGroupLongPress(group) {\n\t\t\t\tconsole.log('[GroupSelector] Group long press triggered');\n\t\t\t\tconsole.log('[GroupSelector] Group object:', JSON.stringify(group));\n\t\t\t\t\n\t\t\t\t// 验证分组对象\n\t\t\t\tif (!group) {\n\t\t\t\t\tconsole.error('[GroupSelector] 分组对象为空');\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '分组数据错误',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保分组有 id 字段（优先使用 id，其次使用 _id）\n\t\t\t\tconst groupId = group.id || group._id;\n\t\t\t\t\n\t\t\t\tconsole.log('[GroupSelector] Group id:', groupId);\n\t\t\t\tconsole.log('[GroupSelector] Group name:', group?.name);\n\t\t\t\tconsole.log('[GroupSelector] Group isDefault:', group?.isDefault);\n\t\t\t\t\n\t\t\t\tif (!groupId) {\n\t\t\t\t\tconsole.error('[GroupSelector] 分组ID为空, group:', group);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '分组ID缺失',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 创建带有正确 id 的分组对象\n\t\t\t\tconst normalizedGroup = {\n\t\t\t\t\t...group,\n\t\t\t\t\tid: groupId\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 调用 mixin 的方法\n\t\t\t\tthis.showGroupActionSheet(normalizedGroup);\n\t\t\t},\n\t\t\t\n\n\t\t\t\n\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.group-selector {\n\t\tpadding: 20rpx 0;\n\t\tbackground-color: #fff;\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tz-index: 100;\n\t\tposition: relative; /* 为分享按钮绝对定位提供参考 */\n\t\t\n\t\t.group-scroll-view {\n\t\t\twidth: 100%;\n\t\t\twhite-space: nowrap;\n\t\t\tpadding-right: 54rpx; /* 为固定在右侧的分享按钮预留空间 */\n\t\t}\n\t\t\n\t\t.group-list {\n\t\t\tpadding: 0 20rpx;\n\t\t\tdisplay: inline-flex;\n\t\t\t\n\t\t\t.group-item {\n\t\t\t\theight: 56rpx;\n\t\t\t\tpadding: 0 22rpx;\n\t\t\t\tmargin-right: 8rpx;\n\t\t\t\tbackground-color: #f0f0f0;\n\t\t\t\tborder-radius: 28rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\ttext-align: center;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\tline-height: 1; /* 确保文字垂直居中 */\n\t\t\t\t\n\t\t\t\t.group-text {\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\tline-height: 1;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.is-default {\n\t\t\t\t\tbackground-color: rgba(138, 43, 226, 0.1);\n\t\t\t\t\tborder: 1px solid rgba(138, 43, 226, 0.2);\n\t\t\t\t\tcolor: #8a2be2;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.active {\n\t\t\t\t\tbackground-color: #8a2be2;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.add-group {\n\t\t\t\t\tbackground-color: #f0e6ff;\n\t\t\t\t\tborder: 1px dashed #8a2be2;\n\t\t\t\t\tcolor: #8a2be2;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\t\n\t\t\t\t\t.add-icon-wrapper {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.plus-icon {\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\tcolor: #8a2be2;\n\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t\tline-height: 40rpx;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tpadding-bottom: 4rpx; /* 微调加号位置 */\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t/* 添加占位元素样式 */\n\t\t\t.spacing-placeholder {\n\t\t\t\twidth: 54rpx; /* 与右侧预留空间一致 */\n\t\t\t\theight: 1rpx;\n\t\t\t\tdisplay: inline-block;\n\t\t\t}\n\t\t}\n\t\t\n\n\t\t\n\n\t\t\n\t\t/* 分享按钮样式 - 固定在右侧 */\n\t\t.share-group-btn {\n\t\t\twidth: 54rpx;\n\t\t\tmin-width: 50rpx; /* 确保最小宽度 */\n\t\t\theight: 70rpx;\n\t\t\tborder-radius: 50% 0 0 50%; /* 左侧保持圆形，右侧平直 */\n\t\t\tbackground-color: #8a2be2;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tpadding-left: 8rpx; /* 减小左边距 */\n\t\t\tpadding-right: 0;\n\t\t\tmargin: 0;\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.3);\n\t\t\tposition: absolute;\n\t\t\ttop: 50%;\n\t\t\tright: 0; /* 紧贴右边框 */\n\t\t\ttransform: translateY(-50%);\n\t\t\tz-index: 101; /* 确保在滚动内容之上 */\n\t\t\ttransition: all 0.2s ease;\n\t\t\toverflow: hidden; /* 用于容纳波纹效果 */\n\t\t\tline-height: 1; /* 重置line-height */\n\t\t\t\n\t\t\t/* 重置button的默认样式 */\n\t\t\t&::after {\n\t\t\t\tborder: none;\n\t\t\t}\n\t\t\t\n\t\t\t.share-icon {\n\t\t\t\twidth: 34rpx; /* 缩小图标尺寸 */\n\t\t\t\theight: 34rpx;\n\t\t\t\tfilter: brightness(5); /* 使图标变白 */\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: 2; /* 确保图标在波纹之上 */\n\t\t\t}\n\t\t\t\n\t\t\t/* 波纹效果 */\n\t\t\t.share-btn-ripple {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 50%;\n\t\t\t\tleft: 30%; /* 更加偏左，适应更窄的半圆形 */\n\t\t\t\twidth: 0;\n\t\t\t\theight: 0;\n\t\t\t\tbackground-color: rgba(255, 255, 255, 0.4);\n\t\t\t\tborder-radius: 50%;\n\t\t\t\ttransform: translate(-50%, -50%);\n\t\t\t\tz-index: 1;\n\t\t\t\topacity: 0;\n\t\t\t}\n\t\t\t\n\t\t\t/* 悬浮提示 */\n\t\t\t.share-tooltip {\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 65rpx;\n\t\t\t\tpadding: 10rpx 16rpx;\n\t\t\t\tbackground-color: rgba(0, 0, 0, 0.7);\n\t\t\t\tcolor: #fff;\n\t\t\t\tborder-radius: 6rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n\t\t\t\topacity: 0;\n\t\t\t\ttransform: translateX(10rpx);\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\t\n\t\t\t\t/* 添加小三角形 */\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: '';\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: -8rpx;\n\t\t\t\t\ttop: 50%;\n\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\twidth: 0;\n\t\t\t\t\theight: 0;\n\t\t\t\t\tborder-style: solid;\n\t\t\t\t\tborder-width: 8rpx 0 8rpx 8rpx;\n\t\t\t\t\tborder-color: transparent transparent transparent rgba(0, 0, 0, 0.7);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.show-tooltip .share-tooltip {\n\t\t\t\topacity: 1;\n\t\t\t\ttransform: translateX(0);\n\t\t\t}\n\t\t\t\n\t\t\t&.active .share-btn-ripple {\n\t\t\t\tanimation: ripple 0.6s ease-out;\n\t\t\t}\n\t\t\t\n\t\t\t@keyframes ripple {\n\t\t\t\t0% {\n\t\t\t\t\twidth: 0;\n\t\t\t\t\theight: 0;\n\t\t\t\t\topacity: 0.5;\n\t\t\t\t}\n\t\t\t\t100% {\n\t\t\t\t\twidth: 100rpx;\n\t\t\t\t\theight: 100rpx;\n\t\t\t\t\topacity: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&:active {\n\t\t\t\ttransform: translateY(-50%) scale(0.92);\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(138, 43, 226, 0.2);\n\t\t\t\topacity: 0.9;\n\t\t\t\tpadding-left: 4rpx; /* 点击时左内边距减小，视觉效果更好 */\n\t\t\t}\n\t\t}\n\t}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/wishlist-uniapp/components/GroupSelector/GroupSelector.vue'\nwx.createComponent(Component)"], "names": ["groupTagOperations", "useGroupStore", "useWishStore", "useUserStore", "uni"], "mappings": ";;;;;;;AA6DC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,0BAAAA,kBAAkB;AAAA,EAC3B,QAAQ;AACP,UAAM,aAAaC,YAAAA,cAAc;AACjC,UAAM,YAAYC,WAAAA,aAAa;AAC/B,UAAM,YAAYC,WAAAA,aAAa;AAE/B,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACA;AAAA;AAAA,EAED,UAAU;AAET,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,aAAa,QAAQ;AAC5D,WAAK,WAAW,WAAW;AAAA,IAC5B;AAGAC,kBAAAA,MAAI,IAAI,aAAa,KAAK,UAAU;AAGpCA,wBAAI,IAAI,iBAAiB,MAAM;AAC9BA,oBAAAA,MAAY,MAAA,OAAA,oDAAA,WAAW;AAEvB,UAAI,KAAK,aAAa;AAGrB,aAAK,cAAc;AAInB,mBAAW,MAAM;AAChB,gBAAM,cAAcA,cAAAA,MAAI,eAAe,oBAAoB;AAC3D,cAAI,eAAe,CAAC,KAAK,yBAAyB;AACjDA,kGAAY,qBAAqB,WAAW;AAC5C,iBAAK,sBAAqB;AAAA,UAC3B;AAAA,QACA,GAAE,GAAI;AAAA,MACR;AAAA,IACD,CAAC;AAAA,EACD;AAAA,EACD,MAAM,UAAU;AACfA,kBAAAA,MAAY,MAAA,OAAA,qDAAA,2DAA2D;AAGvE,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,aAAa,QAAQ;AAC5DA,oBAAAA,MAAA,MAAA,OAAA,qDAAY,4DAA4D;AACxE,UAAI;AACH,cAAM,KAAK,WAAW;AACtBA,sBAAAA,MAAA,MAAA,OAAA,qDAAY,iDAAiD;AAAA,MAC5D,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,qDAAc,gDAAgD,KAAK;AAAA,MACpE;AAAA,IACD;AAGA,SAAK,sBAAqB;AAAA,EAC1B;AAAA;AAAA,EAED,gBAAgB;AACfA,kBAAAA,MAAI,KAAK,aAAa,KAAK,UAAU;AAErCA,wBAAI,KAAK,eAAe;AAAA,EACxB;AAAA,EACD,UAAU;AAAA,IACT,SAAS;AAER,YAAM,YAAY,KAAK,WAAW;AAGlC,YAAM,WAAW,UAAU,IAAI,OAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,GAAG;AAC3D,YAAM,iBAAiB,GAAG,UAAU,MAAM,IAAI,QAAQ;AAGtD,UAAI,KAAK,kBAAkB,kBAAkB,KAAK,aAAa,SAAS,GAAG;AAC1E,eAAO,KAAK;AAAA,MACb;AAGA,YAAM,eAAe,CAAC,GAAG,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM;AAElD,YAAI,EAAE,OAAO;AAAO,iBAAO;AAC3B,YAAI,EAAE,OAAO;AAAO,iBAAO;AAG3B,YAAI,EAAE,aAAa,CAAC,EAAE;AAAW,iBAAO;AACxC,YAAI,CAAC,EAAE,aAAa,EAAE;AAAW,iBAAO;AAGxC,gBAAQ,EAAE,SAAS,MAAM,EAAE,SAAS;AAAA,MACrC,CAAC;AAGD,WAAK,eAAe;AACpB,WAAK,gBAAgB;AAErBA,oBAAA,MAAA,MAAA,OAAA,qDAAY,6CAA6C,cAAc;AACvEA,0BAAA,MAAA,OAAA,qDAAY,kCAAkC,aAAa,IAAI,QAAM,EAAC,IAAI,EAAE,IAAI,MAAM,EAAE,MAAM,OAAO,EAAE,MAAK,EAAE,CAAC;AAE/G,aAAO;AAAA,IACP;AAAA,IACD,iBAAiB;AAChB,aAAO,KAAK,UAAU;AAAA,IACtB;AAAA;AAAA,IAED,eAAe;AACd,aAAO,KAAK,WAAW,aAAa,KAAK,cAAc;AAAA,IACxD;AAAA,EACA;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,qBAAqB;AAAA,MACpB,QAAQ,WAAW,WAAW;AAC7BA,sBAAAA,MAAY,MAAA,OAAA,qDAAA,0DAA0D;AAGtE,YAAI;AAEH,eAAK,UAAU,MAAM;AAEtB,iBAAK,gBAAgB;AACrB,iBAAK,eAAe;AAClBA,0BAAAA,MAAA,MAAA,OAAA,qDAAY,uCAAuC;AAAA,UACpD,CAAC;AAAA,QACA,SAAO,OAAO;AACfA,wBAAA,MAAA,MAAA,SAAA,qDAAc,iDAAiD,KAAK;AAEpE,eAAK,gBAAgB;AACrB,eAAK,eAAe;QACrB;AAAA,MACA;AAAA,MACD,MAAM;AAAA,MACN,WAAW;AAAA,IACX;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,QAAQ,YAAY,YAAY;AAC/BA,4BAAA,MAAA,OAAA,qDAAY,6CAA6C,YAAY,MAAM,UAAU;AAAA,MACrF;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,aAAa;AAAA,MACb,cAAc;AAAA;AAAA,MAEd,aAAa;AAAA;AAAA,MAEb,yBAAyB;AAAA;AAAA,MAEzB,wBAAwB;AAAA;AAAA,MAExB,sBAAsB;AAAA;AAAA,MAEtB,cAAc,CAAE;AAAA;AAAA,MAEhB,eAAe;AAAA,IAChB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,aAAa;AAEZ,UAAI,KAAK,eAAe,KAAK,UAAU,YAAY;AAClDA,sBAAAA,MAAA,MAAA,OAAA,qDAAY,mBAAmB;AAE/B,aAAK,cAAc;AAAA,MACpB;AAGA,YAAM,cAAcA,cAAAA,MAAI,eAAe,oBAAoB;AAE3D,UAAI,eAAe,KAAK,UAAU,cAAc,CAAC,KAAK,yBAAyB;AAE9E,cAAM,cAAc,KAAK;AACzB,cAAM,cAAc,gBAAgB,KAAK;AACzC,cAAM,kBAAmB,cAAc,KAAK,uBAAwB;AAEpE,YAAI,eAAe,iBAAiB;AAEnCA,8BAAI,kBAAkB,oBAAoB;AAC1C;AAAA,QACD;AAEA,aAAK,sBAAqB;AAAA,MAC3B;AAAA,IACA;AAAA;AAAA,IAGD,wBAAwB;AACvB,UAAI;AAEH,YAAI,KAAK,yBAAyB;AACjCA,wBAAAA,wEAAY,iBAAiB;AAC7B;AAAA,QACD;AAGA,aAAK,0BAA0B;AAG/B,cAAM,cAAcA,cAAAA,MAAI,eAAe,oBAAoB;AAC3D,YAAI,aAAa;AAChBA,wBAAY,MAAA,MAAA,OAAA,qDAAA,gBAAgB,WAAW;AAGvC,cAAI,KAAK,UAAU,YAAY;AAE9B,iBAAK,yBAAyB;AAC9B,iBAAK,uBAAuB,KAAK;AAGjC,uBAAW,MAAM;AAEhBA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,iBAAiB;AAAA,gBACjB,UAAU;AAAA,gBACV,SAAS;AAAA,gBACT,SAAS,CAAC,QAAQ;AAEjBA,sCAAI,kBAAkB,oBAAoB;AAE1C,sBAAI,IAAI,WAAW,IAAI,SAAS;AAE/B,yBAAK,gBAAgB,IAAI,SAAS,IAAI;AAAA,kBACvC;AAGA,uBAAK,0BAA0B;AAAA,gBAC/B;AAAA,gBACD,MAAM,MAAM;AAEXA,sCAAI,kBAAkB,oBAAoB;AAC1C,uBAAK,0BAA0B;AAAA,gBAChC;AAAA,cACD,CAAC;AAAA,YA2BD,GAAE,GAAG;AAAA,iBACA;AAEN,iBAAK,0BAA0B;AAAA,UAChC;AAAA,eACM;AAEN,eAAK,0BAA0B;AAAA,QAChC;AAAA,MACD,SAAS,GAAG;AACXA,sBAAA,MAAA,MAAA,SAAA,qDAAc,iBAAiB,CAAC;AAEhC,aAAK,0BAA0B;AAC/BA,4BAAI,kBAAkB,oBAAoB;AAAA,MAC3C;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB;AAElB,WAAK,iBAAgB;AAGrB,WAAK,mBAAkB;AAGvB,UAAI;AACHA,sBAAAA,MAAI,aAAa;AAAA,UAChB,SAAS,MAAM;AACdA,0BAAAA,MAAY,MAAA,OAAA,qDAAA,QAAQ;AAAA,UACrB;AAAA,QACD,CAAC;AAAA,MACF,SAAS,GAAG;AACXA,sBAAc,MAAA,MAAA,SAAA,qDAAA,WAAW,CAAC;AAAA,MAC3B;AAGA,UAAIA,cAAG,MAAC,eAAeA,cAAG,MAAC,YAAW,MAAO,aAAa;AACzD,YAAI;AACHA,wBAAAA,MAAI,cAAc;AAAA,YACjB,iBAAiB;AAAA,YACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,YAC1C,UAAU;AACTA,4BAAAA,MAAY,MAAA,OAAA,qDAAA,UAAU;AAAA,YACtB;AAAA,YACD,KAAK,GAAG;AACPA,4BAAY,MAAA,MAAA,OAAA,qDAAA,YAAY,CAAC;AAAA,YAC1B;AAAA,UACD,CAAC;AAAA,QACA,SAAO,KAAK;AACbA,wBAAc,MAAA,MAAA,SAAA,qDAAA,WAAW,GAAG;AAAA,QAC7B;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB;AAClB,YAAM,eAAe,KAAK;AAC1B,UAAI,CAAC;AAAc;AAGnB,YAAM,cAAc,KAAK,UAAU;AACnC,YAAM,YAAY,YAAY;AAE9B,YAAM,YAAY;AAAA,QACjB,SAAS,aAAa;AAAA,QACtB,WAAW,aAAa;AAAA,QACxB;AAAA,QACA,WAAW,KAAK,IAAI;AAAA;AAIrBA,oBAAAA,MAAI,eAAe,uBAAuB,SAAS;AAGnDA,oBAAG,MAAC,MAAM,uBAAuB;AAAA,QAChC,SAAS,aAAa;AAAA,QACtB,WAAW,aAAa;AAAA,QACxB;AAAA,QACA,WAAW;AAAA,MACZ,CAAC;AAEDA,oBAAAA,MAAY,MAAA,OAAA,qDAAA,YAAY;AAAA,QACvB,SAAS,aAAa;AAAA,QACtB,WAAW,aAAa;AAAA,QACxB,WAAW;AAAA,MACZ,CAAC;AAGD,UAAI;AACH,YAAIA,cAAG,MAAC,WAAWA,cAAG,MAAC,QAAQ,wBAAwB,GAAG;AACzDA,wBAAAA,MAAY,MAAA,OAAA,qDAAA,0BAA0B;AAAA,QACvC;AAAA,MACD,SAAS,GAAG;AACXA,gGAAc,aAAa,CAAC;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB;AAElB,UAAI;AACHA,sBAAG,MAAC,aAAY;AAAA,MACjB,SAAS,GAAG;AACXA,sBAAc,MAAA,MAAA,SAAA,qDAAA,WAAW,CAAC;AAAA,MAC3B;AAGA,WAAK,cAAc;AAGnB,UAAI,KAAK,cAAc;AACtB,qBAAa,KAAK,YAAY;AAAA,MAC/B;AAEA,WAAK,eAAe,WAAW,MAAM;AACpC,aAAK,cAAc;AAAA,MACnB,GAAE,GAAI;AAAA,IACP;AAAA;AAAA,IAGD,YAAY,SAAS;AACpB,WAAK,UAAU,gBAAgB,OAAO;AACtC,WAAK,MAAM,gBAAgB,OAAO;AAAA,IAClC;AAAA;AAAA,IAGD,oBAAoB;AAEnBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,WAAW,IAAI,SAAS;AAG/BA,0BAAAA,MAAI,eAAe,sBAAsB,IAAI,OAAO;AACpD,iBAAK,gBAAgB,IAAI,OAAO;AAAA,UACjC;AAAA,QACD;AAAA,OACA;AAAA,IAmBD;AAAA;AAAA,IAGD,MAAM,gBAAgB,OAAO,kBAAkB,OAAO;AACrD,YAAM,OAAO,MAAM,KAAK;AACxB,UAAI,CAAC,MAAM;AACVA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAEDA,4BAAI,kBAAkB,oBAAoB;AAC1C;AAAA,MACD;AAGA,UAAI,CAAC,KAAK,UAAU,YAAY;AAC/BA,sBAAAA,wEAAY,eAAe;AAG3B,YAAI,CAAC,iBAAiB;AAErBA,wBAAAA,MAAI,eAAe,sBAAsB,IAAI;AAG7C,eAAK,cAAc;AAGnB,eAAK,UAAU;QAChB;AACA;AAAA,MACD;AAGA,UAAI,KAAK,WAAW,yBAAyB,KAAK,WAAW,sBAAsB,IAAI,GAAG;AACzFA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAEDA,4BAAI,kBAAkB,oBAAoB;AAC1C;AAAA,MACD;AAEA,UAAI;AACHA,sBAAA,MAAA,MAAA,OAAA,qDAAY,0CAA0C,IAAI;AAG1D,cAAM,UAAU,MAAM,KAAK,WAAW,SAAS,IAAI;AAEnDA,8FAAY,sCAAsC,OAAO;AAEzD,YAAI,SAAS;AAEZA,8BAAI,kBAAkB,oBAAoB;AAE1CA,gGAAY,4EAA4E,KAAK,UAAU,cAAc;AACrHA,gGAAY,qCAAqC,KAAK,WAAW,aAAa,IAAI,QAAM,EAAC,IAAI,EAAE,IAAI,MAAM,EAAE,KAAI,EAAE,CAAC;AAGlH,eAAK,UAAU,gBAAgB,OAAO;AACtC,eAAK,MAAM,gBAAgB,OAAO;AAElCA,gGAAY,2EAA2E,KAAK,UAAU,cAAc;AACpHA,wBAAY,MAAA,MAAA,OAAA,qDAAA,4CAA4C,KAAK,cAAc;AAG3E,eAAK,gBAAgB;AACrB,eAAK,eAAe;AACpB,eAAK,aAAY;AAGjB,eAAK,UAAU,MAAM;AACpBA,0BAAY,MAAA,MAAA,OAAA,qDAAA,qDAAqD,KAAK,cAAc;AACpFA,0BAAAA,MAAY,MAAA,OAAA,qDAAA,uCAAuC,KAAK,OAAO,IAAI,QAAM,EAAC,IAAI,EAAE,IAAI,MAAM,EAAE,MAAM,QAAQ,KAAK,mBAAmB,EAAE,GAAE,EAAE,CAAC;AAAA,UAC1I,CAAC;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,eACK;AAENA,8BAAI,kBAAkB,oBAAoB;AAE1CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,QACF;AAAA,MACD,SAAS,GAAG;AAEXA,4BAAI,kBAAkB,oBAAoB;AAE1CA,sBAAAA,MAAA,MAAA,SAAA,qDAAc,WAAW,CAAC;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAGA,WAAK,0BAA0B;AAAA,IAC/B;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,sBAAsB;AAG3B,iBAAW,MAAM;AAChB,aAAK,sBAAsB;AAAA,MAC3B,GAAE,GAAG;AAGN,iBAAW,MAAM;AAChB,YAAI;AACHA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF,SAAQ,GAAG;AACVA,wBAAc,MAAA,MAAA,SAAA,qDAAA,aAAa,CAAC;AAAA,QAC7B;AAAA,MACA,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,yBAAyB;AACxBA,oBAAAA,MAAY,MAAA,OAAA,qDAAA,qDAAqD;AAEjEA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACvB,cAAI,IAAI,SAAS;AAChB,gBAAI;AACH,oBAAM,SAAS,MAAM,KAAK,WAAW,gBAAe;AACpDA,4BAAA,MAAA,MAAA,OAAA,qDAAY,6CAA6C,MAAM;AAG/D,mBAAK,gBAAgB;AAErBA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cACX,CAAC;AAAA,YACA,SAAO,OAAO;AACfA,4BAAc,MAAA,MAAA,SAAA,qDAAA,6CAA6C,KAAK;AAChEA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cACX,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB,OAAO;AAC3BA,oBAAAA,MAAA,MAAA,OAAA,qDAAY,4CAA4C;AACxDA,0BAAY,MAAA,OAAA,qDAAA,iCAAiC,KAAK,UAAU,KAAK,CAAC;AAGlE,UAAI,CAAC,OAAO;AACXA,sBAAAA,MAAc,MAAA,SAAA,qDAAA,wBAAwB;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,YAAM,UAAU,MAAM,MAAM,MAAM;AAElCA,4FAAY,6BAA6B,OAAO;AAChDA,oBAAA,MAAA,MAAA,OAAA,qDAAY,+BAA+B,+BAAO,IAAI;AACtDA,oBAAY,MAAA,MAAA,OAAA,qDAAA,oCAAoC,+BAAO,SAAS;AAEhE,UAAI,CAAC,SAAS;AACbA,gGAAc,kCAAkC,KAAK;AACrDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,YAAM,kBAAkB;AAAA,QACvB,GAAG;AAAA,QACH,IAAI;AAAA;AAIL,WAAK,qBAAqB,eAAe;AAAA,IACzC;AAAA,EAKF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzrBD,GAAG,gBAAgB,SAAS;"}
{"version": 3, "file": "historyWish.js", "sources": ["subpkg-profile/pages/historyWish/historyWish.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGtnLXByb2ZpbGVccGFnZXNcaGlzdG9yeVdpc2hcaGlzdG9yeVdpc2gudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"history-container\">\r\n\t\t<!-- 心愿列表 -->\r\n\t\t<view class=\"history-content\">\r\n\t\t\t<view v-if=\"completedWishes.length === 0\" class=\"empty-history\">\r\n\t\t\t\t<view class=\"empty-icon\">\r\n\t\t\t\t\t<uni-icons type=\"calendar-circle\" size=\"80\" color=\"#d9d9d9\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"empty-content\">\r\n\t\t\t\t\t<text class=\"empty-title\">暂无完成记录</text>\r\n\t\t\t\t\t<text class=\"empty-desc\">当心愿实现时，会出现在这里</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"empty-action\">\r\n\t\t\t\t\t<navigator url=\"/pages/index/index\" class=\"go-create-btn\">\r\n\t\t\t\t\t\t<uni-icons type=\"plus-circle\" size=\"16\" color=\"#8a2be2\"></uni-icons>\r\n\t\t\t\t\t\t<text>去创建心愿</text>\r\n\t\t\t\t\t</navigator>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<scroll-view v-else \r\n\t\t\t\tclass=\"history-list\" \r\n\t\t\t\tscroll-y=\"true\"\r\n\t\t\t\t:show-scrollbar=\"false\"\r\n\t\t\t\t:enable-flex=\"true\"\r\n\t\t\t\t@scroll=\"onListScroll\">\r\n\t\t\t\t<HistoryWishCard \r\n\t\t\t\t\tv-for=\"(wish, index) in completedWishes\" \r\n\t\t\t\t\t:key=\"wish.id || wish._id\" \r\n\t\t\t\t\t:wish=\"wish\"\r\n\t\t\t\t\t:index=\"index\"\r\n\t\t\t\t\t:activeCardId=\"activeCardId\"\r\n\t\t\t\t\t@restore=\"restoreWish\"\r\n\t\t\t\t\t@delete=\"deleteWish\"\r\n\t\t\t\t\t@card-open=\"handleCardOpen\"\r\n\t\t\t\t\t@card-close=\"handleCardClose\"\r\n\t\t\t\t\t@card-swipe-start=\"handleCardSwipeStart\"\r\n\t\t\t\t\t@card-scroll-detected=\"handleCardScrollDetected\"\r\n\t\t\t\t/>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 列表底部间距 -->\r\n\t\t\t\t<view class=\"list-bottom-space\"></view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { useWishStore } from '@/store/wish.js'\r\n\timport { useUserStore } from '@/store/user.js'\r\n\timport { computed, ref } from 'vue'\r\n\timport HistoryWishCard from '@/components/HistoryWishCard/HistoryWishCard.vue'\r\n\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tHistoryWishCard\r\n\t\t},\r\n\t\tsetup() {\r\n\t\t\tconst wishStore = useWishStore()\r\n\t\t\tconst userStore = useUserStore()\r\n\t\t\t\r\n\t\t\t// 活动卡片ID，用于跟踪当前打开的滑动卡片\r\n\t\t\tconst activeCardId = ref('')\r\n\t\t\t\r\n\t\t\t// 获取所有已完成的心愿，并按完成时间倒序排列\r\n\t\t\tconst completedWishes = computed(() => {\r\n\t\t\t\treturn wishStore.wishList\r\n\t\t\t\t\t.filter(wish => wish.isCompleted)\r\n\t\t\t\t\t.sort((a, b) => {\r\n\t\t\t\t\t\tconst dateA = new Date(a.completeDate || a.lastCompleteDate || a.updateDate);\r\n\t\t\t\t\t\tconst dateB = new Date(b.completeDate || b.lastCompleteDate || b.updateDate);\r\n\t\t\t\t\t\treturn dateB - dateA;\r\n\t\t\t\t\t})\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\t// 处理卡片打开事件\r\n\t\t\tconst handleCardOpen = (cardId) => {\r\n\t\t\t\tconsole.log('卡片打开:', cardId)\r\n\t\t\t\tactiveCardId.value = cardId\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 处理卡片关闭事件\r\n\t\t\tconst handleCardClose = () => {\r\n\t\t\t\tconsole.log('卡片关闭')\r\n\t\t\t\tactiveCardId.value = ''\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 处理卡片开始滑动事件\r\n\t\t\tconst handleCardSwipeStart = (cardId) => {\r\n\t\t\t\tconsole.log('卡片开始滑动，强制关闭其他卡片')\r\n\t\t\t\t// 如果有其他卡片处于打开状态，发送消息关闭它\r\n\t\t\t\tif (activeCardId.value && activeCardId.value !== cardId) {\r\n\t\t\t\t\tuni.$emit('force-close-cards', { targetCardId: cardId })\r\n\t\t\t\t\tactiveCardId.value = ''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 处理卡片滚动检测事件\r\n\t\t\tconst handleCardScrollDetected = (data) => {\r\n\t\t\t\tconsole.log('检测到卡片滚动:', data)\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 处理列表滚动事件\r\n\t\t\tconst onListScroll = (e) => {\r\n\t\t\t\t// 当列表滚动时，通知所有卡片\r\n\t\t\t\tuni.$emit('page-scroll-event', { \r\n\t\t\t\t\tshouldClose: true, \r\n\t\t\t\t\tscrollTop: e.detail.scrollTop \r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t// 清除活动卡片\r\n\t\t\t\tactiveCardId.value = ''\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 还原心愿\r\n\t\t\tconst restoreWish = (id) => {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '还原心愿',\r\n\t\t\t\t\tcontent: '确定要将此心愿恢复到待完成状态吗？',\r\n\t\t\t\t\tconfirmColor: '#8a2be2',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 调用store的恢复心愿方法\r\n\t\t\t\t\t\t\tconst result = wishStore.restoreWish(id)\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (result) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '心愿已还原',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '操作失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 删除心愿\r\n\t\t\tconst deleteWish = (id) => {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '删除心愿',\r\n\t\t\t\t\tcontent: '确定要永久删除此心愿吗？此操作不可恢复。',\r\n\t\t\t\t\tconfirmColor: '#ff4d4f',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\twishStore.deleteWish(id)\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '心愿已删除',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\t// 🔧 检查并同步心愿数据\r\n\t\t\tconst checkAndSyncWishData = async () => {\r\n\t\t\t\tconsole.log('[HistoryWish] 检查心愿数据同步状态...');\r\n\r\n\t\t\t\t// 检查本地是否有缓存数据\r\n\t\t\t\tconst hasLocalData = wishStore && wishStore.wishList.length > 0;\r\n\t\t\t\tconst lastSyncTime = wishStore ? wishStore.lastSyncTime : null;\r\n\t\t\t\tconst now = Date.now();\r\n\r\n\t\t\t\t// 如果没有本地数据，或者超过5分钟未同步，则从云端获取\r\n\t\t\t\tconst shouldSync = !hasLocalData || !lastSyncTime || (now - lastSyncTime > 5 * 60 * 1000);\r\n\r\n\t\t\t\tif (shouldSync) {\r\n\t\t\t\t\tconsole.log('[HistoryWish] 需要同步心愿数据，原因:',\r\n\t\t\t\t\t\t!hasLocalData ? '无本地数据' :\r\n\t\t\t\t\t\t!lastSyncTime ? '未记录同步时间' : '超过5分钟未同步');\r\n\r\n\t\t\t\t\t// 使用静默同步，避免显示加载弹窗\r\n\t\t\t\t\tawait wishStore.syncFromCloud({ silent: true });\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('[HistoryWish] 使用本地缓存数据');\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn {\r\n\t\t\t\twishStore,\r\n\t\t\t\tcompletedWishes,\r\n\t\t\t\tactiveCardId,\r\n\t\t\t\thandleCardOpen,\r\n\t\t\t\thandleCardClose,\r\n\t\t\t\thandleCardSwipeStart,\r\n\t\t\t\thandleCardScrollDetected,\r\n\t\t\t\tonListScroll,\r\n\t\t\t\trestoreWish,\r\n\t\t\t\tdeleteWish,\r\n\t\t\t\tuserStore,\r\n\t\t\t\tcheckAndSyncWishData\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\t// 检查登录状态，如果未登录则由导航拦截器处理跳转\r\n\t\t\tif (!this.userStore.isLogin) {\r\n\t\t\t\tconsole.log('[historyWish] 用户未登录，等待导航拦截器处理')\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 🔧 页面显示时的处理 - 使用Options API方式\r\n\t\tonShow() {\r\n\t\t\t// 页面显示时检查登录状态\r\n\t\t\tif (this.userStore && this.userStore.isLogin) {\r\n\t\t\t\t// 检查并同步心愿数据\r\n\t\t\t\tthis.checkAndSyncWishData()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.history-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t.history-content {\r\n\t\tflex: 1;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\t\r\n\t.empty-history {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 120rpx 60rpx;\r\n\t\tmin-height: 60vh;\r\n\t}\r\n\t\r\n\t.empty-icon {\r\n\t\tmargin-bottom: 40rpx;\r\n\t\topacity: 0.6;\r\n\t}\r\n\t\r\n\t.empty-content {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 60rpx;\r\n\t}\r\n\t\r\n\t.empty-title {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\t.empty-desc {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\t\r\n\t.empty-action {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\t\r\n\t.go-create-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tgap: 12rpx;\r\n\t\tbackground: linear-gradient(135deg, #8a2be2 0%, #9c4ccc 100%);\r\n\t\tcolor: #ffffff;\r\n\t\tpadding: 24rpx 48rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 500;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(138, 43, 226, 0.3);\r\n\t\ttransition: all 0.3s ease;\r\n\t\t\r\n\t\t&:active {\r\n\t\t\ttransform: translateY(2rpx);\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.4);\r\n\t\t}\r\n\t\t\r\n\t\ttext {\r\n\t\t\tcolor: #ffffff;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.history-list {\r\n\t\tflex: 1;\r\n\t\tpadding-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.list-bottom-space {\r\n\t\theight: 40rpx;\r\n\t}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/subpkg-profile/pages/historyWish/historyWish.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useWishStore", "useUserStore", "ref", "computed", "uni"], "mappings": ";;;;AAmDC,wBAAwB,MAAW;AAEnC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,QAAQ;AACP,UAAM,YAAYA,WAAAA,aAAa;AAC/B,UAAM,YAAYC,WAAAA,aAAa;AAG/B,UAAM,eAAeC,cAAG,IAAC,EAAE;AAG3B,UAAM,kBAAkBC,cAAAA,SAAS,MAAM;AACtC,aAAO,UAAU,SACf,OAAO,UAAQ,KAAK,WAAW,EAC/B,KAAK,CAAC,GAAG,MAAM;AACf,cAAM,QAAQ,IAAI,KAAK,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,UAAU;AAC3E,cAAM,QAAQ,IAAI,KAAK,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,UAAU;AAC3E,eAAO,QAAQ;AAAA,OACf;AAAA,KACF;AAGD,UAAM,iBAAiB,CAAC,WAAW;AAClCC,oBAAAA,MAAA,MAAA,OAAA,0DAAY,SAAS,MAAM;AAC3B,mBAAa,QAAQ;AAAA,IACtB;AAGA,UAAM,kBAAkB,MAAM;AAC7BA,oBAAAA,MAAY,MAAA,OAAA,0DAAA,MAAM;AAClB,mBAAa,QAAQ;AAAA,IACtB;AAGA,UAAM,uBAAuB,CAAC,WAAW;AACxCA,oBAAAA,MAAA,MAAA,OAAA,0DAAY,iBAAiB;AAE7B,UAAI,aAAa,SAAS,aAAa,UAAU,QAAQ;AACxDA,sBAAAA,MAAI,MAAM,qBAAqB,EAAE,cAAc,OAAK,CAAG;AACvD,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACD;AAGA,UAAM,2BAA2B,CAAC,SAAS;AAC1CA,oBAAAA,MAAA,MAAA,OAAA,2DAAY,YAAY,IAAI;AAAA,IAC7B;AAGA,UAAM,eAAe,CAAC,MAAM;AAE3BA,oBAAG,MAAC,MAAM,qBAAqB;AAAA,QAC9B,aAAa;AAAA,QACb,WAAW,EAAE,OAAO;AAAA,OACpB;AAGD,mBAAa,QAAQ;AAAA,IACtB;AAGA,UAAM,cAAc,CAAC,OAAO;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhB,kBAAM,SAAS,UAAU,YAAY,EAAE;AAEvC,gBAAI,QAAQ;AACXA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,eACN;AAAA,mBACK;AACNA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,eACN;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,OACA;AAAA,IACF;AAGA,UAAM,aAAa,CAAC,OAAO;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,sBAAU,WAAW,EAAE;AACvBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,aACN;AAAA,UACF;AAAA,QACD;AAAA,OACA;AAAA,IACF;AAGA,UAAM,uBAAuB,YAAY;AACxCA,oBAAAA,MAAA,MAAA,OAAA,2DAAY,6BAA6B;AAGzC,YAAM,eAAe,aAAa,UAAU,SAAS,SAAS;AAC9D,YAAM,eAAe,YAAY,UAAU,eAAe;AAC1D,YAAM,MAAM,KAAK;AAGjB,YAAM,aAAa,CAAC,gBAAgB,CAAC,gBAAiB,MAAM,eAAe,IAAI,KAAK;AAEpF,UAAI,YAAY;AACfA,sBAAAA;;;UAAY;AAAA,UACX,CAAC,eAAe,UAChB,CAAC,eAAe,YAAY;AAAA,QAAU;AAGvC,cAAM,UAAU,cAAc,EAAE,QAAQ,KAAM,CAAA;AAAA,aACxC;AACNA,sBAAAA,MAAY,MAAA,OAAA,2DAAA,wBAAwB;AAAA,MACrC;AAAA,IACD;AAEA,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACA;AAAA,EAED,SAAS;AAER,QAAI,CAAC,KAAK,UAAU,SAAS;AAC5BA,oBAAAA,MAAY,MAAA,OAAA,2DAAA,+BAA+B;AAC3C;AAAA,IACD;AAAA,EACA;AAAA;AAAA,EAGD,SAAS;AAER,QAAI,KAAK,aAAa,KAAK,UAAU,SAAS;AAE7C,WAAK,qBAAqB;AAAA,IAC3B;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtND,GAAG,WAAW,eAAe;"}
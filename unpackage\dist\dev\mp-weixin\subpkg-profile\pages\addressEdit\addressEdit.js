"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_user = require("../../../store/user.js");
const _sfc_main = {
  setup() {
    const userStore = store_user.useUserStore();
    const addressForm = common_vendor.reactive({
      name: "",
      phone: "",
      province: "",
      city: "",
      district: "",
      address: "",
      isDefault: false
    });
    const regionText = common_vendor.ref("");
    const isPasteBoardShown = common_vendor.ref(false);
    const pasteContent = common_vendor.ref("");
    common_vendor.ref(-1);
    const hasLoaded = common_vendor.ref(false);
    const showCountryCodePicker = () => {
      common_vendor.index.showToast({
        title: "目前仅支持中国大陆(+86)",
        icon: "none"
      });
    };
    const togglePasteBoard = () => {
      isPasteBoardShown.value = !isPasteBoardShown.value;
    };
    const parseAddress = () => {
      if (!pasteContent.value) {
        common_vendor.index.showToast({
          title: "请先粘贴内容",
          icon: "none"
        });
        return;
      }
      const text = pasteContent.value;
      try {
        const phoneRegex = /(?:13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])[0-9]{8}/;
        const phoneMatch = text.match(phoneRegex);
        if (phoneMatch) {
          addressForm.phone = phoneMatch[0];
        }
        let nameMatch = null;
        if (text.includes("收件人")) {
          const nameRegex = /收件人[：:]\s*([^\s,，。；;]{2,4})/;
          nameMatch = text.match(nameRegex);
          if (nameMatch) {
            addressForm.name = nameMatch[1];
          }
        } else {
          const firstLine = text.split(/[\n,，。；;]/)[0];
          const nameRegex = /^[\u4e00-\u9fa5]{2,4}/;
          nameMatch = firstLine.match(nameRegex);
          if (nameMatch) {
            addressForm.name = nameMatch[0];
          }
        }
        const provinceRegex = /(北京|天津|上海|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)[省市自治区特别行政区]/;
        const provinceMatch = text.match(provinceRegex);
        if (provinceMatch) {
          addressForm.province = provinceMatch[0];
          const cityRegex = new RegExp(provinceMatch[1] + "[省市自治区特别行政区]([^市]+市)");
          const cityMatch = text.match(cityRegex) || text.match(/([^市]+市)/g);
          if (cityMatch) {
            addressForm.city = cityMatch[1] || cityMatch[0];
            const districtRegex = new RegExp(cityMatch[1] ? cityMatch[1] : cityMatch[0] + "([^区县]+区|[^区县]+县)");
            const districtMatch = text.match(districtRegex) || text.match(/([^区县]+区|[^区县]+县)/);
            if (districtMatch) {
              addressForm.district = districtMatch[1] || districtMatch[0];
              const addressStart = text.indexOf(addressForm.district) + addressForm.district.length;
              if (addressStart > 0 && addressStart < text.length) {
                const detailAddress = text.substring(addressStart).trim();
                if (detailAddress) {
                  addressForm.address = detailAddress.replace(/[,，。；;]/g, "").substring(0, 50);
                }
              }
            }
          }
        }
        updateRegionText();
        common_vendor.index.showToast({
          title: "已识别地址信息",
          icon: "success"
        });
        isPasteBoardShown.value = false;
      } catch (e) {
        common_vendor.index.__f__("error", "at subpkg-profile/pages/addressEdit/addressEdit.vue:244", "地址解析失败:", e);
        common_vendor.index.showToast({
          title: "地址解析失败",
          icon: "none"
        });
      }
    };
    common_vendor.watch(regionText, (newVal) => {
      if (newVal) {
        try {
          if (newVal.length >= 6) {
            const provinceEnd = newVal.indexOf("省");
            const cityEnd = newVal.indexOf("市");
            if (provinceEnd > 0 && cityEnd > provinceEnd) {
              addressForm.province = newVal.substring(0, provinceEnd + 1);
              addressForm.city = newVal.substring(provinceEnd + 1, cityEnd + 1);
              addressForm.district = newVal.substring(cityEnd + 1);
            }
          }
        } catch (e) {
          common_vendor.index.__f__("error", "at subpkg-profile/pages/addressEdit/addressEdit.vue:268", "地址解析失败:", e);
        }
      } else {
        addressForm.province = "";
        addressForm.city = "";
        addressForm.district = "";
      }
    });
    const updateRegionText = () => {
      if (addressForm.province || addressForm.city || addressForm.district) {
        regionText.value = `${addressForm.province || ""}${addressForm.city || ""}${addressForm.district || ""}`;
      } else {
        regionText.value = "";
      }
    };
    const loadEditData = async () => {
      if (hasLoaded.value) {
        common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:291", "[addressEdit] 已经加载过数据，跳过重复加载");
        return;
      }
      const editId = common_vendor.index.getStorageSync("edit_address_id");
      common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:297", "[addressEdit] loadEditData - editId:", editId);
      if (editId) {
        common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:300", "[addressEdit] 编辑模式, id:", editId);
        try {
          common_vendor.index.showLoading({ title: "加载中..." });
          const result = await common_vendor.nr.importObject("address-center").getAddressDetail(editId);
          common_vendor.index.hideLoading();
          if (result.code === 0) {
            const address = result.data;
            common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:312", "[addressEdit] 获取地址详情成功:", address);
            common_vendor.nextTick$1(() => {
              Object.assign(addressForm, {
                name: address.name || "",
                phone: address.mobile || "",
                province: address.province || "",
                city: address.city || "",
                district: address.district || "",
                address: address.address || "",
                isDefault: address.is_default || false
              });
              updateRegionText();
              hasLoaded.value = true;
              common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:329", "[addressEdit] 表单数据已填充:", JSON.stringify(addressForm));
            });
          } else {
            common_vendor.index.showToast({
              title: result.message || "获取地址失败",
              icon: "none"
            });
            common_vendor.index.__f__("error", "at subpkg-profile/pages/addressEdit/addressEdit.vue:336", "[addressEdit] 获取地址详情失败:", result.message);
          }
        } catch (error) {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at subpkg-profile/pages/addressEdit/addressEdit.vue:340", "[addressEdit] 获取地址详情异常:", error);
          common_vendor.index.showToast({
            title: "网络错误",
            icon: "none"
          });
        }
      } else {
        common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:347", "[addressEdit] 新增模式");
        hasLoaded.value = true;
      }
    };
    const getLocationAddress = () => {
      common_vendor.index.showLoading({
        title: "获取位置中..."
      });
      common_vendor.index.getLocation({
        type: "gcj02",
        // 使用国测局坐标系
        success: (res) => {
          common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:364", "获取位置成功", res);
          common_vendor.index.request({
            url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${res.latitude},${res.longitude}&key=YOURKEY`,
            // 实际使用时需要替换为您的腾讯地图API密钥
            success: (response) => {
              common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:370", "逆地址解析成功", response);
              if (response.statusCode === 200 && response.data.status === 0) {
                const result = response.data.result;
                addressForm.province = result.address_component.province;
                addressForm.city = result.address_component.city;
                addressForm.district = result.address_component.district;
                updateRegionText();
                common_vendor.index.showToast({
                  title: "已获取当前位置",
                  icon: "success"
                });
              } else {
                common_vendor.index.chooseLocation({
                  success: (chooseRes) => {
                    common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:391", "选择位置成功", chooseRes);
                    const address = chooseRes.address || "";
                    const name = chooseRes.name || "";
                    const fullAddress = address + name;
                    let province = "", city = "", district = "";
                    const provinceMatch = fullAddress.match(/(北京|天津|上海|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆)[省市自治区]/);
                    if (provinceMatch) {
                      province = provinceMatch[0];
                      const cityMatch = fullAddress.match(/[^市县区]+市/g);
                      if (cityMatch && cityMatch.length > 0) {
                        city = cityMatch[0];
                        const districtMatch = fullAddress.match(/[^市县区]+[区县]/g);
                        if (districtMatch && districtMatch.length > 0) {
                          district = districtMatch[0];
                        }
                      }
                    }
                    addressForm.province = province;
                    addressForm.city = city;
                    addressForm.district = district;
                    if (province || city || district) {
                      updateRegionText();
                    } else {
                      regionText.value = fullAddress;
                    }
                    common_vendor.index.showToast({
                      title: "已获取选择位置",
                      icon: "success"
                    });
                  },
                  fail: () => {
                    common_vendor.index.showToast({
                      title: "获取位置失败",
                      icon: "none"
                    });
                  }
                });
              }
            },
            fail: () => {
              common_vendor.index.chooseLocation({
                success: (chooseRes) => {
                  common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:449", "选择位置成功", chooseRes);
                  const address = chooseRes.address || "";
                  chooseRes.name || "";
                  regionText.value = address;
                  common_vendor.index.showToast({
                    title: "已获取选择位置",
                    icon: "success"
                  });
                },
                fail: () => {
                  common_vendor.index.showToast({
                    title: "获取位置失败",
                    icon: "none"
                  });
                }
              });
            },
            complete: () => {
              common_vendor.index.hideLoading();
            }
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:477", "获取位置失败", err);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "无法获取当前位置，请手动选择",
            icon: "none",
            duration: 2e3
          });
          setTimeout(() => {
            common_vendor.index.chooseLocation({
              success: (res) => {
                common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:491", "选择位置成功", res);
                const address = res.address || "";
                res.name || "";
                regionText.value = address;
                common_vendor.index.showToast({
                  title: "已获取选择位置",
                  icon: "success"
                });
              }
            });
          }, 1e3);
        }
      });
    };
    const selectProvince = () => {
      const provinces = [
        "北京市",
        "天津市",
        "上海市",
        "重庆市",
        "河北省",
        "山西省",
        "辽宁省",
        "吉林省",
        "黑龙江省",
        "江苏省",
        "浙江省",
        "安徽省",
        "福建省",
        "江西省",
        "山东省",
        "河南省",
        "湖北省",
        "湖南省",
        "广东省",
        "海南省",
        "四川省",
        "贵州省",
        "云南省",
        "陕西省",
        "甘肃省",
        "青海省",
        "台湾省",
        "内蒙古自治区",
        "广西壮族自治区",
        "西藏自治区",
        "宁夏回族自治区",
        "新疆维吾尔自治区",
        "香港特别行政区",
        "澳门特别行政区"
      ];
      common_vendor.index.showActionSheet({
        itemList: provinces,
        success: function(res) {
          addressForm.province = provinces[res.tapIndex];
          addressForm.city = "";
          addressForm.district = "";
          updateRegionText();
          setTimeout(() => {
            selectCity();
          }, 300);
        }
      });
    };
    const selectCity = () => {
      let cities = [];
      if (addressForm.province === "广东省") {
        cities = ["广州市", "深圳市", "珠海市", "汕头市", "佛山市", "韶关市", "湛江市", "肇庆市", "江门市", "茂名市", "惠州市", "梅州市", "汕尾市", "河源市", "阳江市", "清远市", "东莞市", "中山市", "潮州市", "揭阳市", "云浮市"];
      } else if (addressForm.province === "北京市") {
        cities = ["北京市"];
      } else if (addressForm.province === "上海市") {
        cities = ["上海市"];
      } else if (addressForm.province === "浙江省") {
        cities = ["杭州市", "宁波市", "温州市", "嘉兴市", "湖州市", "绍兴市", "金华市", "衢州市", "舟山市", "台州市", "丽水市"];
      } else if (addressForm.province === "江苏省") {
        cities = ["南京市", "无锡市", "徐州市", "常州市", "苏州市", "南通市", "连云港市", "淮安市", "盐城市", "扬州市", "镇江市", "泰州市", "宿迁市"];
      } else {
        cities = ["城市1", "城市2", "城市3", "城市4", "城市5"];
      }
      if (!addressForm.province) {
        common_vendor.index.showToast({
          title: "请先选择省份",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showActionSheet({
        itemList: cities,
        success: function(res) {
          addressForm.city = cities[res.tapIndex];
          addressForm.district = "";
          updateRegionText();
          setTimeout(() => {
            selectDistrict();
          }, 300);
        }
      });
    };
    const selectDistrict = () => {
      let districts = [];
      if (addressForm.province === "广东省" && addressForm.city === "深圳市") {
        districts = ["福田区", "罗湖区", "南山区", "宝安区", "龙岗区", "盐田区", "龙华区", "坪山区", "光明区"];
      } else if (addressForm.province === "广东省" && addressForm.city === "广州市") {
        districts = ["越秀区", "海珠区", "荔湾区", "天河区", "白云区", "黄埔区", "番禺区", "花都区", "南沙区", "从化区", "增城区"];
      } else {
        districts = ["区县1", "区县2", "区县3", "区县4", "区县5"];
      }
      if (!addressForm.city) {
        common_vendor.index.showToast({
          title: "请先选择城市",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showActionSheet({
        itemList: districts,
        success: function(res) {
          addressForm.district = districts[res.tapIndex];
          updateRegionText();
        }
      });
    };
    const toggleDefault = (e) => {
      addressForm.isDefault = e.detail.value;
    };
    const saveAddress = async () => {
      if (!userStore.checkLoginAndRedirect()) {
        common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:629", "用户未登录，无法保存地址");
        return;
      }
      if (!addressForm.name) {
        common_vendor.index.showToast({
          title: "请输入收货人姓名",
          icon: "none"
        });
        return;
      }
      if (!addressForm.phone) {
        common_vendor.index.showToast({
          title: "请输入手机号码",
          icon: "none"
        });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(addressForm.phone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号码",
          icon: "none"
        });
        return;
      }
      if (!regionText.value) {
        common_vendor.index.showToast({
          title: "请选择或输入地址",
          icon: "none"
        });
        return;
      }
      if (!addressForm.address) {
        common_vendor.index.showToast({
          title: "请输入门牌号",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "保存中..." });
        common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:678", "保存地址：", addressForm);
        common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:679", "地区文本：", regionText.value);
        let province = addressForm.province;
        let city = addressForm.city;
        let district = addressForm.district;
        if (!province && !city) {
          const addressText = regionText.value || "";
          if (addressText) {
            if (addressText.includes("省")) {
              const parts = addressText.split("省");
              province = parts[0] + "省";
              city = parts[1] || addressText;
            } else if (addressText.includes("市")) {
              const parts = addressText.split("市");
              province = parts[0] + "市";
              city = parts[0] + "市";
            } else {
              province = addressText;
              city = addressText;
            }
          } else {
            province = "省份";
            city = "城市";
          }
        }
        const addressData = {
          name: addressForm.name,
          mobile: addressForm.phone,
          province: province || "省份",
          city: city || "城市",
          district: district || "区县",
          address: addressForm.address,
          is_default: addressForm.isDefault,
          tag: "home"
          // 默认标签
        };
        let result;
        const editId = common_vendor.index.getStorageSync("edit_address_id");
        if (editId) {
          result = await common_vendor.nr.importObject("address-center").updateAddress(editId, addressData);
        } else {
          result = await common_vendor.nr.importObject("address-center").addAddress(addressData);
        }
        common_vendor.index.hideLoading();
        if (result.code === 0) {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1e3);
        } else {
          common_vendor.index.showToast({
            title: result.message || "保存失败",
            icon: "none"
          });
        }
      } catch (e) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subpkg-profile/pages/addressEdit/addressEdit.vue:754", "保存地址失败:", e);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    };
    const clearPasteContent = () => {
      pasteContent.value = "";
    };
    return {
      userStore,
      addressForm,
      regionText,
      isPasteBoardShown,
      pasteContent,
      hasLoaded,
      saveAddress,
      toggleDefault,
      togglePasteBoard,
      parseAddress,
      clearPasteContent,
      showCountryCodePicker,
      getLocationAddress,
      selectProvince,
      selectCity,
      selectDistrict,
      updateRegionText,
      loadEditData
    };
  },
  // 页面加载时保存参数
  onLoad(options) {
    common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:790", "[addressEdit] onLoad - options:", options);
    if (options.id) {
      common_vendor.index.setStorageSync("edit_address_id", options.id);
      common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:795", "[addressEdit] 保存编辑ID:", options.id);
    } else {
      common_vendor.index.removeStorageSync("edit_address_id");
      common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:798", "[addressEdit] 清除编辑ID - 新增模式");
    }
    if (!this.userStore.checkLoginAndRedirect()) {
      common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:803", "用户未登录，跳转到登录页面");
      return;
    }
    this.loadEditData();
  },
  // 页面显示时调用，从登录页面返回时重新加载数据
  onShow() {
    if (this.hasLoaded && this.userStore.isLoggedIn && this.addressForm.name === "") {
      this.loadEditData();
      common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:815", "地址编辑页面显示，重新加载数据");
    }
    this.hasLoaded = true;
  },
  // 页面卸载时清理临时数据
  onUnload() {
    common_vendor.index.removeStorageSync("edit_address_id");
    this.hasLoaded = false;
    common_vendor.index.__f__("log", "at subpkg-profile/pages/addressEdit/addressEdit.vue:823", "[addressEdit] 页面卸载，清理临时数据");
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $setup.addressForm.name,
    b: common_vendor.o(($event) => $setup.addressForm.name = $event.detail.value),
    c: common_vendor.o((...args) => $setup.showCountryCodePicker && $setup.showCountryCodePicker(...args)),
    d: $setup.addressForm.phone,
    e: common_vendor.o(($event) => $setup.addressForm.phone = $event.detail.value),
    f: $setup.regionText,
    g: common_vendor.o(($event) => $setup.regionText = $event.detail.value),
    h: common_vendor.o((...args) => $setup.getLocationAddress && $setup.getLocationAddress(...args)),
    i: $setup.addressForm.address,
    j: common_vendor.o(($event) => $setup.addressForm.address = $event.detail.value),
    k: common_vendor.t($setup.isPasteBoardShown ? "▲" : "▼"),
    l: common_vendor.o((...args) => $setup.togglePasteBoard && $setup.togglePasteBoard(...args)),
    m: $setup.isPasteBoardShown
  }, $setup.isPasteBoardShown ? {
    n: $setup.pasteContent,
    o: common_vendor.o(($event) => $setup.pasteContent = $event.detail.value),
    p: common_vendor.o((...args) => $setup.clearPasteContent && $setup.clearPasteContent(...args)),
    q: common_vendor.o((...args) => $setup.parseAddress && $setup.parseAddress(...args))
  } : {}, {
    r: $setup.addressForm.isDefault,
    s: common_vendor.o((...args) => $setup.toggleDefault && $setup.toggleDefault(...args)),
    t: common_vendor.o((...args) => $setup.saveAddress && $setup.saveAddress(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-153ba1a6"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpkg-profile/pages/addressEdit/addressEdit.js.map

// 心愿管理云对象
const db = uniCloud.database();
const dbCmd = db.command;

module.exports = {
  // 云对象的初始化方法
  _before: function() {
    console.log('[wish-center] _before method called');
    console.log('[wish-center] VERSION: v1.4 - Completely rebuilt to fix deleteMany error');
    
    // 获取客户端信息
    const clientInfo = this.getClientInfo();
    this.clientInfo = clientInfo;
    console.log('[wish-center] clientInfo:', JSON.stringify(clientInfo, null, 2));
    
    // 获取用户ID
    const uniIdToken = this.getUniIdToken();
    console.log('[wish-center] uniIdToken from getUniIdToken():', uniIdToken);
    this.uid = (uniIdToken && uniIdToken.uid) || (clientInfo.uniIdToken && clientInfo.uniIdToken.uid);
    
    // 如果没有从token中获取到uid，尝试其他方式
    if (!this.uid) {
      const token = clientInfo.uniIdToken || clientInfo.token;
      console.log('[wish-center] Trying to parse token:', token ? 'TOKEN_EXISTS' : 'NO_TOKEN');
      if (token) {
        try {
          const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
          console.log('[wish-center] Parsed token payload:', payload);
          this.uid = payload.uid;
        } catch (e) {
          console.error('[wish-center] Token parse error:', e);
        }
      }
    }
    
    console.log('[wish-center] Final uid:', this.uid);
    
    // 检查认证
    const methodsWithoutAuth = ['getPublicWishes'];
    if (!methodsWithoutAuth.includes(this.getMethodName()) && !this.uid) {
      console.error('[wish-center] No uid found, user not authenticated');
      throw new Error('需要登录后才能操作');
    }
    
    console.log('[wish-center] Authentication successful for uid:', this.uid);
  },

  /**
   * 🚀 发送数据同步推送通知（独立推送，减少数据传输）
   */
  async _sendSyncPush(action, dataId, data = null) {
    try {
      // 调用推送云对象
      const syncPush = uniCloud.importObject('sync-push')

      // 🚀 只推送心愿数据变更，减少不必要的数据传输
      const result = await syncPush.pushDataSync({
        userId: this.uid,
        dataType: 'wish',
        action,
        dataId,
        data
      })

      if (result.errCode === 0) {
        console.log(`[wish-center] 推送通知发送成功: ${action}`)
      } else {
        console.warn(`[wish-center] 推送通知发送失败: ${result.errMsg}`)
      }
    } catch (error) {
      console.error('[wish-center] 发送推送通知失败:', error)
      // 推送失败不影响主要业务逻辑
    }
  },
  
  /**
   * 获取用户的心愿列表
   */
  async getWishList(options = {}) {
    const {
      groupId = 'all',
      page = 1,
      pageSize = 20,
      includeCompleted = false
    } = options;
    
    let where = {
      userId: this.uid
    };

    console.log('[wish-center] 查询条件:', { userId: this.uid, groupId, includeCompleted });

    if (groupId !== 'all') {
      where.groupIds = dbCmd.in([groupId]);
    }

    if (!includeCompleted) {
      where.isCompleted = false;
    }

    console.log('[wish-center] 最终查询条件:', where);

    const res = await db.collection('wishes')
      .where(where)
      .orderBy('isCompleted', 'asc')
      .orderBy('order', 'asc')
      .orderBy('groupOrder', 'asc')
      .orderBy('createDate', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();

    const countRes = await db.collection('wishes').where(where).count();

    console.log('[wish-center] 查询结果:', {
      dataCount: res.data.length,
      total: countRes.total,
      firstItem: res.data[0] ? { id: res.data[0]._id, title: res.data[0].title } : null
    });
    
    return {
      errCode: 0,
      data: res.data,
      total: countRes.total,
      page,
      pageSize
    };
  },

  /**
   * 获取用户的心愿列表（兼容性方法，调用 getWishList）
   */
  async getWishesByUser(options = {}) {
    // 为了兼容性，直接调用 getWishList 方法
    return await this.getWishList({
      ...options,
      page: options.page || 1,
      pageSize: options.pageSize || 1000,
      includeCompleted: options.includeCompleted !== false // 默认包含已完成的心愿
    });
  },

  /**
   * 根据ID获取单个心愿（用于增量同步）
   */
  async getWishById(wishId) {
    try {
      if (!wishId) {
        return {
          errCode: 400,
          errMsg: '心愿ID不能为空'
        }
      }

      const result = await db.collection('wishes')
        .where({
          _id: wishId,
          userId: this.uid
        })
        .get()

      if (result.data.length === 0) {
        // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回空结果
        console.log('[wish-center] 多设备查询冲突：心愿不存在，可能已被其他设备删除，静默处理');
        return {
          errCode: 0,
          data: [],
          message: 'multi_device_query_conflict_handled'
        }
      }

      return {
        errCode: 0,
        errMsg: '获取成功',
        data: result.data[0]
      }

    } catch (error) {
      console.error('[wish-center] 获取单个心愿失败:', error)
      return {
        errCode: 500,
        errMsg: '获取心愿失败: ' + error.message
      }
    }
  },

  /**
   * 批量获取心愿（用于批量同步）
   */
  async getWishesByIds(wishIds) {
    try {
      if (!Array.isArray(wishIds) || wishIds.length === 0) {
        return {
          errCode: 400,
          errMsg: '心愿ID列表不能为空'
        }
      }

      const result = await db.collection('wishes')
        .where({
          _id: dbCmd.in(wishIds),
          userId: this.uid
        })
        .get()

      return {
        errCode: 0,
        errMsg: '获取成功',
        data: result.data
      }

    } catch (error) {
      console.error('[wish-center] 批量获取心愿失败:', error)
      return {
        errCode: 500,
        errMsg: '批量获取心愿失败: ' + error.message
      }
    }
  },

  /**
   * 创建心愿
   */
  async createWish(wishData) {
    if (!wishData.title) {
      throw new Error('心愿标题不能为空');
    }
    
    const wish = {
      userId: this.uid,
      title: wishData.title,
      description: wishData.description || '',
      image: wishData.image || [],
      video: wishData.video || [],
      audio: wishData.audio || [],
      permission: wishData.permission || 'private',
      groupIds: wishData.groupIds || ['all'],
      tags: wishData.tags || [],
      startDate: wishData.startDate || null,
      completeDate: null,
      isCompleted: false,
      order: 0,
      groupOrder: 0,
      viewCount: 0,
      commentCount: 0,
      createDate: new Date(),
      updateDate: new Date(),

      // 🚀 新增：逻辑时钟支持
      logicalTimestamp: wishData.logicalTimestamp || {
        counter: 1,
        deviceId: wishData.deviceId || 'server',
        timestamp: Date.now()
      },
      lastModifiedDevice: wishData.deviceId || 'server',
      version: 1
    };
    
    if (!wish.groupIds.includes('all')) {
      wish.groupIds.push('all');
    }
    
    // 获取当前最大order值
    const maxOrderRes = await db.collection('wishes')
      .where({ userId: this.uid })
      .orderBy('order', 'desc')
      .limit(1)
      .get();
    
    if (maxOrderRes.data.length > 0) {
      wish.order = maxOrderRes.data[0].order + 1;
    } else {
      wish.order = 1;
    }
    
    // 获取分组内最大groupOrder值
    const maxGroupOrderRes = await db.collection('wishes')
      .where({
        userId: this.uid,
        groupIds: dbCmd.in(wish.groupIds)
      })
      .orderBy('groupOrder', 'desc')
      .limit(1)
      .get();
    
    if (maxGroupOrderRes.data.length > 0) {
      wish.groupOrder = maxGroupOrderRes.data[0].groupOrder + 1;
    } else {
      wish.groupOrder = 1;
    }
    
    const res = await db.collection('wishes').add(wish);

    const newWish = {
      _id: res.id,
      id: res.id,
      ...wish
    };

    // 发送推送通知（不影响主要业务逻辑）
    try {
      await this._sendSyncPush('create', res.id, newWish);
    } catch (pushError) {
      console.error('[wish-center] 推送通知发送失败:', pushError);
      // 推送失败不影响心愿创建
    }

    return {
      errCode: 0,
      data: newWish
    };
  },
  
  /**
   * 更新心愿 - 支持逻辑时钟和冲突检测
   */
  async updateWish(wishId, updates) {
    if (!wishId) {
      throw new Error('心愿ID不能为空');
    }

    // 🚀 获取当前数据进行冲突检测
    const currentResult = await db.collection('wishes')
      .where({
        _id: wishId,
        userId: this.uid
      })
      .get();

    if (currentResult.data.length === 0) {
      // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回成功
      console.log('[wish-center] 多设备冲突：心愿不存在，可能已被其他设备删除，静默处理');
      return {
        errCode: 0,
        data: null,
        message: 'multi_device_conflict_handled'
      };
    }

    const currentWish = currentResult.data[0];

    // 🚀 优化版本冲突检测 - 更智能的冲突处理
    const shouldCheckConflict = updates._forceUpdate !== true; // 允许强制更新跳过冲突检测

    if (shouldCheckConflict) {
      // 检查时间戳冲突而不是版本号冲突
      const currentUpdateTime = new Date(currentWish.updateDate || currentWish.createDate).getTime();
      const incomingUpdateTime = new Date(updates.updateDate || updates.createDate || new Date()).getTime();

      // 🔧 只有当云端数据明显更新时才认为是冲突
      const timeDiff = currentUpdateTime - incomingUpdateTime;
      const isSignificantConflict = timeDiff > 5000; // 5秒以上的时间差才认为是冲突

      if (isSignificantConflict) {
        console.log(`[wish-center] 检测到时间冲突: 云端=${new Date(currentUpdateTime).toISOString()}, 客户端=${new Date(incomingUpdateTime).toISOString()}`);

        // 🔧 静默解决冲突：返回云端数据，但不报错
        return {
          errCode: 0, // 🔧 改为成功，避免客户端显示错误
          data: currentWish, // 返回云端数据
          _conflictResolved: true // 标记冲突已解决
        };
      }
    }

    // 🔧 移除逻辑时钟冲突检测，简化冲突处理
    // 在多设备同步场景下，时间戳比逻辑时钟更可靠

    const updateData = {
      updateDate: new Date(),
      // 🚀 更新版本号和逻辑时钟
      version: (currentWish.version || 1) + 1,
      lastModifiedDevice: updates.deviceId || 'server'
    };

    // 🚀 更新逻辑时钟
    if (updates.logicalTimestamp) {
      updateData.logicalTimestamp = updates.logicalTimestamp;
    }

    const allowedFields = [
      'title', 'description', 'image', 'video', 'audio',
      'permission', 'groupIds', 'tags', 'startDate',
      'completeDate', 'isCompleted', 'order', 'groupOrder'
    ];

    allowedFields.forEach(field => {
      if (updates.hasOwnProperty(field)) {
        updateData[field] = updates[field];
      }
    });

    if (updateData.groupIds && !updateData.groupIds.includes('all')) {
      updateData.groupIds.push('all');
    }

    const res = await db.collection('wishes')
      .where({
        _id: wishId,
        userId: this.uid
      })
      .update(updateData);

    if (res.updated > 0) {
      // 🚀 获取更新后的数据
      const updatedResult = await db.collection('wishes').doc(wishId).get();
      const updatedWish = updatedResult.data[0];

      // 发送推送通知（不影响主要业务逻辑）
      try {
        await this._sendSyncPush('update', wishId, updatedWish);
      } catch (pushError) {
        console.error('[wish-center] 推送通知发送失败:', pushError);
        // 推送失败不影响心愿更新
      }

      return {
        errCode: 0,
        errMsg: '更新成功',
        data: updatedWish
      };
    } else {
      return {
        errCode: -1,
        errMsg: '更新失败'
      };
    }
  },

  /**
   * 🚀 比较逻辑时钟
   */
  compareLogicalTimestamp(ts1, ts2) {
    if (!ts1 || !ts2) return 'unknown';

    if (ts1.counter < ts2.counter) return 'before';
    if (ts1.counter > ts2.counter) return 'after';

    // 计数器相同，比较设备ID
    if (ts1.deviceId < ts2.deviceId) return 'before';
    if (ts1.deviceId > ts2.deviceId) return 'after';

    return 'equal';
  },
  
  /**
   * 删除心愿 - 使用单个操作，避免任何批量操作
   */
  async deleteWish(wishId) {
    console.log('[wish-center] deleteWish called with wishId:', wishId);
    
    if (!wishId) {
      throw new Error('心愿ID不能为空');
    }
    
    try {
      // 先验证心愿存在且属于当前用户
      const wishCheck = await db.collection('wishes')
        .where({
          _id: wishId,
          userId: this.uid
        })
        .get();
      
      console.log('[wish-center] Wish check result:', wishCheck);
      
      if (wishCheck.data.length === 0) {
        // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回成功
        console.log('[wish-center] 多设备删除冲突：心愿不存在，可能已被其他设备删除，静默处理');
        return {
          errCode: 0,
          data: { deleted: 1 },
          message: 'multi_device_delete_conflict_handled'
        };
      }
      
      // 🔧 记录删除时间戳，用于后续的冲突检测
      const deleteTimestamp = new Date();

      // 删除心愿 - 使用单个删除操作
      const deleteResult = await db.collection('wishes')
        .doc(wishId)  // 使用 doc() 而不是 where() 避免批量操作
        .remove();

      console.log('[wish-center] Delete result:', deleteResult);

      if (deleteResult.deleted === 0) {
        throw new Error('删除失败');
      }

      // 发送推送通知（不影响主要业务逻辑）
      try {
        // 🔧 在推送消息中包含删除时间戳
        await this._sendSyncPush('delete', wishId, {
          deleteTimestamp: deleteTimestamp.toISOString()
        });
      } catch (pushError) {
        console.error('[wish-center] 推送通知发送失败:', pushError);
        // 推送失败不影响心愿删除
      }

      // 软删除相关评论
      try {
        const commentUpdate = await db.collection('wish_comments')
          .where({ wishId: wishId })
          .update({ isDeleted: true });
        console.log('[wish-center] Comments soft delete result:', commentUpdate);
      } catch (commentError) {
        console.warn('[wish-center] Comment update failed (non-critical):', commentError);
      }

      // 删除相关消息
      try {
        const messageDelete = await db.collection('messages')
          .where({ wishId: wishId })
          .remove();
        console.log('[wish-center] Messages delete result:', messageDelete);
      } catch (messageError) {
        console.warn('[wish-center] Message delete failed (non-critical):', messageError);
      }

      return {
        errCode: 0,
        deleted: deleteResult.deleted
      };
    } catch (error) {
      console.error('[wish-center] deleteWish failed:', error);
      throw new Error(error.message || '删除心愿失败');
    }
  },
  
  /**
   * 完成心愿
   */
  async completeWish(wishId) {
    if (!wishId) {
      throw new Error('心愿ID不能为空');
    }
    
    const checkRes = await db.collection('wishes')
      .where({
        _id: wishId,
        userId: this.uid
      })
      .count();
    
    if (checkRes.total === 0) {
      // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回成功
      console.log('[wish-center] 多设备恢复冲突：心愿不存在，可能已被其他设备删除，静默处理');
      return {
        errCode: 0,
        data: null,
        message: 'multi_device_restore_conflict_handled'
      };
    }
    
    // 🔧 先获取当前数据以便更新版本号
    const currentResult = await db.collection('wishes')
      .where({
        _id: wishId,
        userId: this.uid
      })
      .get();

    if (currentResult.data.length === 0) {
      // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回成功
      console.log('[wish-center] 多设备状态更新冲突：心愿不存在，可能已被其他设备删除，静默处理');
      return {
        errCode: 0,
        data: null,
        message: 'multi_device_status_conflict_handled'
      };
    }

    const currentWish = currentResult.data[0];

    const updateData = {
      isCompleted: true,
      completeDate: new Date(),
      updateDate: new Date(),
      // 🔧 重要：更新版本号
      version: (currentWish.version || 1) + 1
    };

    const res = await db.collection('wishes')
      .where({
        _id: wishId,
        userId: this.uid
      })
      .update(updateData);

    if (res.updated > 0) {
      // 🔧 返回更新后的完整数据，包含新版本号
      const updatedResult = await db.collection('wishes').doc(wishId).get();
      const updatedWish = updatedResult.data[0];

      return {
        errCode: 0,
        updated: res.updated,
        data: updatedWish
      };
    } else {
      return {
        errCode: -1,
        errMsg: '完成操作失败'
      };
    }
  },
  
  /**
   * 恢复心愿（取消完成）
   */
  async restoreWish(wishId) {
    if (!wishId) {
      throw new Error('心愿ID不能为空');
    }
    
    const checkRes = await db.collection('wishes')
      .where({
        _id: wishId,
        userId: this.uid
      })
      .count();
    
    if (checkRes.total === 0) {
      // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回成功
      console.log('[wish-center] 多设备分组更新冲突：心愿不存在，可能已被其他设备删除，静默处理');
      return {
        errCode: 0,
        data: null,
        message: 'multi_device_group_conflict_handled'
      };
    }
    
    const wish = await db.collection('wishes')
      .where({
        _id: wishId,
        userId: this.uid
      })
      .get();

    const currentWish = wish.data[0];

    const updateData = {
      isCompleted: false,
      lastCompleteDate: currentWish.completeDate, // 保存之前的完成时间
      completeDate: null,
      updateDate: new Date(),
      // 🔧 重要：更新版本号
      version: (currentWish.version || 1) + 1
    };

    const res = await db.collection('wishes')
      .where({
        _id: wishId,
        userId: this.uid
      })
      .update(updateData);

    if (res.updated > 0) {
      // 🔧 返回更新后的完整数据，包含新版本号
      const updatedResult = await db.collection('wishes').doc(wishId).get();
      const updatedWish = updatedResult.data[0];

      return {
        errCode: 0,
        updated: res.updated,
        data: updatedWish
      };
    } else {
      return {
        errCode: -1,
        errMsg: '恢复操作失败'
      };
    }
  },
  
  /**
   * 更新心愿排序
   */
  async updateWishOrder(orderedIds, sortType = 'global', groupId = null) {
    if (!Array.isArray(orderedIds) || orderedIds.length === 0) {
      throw new Error('排序数据无效');
    }
    
    const orderField = sortType === 'global' ? 'order' : 'groupOrder';
    
    const promises = orderedIds.map((id, index) => {
      const updateData = {
        [orderField]: index + 1,
        updateDate: new Date()
      };
      
      return db.collection('wishes')
        .where({
          _id: id,
          userId: this.uid
        })
        .update(updateData);
    });
    
    await Promise.all(promises);
    
    return {
      errCode: 0,
      message: '排序更新成功'
    };
  },
  
  /**
   * 获取单个心愿详情
   */
  async getWishDetail(wishId) {
    if (!wishId) {
      throw new Error('心愿ID不能为空');
    }
    
    const wishRes = await db.collection('wishes')
      .where({
        _id: wishId
      })
      .get();
    
    if (wishRes.data.length === 0) {
      throw new Error('心愿不存在');
    }
    
    const wish = wishRes.data[0];
    
    if (wish.userId !== this.uid) {
      if (wish.permission === 'private') {
        throw new Error('无权限查看此心愿');
      }
      
      if (wish.permission === 'friends') {
        // TODO: 检查是否是朋友关系
      }
      
      await db.collection('wishes')
        .where({ _id: wishId })
        .update({
          viewCount: dbCmd.inc(1)
        });
    }
    
    return {
      errCode: 0,
      data: wish
    };
  },
  
  /**
   * 获取同步摘要信息（轻量级API）
   * @param {Object} params 
   * @param {string} params.lastSyncTime - 上次同步时间
   * @param {number} params.localDataCount - 本地数据数量
   * @param {string} params.localLastModified - 本地最后修改时间
   */
  async getSyncSummary(params = {}) {
    const { lastSyncTime, localDataCount, localLastModified } = params;
    
    try {
        // 获取用户心愿的总数和最后修改时间
        const countResult = await db.collection('wishes')
            .where({ userId: this.uid })
            .count();
            
        const latestResult = await db.collection('wishes')
            .where({ userId: this.uid })
            .orderBy('updateDate', 'desc')
            .orderBy('createDate', 'desc')
            .limit(1)
            .get();
        
        const cloudDataCount = countResult.total;
        const cloudLastModified = latestResult.data.length > 0 
            ? (latestResult.data[0].updateDate || latestResult.data[0].createDate)
            : null;
        
        // 生成简单的数据校验和
        const allWishesResult = await db.collection('wishes')
            .where({ userId: this.uid })
            .field({
                _id: true,
                title: true,
                updateDate: true,
                createDate: true,
                isCompleted: true
            })
            .get();
        
        // 计算校验和（内联方式避免方法调用问题）
        const checksum = (() => {
          const dataStr = allWishesResult.data
              .map(w => `${w._id}:${w.title}:${w.updateDate || w.createDate}:${w.isCompleted}`)
              .sort()
              .join('|');
          
          let hash = 0;
          for (let i = 0; i < dataStr.length; i++) {
              const char = dataStr.charCodeAt(i);
              hash = ((hash << 5) - hash) + char;
              hash = hash & hash;
          }
          return hash.toString(36);
        })();
        
        return {
            errCode: 0,
            data: {
                count: cloudDataCount,
                lastModified: cloudLastModified,
                checksum: checksum,
                serverTime: new Date().toISOString()
            }
        };
        
    } catch (error) {
        console.error('[wish-center] getSyncSummary error:', error);
        return {
            errCode: 1,
            errMsg: '获取同步摘要失败: ' + error.message
        };
    }
  },


  
  /**
   * 获取增量变更（增量同步API）
   * @param {Object} params
   * @param {string} params.since - 获取此时间之后的变更
   * @param {boolean} params.includeDeleted - 是否包含已删除的记录
   */
  async getIncrementalChanges(params = {}) {
    const { since, includeDeleted = false } = params;
    
    try {
        // 构建查询条件
        let where = { userId: this.uid };
        if (since) {
            where.updateDate = db.command.gt(since);
        }
        
        // 获取变更的数据
        const result = await db.collection('wishes')
            .where(where)
            .orderBy('updateDate', 'asc')
            .get();
        
        // 分类数据
        const additions = [];
        const updates = [];
        const deletions = [];
        
        result.data.forEach(item => {
            if (since && new Date(item.createDate) > new Date(since)) {
                additions.push(item);
            } else {
                updates.push(item);
            }
        });
        
        return {
            errCode: 0,
            data: {
                additions,
                updates,
                deletions,
                serverTime: new Date().toISOString()
            }
        };
        
    } catch (error) {
        console.error('getIncrementalChanges error:', error);
        return {
            errCode: 1006,
            errMsg: '获取增量变更失败'
        };
    }
  },
  
  /**
   * 强制更新心愿（冲突解决API）
   * @param {string} id - 心愿ID
   * @param {Object} data - 更新数据
   */
  async forceUpdateWish(id, data) {
    try {
        
        // 强制更新，忽略版本冲突
        const updateData = {
            ...data,
            updateDate: new Date().toISOString(),
            userId: this.uid // 确保所有权
        };
        
        // 移除内部字段
        delete updateData._forceUpdate;
        delete updateData._conflictResolution;
        delete updateData._needSync;
        delete updateData._source;
        
        const result = await db.collection('wishes')
            .where({ _id: id, userId: this.uid })
            .update(updateData);
        
        if (result.updated === 0) {
            // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回成功
            console.log('[wish-center] 多设备批量更新冲突：心愿不存在，可能已被其他设备删除，静默处理');
            return {
                errCode: 0,
                data: null,
                message: 'multi_device_batch_update_conflict_handled'
            };
        }
        
        return {
            errCode: 0,
            data: { updated: result.updated }
        };
        
    } catch (error) {
        console.error('forceUpdateWish error:', error);
        return {
            errCode: 1006,
            errMsg: '强制更新失败'
        };
    }
  }
}; 
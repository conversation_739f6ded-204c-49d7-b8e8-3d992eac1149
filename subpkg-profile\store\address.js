import { defineStore } from 'pinia'

export const useAddressStore = defineStore('address', {
  state: () => ({
    addressList: [],
    defaultAddress: null,
    loading: false,
    lastSyncTime: null // 最后同步时间
  }),

  getters: {
    // 获取默认地址
    getDefaultAddress: (state) => {
      return state.addressList.find(addr => addr.isDefault) || null
    },

    // 获取所有有效地址
    getValidAddresses: (state) => {
      return state.addressList.filter(addr => addr.status !== 0)
    }
  },

  actions: {
    /**
     * 设置地址列表
     */
    setAddressList(list) {
      this.addressList = list || []
    },

    /**
     * 添加地址
     */
    addAddress(address) {
      if (address) {
        this.addressList.push(address)
      }
    },

    /**
     * 更新地址
     */
    updateAddress(addressId, updatedData) {
      const index = this.addressList.findIndex(addr => addr._id === addressId)
      if (index !== -1) {
        this.addressList[index] = { ...this.addressList[index], ...updatedData }
      }
    },

    /**
     * 删除地址
     */
    removeAddress(addressId) {
      this.addressList = this.addressList.filter(addr => addr._id !== addressId)
    },

    /**
     * 设置默认地址
     */
    async setDefaultAddress(addressId) {
      try {
        // 调用云函数设置默认地址
        const addressCenter = uniCloud.importObject('address-center')
        const result = await addressCenter.setDefaultAddress(addressId)

        if (result.code === 0) {
          // 云端设置成功，更新本地状态
          this.addressList.forEach(addr => {
            addr.isDefault = false
          })

          // 设置新的默认地址
          const targetAddress = this.addressList.find(addr => addr._id === addressId)
          if (targetAddress) {
            targetAddress.isDefault = true
            this.defaultAddress = targetAddress
          }
        }

        return result
      } catch (error) {
        console.error('设置默认地址失败:', error)
        return {
          code: -1,
          message: error.message || '设置默认地址失败'
        }
      }
    },

    /**
     * 清除默认地址状态（仅本地）
     */
    _clearDefaultAddress() {
      this.addressList.forEach(addr => {
        addr.isDefault = false
      })
      this.defaultAddress = null
    },

    /**
     * 获取地址列表
     */
    async fetchAddressList() {
      try {
        this.loading = true
        const addressCenter = uniCloud.importObject('address-center')
        const result = await addressCenter.getAddressList()
        
        if (result.code === 0) {
          this.setAddressList(result.data.list)
          return result.data
        } else {
          throw new Error(result.message || '获取地址列表失败')
        }
      } catch (error) {
        console.error('获取地址列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取默认地址（从云端）
     */
    async fetchDefaultAddress() {
      try {
        const addressCenter = uniCloud.importObject('address-center')
        const result = await addressCenter.getDefaultAddress()
        
        if (result.code === 0) {
          this.defaultAddress = result.data
          return result.data
        } else {
          this.defaultAddress = null
          return null
        }
      } catch (error) {
        console.error('获取默认地址失败:', error)
        this.defaultAddress = null
        return null
      }
    },

    /**
     * 清空地址数据
     */
    clearAddressData() {
      this.addressList = []
      this.defaultAddress = null
    }
  }
})

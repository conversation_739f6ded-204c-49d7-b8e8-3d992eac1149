<view class="page-container"><view class="form-label">标题</view><input class="{{['form-input', a && 'input-conflict']}}" type="text" placeholder="请输入心愿标题" bindinput="{{b}}" value="{{c}}"/><view wx:if="{{d}}" class="conflict-warning"> ⚠️ 已存在相同标题的心愿，请使用不同的标题 </view><view class="form-label">描述</view><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入心愿描述" value="{{e}}" bindinput="{{f}}"></textarea></block><view class="form-label">图片/视频/音频</view><view class="media-type-tabs"><view class="{{['media-tab', g && 'active']}}" bindtap="{{h}}">图片</view><view class="{{['media-tab', i && 'active']}}" bindtap="{{j}}">视频</view><view class="{{['media-tab', k && 'active']}}" bindtap="{{l}}">音频</view></view><view wx:if="{{m}}" class="image-grid"><view wx:for="{{n}}" wx:for-item="img" wx:key="c" class="image-item" bindtap="{{img.d}}"><image class="thumbnail" src="{{img.a}}" mode="aspectFill"></image><view class="delete-icon" catchtap="{{img.b}}">×</view></view><view wx:if="{{o}}" class="image-uploader" bindtap="{{p}}"><text class="upload-icon">+</text></view></view><view wx:if="{{q}}" class="video-grid"><view wx:for="{{r}}" wx:for-item="video" wx:key="c" class="video-item"><video class="video-thumbnail" src="{{video.a}}" controls poster="{{s}}"></video><view class="delete-icon" catchtap="{{video.b}}">×</view></view><view wx:if="{{t}}" class="video-uploader" bindtap="{{v}}"><text class="upload-icon">+</text><text class="upload-text">添加视频</text></view></view><view wx:if="{{w}}" class="audio-list"><view wx:for="{{x}}" wx:for-item="audio" wx:key="d" class="audio-item"><view class="audio-info"><view class="audio-icon">🎵</view><view class="audio-name">音频 {{audio.a}}</view></view><view class="audio-controls"><view class="audio-play" bindtap="{{audio.b}}">播放</view><view class="audio-delete" bindtap="{{audio.c}}">删除</view></view></view><view wx:if="{{y}}" class="audio-uploader" bindtap="{{z}}"><text class="upload-icon">+</text><text class="upload-text">添加音频</text></view></view><view class="form-label">心愿起止时间</view><view class="date-section"><view class="date-item"><picker mode="date" value="{{C}}" bindchange="{{D}}" class="date-picker"><view class="picker-value"><text wx:if="{{A}}" class="picker-placeholder">开始时间</text><text wx:else>{{B}}</text></view></picker></view><view class="date-item"><picker mode="date" value="{{G}}" bindchange="{{H}}" class="date-picker"><view class="picker-value"><text wx:if="{{E}}" class="picker-placeholder">完成时间</text><text wx:else>{{F}}</text></view></picker></view></view><view class="form-label">权限设置</view><view class="permission-options"><view class="{{['permission-option', I && 'active']}}" bindtap="{{J}}"><view class="permission-icon">🔒</view><view class="permission-name">私密</view></view><view class="{{['permission-option', K && 'active']}}" bindtap="{{L}}"><view class="permission-icon">👥</view><view class="permission-name">朋友可见</view></view><view class="{{['permission-option', M && 'active']}}" bindtap="{{N}}"><view class="permission-icon">🌍</view><view class="permission-name">公开</view></view></view><view class="form-label">分组</view><view class="group-container"><view wx:if="{{O}}" class="no-groups"><text>加载分组数据中...</text></view><view class="group-list-wrap"><view wx:for="{{P}}" wx:for-item="group" wx:key="b" class="{{['group-item', group.c && 'active']}}" bindtap="{{group.d}}" bindlongpress="{{group.e}}">{{group.a}}</view><view class="group-item add-group" bindtap="{{Q}}"><view class="add-icon-wrapper"><text class="add-icon">+</text><text class="add-text">添加</text></view></view></view></view><view class="action-footer"><button class="btn btn-cancel" bindtap="{{R}}">取消</button><button class="btn btn-save" bindtap="{{S}}">保存</button></view></view>
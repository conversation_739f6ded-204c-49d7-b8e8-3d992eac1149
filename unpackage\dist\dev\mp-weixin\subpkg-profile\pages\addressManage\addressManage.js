"use strict";
const common_vendor = require("../../../common/vendor.js");
const subpkgProfile_store_address = require("../../store/address.js");
const store_user = require("../../../store/user.js");
const _sfc_main = {
  data() {
    return {
      addressList: [],
      loading: false,
      addressStore: null,
      userStore: null
    };
  },
  onLoad() {
    this.initStore();
    if (!this.userStore.isLogin) {
      common_vendor.index.__f__("log", "at subpkg-profile/pages/addressManage/addressManage.vue:78", "[addressManage] 用户未登录，等待导航拦截器处理");
      return;
    }
    this.getAddressList();
  },
  onShow() {
    if (this.userStore && this.userStore.isLogin) {
      this.checkAndSyncAddressData();
    }
  },
  methods: {
    // 初始化Store
    initStore() {
      this.addressStore = subpkgProfile_store_address.useAddressStore();
      this.userStore = store_user.useUserStore();
    },
    // 检查并同步地址数据
    async checkAndSyncAddressData() {
      if (this.loading)
        return;
      common_vendor.index.__f__("log", "at subpkg-profile/pages/addressManage/addressManage.vue:103", "[AddressManage] 检查地址数据同步状态...");
      const hasLocalData = this.addressStore && this.addressStore.addressList.length > 0;
      const lastSyncTime = this.addressStore ? this.addressStore.lastSyncTime : null;
      const now = Date.now();
      const shouldSync = !hasLocalData || !lastSyncTime || now - lastSyncTime > 5 * 60 * 1e3;
      if (shouldSync) {
        common_vendor.index.__f__(
          "log",
          "at subpkg-profile/pages/addressManage/addressManage.vue:114",
          "[AddressManage] 需要同步地址数据，原因:",
          !hasLocalData ? "无本地数据" : !lastSyncTime ? "未记录同步时间" : "超过5分钟未同步"
        );
        await this.getAddressList();
      } else {
        common_vendor.index.__f__("log", "at subpkg-profile/pages/addressManage/addressManage.vue:119", "[AddressManage] 使用本地缓存数据");
        this.addressList = this.addressStore.addressList;
      }
    },
    // 获取地址列表
    async getAddressList() {
      if (this.loading)
        return;
      this.loading = true;
      try {
        const result = await common_vendor.nr.importObject("address-center").getAddressList();
        if (result.code === 0) {
          this.addressList = result.data.list;
          if (this.addressStore) {
            this.addressStore.setAddressList(result.data.list);
            this.addressStore.lastSyncTime = Date.now();
          }
          common_vendor.index.__f__("log", "at subpkg-profile/pages/addressManage/addressManage.vue:139", "[AddressManage] 地址数据同步完成，共", result.data.list.length, "条");
        } else {
          common_vendor.index.showToast({
            title: result.message || "获取地址列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-profile/pages/addressManage/addressManage.vue:147", "获取地址列表失败:", error);
        common_vendor.index.showToast({
          title: "网络错误",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 添加新地址
    addNewAddress() {
      common_vendor.index.navigateTo({
        url: "/subpkg-profile/pages/addressEdit/addressEdit"
      });
    },
    // 编辑地址
    editAddress(address, index) {
      common_vendor.index.navigateTo({
        url: `/subpkg-profile/pages/addressEdit/addressEdit?id=${address._id}`
      });
    },
    // 切换默认地址状态
    async toggleDefaultAddress(addressId, index, event) {
      if (!addressId) {
        common_vendor.index.showToast({
          title: "地址ID无效",
          icon: "none"
        });
        return;
      }
      const isChecked = event.detail.value;
      try {
        common_vendor.index.showLoading({ title: isChecked ? "设置中..." : "取消中..." });
        let result;
        if (isChecked) {
          if (this.addressStore) {
            result = await this.addressStore.setDefaultAddress(addressId);
          } else {
            result = await common_vendor.nr.importObject("address-center").setDefaultAddress(addressId);
          }
        } else {
          result = await common_vendor.nr.importObject("address-center").updateAddress(addressId, {
            is_default: false
          });
          if (this.addressStore && result && result.code === 0) {
            this.addressStore._clearDefaultAddress();
          }
        }
        common_vendor.index.__f__("log", "at subpkg-profile/pages/addressManage/addressManage.vue:207", "[AddressManage] toggleDefaultAddress result:", result);
        if (result && result.code === 0) {
          common_vendor.index.showToast({
            title: isChecked ? "设置成功" : "已取消默认",
            icon: "success"
          });
          setTimeout(() => {
            this.getAddressList();
          }, 800);
        } else {
          common_vendor.index.showToast({
            title: result && result.message || "操作失败",
            icon: "none",
            duration: 3e3
          });
          this.getAddressList();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-profile/pages/addressManage/addressManage.vue:228", "操作异常:", error);
        common_vendor.index.showToast({
          title: "操作失败: " + (error.message || error),
          icon: "none",
          duration: 3e3
        });
        this.getAddressList();
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 删除地址
    async deleteAddress(addressId, index) {
      const confirmResult = await new Promise((resolve) => {
        common_vendor.index.showModal({
          title: "删除地址",
          content: "确定要删除这个地址吗？",
          success: (res) => resolve(res.confirm)
        });
      });
      if (!confirmResult)
        return;
      try {
        common_vendor.index.showLoading({ title: "删除中..." });
        const result = await common_vendor.nr.importObject("address-center").deleteAddress(addressId);
        if (result.code === 0) {
          common_vendor.index.showToast({
            title: "删除成功",
            icon: "success"
          });
          this.getAddressList();
        } else {
          common_vendor.index.showToast({
            title: result.message || "删除失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-profile/pages/addressManage/addressManage.vue:271", "删除地址失败:", error);
        common_vendor.index.showToast({
          title: "网络错误",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading().catch(() => {
        });
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.addressList.length === 0 && !$data.loading
  }, $data.addressList.length === 0 && !$data.loading ? {
    b: common_vendor.p({
      type: "plusempty",
      size: "20",
      color: "#fff"
    }),
    c: common_vendor.o((...args) => $options.addNewAddress && $options.addNewAddress(...args))
  } : {
    d: common_vendor.f($data.addressList, (address, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(address.name),
        b: common_vendor.t(address.phone),
        c: address.isDefault
      }, address.isDefault ? {} : {}, {
        d: common_vendor.t(address.province),
        e: common_vendor.t(address.city),
        f: common_vendor.t(address.district),
        g: common_vendor.t(address.address),
        h: "35d57f10-1-" + i0,
        i: common_vendor.o(($event) => $options.editAddress(address, index), address._id),
        j: "35d57f10-2-" + i0,
        k: common_vendor.o(($event) => $options.deleteAddress(address._id, index), address._id),
        l: address.isDefault,
        m: common_vendor.o(($event) => $options.toggleDefaultAddress(address._id, index, $event), address._id),
        n: address._id
      });
    }),
    e: common_vendor.p({
      type: "compose",
      size: "16",
      color: "#8a2be2"
    }),
    f: common_vendor.p({
      type: "trash",
      size: "16",
      color: "#f56c6c"
    }),
    g: common_vendor.p({
      type: "plusempty",
      size: "20",
      color: "#fff"
    }),
    h: common_vendor.o((...args) => $options.addNewAddress && $options.addNewAddress(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpkg-profile/pages/addressManage/addressManage.js.map

"use strict";
const common_vendor = require("../../common/vendor.js");
const store_wish = require("../../store/wish.js");
const store_group = require("../../store/group.js");
const store_user = require("../../store/user.js");
const store_message = require("../../store/message.js");
const store_comment = require("../../store/comment.js");
const utils_syncManager = require("../../utils/syncManager.js");
const utils_animationPerformance = require("../../utils/animationPerformance.js");
const utils_gestureManager = require("../../utils/gestureManager.js");
const utils_envUtils = require("../../utils/envUtils.js");
const WishCard = () => "../../components/WishCard/WishCard.js";
const GroupSelector = () => "../../components/GroupSelector/GroupSelector.js";
const _sfc_main = {
  components: {
    WishCard,
    GroupSelector
  },
  // 添加页面滚动处理函数
  onPageScroll(e) {
    if (e && e.scrollTop > 10) {
      common_vendor.index.$emit("page-scroll-close-cards", {
        scrollTop: e.scrollTop,
        timestamp: Date.now()
      });
    }
  },
  // 新增的方法：强制重置卡片状态
  forceResetCard(card) {
    try {
      if (card.moveX !== void 0 && card.startX !== void 0) {
        card.moveX = card.startX;
      }
      if (card.isOpen !== void 0) {
        card.isOpen = false;
      }
      if (card.resetSwipe) {
        card.resetSwipe();
      }
      if (card.swipeRef && card.swipeRef.style) {
        card.swipeRef.style.transform = "translateX(0px)";
      }
    } catch (e) {
      utils_envUtils.devLog.error("重置卡片状态失败", e);
    }
  },
  // 新增的方法：通知所有卡片组件滚动事件发生
  notifyCardsOfScroll() {
    common_vendor.index.$emit("page-scroll-event", {
      timestamp: Date.now(),
      shouldClose: true
    });
  },
  // 添加页面触底处理
  onReachBottom() {
    common_vendor.index.$emit("page-reach-bottom-close-cards", {
      timestamp: Date.now()
    });
  },
  // 添加页面下拉刷新 - 改为组合式API外部版本
  async onPullDownRefresh() {
    common_vendor.index.$emit("page-pull-down-refresh-start", {
      timestamp: Date.now()
    });
  },
  // 微信小程序分享给朋友
  onShareAppMessage(res) {
    if (res.from === "button" && res.target && res.target.dataset && res.target.dataset.index !== void 0) {
      const index = Number(res.target.dataset.index || 0);
      const wish = this.wishList[index] || {};
      return {
        title: wish.title || "分享我的心愿",
        path: `/pages/wishDetail/wishDetail?id=${wish.id}`,
        imageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? wish.image[0] : wish.image || "/static/images/share-image.png"
      };
    }
    if (res.from === "button" && res.target && res.target.dataset && (res.target.dataset.groupId || res.target.dataset.shareType === "group")) {
      const groupId = res.target.dataset.groupId;
      const groupName = res.target.dataset.groupName || "分组";
      return {
        title: `分享"${groupName}"分组的心愿清单`,
        path: `/pages/index/index?groupId=${groupId}`,
        imageUrl: "/static/images/share-image.png"
      };
    }
    const shareGroupInfo = common_vendor.index.getStorageSync("current_share_group");
    if (shareGroupInfo && Date.now() - shareGroupInfo.timestamp < 3e3) {
      const groupId = shareGroupInfo.groupId;
      const groupName = shareGroupInfo.groupName;
      common_vendor.index.removeStorageSync("current_share_group");
      return {
        title: `分享"${groupName}"分组的心愿清单`,
        path: `/pages/index/index?groupId=${groupId}`,
        imageUrl: "/static/images/share-image.png"
      };
    }
    const currentGroupId = this.wishStore.currentGroupId;
    const groupStore = this.groupStore;
    const currentGroup = groupStore.getGroupById(currentGroupId);
    if (currentGroup && currentGroupId !== "all") {
      return {
        title: `分享"${currentGroup.name}"分组的心愿清单`,
        path: `/pages/index/index?groupId=${currentGroupId}`,
        imageUrl: "/static/images/share-image.png"
      };
    }
    return {
      title: "心愿清单 - 记录你的美好心愿",
      path: "/pages/index/index",
      imageUrl: "/static/images/share-image.png"
    };
  },
  // 微信小程序分享到朋友圈
  onShareTimeline() {
    const shareGroupInfo = common_vendor.index.getStorageSync("current_share_group");
    if (shareGroupInfo && Date.now() - shareGroupInfo.timestamp < 3e3) {
      const groupId = shareGroupInfo.groupId;
      const groupName = shareGroupInfo.groupName;
      return {
        title: `分享"${groupName}"分组的心愿清单`,
        query: `groupId=${groupId}`,
        imageUrl: "/static/images/share-group-image.png"
      };
    }
    const currentGroupId = this.wishStore.currentGroupId;
    const groupStore = this.groupStore;
    const currentGroup = groupStore.getGroupById(currentGroupId);
    if (currentGroup && currentGroupId !== "all") {
      return {
        title: `分享"${currentGroup.name}"分组的心愿清单`,
        query: `groupId=${currentGroupId}`,
        imageUrl: "/static/images/share-group-image.png"
      };
    }
    return {
      title: "心愿清单 - 记录你的美好心愿",
      query: "",
      imageUrl: "/static/images/share-image.png"
    };
  },
  setup() {
    const wishStore = store_wish.useWishStore();
    const groupStore = store_group.useGroupStore();
    const userStore = store_user.useUserStore();
    const messageStore = store_message.useMessageStore();
    store_comment.useCommentStore();
    const wishCards = common_vendor.ref([]);
    const containerRef = common_vendor.ref(null);
    common_vendor.ref(0);
    const windowHeight = common_vendor.ref(0);
    const activeCardId = common_vendor.ref(null);
    common_vendor.onMounted(() => {
      {
        utils_animationPerformance.animationMonitor.startFPSMonitoring();
      }
      utils_gestureManager.gestureManager.setPageInstance(common_vendor.getCurrentInstance());
    });
    const lastTouchY = common_vendor.ref(void 0);
    const isSyncing = common_vendor.ref(false);
    const isPullRefreshDisabled = common_vendor.ref(false);
    const isDragging = common_vendor.ref(false);
    const dragStartIndex = common_vendor.ref(-1);
    const dragCurrentIndex = common_vendor.ref(-1);
    const initialOrder = common_vendor.ref([]);
    const dragStartY = common_vendor.ref(0);
    const dragThrottled = common_vendor.ref(false);
    const cardHeight = common_vendor.ref(160);
    const wishList = common_vendor.computed(() => wishStore.currentGroupWishes);
    const forceUpdateKey = common_vendor.ref(0);
    const isLogin = common_vendor.computed(() => userStore.isLoggedIn);
    const isDevelopment = common_vendor.ref(true);
    common_vendor.index.getSystemInfo({
      success: (res) => {
        windowHeight.value = res.windowHeight;
      }
    });
    common_vendor.index.$on("page-show", () => {
    });
    common_vendor.index.$on("page-scroll-close-cards", (data) => {
      utils_envUtils.devLog.log("[Index] 收到页面滚动关闭卡片事件:", data);
      if (activeCardId.value !== null) {
        closeAllSwipeActions();
      }
    });
    common_vendor.index.$on("page-reach-bottom-close-cards", (data) => {
      utils_envUtils.devLog.log("[Index] 收到页面触底关闭卡片事件:", data);
      closeAllSwipeActions();
    });
    common_vendor.index.$on("page-pull-down-refresh-start", async (data) => {
      utils_envUtils.devLog.log("[Index] 收到下拉刷新开始事件:", data);
      if (!utils_gestureManager.gestureManager.canStartGesture("pullRefresh") || isPullRefreshDisabled.value) {
        utils_envUtils.devLog.log("[Index] 拖拽进行中或已被禁用，忽略下拉刷新");
        common_vendor.index.stopPullDownRefresh();
        return;
      }
      if (!utils_gestureManager.gestureManager.startPullRefresh()) {
        common_vendor.index.stopPullDownRefresh();
        return;
      }
      closeAllSwipeActions();
      try {
        await forceSyncData();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:335", "[Index] 下拉刷新同步失败:", error);
      } finally {
        utils_gestureManager.gestureManager.endPullRefresh();
        common_vendor.index.stopPullDownRefresh();
      }
    });
    common_vendor.index.$on("page-pull-down-refresh", async (data) => {
      utils_envUtils.devLog.log("[Index] 收到下拉刷新事件:", data);
      closeAllSwipeActions();
      try {
        await forceSyncData();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:353", "[Index] 下拉刷新同步失败:", error);
      } finally {
        utils_gestureManager.gestureManager.endPullRefresh();
        common_vendor.index.stopPullDownRefresh();
      }
    });
    common_vendor.index.$on("gesture-disable-pull-refresh", (data) => {
      utils_envUtils.devLog.log("[Index] 手势管理器禁用下拉刷新:", data);
      isPullRefreshDisabled.value = true;
    });
    common_vendor.index.$on("gesture-enable-pull-refresh", (data) => {
      utils_envUtils.devLog.log("[Index] 手势管理器启用下拉刷新:", data);
      setTimeout(() => {
        isPullRefreshDisabled.value = false;
      }, 200);
    });
    common_vendor.index.$on("wish-list-updated", async (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:379", "[Index] 收到心愿列表更新事件:", data);
      await common_vendor.nextTick$1();
      common_vendor.index.__f__("log", "at pages/index/index.vue:385", `[Index] 处理${data.action}操作，强制刷新界面`);
      if (data.updateCounter && data.updateCounter > wishStore.listUpdateCounter) {
        wishStore.listUpdateCounter = data.updateCounter;
      }
      forceUpdateKey.value++;
      await common_vendor.nextTick$1();
      common_vendor.index.__f__("log", "at pages/index/index.vue:398", `[Index] ${data.action}操作界面更新完成，forceUpdateKey: ${forceUpdateKey.value}`);
    });
    common_vendor.index.$on("user-login-success", async (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:403", "[Index] User login success event received:", data);
      const wasDisabled = isPullRefreshDisabled.value;
      isPullRefreshDisabled.value = true;
      try {
        common_vendor.index.__f__("log", "at pages/index/index.vue:411", "[Index] Reinitializing stores after login...");
        await new Promise((resolve) => {
          setTimeout(() => {
            groupStore.resetGroups();
            wishStore.clearLocalData();
            resolve();
          }, 100);
        });
        await groupStore.initGroups();
        common_vendor.index.__f__("log", "at pages/index/index.vue:426", "[Index] Group store reinitialized");
        await wishStore.initWishList();
        common_vendor.index.__f__("log", "at pages/index/index.vue:430", "[Index] Wish store reinitialized");
        await messageStore.initMessages();
        common_vendor.index.__f__("log", "at pages/index/index.vue:434", "[Index] Message store reinitialized");
        common_vendor.index.__f__("log", "at pages/index/index.vue:436", "[Index] Stores reinitialized successfully");
        common_vendor.index.__f__("log", "at pages/index/index.vue:439", "[Index] Page data refreshed after login");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:441", "[Index] Error reinitializing stores:", error);
      } finally {
        setTimeout(() => {
          if (!isDragging.value) {
            isPullRefreshDisabled.value = wasDisabled;
          }
        }, 500);
      }
    });
    common_vendor.index.$on("page-load", async (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:454", "[Index] 收到页面加载事件:", data);
      const options = data.options;
      if (options && options.groupId) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:459", "[Index] Setting group from URL params:", options.groupId);
        wishStore.setCurrentGroup(options.groupId);
      }
      try {
        common_vendor.index.__f__("log", "at pages/index/index.vue:466", "[Index] Initializing groups...");
        await groupStore.initGroups();
        common_vendor.index.__f__("log", "at pages/index/index.vue:470", "[Index] Initializing wishes...");
        await wishStore.initWishList();
        common_vendor.index.__f__("log", "at pages/index/index.vue:473", "[Index] Data initialization completed");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:475", "[Index] Data initialization failed:", error);
      }
    });
    common_vendor.index.$on("page-show-lifecycle", (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:481", "[Index] 收到页面显示生命周期事件:", data);
      common_vendor.index.$emit("page-show");
      if (userStore.isLoggedIn) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:488", "[Index] User is logged in, refreshing data...");
        wishStore.refreshWishList();
        checkUnreadMessages();
      } else {
        common_vendor.index.__f__("log", "at pages/index/index.vue:496", "[Index] User not logged in");
        if (groupStore.getAllGroups.length === 0) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:500", "[Index] Ensuring default groups are available");
          groupStore.ensureDefaultGroups();
        }
      }
      common_vendor.index.__f__("log", "at pages/index/index.vue:506", "[Index] Page shown");
    });
    common_vendor.index.$on("page-hide-lifecycle", (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:511", "[Index] 收到页面隐藏生命周期事件:", data);
      onPageHide();
    });
    common_vendor.index.$on("silent-refresh-data", async (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:517", "[Index] 收到静默刷新事件:", data);
      try {
        await forceSyncData();
        common_vendor.index.__f__("log", "at pages/index/index.vue:522", "[Index] 静默刷新完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:524", "[Index] 静默刷新失败:", error);
      }
    });
    common_vendor.index.$on("lock-page-scroll", (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:530", "锁定页面滚动", data);
      common_vendor.index.$emit("page-scroll-locked", {
        timestamp: Date.now(),
        source: data.source || "unknown"
      });
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.$off("page-show");
      common_vendor.index.$off("page-scroll-close-cards");
      common_vendor.index.$off("page-reach-bottom-close-cards");
      common_vendor.index.$off("page-pull-down-refresh");
      common_vendor.index.$off("page-pull-down-refresh-start");
      common_vendor.index.$off("page-load");
      common_vendor.index.$off("page-show-lifecycle");
      common_vendor.index.$off("page-hide-lifecycle");
      common_vendor.index.$off("lock-page-scroll");
      common_vendor.index.$off("user-login-success");
      common_vendor.index.$off("gesture-disable-pull-refresh");
      common_vendor.index.$off("gesture-enable-pull-refresh");
      common_vendor.index.$off("wish-list-updated");
    });
    const onPageHide = () => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:557", "[onPageHide] 页面隐藏");
      closeAllSwipeActions();
      utils_gestureManager.gestureManager.resetAll();
    };
    const handleDragStart = (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:572", "开始拖拽，数据:", data);
      if (!utils_gestureManager.gestureManager.canStartGesture("drag")) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:576", "[Index] 下拉刷新进行中，忽略拖拽开始");
        return;
      }
      isPullRefreshDisabled.value = true;
      if (!utils_gestureManager.gestureManager.startDrag({ cardId: data.wishId, index: data.index })) {
        isPullRefreshDisabled.value = false;
        return;
      }
      closeAllSwipeActions();
      isDragging.value = true;
      dragStartIndex.value = data.index;
      dragCurrentIndex.value = data.index;
      dragStartY.value = data.clientY;
      common_vendor.index.$emit("lock-page-scroll", { source: "drag" });
      initialOrder.value = wishList.value.map((wish) => wish.id);
      common_vendor.index.__f__("log", "at pages/index/index.vue:604", "初始心愿顺序:", initialOrder.value);
      common_vendor.index.__f__("log", "at pages/index/index.vue:605", "当前心愿列表数量:", wishList.value.length);
      try {
        const query = common_vendor.index.createSelectorQuery();
        query.select(".wish-card-container").boundingClientRect((data2) => {
          if (data2 && data2.height) {
            cardHeight.value = data2.height;
            common_vendor.index.__f__("log", "at pages/index/index.vue:614", "获取到卡片实际高度:", cardHeight.value);
          }
        }).exec();
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:618", "获取卡片高度失败:", e);
        cardHeight.value = 160;
      }
      try {
        common_vendor.index.vibrateShort();
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:627", "振动失败:", e);
      }
      common_vendor.index.__f__("log", "at pages/index/index.vue:630", "拖拽模式初始化完成，当前拖拽索引:", dragCurrentIndex.value);
    };
    const handleDragMove = (data) => {
      if (!isDragging.value) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:636", "非拖拽状态，忽略移动事件");
        return;
      }
      const moveDistance = data.clientY - dragStartY.value;
      common_vendor.index.__f__("log", "at pages/index/index.vue:642", "拖拽移动:", {
        currentY: data.clientY,
        startY: dragStartY.value,
        moveDistance,
        currentIndex: dragCurrentIndex.value
      });
      if (Math.abs(moveDistance) < 5) {
        return;
      }
      if (dragThrottled.value) {
        return;
      }
      dragThrottled.value = true;
      setTimeout(() => {
        dragThrottled.value = false;
      }, 50);
      const direction = moveDistance < 0 ? "up" : "down";
      const currentIndex = dragCurrentIndex.value;
      let targetIndex = -1;
      const moveRatio = Math.abs(moveDistance) / cardHeight.value;
      const shouldMove = moveRatio >= 0.5;
      if (direction === "up" && currentIndex > 0 && shouldMove) {
        targetIndex = currentIndex - 1;
        common_vendor.index.__f__("log", "at pages/index/index.vue:681", "向上拖动，目标索引:", targetIndex);
        checkTargetVisibility(targetIndex);
      } else if (direction === "down" && currentIndex < wishList.value.length - 1 && shouldMove) {
        targetIndex = currentIndex + 1;
        common_vendor.index.__f__("log", "at pages/index/index.vue:688", "向下拖动，目标索引:", targetIndex);
        checkTargetVisibility(targetIndex);
      } else {
        return;
      }
      common_vendor.index.__f__("log", "at pages/index/index.vue:699", "交换位置:", currentIndex, "->", targetIndex);
      swapItems(currentIndex, targetIndex);
      dragCurrentIndex.value = targetIndex;
      dragStartY.value = data.clientY - moveDistance * 0.3;
      try {
        common_vendor.index.vibrateShort({
          success: function() {
            common_vendor.index.__f__("log", "at pages/index/index.vue:715", "交换位置振动触发");
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:719", "震动API调用失败:", e);
      }
    };
    const checkTargetVisibility = (targetIndex) => {
      if (!wishCards.value || wishCards.value.length <= targetIndex) {
        return;
      }
      try {
        const query = common_vendor.index.createSelectorQuery();
        query.select(".wish-list").boundingRect((listRect) => {
          if (!listRect)
            return;
          query.selectAll(".wish-card-container").boundingRect((cardsRect) => {
            if (!cardsRect || !cardsRect[targetIndex])
              return;
            const cardRect = cardsRect[targetIndex];
            const listTop = listRect.top;
            const listBottom = listRect.bottom;
            const cardTop = cardRect.top;
            const cardBottom = cardRect.bottom;
            common_vendor.index.__f__("log", "at pages/index/index.vue:748", "卡片可见性检查:", {
              targetIndex,
              listTop,
              listBottom,
              cardTop,
              cardBottom
            });
            query.select(".wish-list").scrollOffset((scrollData) => {
              if (!scrollData)
                return;
              const currentScrollTop = scrollData.scrollTop;
              let newScrollTop = currentScrollTop;
              if (cardTop < listTop + 100 && currentScrollTop > 0) {
                newScrollTop = Math.max(0, currentScrollTop - (cardRect.height + 20));
                common_vendor.index.__f__("log", "at pages/index/index.vue:767", "向上滚动到:", newScrollTop);
              } else if (cardBottom > listBottom - 100) {
                newScrollTop = currentScrollTop + (cardRect.height + 20);
                common_vendor.index.__f__("log", "at pages/index/index.vue:773", "向下滚动到:", newScrollTop);
              } else {
                return;
              }
              try {
                common_vendor.index.pageScrollTo({
                  scrollTop: newScrollTop,
                  duration: 300
                });
                common_vendor.index.__f__("log", "at pages/index/index.vue:785", "已滚动到位置:", newScrollTop);
              } catch (e) {
                common_vendor.index.__f__("warn", "at pages/index/index.vue:787", "滚动失败:", e);
              }
            }).exec();
          }).exec();
        }).exec();
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:793", "检查卡片可见性失败:", e);
      }
    };
    const handleDragEnd = () => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:841", "结束拖拽");
      if (isDragging.value) {
        isDragging.value = false;
        setTimeout(() => {
          common_vendor.index.$emit("page-scroll-enabled", { timestamp: Date.now() });
        }, 50);
        const newOrder = wishList.value.map((wish) => wish.id);
        const hasOrderChanged = initialOrder.value.some((id, index) => id !== newOrder[index]);
        if (hasOrderChanged) {
          wishStore.updateWishOrder(newOrder);
          try {
            common_vendor.index.vibrateShort();
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/index/index.vue:870", "振动失败:", e);
          }
        }
        utils_gestureManager.gestureManager.endDrag({ hasOrderChanged });
        setTimeout(() => {
          isPullRefreshDisabled.value = false;
        }, 300);
        dragStartIndex.value = -1;
        dragCurrentIndex.value = -1;
        initialOrder.value = [];
      }
    };
    const swapItems = (fromIndex, toIndex) => {
      if (fromIndex === toIndex)
        return;
      common_vendor.index.__f__("log", "at pages/index/index.vue:895", "执行交换:", fromIndex, "->", toIndex);
      const newWishList = [...wishList.value];
      const itemToMove = newWishList[fromIndex];
      newWishList.splice(fromIndex, 1);
      newWishList.splice(toIndex, 0, itemToMove);
      wishStore.setCurrentList(newWishList);
      try {
        common_vendor.index.vibrateShort({
          success: function() {
            common_vendor.index.__f__("log", "at pages/index/index.vue:916", "交换位置振动触发");
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:920", "震动API调用失败:", e);
      }
    };
    const closeAllSwipeActions = () => {
      if (wishCards.value && wishCards.value.length) {
        wishCards.value.forEach((card) => {
          if (card && card.resetSwipe) {
            card.resetSwipe();
          }
        });
      }
      activeCardId.value = null;
    };
    const goToAddWish = () => {
      const currentGroupId = wishStore.currentGroupId;
      const url = currentGroupId !== "all" ? `/subpkg-wish/pages/editWish/editWish?groupId=${currentGroupId}` : "/subpkg-wish/pages/editWish/editWish";
      common_vendor.index.navigateTo({
        url
      });
    };
    const onGroupChange = (groupId) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:955", "[onGroupChange] 开始处理分组变化:", groupId);
      try {
        wishStore.setCurrentGroup(groupId);
        common_vendor.index.__f__("log", "at pages/index/index.vue:958", "[onGroupChange] 分组设置完成");
        closeAllSwipeActions();
        common_vendor.index.__f__("log", "at pages/index/index.vue:962", "[onGroupChange] 卡片关闭完成");
        common_vendor.index.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });
        common_vendor.index.__f__("log", "at pages/index/index.vue:969", "[onGroupChange] 滚动到顶部完成");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:971", "[onGroupChange] 处理分组变化时发生错误:", e);
        common_vendor.index.__f__("error", "at pages/index/index.vue:972", "[onGroupChange] 错误堆栈:", e.stack);
      }
    };
    const handleCardOpen = (cardId) => {
      activeCardId.value = cardId;
    };
    const handleCardClose = () => {
      activeCardId.value = null;
    };
    const handleCardSwipeStart = (cardId) => {
      if (activeCardId.value && activeCardId.value !== cardId) {
        closeAllSwipeActions();
      }
    };
    const handleCardScrollDetected = (data) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:997", "检测到卡片滚动", data);
      if (data && data.deltaY > 50) {
        closeAllSwipeActions();
      }
    };
    const handlePageTouch = (e) => {
      if (isDragging.value) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        if (e.cancelable) {
          e.cancelable && e.preventDefault();
        }
        return false;
      }
      const currentY = e.touches[0].clientY;
      if (lastTouchY.value !== void 0) {
        const deltaY = Math.abs(currentY - lastTouchY.value);
        if (activeCardId.value !== null && deltaY > 60) {
          closeAllSwipeActions();
        }
      }
      lastTouchY.value = currentY;
    };
    const handlePageTouchEnd = (e) => {
      lastTouchY.value = void 0;
    };
    const onPageScroll = (e) => {
      if (activeCardId.value && e && e.scrollTop > 10) {
        closeAllSwipeActions();
      }
    };
    const containerTouchStartX = common_vendor.ref(0);
    const containerTouchStartY = common_vendor.ref(0);
    const handleContainerTouchStart = (e) => {
      if (e.touches && e.touches.length > 0) {
        containerTouchStartX.value = e.touches[0].clientX;
        containerTouchStartY.value = e.touches[0].clientY;
      }
      if (isPullRefreshDisabled.value || isDragging.value) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        return false;
      }
    };
    const handleContainerTouchMove = (e) => {
      if (isPullRefreshDisabled.value || isDragging.value) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
        return false;
      }
      if (e.touches && e.touches.length > 0) {
        const currentX = e.touches[0].clientX;
        const currentY = e.touches[0].clientY;
        const deltaX = Math.abs(currentX - containerTouchStartX.value);
        const deltaY = Math.abs(currentY - containerTouchStartY.value);
        if (deltaX > deltaY && deltaX > 10) {
          e.preventDefault();
          e.stopPropagation();
          return false;
        }
      }
    };
    const handleContainerTouchEnd = (e) => {
      containerTouchStartX.value = 0;
      containerTouchStartY.value = 0;
    };
    const completeWish = async (id) => {
      if (!userStore.checkLoginAndRedirect()) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:1133", "用户未登录，跳转到登录页面");
        return;
      }
      try {
        await wishStore.completeWish(id);
        common_vendor.index.showToast({
          title: "心愿已完成",
          icon: "success",
          duration: 1500
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:1146", "完成心愿失败:", error);
        common_vendor.index.showToast({
          title: "操作失败",
          icon: "none",
          duration: 1500
        });
      }
    };
    const deleteWish = async (id) => {
      if (!userStore.checkLoginAndRedirect()) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:1159", "用户未登录，跳转到登录页面");
        return;
      }
      try {
        closeAllSwipeActions();
        const currentGroupBeforeDelete = wishStore.currentGroupId;
        await wishStore.deleteWish(id);
        await common_vendor.nextTick$1();
        common_vendor.index.$emit("wish-list-updated", {
          timestamp: Date.now(),
          action: "delete",
          deletedId: id
        });
        if (wishStore.currentGroupId !== currentGroupBeforeDelete) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:1186", "分组已变更，触发必要的更新");
        }
        common_vendor.index.showToast({
          title: "心愿已删除",
          icon: "success",
          duration: 1500
        });
        common_vendor.index.__f__("log", "at pages/index/index.vue:1196", "心愿删除成功，界面已更新");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:1199", "删除心愿失败:", error);
      }
    };
    const shareWish = (id) => {
      if (!userStore.checkLoginAndRedirect()) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:1207", "用户未登录，跳转到登录页面");
        return;
      }
      common_vendor.index.showLoading({
        title: "生成海报中...",
        mask: true
      });
      setTimeout(() => {
        common_vendor.index.hideLoading().catch(() => {
        });
        common_vendor.index.showToast({
          title: "分享成功",
          icon: "success",
          duration: 1500
        });
        common_vendor.index.vibrateShort();
      }, 1500);
    };
    const checkLoginStatus = async () => {
      userStore.isLoggedIn;
      try {
        await groupStore.initGroups();
        await wishStore.initWishList();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:1247", "[checkLoginStatus] Data initialization error:", error);
      }
      common_vendor.index.__f__("log", "at pages/index/index.vue:1251", "[checkLoginStatus] Data initialization completed");
    };
    const checkUnreadMessages = () => {
      messageStore.initMessages();
      const unreadCount = messageStore.getUnreadCount;
      if (unreadCount > 0) {
        common_vendor.index.setTabBarBadge({
          index: 1,
          // 消息选项卡索引
          text: unreadCount.toString()
        });
      } else {
        try {
          common_vendor.index.removeTabBarBadge({
            index: 1
          });
        } catch (e) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:1302", "移除徽标失败，可能没有显示徽标");
        }
      }
    };
    const forceSyncData = async () => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:1315", "[Index] Manual sync data triggered");
      if (isSyncing.value) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:1318", "[Index] Sync already in progress");
        return;
      }
      try {
        isSyncing.value = true;
        common_vendor.index.__f__("log", "at pages/index/index.vue:1324", "[Index] 开始同步，用户登录状态:", userStore.isLoggedIn);
        if (!userStore.isLoggedIn) {
          common_vendor.index.__f__("warn", "at pages/index/index.vue:1328", "[Index] 用户未登录，跳过同步");
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none",
            duration: 2e3
          });
          return;
        }
        common_vendor.index.__f__("log", "at pages/index/index.vue:1338", "[Index] 调用 syncManager.manualSync...");
        const result = await utils_syncManager.syncManager.manualSync();
        common_vendor.index.__f__("log", "at pages/index/index.vue:1340", "[Index] 同步结果:", result);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:1343", "[Index] Manual sync failed:", error);
        common_vendor.index.showToast({
          title: "同步失败: " + (error.message || "未知错误"),
          icon: "none",
          duration: 3e3
        });
      } finally {
        isSyncing.value = false;
        common_vendor.index.__f__("log", "at pages/index/index.vue:1351", "[Index] 同步完成，重置状态");
      }
    };
    const testSyncSystem = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/index/index.vue:1358", "[Index] 开始测试同步系统...");
        const syncManagerStatus = {
          isInitialized: utils_syncManager.syncManager.isInitialized,
          userId: utils_syncManager.syncManager.userId,
          pushEnabled: utils_syncManager.syncManager.pushEnabled,
          isOnline: utils_syncManager.syncManager.isOnline
        };
        common_vendor.index.__f__("log", "at pages/index/index.vue:1367", "[Index] SyncManager 状态:", syncManagerStatus);
        const userStatus = {
          isLoggedIn: userStore.isLoggedIn,
          userId: userStore.userId,
          userInfo: userStore.userInfo
        };
        common_vendor.index.__f__("log", "at pages/index/index.vue:1375", "[Index] 用户状态:", userStatus);
        const storeStatus = {
          wishCount: wishStore.wishList.length,
          groupCount: groupStore.groups.length,
          wishSyncStatus: wishStore.syncStatus,
          groupSyncStatus: groupStore.syncStatus
        };
        common_vendor.index.__f__("log", "at pages/index/index.vue:1384", "[Index] Store 状态:", storeStatus);
        common_vendor.index.__f__("log", "at pages/index/index.vue:1387", "[Index] 测试手动同步...");
        const syncResult = await utils_syncManager.syncManager.manualSync();
        common_vendor.index.__f__("log", "at pages/index/index.vue:1389", "[Index] 手动同步结果:", syncResult);
        const summary = {
          syncManager: syncManagerStatus,
          user: userStatus,
          stores: storeStatus,
          syncResult
        };
        if (storeStatus.wishCount === 0) {
          common_vendor.index.showModal({
            title: "同步系统测试结果",
            content: `同步成功但没有心愿数据。是否创建测试数据？

详细信息：${JSON.stringify(summary, null, 2)}`,
            showCancel: true,
            cancelText: "查看详情",
            confirmText: "创建测试数据",
            success: async (res) => {
              if (res.confirm) {
                await createTestWish();
              } else {
                common_vendor.index.showModal({
                  title: "详细测试结果",
                  content: JSON.stringify(summary, null, 2),
                  showCancel: false,
                  confirmText: "确定"
                });
              }
            }
          });
        } else {
          common_vendor.index.showModal({
            title: "同步系统测试结果",
            content: JSON.stringify(summary, null, 2),
            showCancel: false,
            confirmText: "确定"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:1431", "[Index] 同步系统测试失败:", error);
        common_vendor.index.showModal({
          title: "测试失败",
          content: error.message || "未知错误",
          showCancel: false,
          confirmText: "确定"
        });
      }
    };
    const createTestWish = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/index/index.vue:1444", "[Index] 创建测试心愿...");
        const testWish = {
          title: "测试心愿 - " + (/* @__PURE__ */ new Date()).toLocaleString(),
          description: "这是一个测试心愿，用于验证同步功能",
          groupIds: ["all"],
          permission: "private"
        };
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.createWish(testWish);
        common_vendor.index.__f__("log", "at pages/index/index.vue:1457", "[Index] 测试心愿创建结果:", result);
        if (result.errCode === 0) {
          await utils_syncManager.syncManager.manualSync();
          common_vendor.index.showToast({
            title: "测试心愿创建成功",
            icon: "success",
            duration: 2e3
          });
        } else {
          throw new Error(result.errMsg || "创建失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:1473", "[Index] 创建测试心愿失败:", error);
        common_vendor.index.showToast({
          title: "创建失败: " + error.message,
          icon: "none",
          duration: 3e3
        });
      }
    };
    return {
      wishStore,
      groupStore,
      userStore,
      messageStore,
      wishCards,
      containerRef,
      activeCardId,
      lastTouchY,
      isPullRefreshDisabled,
      closeAllSwipeActions,
      handleCardOpen,
      handleCardClose,
      handleCardSwipeStart,
      handleCardScrollDetected,
      handlePageTouch,
      handlePageTouchEnd,
      onPageScroll,
      onPageHide,
      handleContainerTouchStart,
      handleContainerTouchMove,
      handleContainerTouchEnd,
      // 添加拖拽相关方法
      isDragging,
      dragStartIndex,
      dragCurrentIndex,
      dragStartY,
      dragThrottled,
      cardHeight,
      handleDragStart,
      handleDragMove,
      handleDragEnd,
      swapItems,
      // 其他方法
      onGroupChange,
      completeWish,
      deleteWish,
      shareWish,
      goToAddWish,
      wishList,
      isLogin,
      checkLoginStatus,
      checkUnreadMessages,
      forceSyncData,
      isSyncing,
      // 开发调试
      isDevelopment,
      testSyncSystem,
      createTestWish,
      // 强制更新key
      forceUpdateKey
    };
  },
  data() {
    return {
      isSyncing: false
    };
  },
  computed: {
    wishList() {
      return this.wishStore.currentGroupWishes;
    },
    isLogin() {
      return this.userStore.isLoggedIn;
    }
  },
  // 页面加载时执行
  async onLoad(options) {
    common_vendor.index.__f__("log", "at pages/index/index.vue:1551", "[Index onLoad] 页面加载，初始化数据...");
    common_vendor.index.$emit("page-load", {
      options,
      timestamp: Date.now()
    });
  },
  // 页面每次显示时执行
  onShow() {
    common_vendor.index.__f__("log", "at pages/index/index.vue:1562", "[Index onShow] 页面显示，检查数据状态...");
    common_vendor.index.$emit("page-show-lifecycle", {
      timestamp: Date.now()
    });
  },
  // 页面隐藏时执行
  onHide() {
    common_vendor.index.__f__("log", "at pages/index/index.vue:1571", "[Index onHide] 页面隐藏");
    common_vendor.index.$emit("page-hide-lifecycle", {
      timestamp: Date.now()
    });
  },
  // 监听数据变化
  watch: {
    wishList: {
      handler() {
        common_vendor.index.__f__("log", "at pages/index/index.vue:1582", "心愿列表已更新");
      },
      deep: true
    }
  },
  methods: {
    // 清理所有心愿中的"已还原"标记按钮已移除
  }
};
if (!Array) {
  const _easycom_GroupSelector2 = common_vendor.resolveComponent("GroupSelector");
  const _easycom_WishCard2 = common_vendor.resolveComponent("WishCard");
  const _component_transition_group = common_vendor.resolveComponent("transition-group");
  (_easycom_GroupSelector2 + _easycom_WishCard2 + _component_transition_group)();
}
const _easycom_GroupSelector = () => "../../components/GroupSelector/GroupSelector.js";
const _easycom_WishCard = () => "../../components/WishCard/WishCard.js";
if (!Math) {
  (_easycom_GroupSelector + _easycom_WishCard)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o($setup.onGroupChange),
    b: common_vendor.f($setup.wishStore.currentGroupWishes, (wish, index, i0) => {
      return {
        a: common_vendor.sr("wishCards", "4d78428a-2-" + i0 + ",4d78428a-1", {
          "f": 1
        }),
        b: `${wish.id || wish._id}-${$setup.forceUpdateKey}`,
        c: common_vendor.o($setup.handleCardSwipeStart, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        d: common_vendor.o($setup.handleCardOpen, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        e: common_vendor.o($setup.handleCardClose, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        f: common_vendor.o($setup.handleCardScrollDetected, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        g: common_vendor.o($setup.completeWish, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        h: common_vendor.o($setup.deleteWish, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        i: common_vendor.o($setup.shareWish, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        j: common_vendor.o($setup.handleDragStart, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        k: common_vendor.o($setup.handleDragMove, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        l: common_vendor.o($setup.handleDragEnd, `${wish.id || wish._id}-${$setup.forceUpdateKey}`),
        m: "4d78428a-2-" + i0 + ",4d78428a-1",
        n: common_vendor.p({
          wish,
          index,
          ["active-card-id"]: $setup.activeCardId,
          ["is-draggable"]: true,
          ["is-dragging-element"]: $setup.isDragging && $setup.dragCurrentIndex === index
        })
      };
    }),
    c: $setup.forceUpdateKey,
    d: common_vendor.p({
      name: "list-item",
      tag: "view"
    }),
    e: $setup.wishStore.currentGroupWishes.length === 0
  }, $setup.wishStore.currentGroupWishes.length === 0 ? {} : {}, {
    f: common_vendor.o((...args) => _ctx.onCardListTouch && _ctx.onCardListTouch(...args)),
    g: common_vendor.o((...args) => $setup.goToAddWish && $setup.goToAddWish(...args)),
    h: $setup.isDevelopment
  }, $setup.isDevelopment ? {
    i: common_vendor.o((...args) => $setup.testSyncSystem && $setup.testSyncSystem(...args))
  } : {}, {
    j: common_vendor.o((...args) => $setup.closeAllSwipeActions && $setup.closeAllSwipeActions(...args)),
    k: common_vendor.o((...args) => $setup.handleContainerTouchStart && $setup.handleContainerTouchStart(...args)),
    l: common_vendor.o((...args) => $setup.handleContainerTouchMove && $setup.handleContainerTouchMove(...args)),
    m: common_vendor.o((...args) => $setup.handleContainerTouchEnd && $setup.handleContainerTouchEnd(...args)),
    n: $setup.isDragging ? 1 : "",
    o: $setup.isDragging || $setup.isPullRefreshDisabled ? 1 : "",
    p: $setup.isPullRefreshDisabled ? 1 : ""
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map

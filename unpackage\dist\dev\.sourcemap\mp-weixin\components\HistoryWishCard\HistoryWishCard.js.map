{"version": 3, "file": "HistoryWishCard.js", "sources": ["components/HistoryWishCard/HistoryWishCard.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQ3lrMTYvRGVza3RvcC93aXNobGlzdC11bmlhcHAvY29tcG9uZW50cy9IaXN0b3J5V2lzaENhcmQvSGlzdG9yeVdpc2hDYXJkLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"history-card-container\">\r\n\t\t<view class=\"swipe-action-wrapper\" ref=\"swipeRef\"\r\n\t\t\t@touchstart=\"touchStart\"\r\n\t\t\t@touchmove=\"touchMove\"\r\n\t\t\t@touchend=\"touchEnd\">\r\n\t\t\t<!-- 主卡片内容 -->\r\n\t\t\t<view class=\"swipe-content\" :style=\"contentStyle\">\r\n\t\t\t\t<view class=\"wish-card\">\r\n\t\t\t\t\t<view class=\"wish-card-header\">\r\n\t\t\t\t\t\t<view class=\"wish-card-order\">{{ index + 1 }}</view>\r\n\t\t\t\t\t\t<view class=\"wish-card-title text-ellipsis\">{{ wish.title }}</view>\r\n\t\t\t\t\t\t<view class=\"wish-card-status\">\r\n\t\t\t\t\t\t\t<view class=\"completed-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"wish-card-content\">\r\n\t\t\t\t\t\t<view class=\"wish-card-desc\" v-if=\"wish.description\">{{ wish.description }}</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 🔧 图片展示 - 支持多种数据格式 -->\r\n\t\t\t\t\t\t<view class=\"image-container\" v-if=\"convertedImageUrl && hasValidImage(wish.image)\">\r\n\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\tclass=\"wish-card-image\"\r\n\t\t\t\t\t\t\t\t:src=\"convertedImageUrl\"\r\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"\r\n\t\t\t\t\t\t\t\t@error=\"onImageError\"\r\n\t\t\t\t\t\t\t\t@load=\"onImageLoad\"\r\n\t\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t\t<view v-if=\"getImageCount(wish.image) > 1\" class=\"multi-image-badge\">+{{getImageCount(wish.image)}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"wish-card-footer\">\r\n\t\t\t\t\t\t<view class=\"wish-card-date\">完成于 {{ formatDate(wish.completeDate || wish.lastCompleteDate) }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 滑动操作按钮 -->\r\n\t\t\t<view class=\"swipe-buttons\">\r\n\t\t\t\t<view class=\"swipe-button restore\" @click=\"handleButtonClick(0)\">还原</view>\r\n\t\t\t\t<view class=\"swipe-button delete\" @click=\"handleButtonClick(1)\">删除</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue';\r\nimport { convertCloudUrl } from '@/utils/imageUtils.js';\r\n\r\nexport default {\r\n\tname: 'HistoryWishCard',\r\n\tprops: {\r\n\t\twish: {\r\n\t\t\ttype: Object,\r\n\t\t\trequired: true\r\n\t\t},\r\n\t\tindex: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tactiveCardId: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t}\r\n\t},\r\n\tsetup(props, { emit }) {\r\n\t\t// 滑动状态\r\n\t\tconst swiping = ref(false);\r\n\t\tconst startX = ref(0);\r\n\t\tconst moveX = ref(0);\r\n\t\tconst startY = ref(0);\r\n\t\tconst swipeRef = ref(null);\r\n\t\t\r\n\t\t// 记录当前卡片的滑动状态\r\n\t\tconst isOpen = ref(false);\r\n\t\t\r\n\t\t// 按钮总宽度 (还原按钮宽度 + 删除按钮宽度)\r\n\t\tconst buttonsWidth = 120;  // 2个按钮共120px宽度，与主页面一致\r\n\t\t\r\n\t\t// 🔧 检查是否有有效图片 - 支持多种数据格式\r\n\t\tconst hasValidImage = (image) => {\r\n\t\t\tif (!image) return false;\r\n\r\n\t\t\tif (typeof image === 'string') {\r\n\t\t\t\treturn image.trim() !== '';\r\n\t\t\t}\r\n\r\n\t\t\tif (Array.isArray(image)) {\r\n\t\t\t\treturn image.length > 0 && image.some(img => {\r\n\t\t\t\t\tif (typeof img === 'string') {\r\n\t\t\t\t\t\treturn img.trim() !== '';\r\n\t\t\t\t\t} else if (img && typeof img === 'object' && img.url) {\r\n\t\t\t\t\t\treturn img.url.trim() !== '';\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tif (image && typeof image === 'object' && image.url) {\r\n\t\t\t\treturn image.url.trim() !== '';\r\n\t\t\t}\r\n\r\n\t\t\treturn false;\r\n\t\t};\r\n\r\n\t\t// 🔧 获取图片URL - 支持多种数据格式和云存储转换\r\n\t\tconst convertedImageUrl = ref('');\r\n\r\n\t\tconst getImageUrl = async (image) => {\r\n\t\t\tif (!image) return '';\r\n\r\n\t\t\tlet rawUrl = '';\r\n\t\t\tif (typeof image === 'string') {\r\n\t\t\t\trawUrl = image;\r\n\t\t\t} else if (Array.isArray(image) && image.length > 0) {\r\n\t\t\t\tconst firstImage = image[0];\r\n\t\t\t\tif (typeof firstImage === 'string') {\r\n\t\t\t\t\trawUrl = firstImage;\r\n\t\t\t\t} else if (firstImage && typeof firstImage === 'object' && firstImage.url) {\r\n\t\t\t\t\trawUrl = firstImage.url;\r\n\t\t\t\t}\r\n\t\t\t} else if (image && typeof image === 'object' && image.url) {\r\n\t\t\t\trawUrl = image.url;\r\n\t\t\t}\r\n\r\n\t\t\tif (!rawUrl) return '';\r\n\r\n\t\t\ttry {\r\n\t\t\t\treturn await convertCloudUrl(rawUrl);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('[HistoryWishCard] 图片URL转换失败:', error);\r\n\t\t\t\treturn rawUrl;\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// 监听图片变化并转换URL\r\n\t\twatch(() => props.wish?.image, async (newImage) => {\r\n\t\t\tconvertedImageUrl.value = await getImageUrl(newImage);\r\n\t\t}, { immediate: true });\r\n\r\n\t\t// 🔧 获取图片数量\r\n\t\tconst getImageCount = (image) => {\r\n\t\t\tif (!image) return 0;\r\n\r\n\t\t\tif (typeof image === 'string' && image.trim() !== '') {\r\n\t\t\t\treturn 1;\r\n\t\t\t}\r\n\r\n\t\t\tif (Array.isArray(image)) {\r\n\t\t\t\treturn image.filter(img => {\r\n\t\t\t\t\tif (typeof img === 'string') {\r\n\t\t\t\t\t\treturn img.trim() !== '';\r\n\t\t\t\t\t} else if (img && typeof img === 'object' && img.url) {\r\n\t\t\t\t\t\treturn img.url.trim() !== '';\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}).length;\r\n\t\t\t}\r\n\r\n\t\t\tif (image && typeof image === 'object' && image.url && image.url.trim() !== '') {\r\n\t\t\t\treturn 1;\r\n\t\t\t}\r\n\r\n\t\t\treturn 0;\r\n\t\t};\r\n\r\n\t\t// 🔧 图片加载成功处理 - 添加安全检查\r\n\t\tconst onImageLoad = (e) => {\r\n\t\t\tconst src = e?.target?.src || e?.detail?.src || '未知图片源';\r\n\t\t\tconsole.log('[HistoryWishCard] 图片加载成功:', src);\r\n\t\t};\r\n\r\n\t\t// 🔧 图片加载失败处理 - 添加安全检查\r\n\t\tconst onImageError = (e) => {\r\n\t\t\tconst src = e?.target?.src || e?.detail?.src || '未知图片源';\r\n\t\t\tconsole.error('[HistoryWishCard] 图片加载失败:', {\r\n\t\t\t\tsrc: src,\r\n\t\t\t\terror: e\r\n\t\t\t});\r\n\t\t};\r\n\t\t\r\n\t\t// 在组件挂载时添加滚动监听\r\n\t\tonMounted(() => {\r\n\t\t\tuni.$on('page-scroll-event', (data) => {\r\n\t\t\t\tif (isOpen.value && data && data.shouldClose) {\r\n\t\t\t\t\tresetSwipeWithClass();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tuni.$on('onPageScroll', () => {\r\n\t\t\t\tif (isOpen.value) {\r\n\t\t\t\t\tresetSwipeWithClass();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tuni.$on('force-close-cards', (data) => {\r\n\t\t\t\tif (isOpen.value) {\r\n\t\t\t\t\tresetSwipeWithClass();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t\t\r\n\t\t// 在组件卸载时移除事件监听\r\n\t\tonUnmounted(() => {\r\n\t\t\tuni.$off('onPageScroll');\r\n\t\t\tuni.$off('page-scroll-event');\r\n\t\t\tuni.$off('force-close-cards');\r\n\t\t});\r\n\t\t\r\n\t\t// 内容样式计算\r\n\t\tconst contentStyle = computed(() => {\r\n\t\t\tlet x = moveX.value - startX.value;\r\n\t\t\t\r\n\t\t\tif (x > 0) {\r\n\t\t\t\tx = 0;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (x < 0) {\r\n\t\t\t\tif (x < -buttonsWidth) {\r\n\t\t\t\t\tx = -buttonsWidth + (x + buttonsWidth) * 0.2;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\t// 使用 translate3d 启用硬件加速\r\n\t\t\t\ttransform: `translate3d(${x}px, 0, 0)`,\r\n\t\t\t\ttransition: swiping.value ? 'none' : 'transform 0.25s cubic-bezier(0.3, 0.9, 0.3, 1)',\r\n\t\t\t\t// 性能优化\r\n\t\t\t\twillChange: swiping.value ? 'transform' : 'auto',\r\n\t\t\t\tbackfaceVisibility: 'hidden'\r\n\t\t\t};\r\n\t\t});\r\n\t\t\r\n\t\t// 触摸开始\r\n\t\tconst touchStart = (e) => {\r\n\t\t\tswiping.value = true;\r\n\t\t\tstartX.value = e.touches[0].clientX;\r\n\t\t\tmoveX.value = startX.value;\r\n\t\t\tstartY.value = e.touches[0].clientY;\r\n\t\t\t\r\n\t\t\tif (props.activeCardId && props.activeCardId !== props.wish.id) {\r\n\t\t\t\temit('card-swipe-start', props.wish.id);\r\n\t\t\t}\r\n\t\t};\r\n\t\t\r\n\t\t// 触摸移动\r\n\t\tconst touchMove = (e) => {\r\n\t\t\tif (!swiping.value) return;\r\n\t\t\t\r\n\t\t\tconst currentX = e.touches[0].clientX;\r\n\t\t\tconst currentY = e.touches[0].clientY;\r\n\t\t\t\r\n\t\t\tconst deltaY = Math.abs(currentY - startY.value);\r\n\t\t\tconst deltaX = Math.abs(currentX - startX.value);\r\n\t\t\t\r\n\t\t\tif (deltaY > deltaX * 2 && deltaY > 15) {\r\n\t\t\t\tswiping.value = false;\r\n\t\t\t\tmoveX.value = startX.value;\r\n\t\t\t\t\r\n\t\t\t\tif (isOpen.value) {\r\n\t\t\t\t\tresetSwipeWithClass();\r\n\t\t\t\t\temit('card-scroll-detected', {\r\n\t\t\t\t\t\tcardId: props.wish.id,\r\n\t\t\t\t\t\tdeltaY: deltaY\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (currentX === moveX.value) return;\r\n\t\t\tmoveX.value = currentX;\r\n\t\t};\r\n\t\t\r\n\t\t// 触摸结束\r\n\t\tconst touchEnd = (e) => {\r\n\t\t\tswiping.value = false;\r\n\t\t\t\r\n\t\t\tconst distance = moveX.value - startX.value;\r\n\t\t\t\r\n\t\t\tif (distance < -buttonsWidth / 5) {\r\n\t\t\t\tmoveX.value = startX.value - buttonsWidth;\r\n\t\t\t\tisOpen.value = true;\r\n\t\t\t\temit('card-open', props.wish.id);\r\n\t\t\t} else {\r\n\t\t\t\tmoveX.value = startX.value;\r\n\t\t\t\tisOpen.value = false;\r\n\t\t\t\t\r\n\t\t\t\tif (props.activeCardId === props.wish.id) {\r\n\t\t\t\t\temit('card-close');\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\t\t\r\n\t\t// 重置滑动状态\r\n\t\tconst resetSwipe = () => {\r\n\t\t\tmoveX.value = startX.value;\r\n\t\t\tisOpen.value = false;\r\n\t\t};\r\n\t\t\r\n\t\t// 强制重置滑动状态\r\n\t\tconst resetSwipeWithClass = () => {\r\n\t\t\tmoveX.value = startX.value;\r\n\t\t\tisOpen.value = false;\r\n\t\t};\r\n\t\t\r\n\t\t// 按钮点击处理\r\n\t\tconst handleButtonClick = (index) => {\r\n\t\t\tif (index === 0) {\r\n\t\t\t\temit('restore', props.wish.id);\r\n\t\t\t} else if (index === 1) {\r\n\t\t\t\temit('delete', props.wish.id);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tmoveX.value = startX.value;\r\n\t\t\tisOpen.value = false;\r\n\t\t};\r\n\t\t\r\n\t\t// 格式化日期\r\n\t\tconst formatDate = (date) => {\r\n\t\t\tif (!date) return '未知';\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tlet cleanDate = date;\r\n\t\t\t\tif (typeof cleanDate === 'string' && cleanDate.includes('(已还原)')) {\r\n\t\t\t\t\tcleanDate = cleanDate.replace(/\\s*\\(已还原\\)\\s*/g, '').trim();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst d = new Date(cleanDate);\r\n\t\t\t\t\r\n\t\t\t\tif (isNaN(d.getTime())) {\r\n\t\t\t\t\treturn typeof cleanDate === 'string' ? \r\n\t\t\t\t\t\tcleanDate.replace(/\\s*\\(已还原\\)\\s*/g, '') : '未知';\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst diffTime = now.getTime() - d.getTime();\r\n\t\t\t\tconst diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\r\n\t\t\t\t\r\n\t\t\t\tif (diffDays === 0) {\r\n\t\t\t\t\treturn '今天';\r\n\t\t\t\t} else if (diffDays === 1) {\r\n\t\t\t\t\treturn '昨天';\r\n\t\t\t\t} else if (diffDays < 7) {\r\n\t\t\t\t\treturn `${diffDays}天前`;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn `${d.getMonth() + 1}月${d.getDate()}日`;\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('日期格式化错误:', error);\r\n\t\t\t\treturn typeof date === 'string' ? \r\n\t\t\t\t\tdate.replace(/\\s*\\(已还原\\)\\s*/g, '') : '未知';\r\n\t\t\t}\r\n\t\t};\r\n\t\t\r\n\t\treturn {\r\n\t\t\tswipeRef,\r\n\t\t\tcontentStyle,\r\n\t\t\ttouchStart,\r\n\t\t\ttouchMove,\r\n\t\t\ttouchEnd,\r\n\t\t\thandleButtonClick,\r\n\t\t\tresetSwipe,\r\n\t\t\tisOpen,\r\n\t\t\tformatDate,\r\n\t\t\thasValidImage,\r\n\t\t\tconvertedImageUrl,\r\n\t\t\tgetImageUrl,\r\n\t\t\tgetImageCount,\r\n\t\t\tonImageError,\r\n\t\t\tonImageLoad\r\n\t\t};\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.history-card-container {\r\n\tmargin: 10rpx 2%;\r\n\twidth: 96%;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.swipe-action-wrapper {\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tborder-radius: 12rpx;\r\n\tdisplay: flex;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\twidth: 100%;\r\n}\r\n\r\n.swipe-content {\r\n\tflex: 1;\r\n\twidth: 100%;\r\n\tz-index: 2;\r\n\tbackground-color: #fff;\r\n\ttransition: transform 0.3s ease;\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.swipe-buttons {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tz-index: 1;\r\n\tdisplay: flex;\r\n\theight: 100%;\r\n}\r\n\r\n.swipe-button {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\twidth: 60px;\r\n\tcolor: white;\r\n\tfont-size: 26rpx;\r\n\tfont-weight: 500;\r\n\theight: 100%;\r\n\tflex-direction: column;\r\n\t\r\n\t&:active {\r\n\t\topacity: 0.85;\r\n\t}\r\n}\r\n\r\n.restore {\r\n\tbackground-color: #19ad19;\r\n}\r\n\r\n.delete {\r\n\tbackground-color: #fa5151;\r\n}\r\n\r\n.wish-card {\r\n\tpadding: 24rpx;\r\n\tbox-sizing: border-box;\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 12rpx;\r\n\twidth: 100%;\r\n\t\r\n\t.wish-card-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tposition: relative;\r\n\t\t\r\n\t\t.wish-card-order {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tbackground-color: #8a2be2;\r\n\t\t\tcolor: #fff;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tmargin-right: 16rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.wish-card-title {\r\n\t\t\tflex: 1;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tmargin-right: 16rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.wish-card-status {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t\r\n\t\t\t.completed-icon {\r\n\t\t\t\twidth: 32rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tbackground-color: #52c41a;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.wish-card-content {\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tposition: relative;\r\n\t\t\r\n\t\t.wish-card-desc {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\tdisplay: -webkit-box;\r\n\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t-webkit-line-clamp: 3; /* 🔧 修改为最多显示3行 */\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\t\t\r\n\t\t.image-container {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 300rpx;\r\n\t\t\tmargin-top: 16rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\t\t\r\n\t\t.wish-card-image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 300rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\t/* 🔧 移除 object-fit: cover，使用 mode=\"aspectFit\" 完整显示图片 */\r\n\t\t}\r\n\t\t\r\n\t\t.multi-image-badge {\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 12rpx;\r\n\t\t\tbottom: 12rpx;\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\t\t\tcolor: white;\r\n\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tz-index: 5;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.wish-card-footer {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\t\r\n\t\t.wish-card-date {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.text-ellipsis {\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/wishlist-uniapp/components/HistoryWishCard/HistoryWishCard.vue'\nwx.createComponent(Component)"], "names": ["ref", "convertCloudUrl", "uni", "watch", "onMounted", "onUnmounted", "computed"], "mappings": ";;;AAsDA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACV;AAAA,IACD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,MAAM,OAAO,EAAE,QAAQ;AAEtB,UAAM,UAAUA,kBAAI,KAAK;AACzB,UAAM,SAASA,kBAAI,CAAC;AACpB,UAAM,QAAQA,kBAAI,CAAC;AACnB,UAAM,SAASA,kBAAI,CAAC;AACpB,UAAM,WAAWA,kBAAI,IAAI;AAGzB,UAAM,SAASA,kBAAI,KAAK;AAGxB,UAAM,eAAe;AAGrB,UAAM,gBAAgB,CAAC,UAAU;AAChC,UAAI,CAAC;AAAO,eAAO;AAEnB,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO,MAAM,WAAW;AAAA,MACzB;AAEA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO,MAAM,SAAS,KAAK,MAAM,KAAK,SAAO;AAC5C,cAAI,OAAO,QAAQ,UAAU;AAC5B,mBAAO,IAAI,KAAK,MAAM;AAAA,UACvB,WAAW,OAAO,OAAO,QAAQ,YAAY,IAAI,KAAK;AACrD,mBAAO,IAAI,IAAI,KAAI,MAAO;AAAA,UAC3B;AACA,iBAAO;AAAA,QACR,CAAC;AAAA,MACF;AAEA,UAAI,SAAS,OAAO,UAAU,YAAY,MAAM,KAAK;AACpD,eAAO,MAAM,IAAI,KAAI,MAAO;AAAA,MAC7B;AAEA,aAAO;AAAA;AAIR,UAAM,oBAAoBA,kBAAI,EAAE;AAEhC,UAAM,cAAc,OAAO,UAAU;AACpC,UAAI,CAAC;AAAO,eAAO;AAEnB,UAAI,SAAS;AACb,UAAI,OAAO,UAAU,UAAU;AAC9B,iBAAS;AAAA,MACV,WAAW,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS,GAAG;AACpD,cAAM,aAAa,MAAM,CAAC;AAC1B,YAAI,OAAO,eAAe,UAAU;AACnC,mBAAS;AAAA,QACV,WAAW,cAAc,OAAO,eAAe,YAAY,WAAW,KAAK;AAC1E,mBAAS,WAAW;AAAA,QACrB;AAAA,iBACU,SAAS,OAAO,UAAU,YAAY,MAAM,KAAK;AAC3D,iBAAS,MAAM;AAAA,MAChB;AAEA,UAAI,CAAC;AAAQ,eAAO;AAEpB,UAAI;AACH,eAAO,MAAMC,iBAAAA,gBAAgB,MAAM;AAAA,MAClC,SAAO,OAAO;AACfC,oGAAc,gCAAgC,KAAK;AACnD,eAAO;AAAA,MACR;AAAA;AAIDC,kBAAAA,MAAM,MAAA;;AAAM,yBAAM,SAAN,mBAAY;AAAA,OAAO,OAAO,aAAa;AAClD,wBAAkB,QAAQ,MAAM,YAAY,QAAQ;AAAA,IACrD,GAAG,EAAE,WAAW,KAAG,CAAG;AAGtB,UAAM,gBAAgB,CAAC,UAAU;AAChC,UAAI,CAAC;AAAO,eAAO;AAEnB,UAAI,OAAO,UAAU,YAAY,MAAM,KAAI,MAAO,IAAI;AACrD,eAAO;AAAA,MACR;AAEA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO,MAAM,OAAO,SAAO;AAC1B,cAAI,OAAO,QAAQ,UAAU;AAC5B,mBAAO,IAAI,KAAK,MAAM;AAAA,UACvB,WAAW,OAAO,OAAO,QAAQ,YAAY,IAAI,KAAK;AACrD,mBAAO,IAAI,IAAI,KAAI,MAAO;AAAA,UAC3B;AACA,iBAAO;AAAA,QACP,CAAA,EAAE;AAAA,MACJ;AAEA,UAAI,SAAS,OAAO,UAAU,YAAY,MAAM,OAAO,MAAM,IAAI,KAAK,MAAM,IAAI;AAC/E,eAAO;AAAA,MACR;AAEA,aAAO;AAAA;AAIR,UAAM,cAAc,CAAC,MAAM;;AAC1B,YAAM,QAAM,4BAAG,WAAH,mBAAW,UAAO,4BAAG,WAAH,mBAAW,QAAO;AAChDD,oBAAA,MAAA,MAAA,OAAA,yDAAY,6BAA6B,GAAG;AAAA;AAI7C,UAAM,eAAe,CAAC,MAAM;;AAC3B,YAAM,QAAM,4BAAG,WAAH,mBAAW,UAAO,4BAAG,WAAH,mBAAW,QAAO;AAChDA,oBAAAA,MAAc,MAAA,SAAA,yDAAA,6BAA6B;AAAA,QAC1C;AAAA,QACA,OAAO;AAAA,MACR,CAAC;AAAA;AAIFE,kBAAAA,UAAU,MAAM;AACfF,oBAAAA,MAAI,IAAI,qBAAqB,CAAC,SAAS;AACtC,YAAI,OAAO,SAAS,QAAQ,KAAK,aAAa;AAC7C;QACD;AAAA,MACD,CAAC;AAEDA,0BAAI,IAAI,gBAAgB,MAAM;AAC7B,YAAI,OAAO,OAAO;AACjB;QACD;AAAA,MACD,CAAC;AAEDA,oBAAAA,MAAI,IAAI,qBAAqB,CAAC,SAAS;AACtC,YAAI,OAAO,OAAO;AACjB;QACD;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AAGDG,kBAAAA,YAAY,MAAM;AACjBH,0BAAI,KAAK,cAAc;AACvBA,0BAAI,KAAK,mBAAmB;AAC5BA,0BAAI,KAAK,mBAAmB;AAAA,IAC7B,CAAC;AAGD,UAAM,eAAeI,cAAAA,SAAS,MAAM;AACnC,UAAI,IAAI,MAAM,QAAQ,OAAO;AAE7B,UAAI,IAAI,GAAG;AACV,YAAI;AAAA,MACL;AAEA,UAAI,IAAI,GAAG;AACV,YAAI,IAAI,CAAC,cAAc;AACtB,cAAI,CAAC,gBAAgB,IAAI,gBAAgB;AAAA,QAC1C;AAAA,MACD;AAEA,aAAO;AAAA;AAAA,QAEN,WAAW,eAAe,CAAC;AAAA,QAC3B,YAAY,QAAQ,QAAQ,SAAS;AAAA;AAAA,QAErC,YAAY,QAAQ,QAAQ,cAAc;AAAA,QAC1C,oBAAoB;AAAA;IAEtB,CAAC;AAGD,UAAM,aAAa,CAAC,MAAM;AACzB,cAAQ,QAAQ;AAChB,aAAO,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAC5B,YAAM,QAAQ,OAAO;AACrB,aAAO,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAE5B,UAAI,MAAM,gBAAgB,MAAM,iBAAiB,MAAM,KAAK,IAAI;AAC/D,aAAK,oBAAoB,MAAM,KAAK,EAAE;AAAA,MACvC;AAAA;AAID,UAAM,YAAY,CAAC,MAAM;AACxB,UAAI,CAAC,QAAQ;AAAO;AAEpB,YAAM,WAAW,EAAE,QAAQ,CAAC,EAAE;AAC9B,YAAM,WAAW,EAAE,QAAQ,CAAC,EAAE;AAE9B,YAAM,SAAS,KAAK,IAAI,WAAW,OAAO,KAAK;AAC/C,YAAM,SAAS,KAAK,IAAI,WAAW,OAAO,KAAK;AAE/C,UAAI,SAAS,SAAS,KAAK,SAAS,IAAI;AACvC,gBAAQ,QAAQ;AAChB,cAAM,QAAQ,OAAO;AAErB,YAAI,OAAO,OAAO;AACjB;AACA,eAAK,wBAAwB;AAAA,YAC5B,QAAQ,MAAM,KAAK;AAAA,YACnB;AAAA,UACD,CAAC;AAAA,QACF;AACA;AAAA,MACD;AAEA,UAAI,aAAa,MAAM;AAAO;AAC9B,YAAM,QAAQ;AAAA;AAIf,UAAM,WAAW,CAAC,MAAM;AACvB,cAAQ,QAAQ;AAEhB,YAAM,WAAW,MAAM,QAAQ,OAAO;AAEtC,UAAI,WAAW,CAAC,eAAe,GAAG;AACjC,cAAM,QAAQ,OAAO,QAAQ;AAC7B,eAAO,QAAQ;AACf,aAAK,aAAa,MAAM,KAAK,EAAE;AAAA,aACzB;AACN,cAAM,QAAQ,OAAO;AACrB,eAAO,QAAQ;AAEf,YAAI,MAAM,iBAAiB,MAAM,KAAK,IAAI;AACzC,eAAK,YAAY;AAAA,QAClB;AAAA,MACD;AAAA;AAID,UAAM,aAAa,MAAM;AACxB,YAAM,QAAQ,OAAO;AACrB,aAAO,QAAQ;AAAA;AAIhB,UAAM,sBAAsB,MAAM;AACjC,YAAM,QAAQ,OAAO;AACrB,aAAO,QAAQ;AAAA;AAIhB,UAAM,oBAAoB,CAAC,UAAU;AACpC,UAAI,UAAU,GAAG;AAChB,aAAK,WAAW,MAAM,KAAK,EAAE;AAAA,MAC9B,WAAW,UAAU,GAAG;AACvB,aAAK,UAAU,MAAM,KAAK,EAAE;AAAA,MAC7B;AAEA,YAAM,QAAQ,OAAO;AACrB,aAAO,QAAQ;AAAA;AAIhB,UAAM,aAAa,CAAC,SAAS;AAC5B,UAAI,CAAC;AAAM,eAAO;AAElB,UAAI;AACH,YAAI,YAAY;AAChB,YAAI,OAAO,cAAc,YAAY,UAAU,SAAS,OAAO,GAAG;AACjE,sBAAY,UAAU,QAAQ,kBAAkB,EAAE,EAAE;QACrD;AAEA,cAAM,IAAI,IAAI,KAAK,SAAS;AAE5B,YAAI,MAAM,EAAE,QAAO,CAAE,GAAG;AACvB,iBAAO,OAAO,cAAc,WAC3B,UAAU,QAAQ,kBAAkB,EAAE,IAAI;AAAA,QAC5C;AAEA,cAAM,MAAM,oBAAI;AAChB,cAAM,WAAW,IAAI,YAAY,EAAE,QAAO;AAC1C,cAAM,WAAW,KAAK,MAAM,YAAY,MAAO,KAAK,KAAK,GAAG;AAE5D,YAAI,aAAa,GAAG;AACnB,iBAAO;AAAA,QACR,WAAW,aAAa,GAAG;AAC1B,iBAAO;AAAA,QACR,WAAW,WAAW,GAAG;AACxB,iBAAO,GAAG,QAAQ;AAAA,eACZ;AACN,iBAAO,GAAG,EAAE,aAAa,CAAC,IAAI,EAAE,SAAS;AAAA,QAC1C;AAAA,MACC,SAAO,OAAO;AACfJ,sBAAA,MAAA,MAAA,SAAA,yDAAc,YAAY,KAAK;AAC/B,eAAO,OAAO,SAAS,WACtB,KAAK,QAAQ,kBAAkB,EAAE,IAAI;AAAA,MACvC;AAAA;AAGD,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;EAEF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxXA,GAAG,gBAAgB,SAAS;"}
"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_animationPerformance = require("../../utils/animationPerformance.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "WishCard",
  props: {
    wish: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    },
    // 添加一个新属性接收活动卡片的ID，用于判断当前卡片是否需要保持打开状态
    activeCardId: {
      type: String,
      default: ""
    },
    // 添加是否可拖拽的属性
    isDraggable: {
      type: Boolean,
      default: false
    },
    // 添加是否为当前被拖拽的元素
    isDraggingElement: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const swiping = common_vendor.ref(false);
    const startX = common_vendor.ref(0);
    const moveX = common_vendor.ref(0);
    const startY = common_vendor.ref(0);
    const swipeRef = common_vendor.ref(null);
    const isOpen = common_vendor.ref(false);
    const longPressTimer = common_vendor.ref(null);
    const isDragging = common_vendor.ref(false);
    const dragStartY = common_vendor.ref(0);
    const touchMoveThrottled = common_vendor.ref(false);
    const animationFrameId = common_vendor.ref(null);
    const buttonsWidth = 120;
    const handleGlobalScroll = () => {
      if (isOpen.value) {
        resetSwipe();
      }
    };
    common_vendor.onMounted(() => {
      common_vendor.index.$on("page-scroll-event", (data) => {
        if (isOpen.value && data && data.shouldClose) {
          resetSwipeWithClass();
        }
      });
      common_vendor.index.$on("onPageScroll", () => {
        if (isOpen.value) {
          resetSwipeWithClass();
        }
      });
      common_vendor.index.$on("force-close-cards", (data) => {
        if (isOpen.value) {
          resetSwipeWithClass();
        }
      });
      if (typeof window !== "undefined") {
        window.addEventListener("scroll", handleGlobalScroll, true);
        document.addEventListener("scroll", handleGlobalScroll, true);
        const parentElement = document.querySelector(".wish-container");
        if (parentElement) {
          parentElement.addEventListener("scroll", handleGlobalScroll, true);
        }
        const listElement = document.querySelector(".wish-list");
        if (listElement) {
          listElement.addEventListener("scroll", handleGlobalScroll, true);
        }
      }
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.$off("onPageScroll");
      common_vendor.index.$off("page-scroll-event");
      common_vendor.index.$off("force-close-cards");
      if (animationFrameId.value) {
        utils_animationPerformance.AnimationUtils.cancelRAF(animationFrameId.value);
      }
      if (longPressTimer.value) {
        clearTimeout(longPressTimer.value);
      }
      if (typeof window !== "undefined") {
        window.removeEventListener("scroll", handleGlobalScroll, true);
        document.removeEventListener("scroll", handleGlobalScroll, true);
      }
    });
    const contentStyle = common_vendor.computed(() => {
      let x = moveX.value - startX.value;
      if (x > 0) {
        x = 0;
      }
      if (x < 0) {
        if (x < -buttonsWidth) {
          x = -buttonsWidth + (x + buttonsWidth) * 0.2;
        }
      }
      common_vendor.computed(() => {
        const images = wishData.value.image;
        if (!images)
          return false;
        if (Array.isArray(images)) {
          return images.length > 0 && images.some((img) => {
            if (typeof img === "string") {
              return img.trim() !== "";
            } else if (img && typeof img === "object" && img.url) {
              return img.url.trim() !== "";
            }
            return false;
          });
        }
        if (typeof images === "string") {
          return images.trim() !== "";
        }
        return false;
      });
      common_vendor.computed(() => {
        const images = wishData.value.image;
        if (!images)
          return "";
        if (Array.isArray(images) && images.length > 0) {
          const firstImage = images[0];
          if (typeof firstImage === "string") {
            return firstImage;
          } else if (firstImage && typeof firstImage === "object" && firstImage.url) {
            return firstImage.url;
          }
        }
        if (typeof images === "string") {
          return images;
        }
        common_vendor.index.__f__("warn", "at components/WishCard/WishCard.vue:247", "[WishCard] 无法解析图片URL:", images);
        return "";
      });
      common_vendor.computed(() => {
        const images = wishData.value.image;
        if (!images)
          return 0;
        if (Array.isArray(images)) {
          return images.filter((img) => {
            if (typeof img === "string") {
              return img.trim() !== "";
            } else if (img && typeof img === "object" && img.url) {
              return img.url.trim() !== "";
            }
            return false;
          }).length;
        }
        if (typeof images === "string" && images.trim() !== "") {
          return 1;
        }
        return 0;
      });
      return {
        // 使用 translate3d 启用硬件加速
        transform: `translate3d(${x}px, 0, 0)`,
        transition: swiping.value ? "none" : "transform 0.25s cubic-bezier(0.3, 0.9, 0.3, 1)",
        // 性能优化
        willChange: swiping.value ? "transform" : "auto",
        backfaceVisibility: "hidden"
      };
    });
    const wishData = common_vendor.computed(() => props.wish);
    const touchStart = (e) => {
      if (!props.isDraggable) {
        handleSwipeStart(e);
        return;
      }
      swiping.value = true;
      startX.value = e.touches[0].clientX;
      if (!isOpen.value) {
        moveX.value = startX.value;
      }
      startY.value = e.touches[0].clientY;
      dragStartY.value = e.touches[0].clientY;
      if (props.activeCardId && props.activeCardId !== props.wish.id) {
        emit("card-swipe-start", props.wish.id);
      }
      if (longPressTimer.value) {
        clearTimeout(longPressTimer.value);
      }
      longPressTimer.value = setTimeout(() => {
        isDragging.value = true;
        common_vendor.index.$emit("disable-page-scroll", { cardId: props.wish.id, force: true });
        lockPageScroll();
        try {
          common_vendor.index.vibrateShort({
            success: function() {
            }
          });
        } catch (e2) {
          common_vendor.index.__f__("error", "at components/WishCard/WishCard.vue:358", "震动API调用失败:", e2);
        }
        emit("drag-start", {
          wishId: props.wish.id,
          index: props.index,
          clientX: startX.value,
          clientY: startY.value
        });
      }, 500);
    };
    const handleSwipeStart = (e) => {
      swiping.value = true;
      startX.value = e.touches[0].clientX;
      if (!isOpen.value) {
        moveX.value = startX.value;
      }
      startY.value = e.touches[0].clientY;
      if (props.activeCardId && props.activeCardId !== props.wish.id) {
        emit("card-swipe-start", props.wish.id);
      }
    };
    const touchMove = (e) => {
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;
      const deltaX = Math.abs(currentX - startX.value);
      const deltaY = Math.abs(currentY - startY.value);
      if (longPressTimer.value && (deltaX > 20 || deltaY > 15)) {
        clearTimeout(longPressTimer.value);
        longPressTimer.value = null;
      }
      if (isDragging.value) {
        e.stopPropagation();
        if (typeof e.preventDefault === "function") {
          e.preventDefault();
        }
        if (e.cancelable) {
          e.cancelable && e.preventDefault();
        }
        const currentOffsetY = currentY - startY.value;
        if (swipeRef.value && !props.isDraggingElement) {
          try {
            const moveY = currentOffsetY * 0.3;
            if (animationFrameId.value) {
              utils_animationPerformance.AnimationUtils.cancelRAF(animationFrameId.value);
            }
            animationFrameId.value = utils_animationPerformance.AnimationUtils.optimizedRAF(() => {
              if (swipeRef.value) {
                swipeRef.value.style.transform = `translate3d(0, ${moveY}px, 0)`;
                swipeRef.value.style.transition = "none";
                swipeRef.value.style.willChange = "transform";
              }
            });
          } catch (e2) {
            common_vendor.index.__f__("error", "at components/WishCard/WishCard.vue:442", "应用拖拽变换失败:", e2);
          }
        }
        const moveData = {
          wishId: props.wish.id,
          index: props.index,
          clientY: e.touches[0].clientY,
          clientX: e.touches[0].clientX,
          deltaY: e.touches[0].clientY - dragStartY.value,
          timestamp: Date.now()
          // 添加时间戳以便于调试和节流
        };
        if (!touchMoveThrottled.value) {
          emit("drag-move", moveData);
          touchMoveThrottled.value = true;
          setTimeout(() => {
            touchMoveThrottled.value = false;
          }, 16);
        }
        return false;
      }
      handleSwipeMove(e);
    };
    const handleSwipeMove = (e) => {
      if (!swiping.value)
        return;
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;
      const deltaY = Math.abs(currentY - startY.value);
      const deltaX = Math.abs(currentX - startX.value);
      if (deltaY > deltaX * 2 && deltaY > 30) {
        swiping.value = false;
        if (!isOpen.value) {
          moveX.value = startX.value;
        }
        emit("card-scroll-detected", {
          cardId: props.wish.id,
          deltaY
        });
        return;
      }
      if (currentX === moveX.value)
        return;
      moveX.value = currentX;
    };
    const touchEnd = (e) => {
      if (longPressTimer.value) {
        clearTimeout(longPressTimer.value);
        longPressTimer.value = null;
      }
      if (isDragging.value) {
        isDragging.value = false;
        common_vendor.index.$emit("enable-page-scroll", { cardId: props.wish.id });
        if (swipeRef.value) {
          swipeRef.value.style.transform = "";
          swipeRef.value.style.transition = "transform 0.3s ease";
          setTimeout(() => {
            if (swipeRef.value) {
              swipeRef.value.style.transition = "";
            }
          }, 300);
        }
        emit("drag-end", {
          wishId: props.wish.id,
          index: props.index
        });
        return;
      }
      handleSwipeEnd();
    };
    const handleSwipeEnd = (e) => {
      swiping.value = false;
      const distance = moveX.value - startX.value;
      if (distance < -buttonsWidth / 5) {
        if (distance < -buttonsWidth / 2) {
          moveX.value = startX.value - buttonsWidth;
        } else {
          moveX.value = startX.value - buttonsWidth;
        }
        isOpen.value = true;
        emit("card-open", props.wish.id);
      } else {
        moveX.value = startX.value;
        isOpen.value = false;
        if (props.activeCardId === props.wish.id) {
          emit("card-close");
        }
      }
    };
    const handleButtonClick = (index) => {
      if (!props.wish || !props.wish.id) {
        common_vendor.index.__f__("warn", "at components/WishCard/WishCard.vue:591", "[WishCard] 无效的心愿数据，无法执行操作");
        return;
      }
      if (index === 0) {
        emit("complete", props.wish.id);
      } else if (index === 1) {
        handleDeleteWithAnimation();
      }
      moveX.value = startX.value;
    };
    const isDeleting = common_vendor.ref(false);
    const handleDeleteWithAnimation = () => {
      if (!props.wish || !props.wish.id) {
        common_vendor.index.__f__("warn", "at components/WishCard/WishCard.vue:614", "[WishCard] 无效的心愿数据，无法删除");
        return;
      }
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这个心愿吗？",
        confirmText: "删除",
        confirmColor: "#fa5151",
        success: (res) => {
          if (res.confirm) {
            emit("delete", props.wish.id);
          }
        }
      });
    };
    const goToDetail = () => {
      if (!props.wish || !props.wish.id) {
        common_vendor.index.__f__("warn", "at components/WishCard/WishCard.vue:637", "[WishCard] 无效的心愿数据，无法跳转到详情页");
        return;
      }
      if (swiping.value || moveX.value !== startX.value) {
        if (moveX.value !== startX.value) {
          moveX.value = startX.value;
          isOpen.value = false;
        }
        return;
      }
      common_vendor.index.navigateTo({
        url: `/subpkg-wish/pages/wishDetail/wishDetail?id=${props.wish.id}`
      });
    };
    const shareWish = (e) => {
      e.stopPropagation();
      if (!props.wish || !props.wish.id) {
        common_vendor.index.__f__("warn", "at components/WishCard/WishCard.vue:662", "[WishCard] 无效的心愿数据，无法分享");
        return;
      }
      emit("share", props.wish.id);
    };
    const formatDate = (dateString) => {
      if (!dateString)
        return "未知日期";
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return "无效日期";
        }
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at components/WishCard/WishCard.vue:712", "[WishCard] 日期格式化错误:", error);
        return "日期错误";
      }
    };
    const resetSwipe = () => {
      const wasOpen = isOpen.value;
      moveX.value = startX.value;
      isOpen.value = false;
      swiping.value = false;
      if (wasOpen && props.activeCardId === props.wish.id) {
        emit("card-close", props.wish.id);
      }
    };
    const resetSwipeWithClass = () => {
      const wasOpen = isOpen.value;
      moveX.value = startX.value;
      isOpen.value = false;
      swiping.value = false;
      if (wasOpen && props.activeCardId === props.wish.id) {
        emit("card-close", props.wish.id);
      }
    };
    common_vendor.watch(
      () => props.activeCardId,
      (newActiveId, oldActiveId) => {
        if (isOpen.value && (newActiveId !== props.wish.id || newActiveId === null)) {
          resetSwipe();
        }
      },
      { immediate: true }
      // 立即执行一次，确保组件挂载时状态正确
    );
    common_vendor.watch(
      () => props.wish,
      (newWishData, oldWishData) => {
      },
      { deep: true }
      // 深度监听对象变化
    );
    const lockPageScroll = () => {
      try {
        common_vendor.index.$emit("lock-page-scroll", { source: "drag-start" });
        if (typeof common_vendor.index.disablePageScroll === "function") {
          common_vendor.index.disablePageScroll();
        } else if (typeof common_vendor.index.pageScrollTo === "function") {
          common_vendor.index.pageScrollTo({
            scrollTop: 0,
            duration: 0
          });
        }
        setTimeout(() => {
          common_vendor.index.$emit("disable-page-scroll", {
            cardId: props.wish.id,
            force: true,
            timestamp: Date.now()
          });
        }, 50);
      } catch (e) {
        common_vendor.index.__f__("error", "at components/WishCard/WishCard.vue:810", "锁定页面滚动失败:", e);
      }
    };
    const hasValidImage = common_vendor.computed(() => {
      var _a;
      const image = (_a = props.wish) == null ? void 0 : _a.image;
      if (!image)
        return false;
      if (typeof image === "string") {
        return image.trim() !== "";
      }
      if (Array.isArray(image)) {
        return image.length > 0 && image.some((img) => {
          if (typeof img === "string") {
            return img.trim() !== "";
          } else if (img && typeof img === "object" && img.url) {
            return img.url.trim() !== "";
          }
          return false;
        });
      }
      if (image && typeof image === "object" && image.url) {
        return image.url.trim() !== "";
      }
      return false;
    });
    const getFirstImageUrl = common_vendor.computed(() => {
      var _a;
      const image = (_a = props.wish) == null ? void 0 : _a.image;
      if (!image)
        return "";
      if (typeof image === "string") {
        return image;
      }
      if (Array.isArray(image) && image.length > 0) {
        const firstImage = image[0];
        if (typeof firstImage === "string") {
          return firstImage;
        } else if (firstImage && typeof firstImage === "object" && firstImage.url) {
          return firstImage.url;
        }
      }
      if (image && typeof image === "object" && image.url) {
        return image.url;
      }
      common_vendor.index.__f__("warn", "at components/WishCard/WishCard.vue:862", "[WishCard] 无法解析图片URL:", image);
      return "";
    });
    const getImageCount = common_vendor.computed(() => {
      var _a;
      const image = (_a = props.wish) == null ? void 0 : _a.image;
      if (!image)
        return 0;
      if (typeof image === "string" && image.trim() !== "") {
        return 1;
      }
      if (Array.isArray(image)) {
        return image.filter((img) => {
          if (typeof img === "string") {
            return img.trim() !== "";
          } else if (img && typeof img === "object" && img.url) {
            return img.url.trim() !== "";
          }
          return false;
        }).length;
      }
      if (image && typeof image === "object" && image.url && image.url.trim() !== "") {
        return 1;
      }
      return 0;
    });
    const onImageLoad = (e) => {
      var _a, _b;
      const src = ((_a = e == null ? void 0 : e.target) == null ? void 0 : _a.src) || ((_b = e == null ? void 0 : e.detail) == null ? void 0 : _b.src) || "未知图片源";
      common_vendor.index.__f__("log", "at components/WishCard/WishCard.vue:895", "[WishCard] 图片加载成功:", src);
    };
    const onImageError = (e) => {
      var _a, _b;
      const src = ((_a = e == null ? void 0 : e.target) == null ? void 0 : _a.src) || ((_b = e == null ? void 0 : e.detail) == null ? void 0 : _b.src) || "未知图片源";
      common_vendor.index.__f__("error", "at components/WishCard/WishCard.vue:901", "[WishCard] 图片加载失败:", {
        src,
        error: e
      });
    };
    return {
      swipeRef,
      contentStyle,
      touchStart,
      touchMove,
      touchEnd,
      isDeleting,
      handleButtonClick,
      goToDetail,
      shareWish,
      formatDate,
      resetSwipe,
      isOpen,
      // 暴露这些变量给父组件用于紧急状态修复
      moveX,
      startX,
      startY,
      buttonsWidth,
      wish: wishData,
      isDragging,
      // 导出拖拽状态
      touchMoveThrottled,
      // 导出节流控制变量
      // 🔧 图片处理相关
      hasValidImage,
      getFirstImageUrl,
      getImageCount,
      onImageError,
      onImageLoad
    };
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h;
  return common_vendor.e({
    a: common_vendor.t($props.index + 1),
    b: common_vendor.t(((_a = $setup.wish) == null ? void 0 : _a.title) || "未知标题"),
    c: ((_b = $setup.wish) == null ? void 0 : _b.permission) === "private"
  }, ((_c = $setup.wish) == null ? void 0 : _c.permission) === "private" ? {
    d: common_vendor.p({
      type: "lock-filled",
      size: "16",
      color: "#999"
    })
  } : {}, {
    e: (_d = $setup.wish) == null ? void 0 : _d.isCompleted
  }, ((_e = $setup.wish) == null ? void 0 : _e.isCompleted) ? {
    f: common_vendor.p({
      type: "checkmarkempty",
      size: "16",
      color: "#fff"
    })
  } : {}, {
    g: common_assets._imports_0$2,
    h: common_vendor.o((...args) => $setup.shareWish && $setup.shareWish(...args)),
    i: $props.index,
    j: (_f = $setup.wish) == null ? void 0 : _f.description
  }, ((_g = $setup.wish) == null ? void 0 : _g.description) ? {
    k: common_vendor.t($setup.wish.description)
  } : {}, {
    l: $setup.hasValidImage
  }, $setup.hasValidImage ? common_vendor.e({
    m: $setup.getFirstImageUrl,
    n: common_vendor.o((...args) => $setup.onImageError && $setup.onImageError(...args)),
    o: common_vendor.o((...args) => $setup.onImageLoad && $setup.onImageLoad(...args)),
    p: $setup.getImageCount > 1
  }, $setup.getImageCount > 1 ? {
    q: common_vendor.t($setup.getImageCount)
  } : {}) : {}, {
    r: common_vendor.t($setup.formatDate((_h = $setup.wish) == null ? void 0 : _h.createDate)),
    s: common_vendor.o((...args) => $setup.goToDetail && $setup.goToDetail(...args)),
    t: common_vendor.s($setup.contentStyle),
    v: common_vendor.o(($event) => $setup.handleButtonClick(0)),
    w: common_vendor.o(($event) => $setup.handleButtonClick(1)),
    x: common_vendor.o((...args) => $setup.touchStart && $setup.touchStart(...args)),
    y: common_vendor.o((...args) => $setup.touchMove && $setup.touchMove(...args)),
    z: common_vendor.o((...args) => $setup.touchEnd && $setup.touchEnd(...args)),
    A: common_vendor.o((...args) => $setup.touchEnd && $setup.touchEnd(...args)),
    B: $setup.isDragging || $props.isDraggingElement ? 1 : ""
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/WishCard/WishCard.js.map

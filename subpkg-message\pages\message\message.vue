<template>
	<view class="message-container">
		<view class="message-header">
			<view class="title">消息通知</view>
			<view class="read-all" @click="markAllAsRead" v-if="unreadCount > 0">全部已读</view>
		</view>
		
		<view class="message-list">
			<view v-if="messages.length === 0" class="empty-list">
				<text class="empty-text">暂无消息</text>
			</view>
			<view v-else>
				<!-- 区分不同类型的消息 -->
				<view 
					v-for="(message, index) in messages" 
					:key="message.id"
					class="message-item card"
					:class="{ 'unread': !message.isRead, 'comment-message': message.type === 'comment' }"
					@click="handleMessageClick(message)"
				>
					<view class="message-dot" v-if="!message.isRead"></view>
					
					<!-- 评论类型消息 -->
					<template v-if="message.type === 'comment'">
						<view class="message-header-row">
							<image class="comment-avatar" :src="message.commentAvatar || '/static/default-avatar.png'" mode="aspectFill"></image>
							<view class="message-title">{{ message.title }}</view>
						</view>
						<view class="message-wish-title">心愿：{{ message.wishTitle || '未知心愿' }}</view>
						<view class="message-content comment-content">{{ message.content }}</view>
						<view class="message-time">{{ formatTime(message.createDate) }}</view>
						<view class="message-action">点击查看详情</view>
					</template>
					
					<!-- 普通系统消息 -->
					<template v-else>
						<view class="message-title">{{ message.title }}</view>
						<view class="message-content">{{ message.content }}</view>
						<view class="message-time">{{ formatTime(message.createDate) }}</view>
					</template>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { useMessageStore } from '@/store/message.js'
	import { useWishStore } from '@/store/wish.js'
	
	export default {
		setup() {
			const messageStore = useMessageStore()
			const wishStore = useWishStore()
			
			return {
				messageStore,
				wishStore
			}
		},
		computed: {
			messages() {
				return this.messageStore.getAllMessages
			},
			unreadCount() {
				return this.messageStore.getUnreadCount
			}
		},
		onLoad() {
			// 初始化消息
			this.messageStore.initMessages()
		},
		onShow() {
			// 页面显示时刷新消息列表
			this.messageStore.initMessages()
		},
		methods: {
			// 处理消息点击
			handleMessageClick(message) {
				// 先标记为已读
				this.messageStore.markAsRead(message.id)
				
				// 根据消息类型执行不同操作
				if (message.type === 'comment' && message.wishId) {
					// 导航到心愿详情页
					uni.navigateTo({
						url: `/subpkg-wish/pages/wishDetail/wishDetail?id=${message.wishId}`
					})
				}
			},
			
			// 全部标记为已读
			markAllAsRead() {
				this.messageStore.markAllAsRead()
				uni.showToast({
					title: '已全部标记为已读',
					icon: 'success'
				})
			},
			
			// 格式化时间
			formatTime(dateString) {
				const date = new Date(dateString)
				const now = new Date()
				
				// 计算时间差（毫秒）
				const diff = now - date
				
				// 今天内
				if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
					const hours = date.getHours().toString().padStart(2, '0')
					const minutes = date.getMinutes().toString().padStart(2, '0')
					return `今天 ${hours}:${minutes}`
				}
				
				// 昨天
				const yesterday = new Date(now)
				yesterday.setDate(yesterday.getDate() - 1)
				if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth() && date.getFullYear() === yesterday.getFullYear()) {
					const hours = date.getHours().toString().padStart(2, '0')
					const minutes = date.getMinutes().toString().padStart(2, '0')
					return `昨天 ${hours}:${minutes}`
				}
				
				// 其他日期
				return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
			}
		}
	}
</script>

<style lang="scss">
	.message-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
	}
	
	.message-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #fff;
		
		.title {
			font-size: 32rpx;
			font-weight: 500;
		}
		
		.read-all {
			font-size: 28rpx;
			color: #8a2be2;
		}
	}
	
	.message-list {
		padding: 20rpx;
	}
	
	.empty-list {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 100rpx;
		
		.empty-text {
			font-size: 28rpx;
			color: #999;
		}
	}
	
	.message-item {
		padding: 30rpx;
		margin-bottom: 20rpx;
		position: relative;
		
		&.unread {
			border-left: 4rpx solid #8a2be2;
		}
		
		&.comment-message {
			background-color: #fafafa;
		}
		
		.message-dot {
			position: absolute;
			width: 16rpx;
			height: 16rpx;
			background-color: #8a2be2;
			border-radius: 50%;
			top: 38rpx;
			right: 30rpx;
		}
		
		.message-header-row {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
			
			.comment-avatar {
				width: 60rpx;
				height: 60rpx;
				border-radius: 30rpx;
				margin-right: 20rpx;
			}
		}
		
		.message-title {
			font-size: 32rpx;
			font-weight: 500;
			margin-bottom: 16rpx;
		}
		
		.message-wish-title {
			font-size: 28rpx;
			color: #8a2be2;
			margin-bottom: 12rpx;
		}
		
		.message-content {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 20rpx;
			line-height: 1.5;
			
			&.comment-content {
				background-color: #f0f0f0;
				padding: 16rpx;
				border-radius: 8rpx;
				color: #333;
			}
		}
		
		.message-time {
			font-size: 24rpx;
			color: #999;
			text-align: right;
		}
		
		.message-action {
			font-size: 24rpx;
			color: #8a2be2;
			text-align: right;
			margin-top: 10rpx;
		}
	}
</style> 
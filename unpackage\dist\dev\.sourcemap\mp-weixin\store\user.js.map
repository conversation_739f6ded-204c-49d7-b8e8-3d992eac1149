{"version": 3, "file": "user.js", "sources": ["store/user.js"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport loadingManager from '@/utils/loadingManager.js'\nimport { getCurrentInstance } from 'vue'\nimport syncManager from '@/utils/syncManager.js'\n\n// uni-id-co的云对象，延迟初始化\nlet uniIdCo = null;\n\n// 获取uni-id-co云对象的函数\nfunction getUniIdCo() {\n  if (!uniIdCo) {\n    try {\n      if (typeof uniCloud !== 'undefined' && uniCloud.importObject) {\n\tuniIdCo = uniCloud.importObject('uni-id-co', {\n          customUI: true\n\t});\n        // uni-id-co 初始化成功\n} else {\n        console.error('[store/user.js] uniCloud not available or not initialized');\n        return null;\n      }\n    } catch (error) {\n      console.error('[store/user.js] Failed to initialize uni-id-co:', error);\n      return null;\n    }\n  }\n  return uniIdCo;\n}\n\nexport const useUserStore = defineStore('user', {\n  state: () => ({\n    userInfo: (() => {\n      try {\n        const stored = uni.getStorageSync('userInfo');\n        return stored ? (typeof stored === 'string' ? JSON.parse(stored) : stored) : {};\n      } catch (e) {\n        console.error('[userStore] Error parsing userInfo from storage:', e);\n        return {};\n      }\n    })(),\n    isLogin: false,\n    token: uni.getStorageSync('uni_id_token') || uni.getStorageSync('token') || '', // 优先从uni_id_token读取\n    tokenExpired: 0,\n\n    // 是否跳过登录检查，设置为false时要求用户登录才能执行关键操作\n    skipLoginCheck: false,\n    // 新增：用于存储登录成功后需要执行的动作\n    postLoginAction: null, // 可以是一个函数引用或一个描述对象\n\n    // 🚀 新增：同步状态管理\n    _isSyncInProgress: false, // 防止重复同步\n\n    // 🚀 新增：智能验证缓存机制\n    lastTokenVerifyTime: 0, // 上次token验证时间\n    tokenVerifyInterval: 5 * 60 * 1000, // 验证间隔：5分钟\n    isTokenVerifying: false, // 是否正在验证token，避免并发验证\n    appLastActiveTime: Date.now(), // 应用最后活跃时间\n    quickAuthCache: { // 快速验证缓存\n      isValid: false,\n      timestamp: 0,\n      ttl: 30 * 1000 // 30秒内有效\n    },\n    \n    // 🔒 防重复导航机制\n    lastNavigationTime: 0, // 上次导航时间\n    navigationCooldown: 1000, // 导航冷却时间：1秒\n  }),\n  \n  getters: {\n    hasLogin: (state) => state.isLogin,\n    getUserInfo: (state) => state.userInfo,\n    // isLoggedIn 主要依赖 state.isLogin，skipLoginCheck 可作为特殊用途保留\n    isLoggedIn: (state) => state.isLogin || state.skipLoginCheck,\n    // Getter to safely get nickname\n    nickname: (state) => (state.userInfo && state.userInfo.nickname) ? state.userInfo.nickname : '未登录',\n    // Getter to safely get avatar\n    avatarUrl: (state) => {\n      if (state.userInfo) {\n        if (state.userInfo.avatarUrl) { // 1. 检查我们明确设置的 avatarUrl (通常是更新后)\n          return state.userInfo.avatarUrl;\n        }\n        if (state.userInfo.avatar_file && state.userInfo.avatar_file.url) { // 2. 检查 avatar_file.url (通常是数据库加载的)\n          return state.userInfo.avatar_file.url;\n        }\n        if (state.userInfo.avatar) { // 3. 检查 uni-id 可能使用的顶层 avatar 字段\n          return state.userInfo.avatar;\n        }\n      }\n      return '/static/default_avatar.png'; // 默认头像\n    },\n    userId: (state) => (state.userInfo && state.userInfo.uid) ? state.userInfo.uid : null,\n  },\n  \n  actions: {\n    // 登录成功时的处理逻辑\n    async loginSuccess(data, showSuccessToast = false) {\n      this.isLogin = true\n      this.userInfo = data.userInfo || {}\n      this.token = data.token || ''\n      this.tokenExpired = data.tokenExpired || 0\n      \n      // 🚀 更新验证缓存\n      this.lastTokenVerifyTime = Date.now()\n      this.quickAuthCache = {\n        isValid: true,\n        timestamp: Date.now(),\n        ttl: 30 * 1000\n      }\n      \n      // 确保userInfo为对象，不是字符串\n      if (typeof this.userInfo === 'string') {\n        try {\n          this.userInfo = JSON.parse(this.userInfo);\n        } catch (e) {\n          console.error('[userStore] Error parsing userInfo from string:', e);\n          this.userInfo = {};\n        }\n      }\n      \n      // 存储到本地\n      uni.setStorageSync('userInfo', JSON.stringify(this.userInfo))\n      if (this.token) {\n        // 优先使用uni-id的标准key\n        uni.setStorageSync('uni_id_token', this.token);\n        // 为了兼容性，也保存一份到token key\n        uni.setStorageSync('token', this.token);\n      } else {\n        uni.removeStorageSync('uni_id_token');\n        uni.removeStorageSync('token');\n      }\n\n      // 登录成功后，重新初始化需要认证的stores\n      await this.initDependentStores(showSuccessToast);\n\n      // 检查并执行登录后动作\n      if (typeof this.postLoginAction === 'function') {\n        try {\n          this.postLoginAction();\n        } catch (e) {\n          console.error('[userStore] Error executing postLoginAction:', e);\n        }\n        this.clearPostLoginAction(); // 执行后清除\n      } else if (this.postLoginAction && typeof this.postLoginAction.handlerName === 'string') {\n        // 如果是更复杂的场景，可能需要根据handlerName和payload去调用预定义的函数\n        this.clearPostLoginAction();\n      }\n    },\n    \n    // 退出登录\n    logout() {\n      \n      this.userInfo = {}\n      this.isLogin = false\n      this.token = ''\n      this.tokenExpired = 0\n      \n      // 🚀 清除验证缓存\n      this.lastTokenVerifyTime = 0\n      this.quickAuthCache = {\n        isValid: false,\n        timestamp: 0,\n        ttl: 30 * 1000\n      }\n      this.isTokenVerifying = false\n      \n      // 清除所有可能的token存储\n      uni.removeStorageSync('userInfo')\n      uni.removeStorageSync('token') // 旧的 token key，以防万一\n      uni.removeStorageSync('uni_id_token')\n      uni.removeStorageSync('uni_id_token_expired')\n      uni.removeStorageSync('uid') // 旧的 uid key\n\n      // 清除用户相关的业务数据存储\n      uni.removeStorageSync('wishList')\n      uni.removeStorageSync('wishLastSyncTime')\n      uni.removeStorageSync('groups')\n      uni.removeStorageSync('groupsLastSyncTime')\n      uni.removeStorageSync('messages')\n      uni.removeStorageSync('messagesLastSyncTime')\n      \n      // 清理其他 store 的数据\n      try {\n        // 使用动态导入避免循环依赖\n        const { useWishStore } = require('./wish.js')\n        const { useGroupStore } = require('./group.js')\n        \n        const wishStore = useWishStore()\n        const groupStore = useGroupStore()\n        \n        // 清理 wishStore\n        if (wishStore) {\n          wishStore.clearLocalData()\n        }\n        \n        // 清理 groupStore\n        if (groupStore && typeof groupStore.clearLocalData === 'function') {\n          groupStore.clearLocalData()\n        }\n      } catch (error) {\n        console.warn('[userStore] Error clearing other stores:', error)\n        // 即使其他 store 清理失败，也要继续完成登出\n      }\n\n      // 销毁同步管理器\n      try {\n        syncManager.destroy()\n      } catch (syncError) {\n        console.warn('[userStore] Error destroying sync manager:', syncError)\n      }\n    },\n    \n    // 🚀 智能验证方法：本地快速检查\n    quickLocalAuthCheck() {\n      let now\n      try {\n        // 0. 确保缓存对象存在\n        if (!this.quickAuthCache) {\n          this.quickAuthCache = {\n            isValid: false,\n            timestamp: 0,\n            ttl: 30 * 1000\n          }\n        }\n        \n        // 1. 检查快速缓存\n        now = Date.now()\n        if (this.quickAuthCache.isValid && \n            (now - this.quickAuthCache.timestamp) < this.quickAuthCache.ttl) {\n          return { needsVerification: false, isAuthenticated: true }\n        }\n        \n        // 2. 检查基本状态\n        if (!this._performLoginCheck()) {\n          return { needsVerification: false, isAuthenticated: false }\n        }\n      } catch (error) {\n        console.error('[userStore] Error in quickLocalAuthCheck initial checks:', error)\n        return { needsVerification: false, isAuthenticated: false }\n      }\n      \n      // 3. 检查token是否明显过期（本地解析）\n      try {\n        const tokenParts = this.token.split('.')\n        if (tokenParts.length === 3) {\n          // 使用 uni-app 兼容的 base64 解码方式\n          const base64Str = tokenParts[1]\n          // 添加填充字符以确保长度是4的倍数\n          const paddedBase64 = base64Str + '==='.slice((base64Str.length + 3) % 4)\n          \n          let decodedStr = ''\n          if (typeof atob !== 'undefined') {\n            // 浏览器环境\n            decodedStr = atob(paddedBase64)\n          } else {\n            // 小程序环境：使用简单的 base64 解码替代方案\n            try {\n              // 先尝试 uni 的方法\n              if (uni.base64ToArrayBuffer) {\n                const buffer = uni.base64ToArrayBuffer(paddedBase64)\n                decodedStr = String.fromCharCode.apply(null, new Uint8Array(buffer))\n              } else {\n                // 跳过本地token解析，直接进行服务端验证\n              }\n            } catch (decodeError) {\n              console.warn('[userStore] Base64 decode failed, skipping local token check:', decodeError)\n            }\n          }\n          \n          if (decodedStr) {\n            const payload = JSON.parse(decodedStr)\n            if (payload.exp && payload.exp * 1000 < Date.now()) {\n              return { needsVerification: false, isAuthenticated: false }\n            }\n          }\n        }\n      } catch (e) {\n        console.warn('[userStore] Error parsing token locally:', e)\n        // 解析失败不影响整体流程，继续后续检查\n      }\n      \n      try {\n        // 4. 检查是否需要服务端验证\n        if (!now) {\n          now = Date.now() // 确保 now 有值\n        }\n        const needsServerVerification = (now - this.lastTokenVerifyTime) > this.tokenVerifyInterval\n        if (!needsServerVerification) {\n          this.quickAuthCache = { isValid: true, timestamp: now, ttl: 30 * 1000 }\n          return { needsVerification: false, isAuthenticated: true }\n        }\n        \n        return { needsVerification: true, isAuthenticated: true }\n      } catch (error) {\n        console.error('[userStore] Error in quickLocalAuthCheck final checks:', error)\n        return { needsVerification: false, isAuthenticated: false }\n      }\n    },\n    \n    // 🚀 智能验证方法：按安全级别分层验证\n    async smartAuthCheck(securityLevel = 'basic') {\n      // 更新应用活跃时间\n      this.appLastActiveTime = Date.now()\n      \n      // 1. 快速本地检查\n      const quickCheck = this.quickLocalAuthCheck()\n      if (!quickCheck.needsVerification) {\n        return quickCheck.isAuthenticated\n      }\n      \n      // 2. 防止并发验证\n      if (this.isTokenVerifying) {\n        // 等待当前验证完成\n        let retries = 0\n        while (this.isTokenVerifying && retries < 20) { // 最多等待2秒\n          await new Promise(resolve => setTimeout(resolve, 100))\n          retries++\n        }\n        // 重新检查快速缓存\n        const recheckResult = this.quickLocalAuthCheck()\n        return !recheckResult.needsVerification && recheckResult.isAuthenticated\n      }\n      \n      // 3. 根据安全级别决定是否进行服务端验证\n      switch (securityLevel) {\n        case 'low':\n          // 低安全级别：信任本地状态，只在长时间未验证时才检查\n          if ((Date.now() - this.lastTokenVerifyTime) < this.tokenVerifyInterval * 3) {\n            this.quickAuthCache = { isValid: true, timestamp: Date.now(), ttl: 30 * 1000 }\n            return true\n          }\n          break\n          \n        case 'high':\n          // 高安全级别：每次都验证\n          break\n          \n        case 'basic':\n        default:\n          // 基础级别：按正常间隔验证\n          break\n      }\n      \n      // 4. 执行服务端验证\n      return await this.performServerTokenVerification()\n    },\n    \n    // 🚀 执行服务端token验证\n    async performServerTokenVerification() {\n      this.isTokenVerifying = true\n      \n      try {\n        const localToken = this.token\n        if (!localToken) {\n          this.logout()\n          return false\n        }\n        \n        const uniIdCo = getUniIdCo()\n        if (!uniIdCo) {\n          console.error('[userStore] uni-id-co not available for token check.')\n          this.logout()\n          return false\n        }\n        \n        const res = await uniIdCo.getAccountInfo()\n        \n        if (res.errCode === 0) {\n          // 验证成功，更新缓存\n          this.lastTokenVerifyTime = Date.now()\n          this.quickAuthCache = {\n            isValid: true,\n            timestamp: Date.now(),\n            ttl: 30 * 1000\n          }\n          console.log('[userStore] Server token verification successful')\n          return true\n        } else if (res.errCode === 'uni-id-token-expired' || res.errCode === 'uni-id-check-token-failed') {\n          console.warn('[userStore] Token expired or invalid on server:', res.errMsg)\n          this.logout()\n          return false\n        } else {\n          console.error('[userStore] Server token verification error:', res.errMsg)\n          this.logout()\n          return false\n        }\n      } catch (error) {\n        console.error('[userStore] Exception during server token verification:', error)\n        this.logout()\n        return false\n      } finally {\n        this.isTokenVerifying = false\n      }\n    },\n    \n    // 🚀 优化后的检查登录并重定向方法\n    async checkLoginAndRedirect(securityLevel = 'basic') {\n      // 快速检查，大多数情况下不需要网络请求\n      const isAuthenticated = await this.smartAuthCheck(securityLevel)\n      \n      if (!isAuthenticated && !this.skipLoginCheck) {\n        this.safeNavigateToLogin()\n        return false\n      }\n      \n      return isAuthenticated\n    },\n    \n    // 🔧 检测是否在开发者工具环境\n    isDevTool() {\n      // 简化检测逻辑，减少可能的问题\n      return process.env.NODE_ENV === 'development';\n    },\n\n    // 🔒 安全导航方法：防止重复导航（针对开发者工具优化）\n    safeNavigateToLogin() {\n      const now = Date.now()\n      // 🔧 开发者工具环境：增加冷却时间\n      const cooldown = this.isDevTool() ? this.navigationCooldown * 2 : this.navigationCooldown\n      \n      if (now - this.lastNavigationTime < cooldown) {\n        return false\n      }\n      \n      this.lastNavigationTime = now\n      \n      // 🔧 开发者工具环境：添加延迟和重试机制\n      const performNavigation = () => {\n        uni.navigateTo({\n          url: '/pages/login/login',\n          fail: (error) => {\n            console.error('[userStore] Navigation to login failed:', error)\n            \n            // 🔧 开发者工具环境：如果是超时错误，尝试重试\n            if (this.isDevTool() && error.errMsg && error.errMsg.includes('timeout')) {\n              setTimeout(() => {\n                uni.navigateTo({\n                  url: '/pages/login/login',\n                  fail: (retryError) => {\n                    console.error('[userStore] Retry login navigation also failed:', retryError)\n                    this.lastNavigationTime = 0 // 重置时间，允许再次重试\n                    uni.showToast({\n                      title: '跳转登录页失败，请重试',\n                      icon: 'none',\n                      duration: 2000\n                    })\n                  }\n                })\n              }, 1000)\n            } else {\n              // 导航失败时重置时间，允许重试\n              this.lastNavigationTime = 0\n            }\n          }\n        })\n      }\n      \n      // 🔧 开发者工具环境：添加短暂延迟\n      if (this.isDevTool()) {\n        setTimeout(performNavigation, 200)\n      } else {\n        performNavigation()\n      }\n      \n      return true\n    },\n\n    // 🚀 按需验证：根据操作类型选择验证级别\n    async ensureAuthenticated(options = {}) {\n      const {\n        operation = 'read',     // 'read', 'write', 'sensitive'\n        showToast = true,\n        redirectOnFail = true\n      } = options\n      \n      // 根据操作类型确定安全级别\n      let securityLevel = 'basic'\n      switch (operation) {\n        case 'read':\n          securityLevel = 'low'    // 读操作：低安全级别\n          break\n        case 'write':\n          securityLevel = 'basic'  // 写操作：基础级别  \n          break\n        case 'sensitive':\n          securityLevel = 'high'   // 敏感操作：高安全级别\n          break\n      }\n      \n      const isAuthenticated = await this.smartAuthCheck(securityLevel)\n      \n      if (!isAuthenticated) {\n        if (showToast) {\n          uni.showToast({\n            title: '请先登录',\n            icon: 'none'\n          })\n        }\n        \n        if (redirectOnFail) {\n          this.safeNavigateToLogin()\n        }\n      }\n      \n      return isAuthenticated\n    },\n    \n    // 用于 uni.getUserProfile 获取信息后更新 store 和（可选）后端\n    async updateProfileDetails(profileFromWx) { // profileFromWx 是 uni.getUserProfile 返回的原始对象\n      if (!this.userInfo || !this.userInfo.uid) {\n        console.error('Cannot update profile, user or UID not found in store.');\n        return;\n      }\n      \n      const newProfileData = {\n        nickname: profileFromWx.nickName,\n        avatarUrl: profileFromWx.avatarUrl,\n        gender: profileFromWx.gender, // 0: 未知, 1: 男性, 2: 女性\n        country: profileFromWx.country,\n        province: profileFromWx.province,\n        city: profileFromWx.city,\n      };\n\n      this.userInfo = {\n        ...this.userInfo,\n        ...newProfileData\n      };\n      uni.setStorageSync('userInfo', JSON.stringify(this.userInfo));\n\n      // 重要：将更新后的用户信息发送到 uni-id 后端进行持久化\n      try {\n        const uniIdCo = getUniIdCo();\n        if (!uniIdCo) {\n          console.error('Cannot update profile: uni-id-co not available');\n          return;\n        }\n        \n        // uni-id 可能期望 avatarUrl 存储在 avatar 字段，或需要特殊处理文件\n        // 检查 uni-id 文档关于 updateUser 或类似方法的具体参数\n        const updateResult = await uniIdCo.updateUser({\n          uid: this.userInfo.uid, // 确保传递了uid\n          nickname: newProfileData.nickname,\n          avatar: newProfileData.avatarUrl // uni-id users表通常用 avatar 字段存头像URL\n          // 根据需要可以传递 gender 等其他信息\n        });\n        if (updateResult.errCode !== 0) {\n          console.error('Failed to update profile on uni-id server:', updateResult.errMsg);\n        }\n      } catch (error) {\n        console.error('Error calling uni-id to update user profile:', error);\n      }\n    },\n\n    // 从本地存储加载并验证用户状态\n    // 🚀 优化后的从本地存储加载用户数据（仅在应用启动时使用）\n    async loadUserFromStorage() {\n      const localToken = uni.getStorageSync('uni_id_token') || uni.getStorageSync('token');\n      \n      if (!localToken) {\n        this.logout();\n        return Promise.resolve(false);\n      }\n      \n      // 设置基本状态，但不立即验证（除非必要）\n      this.token = localToken;\n      \n      // 尝试从本地存储恢复用户信息\n      let storedUserInfo = {};\n      try {\n        const stored = uni.getStorageSync('userInfo');\n        storedUserInfo = stored ? (typeof stored === 'string' ? JSON.parse(stored) : stored) : {};\n      } catch (e) {\n        console.error('[userStore] Error parsing stored userInfo:', e);\n        storedUserInfo = {};\n      }\n      \n      if (storedUserInfo && Object.keys(storedUserInfo).length > 0) {\n        // 有本地用户信息，先恢复状态\n        this.isLogin = true;\n        this.userInfo = storedUserInfo;\n        \n        // 延迟验证token（非阻塞）\n        setTimeout(() => {\n          this.smartAuthCheck('basic').catch(error => {\n            console.error('[userStore] Background token verification failed:', error);\n          });\n        }, 1000);\n        \n        return Promise.resolve(true);\n      } else {\n        // 没有本地用户信息，需要立即验证\n        return await this.performServerTokenVerification();\n      }\n    },\n\n    // 通用登录状态检查方法（避免重复逻辑）\n    _performLoginCheck() {\n      // 基础检查：token 和登录状态\n      if (!this.token || !this.isLogin) {\n        return false;\n      }\n\n      // 检查用户信息完整性\n      if (!this.userInfo || Object.keys(this.userInfo).length === 0) {\n        return false;\n      }\n\n      return true;\n    },\n\n    // 检查登录状态 (可被 loadUserFromStorage 替代或基于其结果)\n    checkLoginStatus() {\n      this.loadUserFromStorage(); // 确保状态最新\n      return this._performLoginCheck();\n    },\n    \n\n    \n    setSkipLoginCheck(skip) {\n      this.skipLoginCheck = skip;\n      // console.log(`登录检查已${skip ? '跳过' : '启用'}`);\n    },\n    \n    updateUserInfo(updatedInfo) {\n      this.userInfo = { ...this.userInfo, ...updatedInfo }\n      uni.setStorageSync('userInfo', JSON.stringify(this.userInfo))\n      return true\n    },\n    \n\n    \n    clearStorage() {\n      try {\n        uni.removeStorageSync('userInfo')\n        uni.removeStorageSync('token')\n        uni.removeStorageSync('uni_id_token')\n        uni.removeStorageSync('uni_id_token_expired')\n        uni.removeStorageSync('uid')\n        \n        this.userInfo = null\n        this.isLogin = false\n        this.token = ''\n        this.tokenExpired = 0\n        \n\n        \n        return true\n      } catch (e) {\n        console.error('清除存储数据失败:', e)\n        return false\n      }\n    },\n\n    // 设置登录后要执行的动作\n    setPostLoginAction(action) {\n      // action 可以是一个函数，或者一个描述对象 { handlerName: 'name', payload: {} }\n      this.postLoginAction = action;\n    },\n\n    // 清除登录后动作\n    clearPostLoginAction() {\n      this.postLoginAction = null;\n    },\n\n    // 检查用户是否有特定角色 (如果未来引入RBAC)\n    hasRole(role) {\n      if (!this.isLogin || !this.userInfo || !this.userInfo.roles) {\n        return false;\n      }\n      return this.userInfo.roles.includes(role);\n    },\n\n    // 强制清除所有认证数据的方法\n    forceLogout() {\n      this.logout();\n      // 额外清理一些可能遗留的数据\n      uni.removeStorageSync('uni_id_token_expired');\n      uni.removeStorageSync('uni_id_user');\n      uni.removeStorageSync('uni_id_uid');\n    },\n\n    // 修复损坏的userInfo数据\n    fixUserInfoData() {\n      try {\n        const stored = uni.getStorageSync('userInfo');\n        \n        // 如果userInfo是错误的格式（类数组对象），清除它\n        if (stored && typeof stored === 'object' && !Array.isArray(stored) && stored.hasOwnProperty('0')) {\n          uni.removeStorageSync('userInfo');\n          this.userInfo = {};\n          this.logout(); // 强制重新登录\n          return true;\n        }\n        \n        return false;\n      } catch (e) {\n        console.error('[userStore] Error during userInfo fix:', e);\n        uni.removeStorageSync('userInfo');\n        this.userInfo = {};\n        this.logout();\n        return true;\n      }\n    },\n\n    // 登录成功后，重新初始化需要认证的stores\n    async initDependentStores(showSuccessToast = false) {\n      try {\n        // 优化方案：智能同步而不是清理本地数据\n        // 使用动态导入避免循环依赖\n        const { useGroupStore } = require('./group.js');\n        const { useWishStore } = require('./wish.js');\n        \n        const groupStore = useGroupStore();\n        const wishStore = useWishStore();\n        \n        // 统一的数据初始化和同步\n        console.log('[userStore] 开始初始化和同步数据...')\n\n        try {\n          // 初始化分组数据（包含同步）\n          console.log('[userStore] 初始化分组数据...')\n          await groupStore.initGroups();\n          console.log('[userStore] ✅ 分组数据初始化完成')\n        } catch (error) {\n          console.error('[userStore] ❌ 分组数据初始化失败:', error)\n        }\n\n        try {\n          // 初始化心愿数据（包含同步）\n          console.log('[userStore] 初始化心愿数据...')\n          await wishStore.initWishList();\n          console.log('[userStore] ✅ 心愿数据初始化完成')\n        } catch (error) {\n          console.error('[userStore] ❌ 心愿数据初始化失败:', error)\n        }\n        \n        // 初始化同步管理器\n        try {\n          // 启动同步管理器\n          await syncManager.init();\n        } catch (syncError) {\n          console.error('[userStore] ❌ Sync manager initialization failed:', syncError);\n          // 同步管理器初始化失败不影响登录流程\n        }\n        \n        // 只在真正登录时显示成功提示，token验证时不显示\n        if (showSuccessToast) {\n          uni.showToast({\n            title: '登录成功！',\n            icon: 'success',\n            duration: 1500\n          });\n        }\n        \n        // 短暂延迟后发送全局刷新事件\n        setTimeout(() => {\n          uni.$emit('user-login-success', {\n            userId: this.userId,\n            timestamp: Date.now()\n          });\n        }, 500);\n        \n      } catch (error) {\n        console.error('[userStore] Error reinitializing dependent stores:', error);\n        \n        // 显示提示\n        uni.showToast({\n          title: '登录成功',\n          icon: 'success',\n          duration: 1500\n        });\n      }\n    },\n\n    // 手动同步所有数据（用于下拉刷新）- 智能增量同步\n    async manualSyncAllData(silent = false) {\n\n      if (!this.isLogin) {\n        if (!silent) {\n          uni.showToast({\n            title: '请先登录',\n            icon: 'none'\n          });\n        }\n        return false;\n      }\n\n      try {\n        // 根据silent参数决定是否使用loadingManager\n        if (silent) {\n          // 静默模式：直接调用同步方法，不显示任何加载提示\n          const syncResult = await this.performIntelligentSync(silent);\n          return syncResult;\n        } else {\n          // 非静默模式：使用loadingManager显示加载提示\n          const syncResult = await loadingManager.wrap(\n            () => this.performIntelligentSync(silent),\n            {\n              title: '正在检查数据更新...',\n              id: 'user_intelligent_sync',\n              timeout: 15000,\n              silent: false,\n              showSuccess: false, // 智能同步方法内部处理成功提示\n              showError: false, // 智能同步方法内部处理错误\n              errorTitle: '同步失败，请重试'\n            }\n          );\n          return syncResult;\n        }\n      } catch (error) {\n        console.error('[userStore] Manual sync failed:', error);\n        return false;\n      }\n    },\n\n    // 执行智能增量同步 - 🚀 增强防重复机制\n    async performIntelligentSync(silent = false) {\n      console.log('[userStore] 开始执行智能增量同步, silent:', silent);\n\n      // 🚀 防重复检查：如果正在同步中，直接返回\n      if (this._isSyncInProgress) {\n        console.log('[userStore] 智能同步已在进行中，跳过重复请求');\n        return false;\n      }\n\n      try {\n        // 🚀 标记同步开始\n        this._isSyncInProgress = true;\n        console.log('[userStore] 同步状态标记为进行中');\n\n        // 使用动态导入避免循环依赖\n        const { useGroupStore } = require('./group.js');\n        const { useWishStore } = require('./wish.js');\n\n        const groupStore = useGroupStore();\n        const wishStore = useWishStore();\n\n        console.log('[userStore] Store实例获取成功:', {\n          groupStore: !!groupStore,\n          wishStore: !!wishStore,\n          groupSmartSync: typeof groupStore.smartSync,\n          wishSmartSync: typeof wishStore.smartSync\n        });\n\n        let hasUpdates = false;\n        const syncResults = {\n          groups: { checked: false, updated: false, count: 0 },\n          wishes: { checked: false, updated: false, count: 0 }\n        };\n\n        // 1. 智能同步分组数据\n        console.log('[userStore] 开始同步分组数据...');\n        try {\n          const groupSyncResult = await groupStore.smartSync();\n          console.log('[userStore] 分组同步结果:', groupSyncResult);\n          syncResults.groups.checked = true;\n          syncResults.groups.updated = groupSyncResult.hasUpdates;\n          syncResults.groups.count = groupSyncResult.updatedCount || 0;\n\n          if (groupSyncResult.hasUpdates) {\n            hasUpdates = true;\n          }\n        } catch (groupError) {\n          console.error('[userStore] 分组同步出错:', groupError);\n          // 捕获并处理分组同步错误，但不中断整个同步流程\n          if (this._isMultiDeviceConflictError(groupError)) {\n            console.log('[userStore] 分组多设备冲突，静默处理');\n            // 多设备冲突静默处理\n          } else {\n            console.error('[userStore] Group sync failed:', groupError);\n            // 非冲突错误继续抛出\n            throw groupError;\n          }\n        }\n\n        // 2. 智能同步心愿数据\n        console.log('[userStore] 开始同步心愿数据...');\n        try {\n          const wishSyncResult = await wishStore.smartSync();\n          console.log('[userStore] 心愿同步结果:', wishSyncResult);\n          syncResults.wishes.checked = true;\n          syncResults.wishes.updated = wishSyncResult.hasUpdates;\n          syncResults.wishes.count = wishSyncResult.updatedCount || 0;\n\n          if (wishSyncResult.hasUpdates) {\n            hasUpdates = true;\n          }\n        } catch (wishError) {\n          console.error('[userStore] 心愿同步出错:', wishError);\n          // 捕获并处理心愿同步错误，但不中断整个同步流程\n          if (this._isMultiDeviceConflictError(wishError)) {\n            console.log('[userStore] 心愿多设备冲突，静默处理');\n            // 多设备冲突静默处理\n          } else {\n            console.error('[userStore] Wish sync failed:', wishError);\n            // 非冲突错误继续抛出\n            throw wishError;\n          }\n        }\n\n        // 3. 显示同步结果（仅在非静默模式下）\n        console.log('[userStore] 同步完成，最终结果:', {\n          hasUpdates,\n          syncResults,\n          silent\n        });\n\n        if (!silent) {\n          if (hasUpdates) {\n            let message = '数据已更新';\n            const updates = [];\n            if (syncResults.groups.updated) updates.push(`分组${syncResults.groups.count}个`);\n            if (syncResults.wishes.updated) updates.push(`心愿${syncResults.wishes.count}个`);\n\n            if (updates.length > 0) {\n              message = `更新了${updates.join('、')}`;\n            }\n\n            // 显示同步成功提示\n            console.log('[userStore] 同步完成:', message);\n            uni.showToast({\n              title: message,\n              icon: 'success',\n              duration: 2000\n            });\n          } else {\n            // 显示数据已是最新提示\n            console.log('[userStore] 数据已是最新');\n            uni.showToast({\n              title: '数据已是最新',\n              icon: 'success',\n              duration: 1500\n            });\n          }\n        }\n\n        return hasUpdates;\n\n      } catch (error) {\n        // 检查是否是多设备冲突错误 - 🔧 完全静默处理\n        if (this._isMultiDeviceConflictError(error)) {\n          // 冲突错误完全静默处理，不显示任何弹窗\n          console.log('[userStore] 多设备冲突已静默处理');\n          return false;\n        }\n        \n        // 检查是否是getSyncSummary相关的错误 - 🔧 静默处理\n        if (error.message && (\n          error.message.includes('getSyncSummary') ||\n          error.message.includes('获取云端') ||\n          error.message.includes('摘要失败')\n        )) {\n          console.error('[userStore] Sync summary API error:', error);\n          // 🔧 删除弹窗，静默处理\n          return false;\n        }\n\n        console.error('[userStore] Intelligent sync failed:', error);\n        // 🔧 删除同步失败弹窗，静默处理\n        return false; // 不抛出错误，避免阻塞用户操作\n      } finally {\n        // 🚀 重置同步状态标记\n        this._isSyncInProgress = false;\n      }\n    },\n\n    // 检查是否是多设备冲突错误\n    _isMultiDeviceConflictError(error) {\n      if (!error || !error.message) return false;\n      \n      const conflictMessages = [\n        '心愿不存在或无权限',\n        '分组不存在或无权限',\n        'not found',\n        'permission denied',\n        '无权限操作'\n      ];\n      \n      return conflictMessages.some(msg => \n        error.message.toLowerCase().includes(msg.toLowerCase())\n      );\n    }\n  }\n}) "], "names": ["uniCloud", "uni", "defineStore", "syncManager", "uniIdCo", "loadingManager"], "mappings": ";;;;;AAMA,IAAI,UAAU;AAGd,SAAS,aAAa;AACpB,MAAI,CAAC,SAAS;AACR,QAAA;AACF,UAAI,OAAOA,cAAA,OAAa,eAAeA,cAAAA,GAAS,cAAc;AACzD,kBAAAA,cAAAA,GAAS,aAAa,aAAa;AAAA,UACpC,UAAU;AAAA,QAAA,CAClB;AAAA,MAAA,OAEK;AACeC,sBAAAA,MAAA,MAAA,SAAA,uBAAA,2DAA2D;AAClE,eAAA;AAAA,MACT;AAAA,aACO,OAAO;AACdA,oBAAA,MAAA,MAAA,SAAA,uBAAc,mDAAmD,KAAK;AAC/D,aAAA;AAAA,IACT;AAAA,EACF;AACO,SAAA;AACT;AAEa,MAAA,eAAeC,0BAAY,QAAQ;AAAA,EAC9C,OAAO,OAAO;AAAA,IACZ,WAAW,MAAM;AACX,UAAA;AACI,cAAA,SAASD,cAAAA,MAAI,eAAe,UAAU;AACrC,eAAA,SAAU,OAAO,WAAW,WAAW,KAAK,MAAM,MAAM,IAAI,SAAU;eACtE,GAAG;AACVA,sBAAA,MAAA,MAAA,SAAA,uBAAc,oDAAoD,CAAC;AACnE,eAAO;MACT;AAAA,IAAA,GACC;AAAA,IACH,SAAS;AAAA,IACT,OAAOA,cAAAA,MAAI,eAAe,cAAc,KAAKA,cAAAA,MAAI,eAAe,OAAO,KAAK;AAAA;AAAA,IAC5E,cAAc;AAAA;AAAA,IAGd,gBAAgB;AAAA;AAAA,IAEhB,iBAAiB;AAAA;AAAA;AAAA,IAGjB,mBAAmB;AAAA;AAAA;AAAA,IAGnB,qBAAqB;AAAA;AAAA,IACrB,qBAAqB,IAAI,KAAK;AAAA;AAAA,IAC9B,kBAAkB;AAAA;AAAA,IAClB,mBAAmB,KAAK,IAAI;AAAA;AAAA,IAC5B,gBAAgB;AAAA;AAAA,MACd,SAAS;AAAA,MACT,WAAW;AAAA,MACX,KAAK,KAAK;AAAA;AAAA,IACZ;AAAA;AAAA,IAGA,oBAAoB;AAAA;AAAA,IACpB,oBAAoB;AAAA;AAAA,EAAA;AAAA,EAGtB,SAAS;AAAA,IACP,UAAU,CAAC,UAAU,MAAM;AAAA,IAC3B,aAAa,CAAC,UAAU,MAAM;AAAA;AAAA,IAE9B,YAAY,CAAC,UAAU,MAAM,WAAW,MAAM;AAAA;AAAA,IAE9C,UAAU,CAAC,UAAW,MAAM,YAAY,MAAM,SAAS,WAAY,MAAM,SAAS,WAAW;AAAA;AAAA,IAE7F,WAAW,CAAC,UAAU;AACpB,UAAI,MAAM,UAAU;AACd,YAAA,MAAM,SAAS,WAAW;AAC5B,iBAAO,MAAM,SAAS;AAAA,QACxB;AACA,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,YAAY,KAAK;AACzD,iBAAA,MAAM,SAAS,YAAY;AAAA,QACpC;AACI,YAAA,MAAM,SAAS,QAAQ;AACzB,iBAAO,MAAM,SAAS;AAAA,QACxB;AAAA,MACF;AACO,aAAA;AAAA,IACT;AAAA,IACA,QAAQ,CAAC,UAAW,MAAM,YAAY,MAAM,SAAS,MAAO,MAAM,SAAS,MAAM;AAAA,EACnF;AAAA,EAEA,SAAS;AAAA;AAAA,IAEP,MAAM,aAAa,MAAM,mBAAmB,OAAO;AACjD,WAAK,UAAU;AACV,WAAA,WAAW,KAAK,YAAY,CAAA;AAC5B,WAAA,QAAQ,KAAK,SAAS;AACtB,WAAA,eAAe,KAAK,gBAAgB;AAGpC,WAAA,sBAAsB,KAAK;AAChC,WAAK,iBAAiB;AAAA,QACpB,SAAS;AAAA,QACT,WAAW,KAAK,IAAI;AAAA,QACpB,KAAK,KAAK;AAAA,MAAA;AAIR,UAAA,OAAO,KAAK,aAAa,UAAU;AACjC,YAAA;AACF,eAAK,WAAW,KAAK,MAAM,KAAK,QAAQ;AAAA,iBACjC,GAAG;AACVA,wBAAA,MAAA,MAAA,SAAA,wBAAc,mDAAmD,CAAC;AAClE,eAAK,WAAW;QAClB;AAAA,MACF;AAGAA,oBAAA,MAAI,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC5D,UAAI,KAAK,OAAO;AAEVA,sBAAAA,MAAA,eAAe,gBAAgB,KAAK,KAAK;AAEzCA,sBAAAA,MAAA,eAAe,SAAS,KAAK,KAAK;AAAA,MAAA,OACjC;AACLA,4BAAI,kBAAkB,cAAc;AACpCA,4BAAI,kBAAkB,OAAO;AAAA,MAC/B;AAGM,YAAA,KAAK,oBAAoB,gBAAgB;AAG3C,UAAA,OAAO,KAAK,oBAAoB,YAAY;AAC1C,YAAA;AACF,eAAK,gBAAgB;AAAA,iBACd,GAAG;AACVA,wBAAA,MAAA,MAAA,SAAA,wBAAc,gDAAgD,CAAC;AAAA,QACjE;AACA,aAAK,qBAAqB;AAAA,MAAA,WACjB,KAAK,mBAAmB,OAAO,KAAK,gBAAgB,gBAAgB,UAAU;AAEvF,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAAA;AAAA,IAGA,SAAS;AAEP,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,eAAe;AAGpB,WAAK,sBAAsB;AAC3B,WAAK,iBAAiB;AAAA,QACpB,SAAS;AAAA,QACT,WAAW;AAAA,QACX,KAAK,KAAK;AAAA,MAAA;AAEZ,WAAK,mBAAmB;AAGxBA,0BAAI,kBAAkB,UAAU;AAChCA,0BAAI,kBAAkB,OAAO;AAC7BA,0BAAI,kBAAkB,cAAc;AACpCA,0BAAI,kBAAkB,sBAAsB;AAC5CA,0BAAI,kBAAkB,KAAK;AAG3BA,0BAAI,kBAAkB,UAAU;AAChCA,0BAAI,kBAAkB,kBAAkB;AACxCA,0BAAI,kBAAkB,QAAQ;AAC9BA,0BAAI,kBAAkB,oBAAoB;AAC1CA,0BAAI,kBAAkB,UAAU;AAChCA,0BAAI,kBAAkB,sBAAsB;AAGxC,UAAA;AAEF,cAAM,EAAE,aAAA,IAAiB,QAAQ,WAAW;AAC5C,cAAM,EAAE,cAAA,IAAkB,QAAQ,YAAY;AAE9C,cAAM,YAAY;AAClB,cAAM,aAAa;AAGnB,YAAI,WAAW;AACb,oBAAU,eAAe;AAAA,QAC3B;AAGA,YAAI,cAAc,OAAO,WAAW,mBAAmB,YAAY;AACjE,qBAAW,eAAe;AAAA,QAC5B;AAAA,eACO,OAAO;AACdA,sBAAA,MAAa,MAAA,QAAA,wBAAA,4CAA4C,KAAK;AAAA,MAEhE;AAGI,UAAA;AACFE,0BAAA,YAAY,QAAQ;AAAA,eACb,WAAW;AAClBF,sBAAA,MAAA,MAAA,QAAA,wBAAa,8CAA8C,SAAS;AAAA,MACtE;AAAA,IACF;AAAA;AAAA,IAGA,sBAAsB;AAChB,UAAA;AACA,UAAA;AAEE,YAAA,CAAC,KAAK,gBAAgB;AACxB,eAAK,iBAAiB;AAAA,YACpB,SAAS;AAAA,YACT,WAAW;AAAA,YACX,KAAK,KAAK;AAAA,UAAA;AAAA,QAEd;AAGA,cAAM,KAAK;AACP,YAAA,KAAK,eAAe,WACnB,MAAM,KAAK,eAAe,YAAa,KAAK,eAAe,KAAK;AACnE,iBAAO,EAAE,mBAAmB,OAAO,iBAAiB,KAAK;AAAA,QAC3D;AAGI,YAAA,CAAC,KAAK,sBAAsB;AAC9B,iBAAO,EAAE,mBAAmB,OAAO,iBAAiB,MAAM;AAAA,QAC5D;AAAA,eACO,OAAO;AACdA,sBAAA,MAAc,MAAA,SAAA,wBAAA,4DAA4D,KAAK;AAC/E,eAAO,EAAE,mBAAmB,OAAO,iBAAiB,MAAM;AAAA,MAC5D;AAGI,UAAA;AACF,cAAM,aAAa,KAAK,MAAM,MAAM,GAAG;AACnC,YAAA,WAAW,WAAW,GAAG;AAErB,gBAAA,YAAY,WAAW,CAAC;AAE9B,gBAAM,eAAe,YAAY,MAAM,OAAO,UAAU,SAAS,KAAK,CAAC;AAEvE,cAAI,aAAa;AACb,cAAA,OAAO,SAAS,aAAa;AAE/B,yBAAa,KAAK,YAAY;AAAA,UAAA,OACzB;AAED,gBAAA;AAEF,kBAAIA,cAAAA,MAAI,qBAAqB;AACrB,sBAAA,SAASA,cAAAA,MAAI,oBAAoB,YAAY;AACnD,6BAAa,OAAO,aAAa,MAAM,MAAM,IAAI,WAAW,MAAM,CAAC;AAAA,cAAA,OAC9D;AAAA,cAEP;AAAA,qBACO,aAAa;AACpBA,4BAAA,MAAa,MAAA,QAAA,wBAAA,iEAAiE,WAAW;AAAA,YAC3F;AAAA,UACF;AAEA,cAAI,YAAY;AACR,kBAAA,UAAU,KAAK,MAAM,UAAU;AACrC,gBAAI,QAAQ,OAAO,QAAQ,MAAM,MAAO,KAAK,OAAO;AAClD,qBAAO,EAAE,mBAAmB,OAAO,iBAAiB,MAAM;AAAA,YAC5D;AAAA,UACF;AAAA,QACF;AAAA,eACO,GAAG;AACVA,sBAAA,MAAA,MAAA,QAAA,wBAAa,4CAA4C,CAAC;AAAA,MAE5D;AAEI,UAAA;AAEF,YAAI,CAAC,KAAK;AACR,gBAAM,KAAK;QACb;AACA,cAAM,0BAA2B,MAAM,KAAK,sBAAuB,KAAK;AACxE,YAAI,CAAC,yBAAyB;AACvB,eAAA,iBAAiB,EAAE,SAAS,MAAM,WAAW,KAAK,KAAK,KAAK;AACjE,iBAAO,EAAE,mBAAmB,OAAO,iBAAiB,KAAK;AAAA,QAC3D;AAEA,eAAO,EAAE,mBAAmB,MAAM,iBAAiB,KAAK;AAAA,eACjD,OAAO;AACdA,sBAAA,MAAc,MAAA,SAAA,wBAAA,0DAA0D,KAAK;AAC7E,eAAO,EAAE,mBAAmB,OAAO,iBAAiB,MAAM;AAAA,MAC5D;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,eAAe,gBAAgB,SAAS;AAEvC,WAAA,oBAAoB,KAAK;AAGxB,YAAA,aAAa,KAAK;AACpB,UAAA,CAAC,WAAW,mBAAmB;AACjC,eAAO,WAAW;AAAA,MACpB;AAGA,UAAI,KAAK,kBAAkB;AAEzB,YAAI,UAAU;AACP,eAAA,KAAK,oBAAoB,UAAU,IAAI;AAC5C,gBAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,SAAS,GAAG,CAAC;AACrD;AAAA,QACF;AAEM,cAAA,gBAAgB,KAAK;AACpB,eAAA,CAAC,cAAc,qBAAqB,cAAc;AAAA,MAC3D;AAGA,cAAQ,eAAe;AAAA,QACrB,KAAK;AAEH,cAAK,KAAK,QAAQ,KAAK,sBAAuB,KAAK,sBAAsB,GAAG;AACrE,iBAAA,iBAAiB,EAAE,SAAS,MAAM,WAAW,KAAK,OAAO,KAAK,KAAK,IAAK;AACtE,mBAAA;AAAA,UACT;AACA;AAAA,MAUJ;AAGO,aAAA,MAAM,KAAK;IACpB;AAAA;AAAA,IAGA,MAAM,iCAAiC;AACrC,WAAK,mBAAmB;AAEpB,UAAA;AACF,cAAM,aAAa,KAAK;AACxB,YAAI,CAAC,YAAY;AACf,eAAK,OAAO;AACL,iBAAA;AAAA,QACT;AAEA,cAAMG,WAAU;AAChB,YAAI,CAACA,UAAS;AACZH,wBAAAA,MAAA,MAAA,SAAA,wBAAc,sDAAsD;AACpE,eAAK,OAAO;AACL,iBAAA;AAAA,QACT;AAEM,cAAA,MAAM,MAAMG,SAAQ;AAEtB,YAAA,IAAI,YAAY,GAAG;AAEhB,eAAA,sBAAsB,KAAK;AAChC,eAAK,iBAAiB;AAAA,YACpB,SAAS;AAAA,YACT,WAAW,KAAK,IAAI;AAAA,YACpB,KAAK,KAAK;AAAA,UAAA;AAEZH,wBAAAA,MAAA,MAAA,OAAA,wBAAY,kDAAkD;AACvD,iBAAA;AAAA,QAAA,WACE,IAAI,YAAY,0BAA0B,IAAI,YAAY,6BAA6B;AAChGA,wBAAA,MAAa,MAAA,QAAA,wBAAA,mDAAmD,IAAI,MAAM;AAC1E,eAAK,OAAO;AACL,iBAAA;AAAA,QAAA,OACF;AACLA,wBAAA,MAAc,MAAA,SAAA,wBAAA,gDAAgD,IAAI,MAAM;AACxE,eAAK,OAAO;AACL,iBAAA;AAAA,QACT;AAAA,eACO,OAAO;AACdA,sBAAA,MAAc,MAAA,SAAA,wBAAA,2DAA2D,KAAK;AAC9E,aAAK,OAAO;AACL,eAAA;AAAA,MAAA,UACP;AACA,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,sBAAsB,gBAAgB,SAAS;AAEnD,YAAM,kBAAkB,MAAM,KAAK,eAAe,aAAa;AAE/D,UAAI,CAAC,mBAAmB,CAAC,KAAK,gBAAgB;AAC5C,aAAK,oBAAoB;AAClB,eAAA;AAAA,MACT;AAEO,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,YAAY;AAEH,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,sBAAsB;AACd,YAAA,MAAM,KAAK;AAEjB,YAAM,WAAW,KAAK,cAAc,KAAK,qBAAqB,IAAI,KAAK;AAEnE,UAAA,MAAM,KAAK,qBAAqB,UAAU;AACrC,eAAA;AAAA,MACT;AAEA,WAAK,qBAAqB;AAG1B,YAAM,oBAAoB,MAAM;AAC9BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL,MAAM,CAAC,UAAU;AACfA,0BAAA,MAAA,MAAA,SAAA,wBAAc,2CAA2C,KAAK;AAG1D,gBAAA,KAAK,UAAe,KAAA,MAAM,UAAU,MAAM,OAAO,SAAS,SAAS,GAAG;AACxE,yBAAW,MAAM;AACfA,8BAAAA,MAAI,WAAW;AAAA,kBACb,KAAK;AAAA,kBACL,MAAM,CAAC,eAAe;AACpBA,kCAAA,MAAc,MAAA,SAAA,wBAAA,mDAAmD,UAAU;AAC3E,yBAAK,qBAAqB;AAC1BA,kCAAAA,MAAI,UAAU;AAAA,sBACZ,OAAO;AAAA,sBACP,MAAM;AAAA,sBACN,UAAU;AAAA,oBAAA,CACX;AAAA,kBACH;AAAA,gBAAA,CACD;AAAA,iBACA,GAAI;AAAA,YAAA,OACF;AAEL,mBAAK,qBAAqB;AAAA,YAC5B;AAAA,UACF;AAAA,QAAA,CACD;AAAA,MAAA;AAIC,UAAA,KAAK,aAAa;AACpB,mBAAW,mBAAmB,GAAG;AAAA,MAAA,OAC5B;AACa;MACpB;AAEO,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,MAAM,oBAAoB,UAAU,IAAI;AAChC,YAAA;AAAA,QACJ,YAAY;AAAA;AAAA,QACZ,YAAY;AAAA,QACZ,iBAAiB;AAAA,MACf,IAAA;AAGJ,UAAI,gBAAgB;AACpB,cAAQ,WAAW;AAAA,QACjB,KAAK;AACa,0BAAA;AAChB;AAAA,QACF,KAAK;AACa,0BAAA;AAChB;AAAA,QACF,KAAK;AACa,0BAAA;AAChB;AAAA,MACJ;AAEA,YAAM,kBAAkB,MAAM,KAAK,eAAe,aAAa;AAE/D,UAAI,CAAC,iBAAiB;AACpB,YAAI,WAAW;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAEA,YAAI,gBAAgB;AAClB,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MACF;AAEO,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,MAAM,qBAAqB,eAAe;AACxC,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,SAAS,KAAK;AACxCA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,wDAAwD;AACtE;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA,QACrB,UAAU,cAAc;AAAA,QACxB,WAAW,cAAc;AAAA,QACzB,QAAQ,cAAc;AAAA;AAAA,QACtB,SAAS,cAAc;AAAA,QACvB,UAAU,cAAc;AAAA,QACxB,MAAM,cAAc;AAAA,MAAA;AAGtB,WAAK,WAAW;AAAA,QACd,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,MAAA;AAELA,oBAAA,MAAI,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAGxD,UAAA;AACF,cAAMG,WAAU;AAChB,YAAI,CAACA,UAAS;AACEH,wBAAAA,MAAA,MAAA,SAAA,wBAAA,gDAAgD;AAC9D;AAAA,QACF;AAIM,cAAA,eAAe,MAAMG,SAAQ,WAAW;AAAA,UAC5C,KAAK,KAAK,SAAS;AAAA;AAAA,UACnB,UAAU,eAAe;AAAA,UACzB,QAAQ,eAAe;AAAA;AAAA;AAAA,QAAA,CAExB;AACG,YAAA,aAAa,YAAY,GAAG;AAC9BH,wBAAA,MAAA,MAAA,SAAA,wBAAc,8CAA8C,aAAa,MAAM;AAAA,QACjF;AAAA,eACO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,wBAAc,gDAAgD,KAAK;AAAA,MACrE;AAAA,IACF;AAAA;AAAA;AAAA,IAIA,MAAM,sBAAsB;AAC1B,YAAM,aAAaA,cAAI,MAAA,eAAe,cAAc,KAAKA,oBAAI,eAAe,OAAO;AAEnF,UAAI,CAAC,YAAY;AACf,aAAK,OAAO;AACL,eAAA,QAAQ,QAAQ,KAAK;AAAA,MAC9B;AAGA,WAAK,QAAQ;AAGb,UAAI,iBAAiB,CAAA;AACjB,UAAA;AACI,cAAA,SAASA,cAAAA,MAAI,eAAe,UAAU;AAC3B,yBAAA,SAAU,OAAO,WAAW,WAAW,KAAK,MAAM,MAAM,IAAI,SAAU;eAChF,GAAG;AACVA,sBAAA,MAAc,MAAA,SAAA,wBAAA,8CAA8C,CAAC;AAC7D,yBAAiB,CAAA;AAAA,MACnB;AAEA,UAAI,kBAAkB,OAAO,KAAK,cAAc,EAAE,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,aAAK,WAAW;AAGhB,mBAAW,MAAM;AACf,eAAK,eAAe,OAAO,EAAE,MAAM,CAAS,UAAA;AAC1CA,0BAAA,MAAc,MAAA,SAAA,wBAAA,qDAAqD,KAAK;AAAA,UAAA,CACzE;AAAA,WACA,GAAI;AAEA,eAAA,QAAQ,QAAQ,IAAI;AAAA,MAAA,OACtB;AAEE,eAAA,MAAM,KAAK;MACpB;AAAA,IACF;AAAA;AAAA,IAGA,qBAAqB;AAEnB,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,SAAS;AACzB,eAAA;AAAA,MACT;AAGI,UAAA,CAAC,KAAK,YAAY,OAAO,KAAK,KAAK,QAAQ,EAAE,WAAW,GAAG;AACtD,eAAA;AAAA,MACT;AAEO,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,mBAAmB;AACjB,WAAK,oBAAoB;AACzB,aAAO,KAAK;IACd;AAAA,IAIA,kBAAkB,MAAM;AACtB,WAAK,iBAAiB;AAAA,IAExB;AAAA,IAEA,eAAe,aAAa;AAC1B,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG;AACvCA,oBAAA,MAAI,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AACrD,aAAA;AAAA,IACT;AAAA,IAIA,eAAe;AACT,UAAA;AACFA,4BAAI,kBAAkB,UAAU;AAChCA,4BAAI,kBAAkB,OAAO;AAC7BA,4BAAI,kBAAkB,cAAc;AACpCA,4BAAI,kBAAkB,sBAAsB;AAC5CA,4BAAI,kBAAkB,KAAK;AAE3B,aAAK,WAAW;AAChB,aAAK,UAAU;AACf,aAAK,QAAQ;AACb,aAAK,eAAe;AAIb,eAAA;AAAA,eACA,GAAG;AACVA,sBAAA,MAAc,MAAA,SAAA,wBAAA,aAAa,CAAC;AACrB,eAAA;AAAA,MACT;AAAA,IACF;AAAA;AAAA,IAGA,mBAAmB,QAAQ;AAEzB,WAAK,kBAAkB;AAAA,IACzB;AAAA;AAAA,IAGA,uBAAuB;AACrB,WAAK,kBAAkB;AAAA,IACzB;AAAA;AAAA,IAGA,QAAQ,MAAM;AACR,UAAA,CAAC,KAAK,WAAW,CAAC,KAAK,YAAY,CAAC,KAAK,SAAS,OAAO;AACpD,eAAA;AAAA,MACT;AACA,aAAO,KAAK,SAAS,MAAM,SAAS,IAAI;AAAA,IAC1C;AAAA;AAAA,IAGA,cAAc;AACZ,WAAK,OAAO;AAEZA,0BAAI,kBAAkB,sBAAsB;AAC5CA,0BAAI,kBAAkB,aAAa;AACnCA,0BAAI,kBAAkB,YAAY;AAAA,IACpC;AAAA;AAAA,IAGA,kBAAkB;AACZ,UAAA;AACI,cAAA,SAASA,cAAAA,MAAI,eAAe,UAAU;AAG5C,YAAI,UAAU,OAAO,WAAW,YAAY,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,eAAe,GAAG,GAAG;AAChGA,8BAAI,kBAAkB,UAAU;AAChC,eAAK,WAAW;AAChB,eAAK,OAAO;AACL,iBAAA;AAAA,QACT;AAEO,eAAA;AAAA,eACA,GAAG;AACVA,sBAAA,MAAA,MAAA,SAAA,wBAAc,0CAA0C,CAAC;AACzDA,4BAAI,kBAAkB,UAAU;AAChC,aAAK,WAAW;AAChB,aAAK,OAAO;AACL,eAAA;AAAA,MACT;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,oBAAoB,mBAAmB,OAAO;AAC9C,UAAA;AAGF,cAAM,EAAE,cAAA,IAAkB,QAAQ,YAAY;AAC9C,cAAM,EAAE,aAAA,IAAiB,QAAQ,WAAW;AAE5C,cAAM,aAAa;AACnB,cAAM,YAAY;AAGNA,sBAAAA,MAAA,MAAA,OAAA,wBAAA,2BAA2B;AAEnC,YAAA;mEAEU,wBAAwB;AACpC,gBAAM,WAAW;mEACL,yBAAyB;AAAA,iBAC9B,OAAO;AACdA,wBAAA,6CAAc,4BAA4B,KAAK;AAAA,QACjD;AAEI,YAAA;mEAEU,wBAAwB;AACpC,gBAAM,UAAU;mEACJ,yBAAyB;AAAA,iBAC9B,OAAO;AACdA,wBAAA,6CAAc,4BAA4B,KAAK;AAAA,QACjD;AAGI,YAAA;AAEF,gBAAME,kBAAAA,YAAY;iBACX,WAAW;AAClBF,wBAAA,6CAAc,qDAAqD,SAAS;AAAA,QAE9E;AAGA,YAAI,kBAAkB;AACpBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACX;AAAA,QACH;AAGA,mBAAW,MAAM;AACfA,wBAAA,MAAI,MAAM,sBAAsB;AAAA,YAC9B,QAAQ,KAAK;AAAA,YACb,WAAW,KAAK,IAAI;AAAA,UAAA,CACrB;AAAA,WACA,GAAG;AAAA,eAEC,OAAO;AACdA,sBAAA,MAAc,MAAA,SAAA,wBAAA,sDAAsD,KAAK;AAGzEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,kBAAkB,SAAS,OAAO;AAElC,UAAA,CAAC,KAAK,SAAS;AACjB,YAAI,CAAC,QAAQ;AACXA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AACO,eAAA;AAAA,MACT;AAEI,UAAA;AAEF,YAAI,QAAQ;AAEV,gBAAM,aAAa,MAAM,KAAK,uBAAuB,MAAM;AACpD,iBAAA;AAAA,QAAA,OACF;AAEC,gBAAA,aAAa,MAAMI,qBAAAA,eAAe;AAAA,YACtC,MAAM,KAAK,uBAAuB,MAAM;AAAA,YACxC;AAAA,cACE,OAAO;AAAA,cACP,IAAI;AAAA,cACJ,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,aAAa;AAAA;AAAA,cACb,WAAW;AAAA;AAAA,cACX,YAAY;AAAA,YACd;AAAA,UAAA;AAEK,iBAAA;AAAA,QACT;AAAA,eACO,OAAO;AACdJ,sBAAA,MAAc,MAAA,SAAA,wBAAA,mCAAmC,KAAK;AAC/C,eAAA;AAAA,MACT;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,uBAAuB,SAAS,OAAO;AAC3CA,oBAAA,MAAY,MAAA,OAAA,wBAAA,mCAAmC,MAAM;AAGrD,UAAI,KAAK,mBAAmB;AAC1BA,sBAAAA,MAAA,MAAA,OAAA,wBAAY,8BAA8B;AACnC,eAAA;AAAA,MACT;AAEI,UAAA;AAEF,aAAK,oBAAoB;AACbA,sBAAAA,MAAA,MAAA,OAAA,wBAAA,wBAAwB;AAGpC,cAAM,EAAE,cAAA,IAAkB,QAAQ,YAAY;AAC9C,cAAM,EAAE,aAAA,IAAiB,QAAQ,WAAW;AAE5C,cAAM,aAAa;AACnB,cAAM,YAAY;AAElBA,sBAAAA,MAAA,MAAA,OAAA,wBAAY,4BAA4B;AAAA,UACtC,YAAY,CAAC,CAAC;AAAA,UACd,WAAW,CAAC,CAAC;AAAA,UACb,gBAAgB,OAAO,WAAW;AAAA,UAClC,eAAe,OAAO,UAAU;AAAA,QAAA,CACjC;AAED,YAAI,aAAa;AACjB,cAAM,cAAc;AAAA,UAClB,QAAQ,EAAE,SAAS,OAAO,SAAS,OAAO,OAAO,EAAE;AAAA,UACnD,QAAQ,EAAE,SAAS,OAAO,SAAS,OAAO,OAAO,EAAE;AAAA,QAAA;AAIzCA,sBAAAA,MAAA,MAAA,OAAA,wBAAA,yBAAyB;AACjC,YAAA;AACI,gBAAA,kBAAkB,MAAM,WAAW;AACzCA,wBAAA,MAAY,MAAA,OAAA,wBAAA,uBAAuB,eAAe;AAClD,sBAAY,OAAO,UAAU;AACjB,sBAAA,OAAO,UAAU,gBAAgB;AACjC,sBAAA,OAAO,QAAQ,gBAAgB,gBAAgB;AAE3D,cAAI,gBAAgB,YAAY;AACjB,yBAAA;AAAA,UACf;AAAA,iBACO,YAAY;AACnBA,wBAAA,MAAA,MAAA,SAAA,wBAAc,uBAAuB,UAAU;AAE3C,cAAA,KAAK,4BAA4B,UAAU,GAAG;AACpCA,0BAAAA,MAAA,MAAA,OAAA,wBAAA,0BAA0B;AAAA,UAAA,OAEjC;AACLA,0BAAA,MAAc,MAAA,SAAA,wBAAA,kCAAkC,UAAU;AAEpD,kBAAA;AAAA,UACR;AAAA,QACF;AAGYA,sBAAAA,MAAA,MAAA,OAAA,wBAAA,yBAAyB;AACjC,YAAA;AACI,gBAAA,iBAAiB,MAAM,UAAU;AACvCA,wBAAA,MAAY,MAAA,OAAA,wBAAA,uBAAuB,cAAc;AACjD,sBAAY,OAAO,UAAU;AACjB,sBAAA,OAAO,UAAU,eAAe;AAChC,sBAAA,OAAO,QAAQ,eAAe,gBAAgB;AAE1D,cAAI,eAAe,YAAY;AAChB,yBAAA;AAAA,UACf;AAAA,iBACO,WAAW;AAClBA,wBAAA,6CAAc,uBAAuB,SAAS;AAE1C,cAAA,KAAK,4BAA4B,SAAS,GAAG;AACnCA,0BAAAA,MAAA,MAAA,OAAA,wBAAA,0BAA0B;AAAA,UAAA,OAEjC;AACLA,0BAAA,MAAA,MAAA,SAAA,wBAAc,iCAAiC,SAAS;AAElD,kBAAA;AAAA,UACR;AAAA,QACF;AAGYA,sBAAAA,MAAA,MAAA,OAAA,wBAAA,0BAA0B;AAAA,UACpC;AAAA,UACA;AAAA,UACA;AAAA,QAAA,CACD;AAED,YAAI,CAAC,QAAQ;AACX,cAAI,YAAY;AACd,gBAAI,UAAU;AACd,kBAAM,UAAU,CAAA;AAChB,gBAAI,YAAY,OAAO;AAAS,sBAAQ,KAAK,KAAK,YAAY,OAAO,KAAK,GAAG;AAC7E,gBAAI,YAAY,OAAO;AAAS,sBAAQ,KAAK,KAAK,YAAY,OAAO,KAAK,GAAG;AAEzE,gBAAA,QAAQ,SAAS,GAAG;AACtB,wBAAU,MAAM,QAAQ,KAAK,GAAG,CAAC;AAAA,YACnC;AAGAA,0BAAA,2CAAY,qBAAqB,OAAO;AACxCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAAA,CACX;AAAA,UAAA,OACI;AAEOA,0BAAAA,MAAA,MAAA,OAAA,wBAAA,oBAAoB;AAChCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YAAA,CACX;AAAA,UACH;AAAA,QACF;AAEO,eAAA;AAAA,eAEA,OAAO;AAEV,YAAA,KAAK,4BAA4B,KAAK,GAAG;mEAE/B,wBAAwB;AAC7B,iBAAA;AAAA,QACT;AAGA,YAAI,MAAM,YACR,MAAM,QAAQ,SAAS,gBAAgB,KACvC,MAAM,QAAQ,SAAS,MAAM,KAC7B,MAAM,QAAQ,SAAS,MAAM,IAC5B;AACDA,wBAAA,MAAA,MAAA,SAAA,wBAAc,uCAAuC,KAAK;AAEnD,iBAAA;AAAA,QACT;AAEAA,sBAAA,MAAA,MAAA,SAAA,wBAAc,wCAAwC,KAAK;AAEpD,eAAA;AAAA,MAAA,UACP;AAEA,aAAK,oBAAoB;AAAA,MAC3B;AAAA,IACF;AAAA;AAAA,IAGA,4BAA4B,OAAO;AAC7B,UAAA,CAAC,SAAS,CAAC,MAAM;AAAgB,eAAA;AAErC,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAGF,aAAO,iBAAiB;AAAA,QAAK,CAAA,QAC3B,MAAM,QAAQ,cAAc,SAAS,IAAI,aAAa;AAAA,MAAA;AAAA,IAE1D;AAAA,EACF;AACF,CAAC;;"}
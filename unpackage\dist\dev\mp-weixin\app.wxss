
	/*每个页面公共css */
	/* 引入字体图标 */
@font-face {
  font-family: "iconfont";
  src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAALcAAsAAAAABqQAAAKPAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACCcAqBaIFCATYCJAMICwYABCAFhGcHMhvdBcgOJUHBwAAAYBBBPHzt9/vM7JV3ffGkm+Z/kURNIxVCJ9HQCZFIhETznubS3cTa11WSuavdUxXu7q5+qldPJG9pmhCJkJZIVs/8uePvAjVMM/UWIEAf93/O5WvbwGQTWovrVj1a5+uzgKbHgQLMBQo0xqYKZAmZN4xd8AJfD8FZJrWUtouOQYqMdVkgbiWEQEoxorKyiIRQb9iaxJ0oi+9p8AEI388Xf8JRSGqZdc3JTd4i1N9y7pOpZmqCkAh0PEfWyGgLJOJio3QMpjBMw7lqEHCPagW/5dhVZsWuiT1+eRLRkR0dw+BIJJCNtgLxbBGvqfBaausPkrHW6bxZePdGOHGCfPw43PeKfvYi9OaPRsdP0C8+Bo9f/cYfJvuHm7mB9uG71uHhZl9LbDO7MXHwbsrz3LnW15ePxS9y/dO7t4Zj27l0Hp57e9mzWVX4xd2jx+uvRb+Gxo3ZH/1D//W3+ubbt6n++/cxz7B5lDFYvGllJBpZ9xf5dxWBNb/yNwCvHRdTbRn7MxnoGwd/WK2UH1qrRlY3tXOZsLUULnDWH8LvP5UxTfEXUQaJ6CySykoyUduoFNs2aomuWYNu2+PHBqOIZWDLhwNCXx8kPT+Q9Y2QiH2hMvILtb4fcNYPh9MOdxIZVwWiJdAwjaSaaiSPuMVUQaO2dKTCQ5LRB8ZpMcFAzIVxzEHpMZaJSBm9dM0JI0wUimIvSKc8cL0gKRY9U3RlWSIpprKRdSQNkhqCjEYiNVNaEXkIN2FVoJHv5YjUw4NIjLzA7Gp3AglEOTE5y6FoQ1Wnkbqu6zxdZgmGUEiQKMwLRE7igdMTSCQWemSLXIXJRGQbqSxJO6QypKyHxe31x3cAZ+Ywp5SZVfXZoU1bAAAA') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-location:before {
  content: "\e604";
}
	/* 全局样式 */
page {
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
		color: #333;
}
	/* 卡片样式 */
.card {
		background-color: #ffffff;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		padding: 24rpx;
		margin-bottom: 20rpx;
}
	/* 毛玻璃效果 */
.glass {
		background: rgba(255, 255, 255, 0.7);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
}
	/* 主题颜色 */
.primary-bg {
		background-color: #8a2be2; /* 紫色，适合心愿主题 */
}
.primary-text {
		color: #8a2be2;
}
.secondary-bg {
		background-color: #f0e6ff;
}
	/* 文本溢出显示省略号 */
.text-ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}
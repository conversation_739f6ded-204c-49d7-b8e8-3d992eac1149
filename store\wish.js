/**
 * 心愿数据管理 Store - 简化版本
 */

import { defineStore } from 'pinia'

// 错误代码常量
const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR'
}

// 错误处理工具类
const ErrorHandler = {
  getUserFriendlyMessage(error) {
    if (typeof error === 'string') return error
    return error.message || error.errMsg || '未知错误'
  },
  
  isNetworkError(error) {
    if (error.errCode === ERROR_CODES.NETWORK_ERROR) return true
    if (error.code === 'NETWORK_ERROR') return true
    return false
  },
  
  isVersionConflictError(error) {
    return error && error.message && (
      error.message.includes('数据版本冲突') ||
      error.message.includes('逻辑时钟冲突') ||
      error.message.includes('version conflict') ||
      error.message.includes('conflict')
    )
  },

  showError(error, title = '操作失败', context = 'unknown') {
    const message = this.getUserFriendlyMessage(error)
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
    console.error(`${title}:`, error)
  }
}

export const useWishStore = defineStore('wish', {
  state: () => ({
    wishList: [],
    currentGroupId: 'all',
    isLoading: false,
    lastSyncTime: null,
    isOnline: true,
    listUpdateCounter: 0  // 🚀 添加列表更新计数器
  }),

  getters: {
    activeWishes: (state) => {
      return state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)
    },

    // 🚀 添加当前分组的心愿列表
    currentGroupWishes: (state) => {
      const activeWishes = state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)

      if (state.currentGroupId === 'all') {
        return activeWishes
      } else if (state.currentGroupId === 'completed') {
        return state.wishList.filter(wish => !wish._deleted && wish.isCompleted)
      } else {
        // 特定分组
        return activeWishes.filter(wish =>
          wish.groupIds && wish.groupIds.includes(state.currentGroupId)
        )
      }
    },

    // 🚀 添加其他必要的 getters
    getWishById: (state) => (id) => {
      return state.wishList.find(wish =>
        (wish._id === id || wish.id === id) && !wish._deleted
      ) || null
    }
  },

  actions: {
    async initWishList() {
      console.log('[wishStore] Initializing wish list...')
      this.isLoading = true

      try {
        // 从本地存储加载数据
        const storedWishes = uni.getStorageSync('wishList')
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes)
          this.wishList = parsed.map(wish => ({
            ...wish,
            id: wish.id || wish._id
          }))
        }

        console.log('[wishStore] Wish list initialization completed')
      } catch (error) {
        console.error('[wishStore] Failed to initialize wish list:', error)
      } finally {
        this.isLoading = false
      }
    },

    // 🚀 添加基本的同步方法来测试云函数修改
    async syncFromCloud() {
      console.log('[wishStore] 开始从云端同步数据...')

      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.getWishList({
          page: 1,
          pageSize: 1000,
          includeCompleted: true
        })

        if (result.errCode === 0) {
          this.wishList = result.data.map(wish => ({
            ...wish,
            id: wish._id
          }))

          // 保存到本地存储
          uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          this.lastSyncTime = new Date().toISOString()

          console.log('[wishStore] 同步成功，获取到', result.data.length, '个心愿')
        } else {
          console.error('[wishStore] 同步失败:', result.errMsg)
        }
      } catch (error) {
        console.error('[wishStore] 同步出错:', error)
        ErrorHandler.showError(error, '同步失败')
      }
    },

    // 🚀 添加删除方法来测试多设备冲突处理
    async deleteWish(id) {
      console.log('[wishStore] 删除心愿:', id)

      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.deleteWish(id)

        if (result.errCode === 0) {
          // 从本地列表中移除
          const index = this.wishList.findIndex(w => w._id === id || w.id === id)
          if (index !== -1) {
            this.wishList.splice(index, 1)
            uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          }

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })

          console.log('[wishStore] 删除成功')
        } else {
          console.error('[wishStore] 删除失败:', result.errMsg)
          ErrorHandler.showError({ message: result.errMsg }, '删除失败')
        }
      } catch (error) {
        console.error('[wishStore] 删除出错:', error)
        ErrorHandler.showError(error, '删除失败')
      }
    },

    // 🚀 添加其他必要的方法
    setCurrentGroup(groupId) {
      this.currentGroupId = groupId
    },

    // 强制同步数据（用于下拉刷新）
    async forceSyncData() {
      console.log('[wishStore] 强制同步数据...')
      await this.syncFromCloud()
    },

    // 手动同步（兼容原有接口）
    async manualSync(silent = false) {
      if (!silent) {
        uni.showLoading({ title: '同步中...' })
      }

      try {
        await this.syncFromCloud()

        if (!silent) {
          uni.hideLoading()
          uni.showToast({
            title: '同步完成',
            icon: 'success'
          })
        }
      } catch (error) {
        if (!silent) {
          uni.hideLoading()
        }
        throw error
      }
    },

    // 完成心愿
    async completeWish(id) {
      console.log('[wishStore] 完成心愿:', id)

      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.completeWish(id)

        if (result.errCode === 0) {
          // 更新本地数据
          const wish = this.wishList.find(w => w._id === id || w.id === id)
          if (wish) {
            wish.isCompleted = true
            wish.completeDate = new Date().toISOString()
            uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          }

          uni.showToast({
            title: '心愿已完成',
            icon: 'success'
          })
        } else {
          ErrorHandler.showError({ message: result.errMsg }, '完成失败')
        }
      } catch (error) {
        console.error('[wishStore] 完成心愿出错:', error)
        ErrorHandler.showError(error, '完成失败')
      }
    },

    // 恢复心愿
    async restoreWish(id) {
      console.log('[wishStore] 恢复心愿:', id)

      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.restoreWish(id)

        if (result.errCode === 0) {
          // 更新本地数据
          const wish = this.wishList.find(w => w._id === id || w.id === id)
          if (wish) {
            wish.isCompleted = false
            wish.completeDate = null
            uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          }

          uni.showToast({
            title: '心愿已恢复',
            icon: 'success'
          })
        } else {
          ErrorHandler.showError({ message: result.errMsg }, '恢复失败')
        }
      } catch (error) {
        console.error('[wishStore] 恢复心愿出错:', error)
        ErrorHandler.showError(error, '恢复失败')
      }
    },

    // 🚀 清理本地数据（登录后重新初始化时使用）
    clearLocalData() {
      console.log('[wishStore] 清理本地数据...')

      // 清空当前数据
      this.wishList = []
      this.currentGroupId = 'all'
      this.lastSyncTime = null
      this.isLoading = false

      // 清理本地存储
      uni.removeStorageSync('wishList')
      uni.removeStorageSync('wishLastSyncTime')

      console.log('[wishStore] 本地数据清理完成')
    },

    // 🚀 强制初始化（登录后使用）
    async forceInit() {
      console.log('[wishStore] 强制初始化...')

      // 清空当前数据
      this.clearLocalData()

      // 强制从云端同步
      await this.syncFromCloud()

      console.log('[wishStore] 强制初始化完成')
    },

    // 🚀 用户登出时清理数据
    clearUserData() {
      console.log('[wishStore] 清理用户数据...')

      // 清空用户的心愿数据
      this.wishList = []

      // 重置为默认分组（全部）
      this.currentGroupId = 'all'

      this.lastSyncTime = null
      this.isLoading = false

      // 清除本地存储中的用户数据
      uni.removeStorageSync('wishList')
      uni.removeStorageSync('wishLastSyncTime')

      console.log('[wishStore] 用户数据清理完成')
    },

    // 🚀 刷新心愿列表（从本地存储重新加载）
    refreshWishList() {
      console.log('[wishStore] 刷新心愿列表...')

      try {
        const storedWishes = uni.getStorageSync('wishList')
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes)
          this.wishList = parsed.map(wish => ({
            ...wish,
            id: wish.id || wish._id
          }))
          console.log('[wishStore] 心愿列表已刷新，共', this.wishList.length, '个心愿')
        }
      } catch (error) {
        console.error('[wishStore] 刷新心愿列表失败:', error)
      }
    },

    // 🚀 更新心愿排序
    updateWishOrder(orderedIds) {
      console.log('[wishStore] 更新心愿排序:', orderedIds)

      // 简单实现：根据新的ID顺序重新排列心愿
      const reorderedWishes = []
      orderedIds.forEach(id => {
        const wish = this.wishList.find(w => w._id === id || w.id === id)
        if (wish) {
          reorderedWishes.push(wish)
        }
      })

      // 添加不在排序列表中的心愿
      this.wishList.forEach(wish => {
        if (!orderedIds.includes(wish._id) && !orderedIds.includes(wish.id)) {
          reorderedWishes.push(wish)
        }
      })

      this.wishList = reorderedWishes
      uni.setStorageSync('wishList', JSON.stringify(this.wishList))

      console.log('[wishStore] 心愿排序已更新')
    },

    // 🚀 设置当前心愿列表
    setCurrentList(newWishList) {
      console.log('[wishStore] 设置当前心愿列表:', newWishList.length, '个心愿')

      this.wishList = newWishList.map(wish => ({
        ...wish,
        id: wish.id || wish._id
      }))

      uni.setStorageSync('wishList', JSON.stringify(this.wishList))
    },

    // 🚀 添加心愿
    async addWish(wishData) {
      console.log('[wishStore] 添加心愿:', wishData)

      try {
        // 增强心愿数据
        const enhancedWishData = {
          ...wishData,
          createDate: new Date().toISOString(),
          updateDate: new Date().toISOString(),
          isCompleted: false,
          userId: '', // 会在云函数中设置
          permission: wishData.permission || 'private',
          groupIds: wishData.groupIds || ['all'],
          order: this.wishList.length + 1
        }

        // 检查网络状态
        if (!this.isOnline) {
          console.log('当前离线，心愿将保存到本地')
          return this._addWishOffline(enhancedWishData)
        }

        // 先调用云函数创建
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.createWish(enhancedWishData)

        if (result.errCode === 0) {
          // 云端创建成功，添加到本地列表
          const wishWithId = {
            ...result.data,
            id: result.data._id // 确保有 id 字段供前端组件使用
          }
          this.wishList.push(wishWithId)
          uni.setStorageSync('wishList', JSON.stringify(this.wishList))

          uni.showToast({
            title: '心愿创建成功',
            icon: 'success'
          })

          return wishWithId
        } else {
          throw new Error(result.errMsg || '创建心愿失败')
        }
      } catch (error) {
        console.error('添加心愿失败:', error)

        // 网络错误时，保存到本地（离线模式）
        if (ErrorHandler.isNetworkError(error)) {
          this.isOnline = false // 更新网络状态
          return this._addWishOffline(enhancedWishData)
        }

        // 版本冲突时，使用离线模式
        if (ErrorHandler.isVersionConflictError(error)) {
          return this._addWishOffline(enhancedWishData)
        }

        // 其他错误，显示错误信息
        ErrorHandler.showError(error, '创建心愿失败')
        throw error
      }
    },

    // 🚀 离线模式添加心愿
    _addWishOffline(wishData) {
      console.log('[wishStore] 离线模式添加心愿:', wishData.title)

      // 生成临时ID
      const tempId = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)

      const wish = {
        _id: tempId,
        id: tempId,
        ...wishData,
        _needSync: true, // 标记需要同步到云端
        _isLocal: true   // 标记为本地创建
      }

      this.wishList.push(wish)
      uni.setStorageSync('wishList', JSON.stringify(this.wishList))

      // 显示离线提示
      uni.showToast({
        title: this.isOnline ? '已保存到本地，稍后将同步到云端' : '当前离线，已保存到本地',
        icon: 'none',
        duration: 2500
      })

      return wish
    }
  }
})

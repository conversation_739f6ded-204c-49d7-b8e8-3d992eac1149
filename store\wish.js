/**
 * 心愿数据管理 Store - 简化版本
 */

import { defineStore } from 'pinia'

// 错误代码常量
const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR'
}

// 错误处理工具类
const ErrorHandler = {
  getUserFriendlyMessage(error) {
    if (typeof error === 'string') return error
    return error.message || error.errMsg || '未知错误'
  },
  
  isNetworkError(error) {
    if (error.errCode === ERROR_CODES.NETWORK_ERROR) return true
    if (error.code === 'NETWORK_ERROR') return true
    return false
  },
  
  isVersionConflictError(error) {
    return error && error.message && (
      error.message.includes('数据版本冲突') ||
      error.message.includes('逻辑时钟冲突') ||
      error.message.includes('version conflict') ||
      error.message.includes('conflict')
    )
  },

  showError(error, title = '操作失败', context = 'unknown') {
    const message = this.getUserFriendlyMessage(error)
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
    console.error(`${title}:`, error)
  }
}

export const useWishStore = defineStore('wish', {
  state: () => ({
    wishList: [],
    currentGroupId: 'all',
    isLoading: false,
    lastSyncTime: null,
    isOnline: true,
    listUpdateCounter: 0,  // 🚀 添加列表更新计数器

    // 同步状态管理
    syncStatus: {
      issyncing: false,
      lastSyncResult: null,
      pendingCount: 0,
      errorCount: 0,
      lastError: null
    },

    // 防重复同步机制
    _syncOperations: new Set(),
    _lastSyncTime: 0
  }),

  getters: {
    activeWishes: (state) => {
      return state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)
    },

    // 🚀 添加当前分组的心愿列表
    currentGroupWishes: (state) => {
      const activeWishes = state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)

      if (state.currentGroupId === 'all') {
        return activeWishes
      } else if (state.currentGroupId === 'completed') {
        return state.wishList.filter(wish => !wish._deleted && wish.isCompleted)
      } else {
        // 特定分组
        return activeWishes.filter(wish =>
          wish.groupIds && wish.groupIds.includes(state.currentGroupId)
        )
      }
    },

    // 🚀 添加其他必要的 getters
    getWishById: (state) => (id) => {
      return state.wishList.find(wish =>
        (wish._id === id || wish.id === id) && !wish._deleted
      ) || null
    }
  },

  actions: {
    async initWishList() {
      console.log('[wishStore] Initializing wish list...')
      this.isLoading = true

      try {
        // 从本地存储加载数据
        const storedWishes = uni.getStorageSync('wishList')
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes)
          this.wishList = parsed.map(wish => ({
            ...wish,
            id: wish.id || wish._id
          }))
        }

        // 尝试从云端同步数据
        console.log('[wishStore] 开始从云端同步最新数据...')
        await this.syncFromCloud()

        console.log('[wishStore] Wish list initialization completed')
      } catch (error) {
        console.error('[wishStore] Failed to initialize wish list:', error)
      } finally {
        this.isLoading = false
      }
    },

    // 🚀 添加基本的同步方法来测试云函数修改
    async syncFromCloud() {
      const operation = 'syncFromCloud'

      // 检查是否可以开始同步
      if (!this._canStartSync(operation)) {
        return { success: false, reason: 'sync_in_progress' }
      }

      console.log('[wishStore] 开始从云端同步数据...')

      try {
        this._startSyncOperation(operation)
        this._updateSyncStatus({ issyncing: true })
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.getWishList({
          page: 1,
          pageSize: 1000,
          includeCompleted: true
        })

        if (result.errCode === 0) {
          const cloudWishes = result.data || []
          console.log('[wishStore] 从云端获取到', cloudWishes.length, '个心愿')

          // 执行智能数据合并而不是直接覆盖
          const mergedData = this._mergeWishData(this.wishList, cloudWishes)

          // 更新本地数据
          this.wishList = mergedData.map(wish => ({
            ...wish,
            id: wish._id || wish.id // 确保有 id 字段
          }))

          // 保存到本地存储
          uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          this.lastSyncTime = new Date().toISOString()
          uni.setStorageSync('wishListLastSyncTime', this.lastSyncTime)

          console.log('[wishStore] 智能同步完成，合并后共', this.wishList.length, '个心愿')

          // 更新同步状态
          this._updateSyncStatus({
            lastSyncResult: 'success',
            errorCount: 0,
            lastError: null
          })

          return { success: true, count: cloudWishes.length }
        } else {
          const errorMsg = result.errMsg || '云函数返回错误'
          console.error('[wishStore] 云函数错误:', errorMsg)

          // 更新错误状态
          this._updateSyncStatus({
            lastSyncResult: 'error',
            errorCount: this.syncStatus.errorCount + 1,
            lastError: errorMsg
          })

          throw new Error(errorMsg)
        }
      } catch (error) {
        console.error('[wishStore] 同步出错:', error)

        // 更新错误状态
        this._updateSyncStatus({
          lastSyncResult: 'error',
          errorCount: this.syncStatus.errorCount + 1,
          lastError: error.message
        })

        throw error
      } finally {
        this._updateSyncStatus({ issyncing: false })
        this._endSyncOperation(operation)
      }
    },

    // 🚀 更新心愿
    async updateWish(updatedWish) {
      console.log('[wishStore] 更新心愿:', updatedWish)

      try {
        // 验证更新数据的完整性
        if (!updatedWish || (!updatedWish._id && !updatedWish.id)) {
          throw new Error('更新数据缺少必要的ID字段')
        }

        const wishId = updatedWish._id || updatedWish.id

        // 准备更新数据
        const updateData = {
          ...updatedWish,
          updateDate: new Date().toISOString()
        }

        // 调用云函数更新
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.updateWish(wishId, updateData)

        if (result.errCode === 0) {
          // 云端更新成功，更新本地数据
          const index = this.wishList.findIndex(w => w._id === wishId || w.id === wishId)
          if (index !== -1) {
            // 合并数据，确保使用正确的字段名
            const mergedWish = {
              ...this.wishList[index],
              ...updateData,
              id: wishId,
              _id: wishId
            }
            this.wishList[index] = mergedWish
            uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          }

          uni.showToast({
            title: '更新成功',
            icon: 'success'
          })

          console.log('[wishStore] 更新成功')
        } else {
          throw new Error(result.errMsg || '更新心愿失败')
        }
      } catch (error) {
        console.error('[wishStore] 更新心愿失败:', error)
        ErrorHandler.showError(error, '更新失败')
        throw error // 重新抛出错误，让调用方知道失败了
      }
    },

    // 🚀 添加删除方法来测试多设备冲突处理
    async deleteWish(id) {
      console.log('[wishStore] 删除心愿:', id)

      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.deleteWish(id)

        if (result.errCode === 0) {
          // 从本地列表中移除
          const index = this.wishList.findIndex(w => w._id === id || w.id === id)
          if (index !== -1) {
            this.wishList.splice(index, 1)
            uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          }

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })

          console.log('[wishStore] 删除成功')
        } else {
          console.error('[wishStore] 删除失败:', result.errMsg)
          ErrorHandler.showError({ message: result.errMsg }, '删除失败')
        }
      } catch (error) {
        console.error('[wishStore] 删除出错:', error)
        ErrorHandler.showError(error, '删除失败')
      }
    },

    // 智能合并本地和云端数据
    _mergeWishData(localWishes, cloudWishes) {
      console.log('[wishStore] 智能合并本地和云端数据...')
      console.log('[wishStore] 本地数据:', localWishes.length, '个心愿')
      console.log('[wishStore] 云端数据:', cloudWishes.length, '个心愿')

      const mergedMap = new Map()

      // 首先处理云端数据
      cloudWishes.forEach(cloudWish => {
        mergedMap.set(cloudWish._id, {
          ...cloudWish,
          _source: 'cloud'
        })
      })

      // 然后处理本地数据，根据时间戳决定是否覆盖
      localWishes.forEach(localWish => {
        const id = localWish._id || localWish.id
        const existingWish = mergedMap.get(id)

        if (!existingWish) {
          // 本地独有的数据，保留
          mergedMap.set(id, {
            ...localWish,
            _id: id,
            _source: 'local'
          })
        } else {
          // 数据冲突，比较时间戳
          const localTime = new Date(localWish.updatedAt || localWish.createdAt || 0).getTime()
          const cloudTime = new Date(existingWish.updatedAt || existingWish.createdAt || 0).getTime()

          if (localTime > cloudTime) {
            // 本地数据更新，使用本地数据
            mergedMap.set(id, {
              ...localWish,
              _id: id,
              _source: 'local_newer'
            })
          }
          // 否则使用云端数据（已经在map中）
        }
      })

      const mergedArray = Array.from(mergedMap.values())
      console.log('[wishStore] 合并完成，最终数据:', mergedArray.length, '个心愿')

      return mergedArray
    },

    // 检查同步操作是否可以执行
    _canStartSync(operation) {
      const now = Date.now()

      // 检查是否有相同操作正在进行
      if (this._syncOperations.has(operation)) {
        console.log(`[wishStore] 同步操作 ${operation} 已在进行中`)
        return false
      }

      // 检查是否距离上次同步太近（防抖）
      if (now - this._lastSyncTime < 1000) {
        console.log(`[wishStore] 同步操作过于频繁，跳过`)
        return false
      }

      return true
    },

    // 开始同步操作
    _startSyncOperation(operation) {
      this._syncOperations.add(operation)
      this._lastSyncTime = Date.now()
      console.log(`[wishStore] 开始同步操作: ${operation}`)
    },

    // 结束同步操作
    _endSyncOperation(operation) {
      this._syncOperations.delete(operation)
      console.log(`[wishStore] 结束同步操作: ${operation}`)
    },

    // 更新同步状态
    _updateSyncStatus(updates) {
      Object.assign(this.syncStatus, updates)
    },

    // 🚀 添加其他必要的方法
    setCurrentGroup(groupId) {
      this.currentGroupId = groupId
    },

    // 强制同步数据（用于下拉刷新）
    async forceSyncData() {
      console.log('[wishStore] 强制同步数据...')
      await this.syncFromCloud()
    },

    // 手动同步（兼容原有接口）
    async manualSync(silent = false) {
      if (!silent) {
        uni.showLoading({ title: '同步中...' })
      }

      try {
        await this.syncFromCloud()

        if (!silent) {
          uni.hideLoading()
          uni.showToast({
            title: '同步完成',
            icon: 'success'
          })
        }
      } catch (error) {
        if (!silent) {
          uni.hideLoading()
        }
        throw error
      }
    },

    // 智能同步方法（兼容 userStore 调用）
    async smartSync() {
      console.log('[wishStore] Starting smart sync...')

      try {
        // 检查网络状态
        const networkInfo = await uni.getNetworkType()
        if (networkInfo.networkType === 'none') {
          console.warn('[wishStore] Network unavailable, skipping smart sync')
          return { hasUpdates: false, updatedCount: 0, reason: 'offline' }
        }

        // 执行同步
        const result = await this.syncFromCloud()

        if (result && result.success) {
          return {
            hasUpdates: true,
            updatedCount: result.count || 0,
            reason: 'success'
          }
        } else {
          return {
            hasUpdates: false,
            updatedCount: 0,
            reason: 'no_updates'
          }
        }
      } catch (error) {
        console.error('[wishStore] Smart sync failed:', error)
        return {
          hasUpdates: false,
          updatedCount: 0,
          reason: 'error',
          error: error.message
        }
      }
    },

    // 完成心愿
    async completeWish(id) {
      console.log('[wishStore] 完成心愿:', id)

      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.completeWish(id)

        if (result.errCode === 0) {
          // 更新本地数据
          const wish = this.wishList.find(w => w._id === id || w.id === id)
          if (wish) {
            wish.isCompleted = true
            wish.completeDate = new Date().toISOString()
            uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          }

          uni.showToast({
            title: '心愿已完成',
            icon: 'success'
          })
        } else {
          ErrorHandler.showError({ message: result.errMsg }, '完成失败')
        }
      } catch (error) {
        console.error('[wishStore] 完成心愿出错:', error)
        ErrorHandler.showError(error, '完成失败')
      }
    },

    // 恢复心愿
    async restoreWish(id) {
      console.log('[wishStore] 恢复心愿:', id)

      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.restoreWish(id)

        if (result.errCode === 0) {
          // 更新本地数据
          const wish = this.wishList.find(w => w._id === id || w.id === id)
          if (wish) {
            wish.isCompleted = false
            wish.completeDate = null
            uni.setStorageSync('wishList', JSON.stringify(this.wishList))
          }

          uni.showToast({
            title: '心愿已恢复',
            icon: 'success'
          })
        } else {
          ErrorHandler.showError({ message: result.errMsg }, '恢复失败')
        }
      } catch (error) {
        console.error('[wishStore] 恢复心愿出错:', error)
        ErrorHandler.showError(error, '恢复失败')
      }
    },

    // 🚀 清理本地数据（登录后重新初始化时使用）
    clearLocalData() {
      console.log('[wishStore] 清理本地数据...')

      // 清空当前数据
      this.wishList = []
      this.currentGroupId = 'all'
      this.lastSyncTime = null
      this.isLoading = false

      // 清理本地存储
      uni.removeStorageSync('wishList')
      uni.removeStorageSync('wishLastSyncTime')

      console.log('[wishStore] 本地数据清理完成')
    },

    // 🚀 强制初始化（登录后使用）
    async forceInit() {
      console.log('[wishStore] 强制初始化...')

      // 清空当前数据
      this.clearLocalData()

      // 强制从云端同步
      await this.syncFromCloud()

      console.log('[wishStore] 强制初始化完成')
    },

    // 🚀 用户登出时清理数据
    clearUserData() {
      console.log('[wishStore] 清理用户数据...')

      // 清空用户的心愿数据
      this.wishList = []

      // 重置为默认分组（全部）
      this.currentGroupId = 'all'

      this.lastSyncTime = null
      this.isLoading = false

      // 清除本地存储中的用户数据
      uni.removeStorageSync('wishList')
      uni.removeStorageSync('wishLastSyncTime')

      console.log('[wishStore] 用户数据清理完成')
    },

    // 🚀 刷新心愿列表（从本地存储重新加载）
    refreshWishList() {
      console.log('[wishStore] 刷新心愿列表...')

      try {
        const storedWishes = uni.getStorageSync('wishList')
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes)
          this.wishList = parsed.map(wish => ({
            ...wish,
            id: wish.id || wish._id
          }))
          console.log('[wishStore] 心愿列表已刷新，共', this.wishList.length, '个心愿')
        }
      } catch (error) {
        console.error('[wishStore] 刷新心愿列表失败:', error)
      }
    },

    // 🚀 更新心愿排序
    updateWishOrder(orderedIds) {
      console.log('[wishStore] 更新心愿排序:', orderedIds)

      // 简单实现：根据新的ID顺序重新排列心愿
      const reorderedWishes = []
      orderedIds.forEach(id => {
        const wish = this.wishList.find(w => w._id === id || w.id === id)
        if (wish) {
          reorderedWishes.push(wish)
        }
      })

      // 添加不在排序列表中的心愿
      this.wishList.forEach(wish => {
        if (!orderedIds.includes(wish._id) && !orderedIds.includes(wish.id)) {
          reorderedWishes.push(wish)
        }
      })

      this.wishList = reorderedWishes
      uni.setStorageSync('wishList', JSON.stringify(this.wishList))

      console.log('[wishStore] 心愿排序已更新')
    },

    // 🚀 设置当前心愿列表
    setCurrentList(newWishList) {
      console.log('[wishStore] 设置当前心愿列表:', newWishList.length, '个心愿')

      this.wishList = newWishList.map(wish => ({
        ...wish,
        id: wish.id || wish._id
      }))

      uni.setStorageSync('wishList', JSON.stringify(this.wishList))
    },

    // 🚀 添加心愿
    async addWish(wishData) {
      console.log('[wishStore] 添加心愿:', wishData)

      try {
        // 增强心愿数据
        const enhancedWishData = {
          ...wishData,
          createDate: new Date().toISOString(),
          updateDate: new Date().toISOString(),
          isCompleted: false,
          userId: '', // 会在云函数中设置
          permission: wishData.permission || 'private',
          groupIds: wishData.groupIds || ['all'],
          order: this.wishList.length + 1
        }

        // 检查网络状态
        if (!this.isOnline) {
          console.log('当前离线，心愿将保存到本地')
          return this._addWishOffline(enhancedWishData)
        }

        // 先调用云函数创建
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.createWish(enhancedWishData)

        if (result.errCode === 0) {
          // 云端创建成功，添加到本地列表
          const wishWithId = {
            ...result.data,
            id: result.data._id // 确保有 id 字段供前端组件使用
          }
          this.wishList.push(wishWithId)
          uni.setStorageSync('wishList', JSON.stringify(this.wishList))

          uni.showToast({
            title: '心愿创建成功',
            icon: 'success'
          })

          return wishWithId
        } else {
          throw new Error(result.errMsg || '创建心愿失败')
        }
      } catch (error) {
        console.error('添加心愿失败:', error)

        // 网络错误时，保存到本地（离线模式）
        if (ErrorHandler.isNetworkError(error)) {
          this.isOnline = false // 更新网络状态
          return this._addWishOffline(enhancedWishData)
        }

        // 版本冲突时，使用离线模式
        if (ErrorHandler.isVersionConflictError(error)) {
          return this._addWishOffline(enhancedWishData)
        }

        // 其他错误，显示错误信息
        ErrorHandler.showError(error, '创建心愿失败')
        throw error
      }
    },

    // 🚀 离线模式添加心愿
    _addWishOffline(wishData) {
      console.log('[wishStore] 离线模式添加心愿:', wishData.title)

      // 生成临时ID
      const tempId = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)

      const wish = {
        _id: tempId,
        id: tempId,
        ...wishData,
        _needSync: true, // 标记需要同步到云端
        _isLocal: true   // 标记为本地创建
      }

      this.wishList.push(wish)
      uni.setStorageSync('wishList', JSON.stringify(this.wishList))

      // 显示离线提示
      uni.showToast({
        title: this.isOnline ? '已保存到本地，稍后将同步到云端' : '当前离线，已保存到本地',
        icon: 'none',
        duration: 2500
      })

      return wish
    }
  }
})

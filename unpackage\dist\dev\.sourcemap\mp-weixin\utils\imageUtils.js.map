{"version": 3, "file": "imageUtils.js", "sources": ["utils/imageUtils.js"], "sourcesContent": ["/**\r\n * 图片处理工具模块\r\n * 提供统一的图片处理逻辑，避免重复代码\r\n */\r\n\r\nimport { devLog } from './envUtils.js'\r\n\r\n/**\r\n * 解析图片URL\r\n * @param {string|Array|Object} imageData - 图片数据\r\n * @returns {string} 第一张图片的URL\r\n */\r\nexport function parseImageUrl(imageData) {\r\n  if (!imageData) return ''\r\n\r\n  // 字符串类型直接返回\r\n  if (typeof imageData === 'string') {\r\n    return imageData.trim()\r\n  }\r\n\r\n  // 数组类型，取第一个有效图片\r\n  if (Array.isArray(imageData) && imageData.length > 0) {\r\n    const firstImage = imageData[0]\r\n    if (typeof firstImage === 'string') {\r\n      return firstImage.trim()\r\n    } else if (firstImage && typeof firstImage === 'object' && firstImage.url) {\r\n      return firstImage.url.trim()\r\n    }\r\n  }\r\n\r\n  // 对象类型，直接取url字段\r\n  if (imageData && typeof imageData === 'object' && imageData.url) {\r\n    return imageData.url.trim()\r\n  }\r\n\r\n  devLog.warn('[ImageUtils] 无法解析图片URL:', imageData)\r\n  return ''\r\n}\r\n\r\n/**\r\n * 转换云存储URL为可访问的临时URL\r\n * @param {string} cloudUrl - 云存储URL\r\n * @returns {Promise<string>} 临时访问URL\r\n */\r\nexport async function convertCloudUrl(cloudUrl) {\r\n  if (!cloudUrl || typeof cloudUrl !== 'string') {\r\n    return ''\r\n  }\r\n\r\n  // 如果不是云存储URL，直接返回\r\n  if (!cloudUrl.startsWith('cloud://')) {\r\n    return cloudUrl\r\n  }\r\n\r\n  try {\r\n    const result = await uniCloud.getTempFileURL({\r\n      fileList: [cloudUrl]\r\n    })\r\n\r\n    if (result && result.fileList && result.fileList.length > 0) {\r\n      return result.fileList[0].tempFileURL || cloudUrl\r\n    }\r\n\r\n    return cloudUrl\r\n  } catch (error) {\r\n    devLog.error('[ImageUtils] 云存储URL转换失败:', error)\r\n    return cloudUrl\r\n  }\r\n}\r\n\r\n/**\r\n * 解析并转换图片URL（支持云存储）\r\n * @param {string|Array|Object} imageData - 图片数据\r\n * @returns {Promise<string>} 可访问的图片URL\r\n */\r\nexport async function parseAndConvertImageUrl(imageData) {\r\n  const rawUrl = parseImageUrl(imageData)\r\n  if (!rawUrl) return ''\r\n\r\n  return await convertCloudUrl(rawUrl)\r\n}\r\n\r\n/**\r\n * 计算有效图片数量\r\n * @param {string|Array|Object} imageData - 图片数据\r\n * @returns {number} 有效图片数量\r\n */\r\nexport function getImageCount(imageData) {\r\n  if (!imageData) return 0\r\n\r\n  // 字符串类型\r\n  if (typeof imageData === 'string') {\r\n    return imageData.trim() !== '' ? 1 : 0\r\n  }\r\n\r\n  // 数组类型\r\n  if (Array.isArray(imageData)) {\r\n    return imageData.filter(img => {\r\n      if (typeof img === 'string') {\r\n        return img.trim() !== ''\r\n      } else if (img && typeof img === 'object' && img.url) {\r\n        return img.url.trim() !== ''\r\n      }\r\n      return false\r\n    }).length\r\n  }\r\n\r\n  // 对象类型\r\n  if (imageData && typeof imageData === 'object' && imageData.url) {\r\n    return imageData.url.trim() !== '' ? 1 : 0\r\n  }\r\n\r\n  return 0\r\n}\r\n\r\n/**\r\n * 检查是否有有效图片\r\n * @param {string|Array|Object} imageData - 图片数据\r\n * @returns {boolean} 是否有有效图片\r\n */\r\nexport function hasValidImage(imageData) {\r\n  return getImageCount(imageData) > 0\r\n}\r\n\r\n/**\r\n * 获取所有有效图片URL\r\n * @param {string|Array|Object} imageData - 图片数据\r\n * @returns {Array<string>} 所有有效图片URL数组\r\n */\r\nexport function getAllImageUrls(imageData) {\r\n  if (!imageData) return []\r\n\r\n  // 字符串类型\r\n  if (typeof imageData === 'string') {\r\n    return imageData.trim() !== '' ? [imageData.trim()] : []\r\n  }\r\n\r\n  // 数组类型\r\n  if (Array.isArray(imageData)) {\r\n    return imageData\r\n      .map(img => {\r\n        if (typeof img === 'string') {\r\n          return img.trim()\r\n        } else if (img && typeof img === 'object' && img.url) {\r\n          return img.url.trim()\r\n        }\r\n        return ''\r\n      })\r\n      .filter(url => url !== '')\r\n  }\r\n\r\n  // 对象类型\r\n  if (imageData && typeof imageData === 'object' && imageData.url) {\r\n    const url = imageData.url.trim()\r\n    return url !== '' ? [url] : []\r\n  }\r\n\r\n  return []\r\n}\r\n\r\n/**\r\n * 图片加载错误处理\r\n * @param {Event} e - 错误事件\r\n * @param {Object} context - 上下文信息\r\n */\r\nexport function handleImageError(e, context = {}) {\r\n  const src = e?.target?.src || e?.detail?.src || '未知图片源'\r\n  \r\n  devLog.error('[ImageUtils] 图片加载失败:', {\r\n    src,\r\n    ...context,\r\n    error: e\r\n  })\r\n\r\n  // 可以在这里添加默认图片逻辑\r\n  // if (e.target && e.target.src !== '/static/images/default-image.png') {\r\n  //   e.target.src = '/static/images/default-image.png'\r\n  // }\r\n}\r\n\r\n/**\r\n * 图片加载成功处理\r\n * @param {Event} e - 加载事件\r\n * @param {Object} context - 上下文信息\r\n */\r\nexport function handleImageLoad(e, context = {}) {\r\n  const src = e?.target?.src || e?.detail?.src || '未知图片源'\r\n  \r\n  devLog.log('[ImageUtils] 图片加载成功:', src, context)\r\n}\r\n\r\n/**\r\n * 图片懒加载管理器\r\n */\r\nexport class ImageLazyLoader {\r\n  constructor(options = {}) {\r\n    this.loadedImages = new Set()\r\n    this.loadingImages = new Set()\r\n    this.failedImages = new Set()\r\n    this.retryCount = options.retryCount || 3\r\n    this.retryDelay = options.retryDelay || 1000\r\n  }\r\n\r\n  /**\r\n   * 检查图片是否已加载\r\n   */\r\n  isLoaded(url) {\r\n    return this.loadedImages.has(url)\r\n  }\r\n\r\n  /**\r\n   * 检查图片是否正在加载\r\n   */\r\n  isLoading(url) {\r\n    return this.loadingImages.has(url)\r\n  }\r\n\r\n  /**\r\n   * 加载图片\r\n   */\r\n  async loadImage(url, retryCount = 0) {\r\n    if (this.isLoaded(url)) {\r\n      return Promise.resolve()\r\n    }\r\n\r\n    if (this.isLoading(url)) {\r\n      return Promise.resolve()\r\n    }\r\n\r\n    this.loadingImages.add(url)\r\n\r\n    try {\r\n      await new Promise((resolve, reject) => {\r\n        const img = new Image()\r\n        img.onload = () => {\r\n          this.loadedImages.add(url)\r\n          this.loadingImages.delete(url)\r\n          this.failedImages.delete(url)\r\n          resolve()\r\n        }\r\n        img.onerror = () => {\r\n          reject(new Error(`图片加载失败: ${url}`))\r\n        }\r\n        img.src = url\r\n      })\r\n    } catch (error) {\r\n      this.loadingImages.delete(url)\r\n      \r\n      if (retryCount < this.retryCount) {\r\n        devLog.warn(`[ImageLazyLoader] 图片加载失败，${this.retryDelay}ms后重试: ${url}`)\r\n        await new Promise(resolve => setTimeout(resolve, this.retryDelay))\r\n        return this.loadImage(url, retryCount + 1)\r\n      } else {\r\n        this.failedImages.add(url)\r\n        devLog.error(`[ImageLazyLoader] 图片加载失败，已达到最大重试次数: ${url}`)\r\n        throw error\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 清除缓存\r\n   */\r\n  clear() {\r\n    this.loadedImages.clear()\r\n    this.loadingImages.clear()\r\n    this.failedImages.clear()\r\n  }\r\n}\r\n\r\n// 创建全局懒加载实例\r\nexport const globalImageLoader = new ImageLazyLoader()\r\n\r\nexport default {\r\n  parseImageUrl,\r\n  getImageCount,\r\n  hasValidImage,\r\n  getAllImageUrls,\r\n  handleImageError,\r\n  handleImageLoad,\r\n  ImageLazyLoader,\r\n  globalImageLoader\r\n} "], "names": ["uniCloud", "devLog"], "mappings": ";;;AA4CO,eAAe,gBAAgB,UAAU;AAC9C,MAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC7C,WAAO;AAAA,EACR;AAGD,MAAI,CAAC,SAAS,WAAW,UAAU,GAAG;AACpC,WAAO;AAAA,EACR;AAED,MAAI;AACF,UAAM,SAAS,MAAMA,cAAQ,GAAC,eAAe;AAAA,MAC3C,UAAU,CAAC,QAAQ;AAAA,IACzB,CAAK;AAED,QAAI,UAAU,OAAO,YAAY,OAAO,SAAS,SAAS,GAAG;AAC3D,aAAO,OAAO,SAAS,CAAC,EAAE,eAAe;AAAA,IAC1C;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdC,0BAAO,MAAM,4BAA4B,KAAK;AAC9C,WAAO;AAAA,EACR;AACH;AA8HO,MAAM,gBAAgB;AAAA,EAC3B,YAAY,UAAU,IAAI;AACxB,SAAK,eAAe,oBAAI,IAAK;AAC7B,SAAK,gBAAgB,oBAAI,IAAK;AAC9B,SAAK,eAAe,oBAAI,IAAK;AAC7B,SAAK,aAAa,QAAQ,cAAc;AACxC,SAAK,aAAa,QAAQ,cAAc;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAKD,SAAS,KAAK;AACZ,WAAO,KAAK,aAAa,IAAI,GAAG;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,KAAK;AACb,WAAO,KAAK,cAAc,IAAI,GAAG;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,UAAU,KAAK,aAAa,GAAG;AACnC,QAAI,KAAK,SAAS,GAAG,GAAG;AACtB,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,QAAI,KAAK,UAAU,GAAG,GAAG;AACvB,aAAO,QAAQ,QAAS;AAAA,IACzB;AAED,SAAK,cAAc,IAAI,GAAG;AAE1B,QAAI;AACF,YAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrC,cAAM,MAAM,IAAI,MAAO;AACvB,YAAI,SAAS,MAAM;AACjB,eAAK,aAAa,IAAI,GAAG;AACzB,eAAK,cAAc,OAAO,GAAG;AAC7B,eAAK,aAAa,OAAO,GAAG;AAC5B,kBAAS;AAAA,QACV;AACD,YAAI,UAAU,MAAM;AAClB,iBAAO,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC;AAAA,QACnC;AACD,YAAI,MAAM;AAAA,MAClB,CAAO;AAAA,IACF,SAAQ,OAAO;AACd,WAAK,cAAc,OAAO,GAAG;AAE7B,UAAI,aAAa,KAAK,YAAY;AAChCA,8BAAO,KAAK,4BAA4B,KAAK,UAAU,UAAU,GAAG,EAAE;AACtE,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,UAAU,CAAC;AACjE,eAAO,KAAK,UAAU,KAAK,aAAa,CAAC;AAAA,MACjD,OAAa;AACL,aAAK,aAAa,IAAI,GAAG;AACzBA,uBAAAA,OAAO,MAAM,uCAAuC,GAAG,EAAE;AACzD,cAAM;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,QAAQ;AACN,SAAK,aAAa,MAAO;AACzB,SAAK,cAAc,MAAO;AAC1B,SAAK,aAAa,MAAO;AAAA,EAC1B;AACH;AAGiC,IAAI,gBAAe;;"}
"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_group = require("../../../store/group.js");
const store_user = require("../../../store/user.js");
const _sfc_main = {
  setup() {
    const groupStore = store_group.useGroupStore();
    const userStore = store_user.useUserStore();
    return {
      groupStore,
      userStore
    };
  },
  data() {
    return {
      addGroupName: "",
      editingGroupId: null,
      editingGroupName: "",
      deletingGroupId: null
    };
  },
  computed: {
    groups() {
      return this.groupStore.getAllGroups;
    }
  },
  methods: {
    // 显示添加分组弹窗
    showAddGroupModal() {
      this.$refs.addGroupPopup.open();
    },
    // 关闭添加分组弹窗
    closeAddGroupModal() {
      this.$refs.addGroupPopup.close();
    },
    // 确认添加分组
    confirmAddGroup(value) {
      if (!value.trim()) {
        common_vendor.index.showToast({
          title: "分组名称不能为空",
          icon: "none"
        });
        return;
      }
      if (!this.userStore.checkLoginAndRedirect()) {
        common_vendor.index.__f__("log", "at subpkg-wish/pages/groupManage/groupManage.vue:130", "用户未登录，跳转到登录页面");
        return;
      }
      this.groupStore.addGroup(value.trim());
      common_vendor.index.showToast({
        title: "添加成功",
        icon: "success"
      });
      this.closeAddGroupModal();
    },
    // 显示编辑分组弹窗
    showEditGroupModal(group) {
      this.editingGroupId = group.id;
      this.editingGroupName = group.name;
      this.$refs.editGroupPopup.open();
    },
    // 关闭编辑分组弹窗
    closeEditGroupModal() {
      this.editingGroupId = null;
      this.editingGroupName = "";
      this.$refs.editGroupPopup.close();
    },
    // 确认编辑分组
    confirmEditGroup(value) {
      if (!this.editingGroupId || !value.trim()) {
        common_vendor.index.showToast({
          title: "分组名称不能为空",
          icon: "none"
        });
        return;
      }
      if (!this.userStore.checkLoginAndRedirect()) {
        common_vendor.index.__f__("log", "at subpkg-wish/pages/groupManage/groupManage.vue:168", "用户未登录，跳转到登录页面");
        return;
      }
      this.groupStore.updateGroup(this.editingGroupId, value.trim());
      common_vendor.index.showToast({
        title: "更新成功",
        icon: "success"
      });
      this.closeEditGroupModal();
    },
    // 显示删除确认
    showDeleteConfirm(group) {
      this.deletingGroupId = group.id;
      this.$refs.deletePopup.open();
    },
    // 关闭删除确认
    closeDeleteConfirm() {
      this.deletingGroupId = null;
      this.$refs.deletePopup.close();
    },
    // 确认删除分组
    confirmDeleteGroup() {
      if (!this.deletingGroupId) {
        return;
      }
      if (!this.userStore.checkLoginAndRedirect()) {
        common_vendor.index.__f__("log", "at subpkg-wish/pages/groupManage/groupManage.vue:200", "用户未登录，跳转到登录页面");
        return;
      }
      this.groupStore.deleteGroup(this.deletingGroupId);
      common_vendor.index.showToast({
        title: "已删除",
        icon: "success"
      });
      this.closeDeleteConfirm();
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_icons2 + _easycom_uni_popup_dialog2 + _easycom_uni_popup2)();
}
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_uni_popup_dialog = () => "../../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_uni_popup_dialog + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      type: "plus",
      size: "18",
      color: "#8a2be2"
    }),
    b: common_vendor.o((...args) => $options.showAddGroupModal && $options.showAddGroupModal(...args)),
    c: $options.groups.length === 0
  }, $options.groups.length === 0 ? {} : {
    d: common_vendor.f($options.groups, (group, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(group.name),
        b: group.isDefault
      }, group.isDefault ? {} : {}, {
        c: "623a7250-1-" + i0,
        d: common_vendor.p({
          type: "compose",
          size: "18",
          color: group.isDefault ? "#ccc" : "#8a2be2"
        }),
        e: group.isDefault ? 1 : "",
        f: common_vendor.o(($event) => !group.isDefault && $options.showEditGroupModal(group), group.id),
        g: "623a7250-2-" + i0,
        h: common_vendor.p({
          type: "trash",
          size: "18",
          color: group.isDefault ? "#ccc" : "#f56c6c"
        }),
        i: group.isDefault ? 1 : "",
        j: common_vendor.o(($event) => !group.isDefault && $options.showDeleteConfirm(group), group.id),
        k: group.id
      });
    })
  }, {
    e: common_vendor.o($options.confirmAddGroup),
    f: common_vendor.o($options.closeAddGroupModal),
    g: common_vendor.p({
      title: "添加分组",
      mode: "input",
      placeholder: "请输入分组名称",
      ["before-close"]: true
    }),
    h: common_vendor.sr("addGroupPopup", "623a7250-3"),
    i: common_vendor.p({
      type: "dialog"
    }),
    j: common_vendor.o($options.confirmEditGroup),
    k: common_vendor.o($options.closeEditGroupModal),
    l: common_vendor.p({
      title: "编辑分组",
      mode: "input",
      value: $data.editingGroupName,
      placeholder: "请输入分组名称",
      ["before-close"]: true
    }),
    m: common_vendor.sr("editGroupPopup", "623a7250-5"),
    n: common_vendor.p({
      type: "dialog"
    }),
    o: common_vendor.o($options.confirmDeleteGroup),
    p: common_vendor.o($options.closeDeleteConfirm),
    q: common_vendor.p({
      title: "删除分组",
      content: "确定要删除此分组吗？删除后无法恢复。",
      ["before-close"]: true
    }),
    r: common_vendor.sr("deletePopup", "623a7250-7"),
    s: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpkg-wish/pages/groupManage/groupManage.js.map

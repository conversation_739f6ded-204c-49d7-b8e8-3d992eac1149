{"version": 3, "file": "wishDetail.js", "sources": ["subpkg-wish/pages/wishDetail/wishDetail.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGtnLXdpc2hccGFnZXNcd2lzaERldGFpbFx3aXNoRGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"wish-detail-container\">\r\n\t\t<!-- 心愿详情 -->\r\n\t\t<view class=\"wish-detail card\">\r\n\t\t\t<view class=\"wish-header\">\r\n\t\t\t\t<view class=\"wish-title-wrap\">\r\n\t\t\t\t\t<view class=\"wish-title\">{{ wish?.title || '心愿详情' }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"wish-status\">\r\n\t\t\t\t\t<view v-if=\"wish?.permission === 'private'\" class=\"tag private-tag\">\r\n\t\t\t\t\t\t<uni-icons type=\"lock-filled\" size=\"12\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t\t<text>私密</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else-if=\"wish?.permission === 'friends'\" class=\"tag friends-tag\">\r\n\t\t\t\t\t\t<uni-icons type=\"people-filled\" size=\"12\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t\t<text>朋友可见</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else-if=\"wish?.permission === 'public'\" class=\"tag public-tag\">\r\n\t\t\t\t\t\t<uni-icons type=\"eye-filled\" size=\"12\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t\t<text>公开</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"wish?.isCompleted\" class=\"tag completed-tag\">\r\n\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"12\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t\t<text>已完成</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"wish-content\">\r\n\t\t\t\t<view class=\"wish-desc\" v-if=\"wish?.description\">{{ wish.description }}</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 单图情况 -->\r\n\t\t\t\t<image v-if=\"wish?.image && typeof wish.image === 'string'\" \r\n\t\t\t\t\t   class=\"wish-image\" \r\n\t\t\t\t\t   :src=\"wish.image\" \r\n\t\t\t\t\t   mode=\"widthFix\"\r\n\t\t\t\t\t   @click=\"previewImages(0)\"></image>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 多图情况，使用轮播 -->\r\n\t\t\t\t<swiper v-else-if=\"wish?.image && Array.isArray(wish.image) && wish.image.length > 0\" \r\n\t\t\t\t\tclass=\"image-swiper\" \r\n\t\t\t\t\t:indicator-dots=\"true\" \r\n\t\t\t\t\t:autoplay=\"false\" \r\n\t\t\t\t\t:duration=\"500\"\r\n\t\t\t\t\tindicator-active-color=\"#8a2be2\">\r\n\t\t\t\t\t<swiper-item v-for=\"(img, index) in wish.image\" :key=\"index\">\r\n\t\t\t\t\t\t<image class=\"swiper-image\" \r\n\t\t\t\t\t\t\t   :src=\"img\" \r\n\t\t\t\t\t\t\t   mode=\"widthFix\" \r\n\t\t\t\t\t\t\t   @click=\"previewImages(index)\"></image>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"wish-time-info\">\r\n\t\t\t\t\t<view class=\"time-item\">\r\n\t\t\t\t\t\t<text class=\"time-label\">创建时间：</text>\r\n\t\t\t\t\t\t<text class=\"time-value\">{{ formatTime(wish?.createDate) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"time-item\" v-if=\"wish?.startDate\">\r\n\t\t\t\t\t\t<text class=\"time-label\">开始时间：</text>\r\n\t\t\t\t\t\t<text class=\"time-value\">{{ formatTime(wish?.startDate) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"time-item\" v-if=\"wish?.completeDate || wish?.lastCompleteDate\">\r\n\t\t\t\t\t\t<text class=\"time-label\">完成时间：</text>\r\n\t\t\t\t\t\t<text class=\"time-value\">{{ getCompleteTime() }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"wish-actions\">\r\n\t\t\t\t<view class=\"action-btn\" @click=\"editWish\">\r\n\t\t\t\t\t<uni-icons type=\"compose\" size=\"18\" color=\"#8a2be2\"></uni-icons>\r\n\t\t\t\t\t<text>编辑</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button open-type=\"share\" class=\"action-btn share-btn\" @click=\"shareWish\">\r\n\t\t\t\t\t<uni-icons type=\"redo-filled\" size=\"18\" color=\"#8a2be2\"></uni-icons>\r\n\t\t\t\t\t<text>分享</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"action-btn\" v-if=\"!wish?.isCompleted\" @click=\"completeWish\">\r\n\t\t\t\t\t<uni-icons type=\"checkbox-filled\" size=\"18\" color=\"#8a2be2\"></uni-icons>\r\n\t\t\t\t\t<text>完成</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-btn delete\" @click=\"showDeleteConfirm\">\r\n\t\t\t\t\t<uni-icons type=\"trash-filled\" size=\"18\" color=\"#f56c6c\"></uni-icons>\r\n\t\t\t\t\t<text>删除</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 分组信息 -->\r\n\t\t<view class=\"wish-groups card\">\r\n\t\t\t<view class=\"section-title\">\r\n\t\t\t\t<text>所属分组</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"group-tags\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"groupId in wish?.groupIds\" \r\n\t\t\t\t\t:key=\"groupId\"\r\n\t\t\t\t\tclass=\"group-tag\"\r\n\t\t\t\t\t@click=\"navigateToGroup(groupId)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{ getGroupName(groupId) }}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 评论区 -->\r\n\t\t<view class=\"wish-comments card\" @click=\"hideDeleteButton\">\r\n\t\t\t<view class=\"section-title\">\r\n\t\t\t\t<text>评论区</text>\r\n\t\t\t\t<text class=\"comment-count\">{{ comments.length || 0 }}条评论</text>\r\n\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-if=\"!comments || comments.length === 0\" class=\"empty-comment\">\r\n\t\t\t\t<text>暂无评论，快来添加第一条评论吧</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-else class=\"comment-list\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"(comment, index) in comments\" \r\n\t\t\t\t\t:key=\"comment._id\"\r\n\t\t\t\t\tclass=\"comment-item\"\r\n\t\t\t\t\t@longpress=\"handleCommentLongPress(comment, $event)\"\r\n\t\t\t\t\t@touchstart=\"handleTouchStart\"\r\n\t\t\t\t\t@touchend=\"handleTouchEnd\"\r\n\t\t\t\t\************\r\n\t\t\t\t>\r\n\t\t\t\t\t<image class=\"comment-avatar\" :src=\"comment.user_info?.avatar || '/static/default-avatar.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<view class=\"comment-content\">\r\n\t\t\t\t\t\t<view class=\"comment-user\">\r\n\t\t\t\t\t\t\t<text class=\"comment-nickname\">{{ comment.user_info?.nickname || '匿名用户' }}</text>\r\n\t\t\t\t\t\t\t<text class=\"comment-time\">{{ formatTime(comment.createDate) }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"comment-text\">{{ comment.content }}</view>\r\n\t\t\t\t\t\t<image v-if=\"comment.image && comment.image.length > 0\" class=\"comment-image\" :src=\"comment.image[0]\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 悬浮删除按钮 -->\r\n\t\t\t<view \r\n\t\t\t\tv-if=\"showDeleteBtn && canDeleteComment(selectedComment)\"\r\n\t\t\t\tclass=\"floating-delete-btn\"\r\n\t\t\t\t:style=\"deleteButtonStyle\"\r\n\t\t\t\************=\"showDeleteCommentConfirm(selectedComment)\"\r\n\t\t\t>\r\n\t\t\t\t<uni-icons type=\"trash-filled\" size=\"16\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t<text>删除</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 评论框 -->\r\n\t\t\t<view class=\"comment-form\">\r\n\t\t\t\t<input \r\n\t\t\t\t\tclass=\"comment-input\"\r\n\t\t\t\t\tv-model=\"commentText\"\r\n\t\t\t\t\tplaceholder=\"添加评论...\"\r\n\t\t\t\t\tconfirm-type=\"send\"\r\n\t\t\t\t\t@confirm=\"submitComment\"\r\n\t\t\t\t/>\r\n\t\t\t\t<view class=\"comment-btn\" @click=\"submitComment\">\r\n\t\t\t\t\t<uni-icons type=\"paperplane-filled\" size=\"20\" color=\"#8a2be2\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 删除心愿确认弹窗 -->\r\n\t\t<uni-popup ref=\"deletePopup\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog\r\n\t\t\t\ttitle=\"删除心愿\"\r\n\t\t\t\tcontent=\"确定要删除这个心愿吗？删除后无法恢复。\"\r\n\t\t\t\t:before-close=\"true\"\r\n\t\t\t\t@confirm=\"confirmDelete\"\r\n\t\t\t\t@close=\"closeDeleteConfirm\"\r\n\t\t\t></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 删除评论确认弹窗 -->\r\n\t\t<uni-popup ref=\"deleteCommentPopup\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog\r\n\t\t\t\ttitle=\"删除评论\"\r\n\t\t\t\tcontent=\"确定要删除这条评论吗？删除后无法恢复。\"\r\n\t\t\t\t:before-close=\"true\"\r\n\t\t\t\t@confirm=\"confirmDeleteComment\"\r\n\t\t\t\t@close=\"closeDeleteCommentConfirm\"\r\n\t\t\t></uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { useWishStore } from '@/store/wish.js'\r\n\timport { useGroupStore } from '@/store/group.js'\r\n\timport { useCommentStore } from '@/store/comment.js'\r\n\timport { useUserStore } from '@/store/user.js'\r\n\timport loadingManager from '@/utils/loadingManager.js'\r\n\timport groupTagOperations from '@/mixins/groupTagOperations.js'\r\n\t\r\n\texport default {\r\n\t\tmixins: [groupTagOperations],\r\n\t\t// 微信小程序分享给朋友\r\n\t\tonShareAppMessage(res) {\r\n\t\t\t// 获取当前心愿详情\r\n\t\t\tconst wish = this.wish || {}\r\n\t\t\treturn {\r\n\t\t\t\ttitle: wish.title || '分享我的心愿',\r\n\t\t\t\tpath: `/pages/wishDetail/wishDetail?id=${this.wishId}`,\r\n\t\t\t\timageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? \r\n\t\t\t\t\twish.image[0] : (wish.image || '/static/images/share-image.png')\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 微信小程序分享到朋友圈\r\n\t\tonShareTimeline() {\r\n\t\t\t// 获取当前心愿详情\r\n\t\t\tconst wish = this.wish || {}\r\n\t\t\treturn {\r\n\t\t\t\ttitle: wish.title || '分享我的心愿',\r\n\t\t\t\tquery: `id=${this.wishId}`,\r\n\t\t\t\timageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? \r\n\t\t\t\t\twish.image[0] : (wish.image || '/static/images/share-image.png')\r\n\t\t\t}\r\n\t\t},\r\n\t\tsetup() {\r\n\t\t\tconst wishStore = useWishStore()\r\n\t\t\tconst groupStore = useGroupStore()\r\n\t\t\tconst commentStore = useCommentStore()\r\n\t\t\tconst userStore = useUserStore()\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\twishStore,\r\n\t\t\t\tgroupStore,\r\n\t\t\t\tcommentStore,\r\n\t\t\t\tuserStore\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\twishId: '',\r\n\t\t\t\twish: null,\r\n\t\t\t\tcommentText: '',\r\n\t\t\t\tcomments: [],\r\n\t\t\t\tnewComment: '',\r\n\t\t\t\tcommentImages: [],\r\n\t\t\t\tshowCommentDialog: false,\r\n\t\t\t\tisLoadingComments: false,\r\n\t\t\t\tisPageLoading: true, // 页面整体加载状态\r\n\t\t\t\tcommentToDelete: null, // 待删除的评论\r\n\t\t\t\tshowDeleteBtn: false, // 是否显示悬浮删除按钮\r\n\t\t\t\tselectedComment: null, // 当前选中的评论\r\n\t\t\t\tdeleteButtonPosition: { x: 0, y: 0 }, // 删除按钮位置\r\n\t\t\t\tlongPressTimeout: null // 长按定时器\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 获取分组名称映射（计算属性，避免重复调用）\r\n\t\t\tgroupNameMap() {\r\n\t\t\t\tif (!this.groupStore || !this.groupStore.getAllGroups) {\r\n\t\t\t\t\treturn {};\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst map = {};\r\n\t\t\t\tthis.groupStore.getAllGroups.forEach(group => {\r\n\t\t\t\t\tmap[group._id || group.id] = group.name;\r\n\t\t\t\t});\r\n\t\t\t\treturn map;\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化心愿状态\r\n\t\t\twishStatus() {\r\n\t\t\t\tif (!this.wish) return ''\r\n\t\t\t\treturn this.wish.isCompleted ? '已完成' : '进行中'\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化权限显示\r\n\t\t\tpermissionText() {\r\n\t\t\t\tif (!this.wish) return ''\r\n\t\t\t\tconst permissionMap = {\r\n\t\t\t\t\t'private': '私密',\r\n\t\t\t\t\t'friends': '朋友可见', \r\n\t\t\t\t\t'public': '公开'\r\n\t\t\t\t}\r\n\t\t\t\treturn permissionMap[this.wish.permission] || '未知'\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除按钮样式\r\n\t\t\tdeleteButtonStyle() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tleft: this.deleteButtonPosition.x + 'px',\r\n\t\t\t\t\ttop: this.deleteButtonPosition.y + 'px'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\r\n\t\t},\r\n\t\tasync onLoad(options) {\r\n\t\t\tif (options.id) {\r\n\t\t\t\tthis.wishId = options.id\r\n\t\t\t\tthis.isPageLoading = true\r\n\r\n\t\t\t\t// 清理任何可能残留的加载状态\r\n\t\t\t\tthis.clearAllLoadingStates()\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('[wishDetail] 开始初始化页面...')\r\n\r\n\t\t\t\t\t// 设置一个总体超时，确保页面不会无限加载\r\n\t\t\t\t\tconst pageTimeout = setTimeout(() => {\r\n\t\t\t\t\t\tconsole.warn('[wishDetail] 页面加载超时，强制结束加载状态')\r\n\t\t\t\t\t\tthis.isPageLoading = false\r\n\t\t\t\t\t\tthis.isLoadingComments = false\r\n\t\t\t\t\t\tthis.clearAllLoadingStates()\r\n\t\t\t\t\t}, 12000) // 12秒总超时\r\n\r\n\t\t\t\t\tawait this.initStores()\r\n\t\t\t\t\tthis.loadWishDetail()\r\n\r\n\t\t\t\t\t// 强制刷新评论，确保获取最新数据\r\n\t\t\t\t\tawait this.loadComments(true)\r\n\r\n\t\t\t\t\tclearTimeout(pageTimeout)\r\n\t\t\t\t\tconsole.log('[wishDetail] 页面初始化完成')\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('页面初始化失败:', error)\r\n\t\t\t\t\tthis.clearAllLoadingStates()\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.isPageLoading = false\r\n\t\t\t\t\tthis.clearAllLoadingStates()\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\r\n\t\t// 页面每次显示时执行，确保数据最新\r\n\t\tonShow() {\r\n\t\t\t// 避免在页面初始加载时重复执行\r\n\t\t\tif (this.isPageLoading) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\t// 清理可能残留的加载状态\r\n\t\t\tthis.clearAllLoadingStates()\r\n\r\n\t\t\t// 从本地存储刷新心愿列表数据\r\n\t\t\tthis.wishStore.refreshWishList();\r\n\r\n\t\t\t// 重新加载当前心愿详情，确保显示最新数据\r\n\t\t\tif (this.wishId) {\r\n\t\t\t\tthis.loadWishDetail();\r\n\r\n\t\t\t\t// 每次显示都强制同步评论数据\r\n\t\t\t\tthis.loadComments(true);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 页面隐藏时清理加载状态\r\n\t\tonHide() {\r\n\t\t\tthis.clearAllLoadingStates()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化stores\r\n\t\t\tasync initStores() {\r\n\t\t\t\tthis.wishStore = useWishStore()\r\n\t\t\t\tthis.groupStore = useGroupStore()\r\n\t\t\t\tthis.commentStore = useCommentStore()\r\n\t\t\t\tthis.userStore = useUserStore()\r\n\r\n\t\t\t\t// 初始化分组store，添加超时处理\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst timeoutPromise = new Promise((_, reject) => {\r\n\t\t\t\t\t\tsetTimeout(() => reject(new Error('分组初始化超时')), 8000) // 8秒超时\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tawait Promise.race([this.groupStore.initGroups(), timeoutPromise])\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('分组Store初始化失败:', error)\r\n\t\t\t\t\t// 确保有默认分组可用\r\n\t\t\t\t\tthis.groupStore.ensureDefaultGroups()\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 🚀 使用绑定同步检查替代传统的评论初始化\r\n\t\t\t\tawait this.checkAndSyncWishAndComments()\r\n\r\n\t\t\t\t// 设置同步监听\r\n\t\t\t\tthis._setupSyncListeners()\r\n\t\t\t},\r\n\r\n\t\t\t// 🚀 检查并同步心愿和评论数据（同步合并：同时调用但不合并内容）\r\n\t\t\tasync checkAndSyncWishAndComments() {\r\n\t\t\t\tif (!this.wishId) return;\r\n\r\n\t\t\t\tconsole.log('[wishDetail] 检查心愿和评论数据同步状态...');\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 🚀 分别检查：减少不必要的数据传输\r\n\t\t\t\t\tconst wishNeedsSync = await this.checkWishNeedsSync();\r\n\t\t\t\t\tconst commentsNeedSync = await this.checkCommentsNeedSync();\r\n\r\n\t\t\t\t\tif (wishNeedsSync || commentsNeedSync) {\r\n\t\t\t\t\t\tconsole.log('[wishDetail] 需要同步数据:', {\r\n\t\t\t\t\t\t\twishNeedsSync,\r\n\t\t\t\t\t\t\tcommentsNeedSync\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 🚀 同步合并：同时调用但只同步需要的数据，减少传输\r\n\t\t\t\t\t\tconst syncPromises = [];\r\n\r\n\t\t\t\t\t\tif (wishNeedsSync) {\r\n\t\t\t\t\t\t\tconsole.log('[wishDetail] 添加心愿数据同步');\r\n\t\t\t\t\t\t\tsyncPromises.push(this.wishStore.syncFromCloud({ silent: true }));\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (commentsNeedSync) {\r\n\t\t\t\t\t\t\tconsole.log('[wishDetail] 添加评论数据同步');\r\n\t\t\t\t\t\t\tsyncPromises.push(this.commentStore.syncCommentsForWish(this.wishId, true, true));\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 同时执行需要的同步操作\r\n\t\t\t\t\t\tawait Promise.allSettled(syncPromises);\r\n\t\t\t\t\t\tconsole.log('[wishDetail] 同步合并完成');\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('[wishDetail] 数据无需同步，使用本地缓存');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('[wishDetail] 同步合并失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 检查心愿数据是否需要同步\r\n\t\t\tasync checkWishNeedsSync() {\r\n\t\t\t\tif (!this.wishStore || !this.wishId) return true;\r\n\r\n\t\t\t\tconst hasLocalWish = this.wishStore.getWishById(this.wishId);\r\n\t\t\t\tconst lastSyncTime = this.wishStore.lastSyncTime;\r\n\t\t\t\tconst now = Date.now();\r\n\r\n\t\t\t\t// 如果没有本地数据，或者超过5分钟未同步\r\n\t\t\t\treturn !hasLocalWish || !lastSyncTime || (now - lastSyncTime > 5 * 60 * 1000);\r\n\t\t\t},\r\n\r\n\t\t\t// 检查评论数据是否需要同步\r\n\t\t\tasync checkCommentsNeedSync() {\r\n\t\t\t\tif (!this.commentStore || !this.wishId) return true;\r\n\r\n\t\t\t\tconst hasLocalComments = this.commentStore.commentsByWish[this.wishId] &&\r\n\t\t\t\t\t\t\t\t\t\tthis.commentStore.commentsByWish[this.wishId].length >= 0; // 允许空数组\r\n\t\t\t\tconst lastSyncTime = this.commentStore.syncTimestamps[this.wishId];\r\n\t\t\t\tconst now = Date.now();\r\n\r\n\t\t\t\t// 如果没有本地评论数据，或者超过5分钟未同步\r\n\t\t\t\treturn !hasLocalComments || !lastSyncTime || (now - lastSyncTime > 5 * 60 * 1000);\r\n\t\t\t},\r\n\r\n\r\n\t\t\t\r\n\t\t\t// 设置同步监听器\r\n\t\t\t_setupSyncListeners() {\r\n\t\t\t\t// 监听评论同步状态\r\n\t\t\t\tthis.commentStore.on && this.commentStore.on('sync_completed', () => {\r\n\t\t\t\t\tconsole.log('评论数据同步完成')\r\n\t\t\t\t\t// 刷新当前页面的评论列表\r\n\t\t\t\t\tif (this.wishId) {\r\n\t\t\t\t\t\tthis.loadComments()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\r\n\t\t\t\r\n\t\t\t// 加载心愿详情\r\n\t\t\tloadWishDetail() {\r\n\t\t\t\tthis.wish = this.wishStore.getWishById(this.wishId)\r\n\t\t\t\t\r\n\t\t\t\tif (!this.wish) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '心愿不存在',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t}, 1500)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载评论列表\r\n\t\t\tasync loadComments(refresh = false) {\r\n\t\t\t\tif (!this.wishId) return\r\n\r\n\t\t\t\tthis.isLoadingComments = true\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 使用统一加载管理器，静默加载（不显示弹窗）\r\n\t\t\t\t\tthis.comments = await loadingManager.wrap(\r\n\t\t\t\t\t\t() => this.commentStore.loadComments(this.wishId, refresh),\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\ttitle: '加载评论中...',\r\n\t\t\t\t\t\t\tid: `load_comments_${this.wishId}`,\r\n\t\t\t\t\t\t\ttimeout: 8000,\r\n\t\t\t\t\t\t\tsilent: true, // 静默加载，不显示弹窗\r\n\t\t\t\t\t\t\tshowError: false // 不显示错误提示，由页面处理\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t)\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载评论失败:', error)\r\n\t\t\t\t\t// 失败时，尝试使用本地缓存数据\r\n\t\t\t\t\tthis.comments = this.commentStore.commentsByWish[this.wishId] || []\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.isLoadingComments = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示评论输入框\r\n\t\t\tshowCommentInput() {\r\n\t\t\t\t// 检查登录状态\r\n\t\t\t\tif (!this.userStore.checkLoginAndRedirect()) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.showCommentDialog = true\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 发布评论\r\n\t\t\tasync submitComment() {\r\n\t\t\t\tif (!this.commentText.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入评论内容',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet loadingShown = false\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.showLoading({ title: '发布中...' })\r\n\t\t\t\t\tloadingShown = true\r\n\r\n\t\t\t\t\tconst commentData = {\r\n\t\t\t\t\t\tcontent: this.commentText.trim(),\r\n\t\t\t\t\t\timage: []\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tawait this.commentStore.addComment(this.wishId, commentData)\r\n\r\n\t\t\t\t\t// 立即强制刷新评论列表，确保数据同步\r\n\t\t\t\t\tawait this.loadComments(true)\r\n\r\n\t\t\t\t\t// 清空输入\r\n\t\t\t\t\tthis.commentText = ''\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '评论成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('发布评论失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: error.message || '评论发布失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tif (loadingShown) {\r\n\t\t\t\t\t\tuni.hideLoading().catch(() => {})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 额外清理，确保没有残留的加载状态\r\n\t\t\t\t\tthis.clearAllLoadingStates()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 检查是否可以删除评论（评论作者或心愿作者）\r\n\t\t\tcanDeleteComment(comment) {\r\n\t\t\t\tif (!this.userStore.isLoggedIn || !comment) return false\r\n\t\t\t\t\r\n\t\t\t\tconst currentUserId = this.userStore.userId || this.userStore.userInfo?.uid\r\n\t\t\t\tconst isCommentAuthor = comment.userId === currentUserId\r\n\t\t\t\tconst isWishAuthor = this.wish?.userId === currentUserId\r\n\t\t\t\t\r\n\t\t\t\treturn isCommentAuthor || isWishAuthor\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理评论长按事件\r\n\t\t\thandleCommentLongPress(comment, event) {\r\n\t\t\t\tif (!this.canDeleteComment(comment)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '无权限删除',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取触摸位置\r\n\t\t\t\tlet touchX = 100\r\n\t\t\t\tlet touchY = 200\r\n\t\t\t\t\r\n\t\t\t\t// 尝试多种方式获取触摸位置\r\n\t\t\t\tif (event && event.detail && (event.detail.x !== undefined || event.detail.y !== undefined)) {\r\n\t\t\t\t\t// 小程序长按事件的位置信息在 detail 中\r\n\t\t\t\t\ttouchX = event.detail.x || touchX\r\n\t\t\t\t\ttouchY = event.detail.y || touchY\r\n\t\t\t\t} else if (event && event.touches && event.touches.length > 0) {\r\n\t\t\t\t\t// 触摸事件\r\n\t\t\t\t\tconst touch = event.touches[0]\r\n\t\t\t\t\ttouchX = touch.clientX || touch.pageX || touchX\r\n\t\t\t\t\ttouchY = touch.clientY || touch.pageY || touchY\r\n\t\t\t\t} else if (event && event.changedTouches && event.changedTouches.length > 0) {\r\n\t\t\t\t\t// 触摸结束事件\r\n\t\t\t\t\tconst touch = event.changedTouches[0]\r\n\t\t\t\t\ttouchX = touch.clientX || touch.pageX || touchX\r\n\t\t\t\t\ttouchY = touch.clientY || touch.pageY || touchY\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取屏幕信息来调整位置\r\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\t\t\tconst screenWidth = systemInfo.screenWidth || 375\r\n\t\t\t\tconst screenHeight = systemInfo.screenHeight || 667\r\n\t\t\t\t\r\n\t\t\t\t// 调整删除按钮位置，确保不超出屏幕\r\n\t\t\t\tconst buttonWidth = 80   // 删除按钮宽度（大约等于padding + 图标 + 文字）\r\n\t\t\t\tconst buttonHeight = 50  // 删除按钮高度\r\n\t\t\t\t\r\n\t\t\t\t// 计算最终位置（在触摸点上方偏左显示）\r\n\t\t\t\tlet finalX = touchX - buttonWidth / 2  // 水平居中在触摸点\r\n\t\t\t\tlet finalY = touchY - buttonHeight - 10  // 在触摸点上方10px\r\n\t\t\t\t\r\n\t\t\t\t// 边界检查\r\n\t\t\t\tif (finalX < 10) {\r\n\t\t\t\t\tfinalX = 10  // 距离左边界至少10px\r\n\t\t\t\t}\r\n\t\t\t\tif (finalX + buttonWidth > screenWidth - 10) {\r\n\t\t\t\t\tfinalX = screenWidth - buttonWidth - 10  // 距离右边界至少10px\r\n\t\t\t\t}\r\n\t\t\t\tif (finalY < 10) {\r\n\t\t\t\t\tfinalY = touchY + 10  // 如果上方空间不够，显示在下方\r\n\t\t\t\t}\r\n\t\t\t\tif (finalY + buttonHeight > screenHeight - 50) {\r\n\t\t\t\t\tfinalY = screenHeight - buttonHeight - 50  // 留出底部空间\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.deleteButtonPosition = {\r\n\t\t\t\t\tx: finalX,\r\n\t\t\t\t\ty: finalY\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.selectedComment = comment\r\n\t\t\t\tthis.showDeleteBtn = true\r\n\t\t\t\t\r\n\t\t\t\t// 3秒后自动隐藏删除按钮\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.hideDeleteButton()\r\n\t\t\t\t}, 3000)\r\n\t\t\t\t\r\n\t\t\t\t// 震动反馈\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.vibrateShort()\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// 震动失败不影响功能\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理触摸开始\r\n\t\t\thandleTouchStart(event) {\r\n\t\t\t\t// 不要立即隐藏删除按钮，让长按事件有机会触发\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理触摸结束\r\n\t\t\thandleTouchEnd() {\r\n\t\t\t\t// 可以在这里添加其他逻辑\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 隐藏删除按钮\r\n\t\t\thideDeleteButton() {\r\n\t\t\t\tthis.showDeleteBtn = false\r\n\t\t\t\tthis.selectedComment = null\r\n\t\t\t},\r\n\t\t\t\r\n\r\n\t\t\t\r\n\t\t\t// 显示删除评论确认弹窗\r\n\t\t\tshowDeleteCommentConfirm(comment) {\r\n\t\t\t\tthis.commentToDelete = comment\r\n\t\t\t\tthis.hideDeleteButton() // 隐藏悬浮按钮\r\n\t\t\t\tthis.$refs.deleteCommentPopup.open()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认删除评论\r\n\t\t\tasync confirmDeleteComment() {\r\n\t\t\t\tif (!this.commentToDelete) return\r\n\r\n\t\t\t\tlet loadingShown = false\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.showLoading({ title: '删除中...' })\r\n\t\t\t\t\tloadingShown = true\r\n\r\n\t\t\t\t\tawait this.commentStore.deleteComment(this.wishId, this.commentToDelete._id)\r\n\r\n\t\t\t\t\t// 立即强制刷新评论列表，确保数据同步\r\n\t\t\t\t\tawait this.loadComments(true)\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('删除评论失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: error.message || '删除失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tif (loadingShown) {\r\n\t\t\t\t\t\tuni.hideLoading().catch(() => {})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.commentToDelete = null\r\n\t\t\t\t\tthis.$refs.deleteCommentPopup.close()\r\n\t\t\t\t\t// 额外清理，确保没有残留的加载状态\r\n\t\t\t\t\tthis.clearAllLoadingStates()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 关闭删除评论确认弹窗\r\n\t\t\tcloseDeleteCommentConfirm() {\r\n\t\t\t\tthis.commentToDelete = null\r\n\t\t\t\tthis.$refs.deleteCommentPopup.close()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除评论（原有方法保留，可能其他地方调用）\r\n\t\t\tasync deleteComment(commentId) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.commentStore.deleteComment(this.wishId, commentId)\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 立即强制刷新评论列表，确保数据同步\r\n\t\t\t\t\tawait this.loadComments(true)\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('删除评论失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 预览图片\r\n\t\t\tpreviewImages(images, current = 0) {\r\n\t\t\t\tif (!images || images.length === 0) return\r\n\t\t\t\t\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\tcurrent: current,\r\n\t\t\t\t\turls: images\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化日期\r\n\t\t\tformatDate(dateStr) {\r\n\t\t\t\tif (!dateStr) return ''\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst date = new Date(dateStr)\r\n\t\t\t\t\tif (isNaN(date.getTime())) return dateStr\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst now = new Date()\r\n\t\t\t\t\tconst diff = now - date\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 小于1分钟\r\n\t\t\t\t\tif (diff < 60000) {\r\n\t\t\t\t\t\treturn '刚刚'\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 小于1小时\r\n\t\t\t\t\tif (diff < 3600000) {\r\n\t\t\t\t\t\treturn `${Math.floor(diff / 60000)}分钟前`\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 小于1天\r\n\t\t\t\t\tif (diff < 86400000) {\r\n\t\t\t\t\t\treturn `${Math.floor(diff / 3600000)}小时前`\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 小于7天\r\n\t\t\t\t\tif (diff < 604800000) {\r\n\t\t\t\t\t\treturn `${Math.floor(diff / 86400000)}天前`\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 超过7天显示具体日期\r\n\t\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('日期格式化错误:', error)\r\n\t\t\t\t\treturn dateStr\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 编辑心愿\r\n\t\t\teditWish() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/subpkg-wish/pages/editWish/editWish?id=${this.wishId}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 完成心愿\r\n\t\t\tasync completeWish() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.wishStore.completeWish(this.wishId)\r\n\t\t\t\t\tthis.wish.isCompleted = true\r\n\t\t\t\t\tthis.wish.completeDate = new Date().toISOString()\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '心愿已完成',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('完成心愿失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '操作失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 恢复心愿\r\n\t\t\tasync restoreWish() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.wishStore.restoreWish(this.wishId)\r\n\t\t\t\t\tthis.wish.isCompleted = false\r\n\t\t\t\t\tthis.wish.completeDate = null\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '心愿已恢复',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('恢复心愿失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '操作失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取分组名称（优化版本，使用缓存）\r\n\t\t\tgetGroupName(groupId) {\r\n\t\t\t\tif (!groupId) return '未知分组';\r\n\r\n\t\t\t\t// 使用缓存的分组名称映射\r\n\t\t\t\tconst name = this.groupNameMap[groupId];\r\n\t\t\t\treturn name || '未知分组';\r\n\t\t\t},\r\n\r\n\t\t\t// 清理所有加载状态\r\n\t\t\tclearAllLoadingStates() {\r\n\t\t\t\t// 使用统一加载管理器强制清理所有加载状态\r\n\t\t\t\tloadingManager.forceHideAll()\r\n\r\n\t\t\t\t// 重置组件内部的加载状态\r\n\t\t\t\tthis.isLoadingComments = false\r\n\t\t\t\tthis.isPageLoading = false\r\n\r\n\t\t\t\tconsole.log('[wishDetail] 已清理所有加载状态')\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化时间\r\n\t\t\tformatTime(timeString) {\r\n\t\t\t\tif (!timeString) return '无';\r\n\t\t\t\t\r\n\t\t\t\t// 如果原始字符串中已经包含\"(已还原)\"，移除它再处理\r\n\t\t\t\tlet cleanTimeString = timeString;\r\n\t\t\t\tif (typeof cleanTimeString === 'string' && cleanTimeString.includes('(已还原)')) {\r\n\t\t\t\t\tcleanTimeString = cleanTimeString.replace('(已还原)', '').trim();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 尝试创建日期对象\r\n\t\t\t\t\tconst date = new Date(cleanTimeString);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 检查日期是否有效\r\n\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\tconsole.error('无效的日期字符串:', cleanTimeString);\r\n\t\t\t\t\t\t// 确保返回的字符串不包含\"(已还原)\"\r\n\t\t\t\t\t\treturn typeof cleanTimeString === 'string' \r\n\t\t\t\t\t\t\t? cleanTimeString.replace(/\\s*\\(已还原\\)\\s*/g, '') \r\n\t\t\t\t\t\t\t: '无';\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('日期格式化错误:', error);\r\n\t\t\t\t\t// 确保返回的字符串不包含\"(已还原)\"\r\n\t\t\t\t\treturn typeof cleanTimeString === 'string' \r\n\t\t\t\t\t\t? cleanTimeString.replace(/\\s*\\(已还原\\)\\s*/g, '') \r\n\t\t\t\t\t\t: '无';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取完成时间（显示completeDate或lastCompleteDate）\r\n\t\t\tgetCompleteTime() {\r\n\t\t\t\t// 如果是已完成状态，显示完成时间\r\n\t\t\t\tif (this.wish?.isCompleted && this.wish?.completeDate) {\r\n\t\t\t\t\treturn this.formatTime(this.wish.completeDate);\r\n\t\t\t\t} \r\n\t\t\t\t\r\n\t\t\t\t// 检查未完成但曾经完成过的心愿\r\n\t\t\t\t// 1. 心愿未完成\r\n\t\t\t\t// 2. 拥有lastCompleteDate字段(表示曾经被完成过)\r\n\t\t\t\tif (!this.wish?.isCompleted && this.wish?.lastCompleteDate) {\r\n\t\t\t\t\t// 直接返回格式化后的时间，不添加任何标记\r\n\t\t\t\t\treturn this.formatTime(this.wish.lastCompleteDate);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 兼容旧数据：有completeDate但isCompleted为false，且没有lastCompleteDate字段\r\n\t\t\t\tif (!this.wish?.isCompleted && this.wish?.completeDate && !this.wish?.lastCompleteDate) {\r\n\t\t\t\t\t// 直接返回格式化后的时间，不添加任何标记\r\n\t\t\t\t\treturn this.formatTime(this.wish.completeDate);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 其他情况（如：从未完成过的心愿）\r\n\t\t\t\treturn '无';\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 分享心愿\r\n\t\t\tshareWish() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 显示分享菜单\r\n\t\t\t\t\tuni.showShareMenu({\r\n\t\t\t\t\t\twithShareTicket: true,\r\n\t\t\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline'],\r\n\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\tconsole.log('显示分享菜单成功')\r\n\t\t\t\t\t\t\t// 触发振动反馈\r\n\t\t\t\t\t\t\tuni.vibrateShort()\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\t\tconsole.log('显示分享菜单失败', err)\r\n\t\t\t\t\t\t\t// 回退到自定义分享方案\r\n\t\t\t\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\t\t\t\titemList: ['分享给微信好友', '分享到朋友圈'],\r\n\t\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\t\t// 这里只是模拟，实际需要调用分享API\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '分享成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\t\tconsole.error('分享操作失败:', err)\r\n\t\t\t\t\t// 回退到自定义分享方案\r\n\t\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\t\titemList: ['分享给微信好友', '分享到朋友圈'],\r\n\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t// 这里只是模拟，实际需要调用分享API\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '分享成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示删除确认\r\n\t\t\tshowDeleteConfirm() {\r\n\t\t\t\tthis.$refs.deletePopup.open()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 关闭删除确认\r\n\t\t\tcloseDeleteConfirm() {\r\n\t\t\t\tthis.$refs.deletePopup.close()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认删除\r\n\t\t\tconfirmDelete() {\r\n\t\t\t\tif (!this.wishId) return\r\n\t\t\t\t\r\n\t\t\t\tthis.wishStore.deleteWish(this.wishId)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已删除',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}, 1500)\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 导航到分组页面\r\n\t\t\tnavigateToGroup(groupId) {\r\n\t\t\t\t// 设置当前分组\r\n\t\t\t\tthis.wishStore.setCurrentGroup(groupId)\r\n\t\t\t\t\r\n\t\t\t\t// 返回到首页\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.wish-detail-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\t\r\n\t.card {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t}\r\n\t\r\n\t.section-title {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tpadding-bottom: 20rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.comment-count {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tfont-weight: normal;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.wish-detail {\r\n\t\tpadding: 30rpx;\r\n\t\t\r\n\t\t.wish-header {\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.wish-title-wrap {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t.wish-title {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.wish-status {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t\r\n\t\t\t\t.tag {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tpadding: 6rpx 16rpx;\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.uni-icons {\r\n\t\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.private-tag {\r\n\t\t\t\t\t\tbackground-color: #ff9800;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.friends-tag {\r\n\t\t\t\t\t\tbackground-color: #2196f3;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.public-tag {\r\n\t\t\t\t\t\tbackground-color: #4caf50;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.completed-tag {\r\n\t\t\t\t\t\tbackground-color: #409eff;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.wish-content {\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\t\r\n\t\t\t.wish-desc {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tline-height: 1.6;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.image-swiper {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 500rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.swiper-image {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tobject-fit: cover;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.wish-image {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tmax-height: 500rpx;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.wish-time-info {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t.time-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.time-label {\r\n\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.time-value {\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.wish-actions {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tborder-top: 1rpx solid #f0f0f0;\r\n\t\t\t\r\n\t\t\t.action-btn, .share-btn {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tbackground: none;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.delete {\r\n\t\t\t\t\tcolor: #f56c6c;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.wish-groups {\r\n\t\tpadding: 30rpx;\r\n\t\t\r\n\t\t.group-tags {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\t\r\n\t\t\t.group-tag {\r\n\t\t\t\tpadding: 6rpx 16rpx;\r\n\t\t\t\tbackground-color: #f0e6ff;\r\n\t\t\t\tcolor: #8a2be2;\r\n\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.wish-comments {\r\n\t\tpadding: 30rpx;\r\n\t\t\r\n\t\t.empty-comment {\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #999;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tpadding: 40rpx 0;\r\n\t\t}\r\n\t\t\r\n\t\t.comment-list {\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.comment-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.comment-avatar {\r\n\t\t\t\t\twidth: 80rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.comment-content {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.comment-user {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.comment-nickname {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.comment-time {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.comment-text {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tline-height: 1.5;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.comment-image {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tmax-width: 450rpx;\r\n\t\t\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 悬浮删除按钮\r\n\t\t.floating-delete-btn {\r\n\t\t\tposition: fixed;\r\n\t\t\tz-index: 1000;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbackground-color: #f56c6c;\r\n\t\t\tcolor: #fff;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tpadding: 8rpx 16rpx;\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.4);\r\n\t\t\tanimation: fadeInScale 0.2s ease-out;\r\n\t\t\twhite-space: nowrap;\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tmargin-left: 6rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.9);\r\n\t\t\t\tbackground-color: #e85a5a;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t@keyframes fadeInScale {\r\n\t\t\t0% {\r\n\t\t\t\topacity: 0;\r\n\t\t\t\ttransform: scale(0.5);\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\topacity: 1;\r\n\t\t\t\ttransform: scale(1);\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.comment-form {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tbackground-color: #f8f8f8;\r\n\t\t\tborder-radius: 35rpx;\r\n\t\t\t\r\n\t\t\t.comment-input {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.comment-btn {\r\n\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\r\n\r\n\t}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/subpkg-wish/pages/wishDetail/wishDetail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useGroupStore", "useCommentStore", "uni", "loadingManager"], "mappings": ";;;;;;;;;;;;;AA4MG,WAAA;AAAA;;;IAKA;AAAA;;EAGD,kBAAA;;AAGC,WAAA;AAAA;;;IAKA;AAAA;EAED,QAAA;;AAEC,UAAA,aAAAA,YAAAA,cAAA;AACA,UAAA,eAAAC,cAAAA,gBAAA;;AAGA,WAAA;AAAA,MACC;AAAA;;MAGA;AAAA,IACD;AAAA;EAED,OAAA;AACC,WAAA;AAAA,MACC,QAAA;AAAA,MACA,MAAA;AAAA,MACA,aAAA;AAAA;;MAGA,eAAA,CAAA;AAAA;;;;;;MAKA,eAAA;AAAA;AAAA;;MAEA,sBAAA,EAAA,GAAA,GAAA,GAAA,EAAA;AAAA;AAAA;;IAED;AAAA;EAED,UAAA;AAAA;AAAA,IAEC,eAAA;;AAEE,eAAA;MACD;;AAGA,WAAA,WAAA,aAAA,QAAA,WAAA;AACC,YAAA,MAAA,OAAA,MAAA,EAAA,IAAA,MAAA;AAAA,MACD,CAAA;AACA,aAAA;AAAA;;;AAKA,UAAA,CAAA,KAAA;AAAA,eAAA;AACA,aAAA,KAAA,KAAA,cAAA,QAAA;AAAA;;IAID,iBAAA;AACC,UAAA,CAAA,KAAA;AAAA,eAAA;AACA,YAAA,gBAAA;AAAA,QACC,WAAA;AAAA,QACA,WAAA;AAAA;MAED;;;;IAKD,oBAAA;AACC,aAAA;AAAA,QACC,MAAA,KAAA,qBAAA,IAAA;AAAA,QACA,KAAA,KAAA,qBAAA,IAAA;AAAA,MACD;AAAA;;EAKF,MAAA,OAAA,SAAA;AACC,QAAA,QAAA,IAAA;AACC,WAAA,SAAA,QAAA;;;AAMA,UAAA;AACCC,sBAAAA,MAAA,MAAA,OAAA,sDAAA,yBAAA;AAGA,cAAA,cAAA,WAAA,MAAA;;;;;QAKA,GAAA,IAAA;AAEA,cAAA,KAAA,WAAA;AACA,aAAA,eAAA;;;AAMAA,sBAAAA,MAAA,MAAA,OAAA,sDAAA,sBAAA;AAAA;AAEAA,sBAAAA,MAAA,MAAA,SAAA,sDAAA,YAAA,KAAA;;MAED,UAAA;;;MAGA;AAAA,IACD;AAAA;;EAKD,SAAA;;;IAIC;;AAMA,SAAA,UAAA;AAGA,QAAA,KAAA,QAAA;AACC,WAAA,eAAA;;IAID;AAAA;;EAID,SAAA;;;EAGA,SAAA;AAAA;AAAA,IAEC,MAAA,aAAA;;AAEC,WAAA,aAAAF,0BAAA;AACA,WAAA,eAAAC,8BAAA;;AAIA,UAAA;AACC,cAAA,iBAAA,IAAA,QAAA,CAAA,GAAA,WAAA;;;AAIA,cAAA,QAAA,KAAA,CAAA,KAAA,WAAA,WAAA,GAAA,cAAA,CAAA;AAAA;AAEAC,sBAAAA,MAAA,MAAA,SAAA,sDAAA,iBAAA,KAAA;AAEA,aAAA,WAAA,oBAAA;AAAA,MACD;AAGA,YAAA,KAAA,4BAAA;;;;IAOD,MAAA,8BAAA;;;;AAKC,UAAA;;;AAKC,YAAA,iBAAA,kBAAA;AACCA,wBAAAA,MAAA,MAAA,OAAA,sDAAA,wBAAA;AAAA;YAEC;AAAA,UACD,CAAA;AAGA,gBAAA,eAAA,CAAA;AAEA,cAAA,eAAA;AACCA,0BAAAA,MAAA,MAAA,OAAA,sDAAA,uBAAA;AACA,yBAAA,KAAA,KAAA,UAAA,cAAA,EAAA,QAAA,KAAA,CAAA,CAAA;AAAA,UACD;AAEA,cAAA,kBAAA;AACCA,0BAAAA,MAAA,MAAA,OAAA,sDAAA,uBAAA;AACA,yBAAA,KAAA,KAAA,aAAA,oBAAA,KAAA,QAAA,MAAA,IAAA,CAAA;AAAA,UACD;AAGA,gBAAA,QAAA,WAAA,YAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,sDAAA,qBAAA;AAAA;;QAGD;AAAA;;MAGD;AAAA;;;AAKA,UAAA,CAAA,KAAA,aAAA,CAAA,KAAA;AAAA,eAAA;;AAGA,YAAA,eAAA,KAAA,UAAA;AACA,YAAA,MAAA,KAAA;AAGA,aAAA,CAAA,gBAAA,CAAA,gBAAA,MAAA,eAAA,IAAA,KAAA;AAAA;;;;;AAOA,YAAA,mBAAA,KAAA,aAAA,eAAA,KAAA,MAAA,KACM,KAAA,aAAA,eAAA,KAAA,MAAA,EAAA,UAAA;AACN,YAAA,eAAA,KAAA,aAAA,eAAA,KAAA,MAAA;AACA,YAAA,MAAA,KAAA;AAGA,aAAA,CAAA,oBAAA,CAAA,gBAAA,MAAA,eAAA,IAAA,KAAA;AAAA;;IAMD,sBAAA;AAEC,WAAA,aAAA,MAAA,KAAA,aAAA,GAAA,kBAAA,MAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,sDAAA,UAAA;AAEA,YAAA,KAAA,QAAA;AACC,eAAA,aAAA;AAAA,QACD;AAAA;;;IAOF,iBAAA;;AAGC,UAAA,CAAA,KAAA,MAAA;AACCA,sBAAAA,MAAA,UAAA;AAAA;;;AAIA,mBAAA,MAAA;AACCA,wBAAAA,MAAA,aAAA;AAAA,QACD,GAAA,IAAA;AAAA,MACD;AAAA;;IAID,MAAA,aAAA,UAAA,OAAA;AACC,UAAA,CAAA,KAAA;AAAA;;AAIA,UAAA;AAEC,aAAA,WAAA,MAAAC,qBAAAA,eAAA;AAAA;UAEC;AAAA,YACC,OAAA;AAAA,YACA,IAAA,iBAAA,KAAA,MAAA;AAAA;;;;;UAID;AAAA,QACD;AAAA;AAEAD,sBAAAA,MAAA,MAAA,SAAA,sDAAA,WAAA,KAAA;AAEA,aAAA,WAAA,KAAA,aAAA,eAAA,KAAA,MAAA,KAAA,CAAA;AAAA,MACD,UAAA;;MAEA;AAAA;;IAID,mBAAA;AAEC,UAAA,CAAA,KAAA,UAAA,yBAAA;;MAEA;;;;IAMD,MAAA,gBAAA;;AAEEA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;;;MAIF;AAEA,UAAA,eAAA;AAEA,UAAA;AACCA,sBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AACA,uBAAA;AAEA,cAAA,cAAA;AAAA,UACC,SAAA,KAAA,YAAA,KAAA;AAAA,UACA,OAAA,CAAA;AAAA,QACD;;;AAQA,aAAA,cAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;;;;AAKAA,sBAAAA,MAAA,MAAA,SAAA,sDAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;;;MAID,UAAA;AACC,YAAA,cAAA;AACCA,wBAAAA,MAAA,YAAA,EAAA,MAAA,MAAA;AAAA,UAAA,CAAA;AAAA,QACD;;MAGD;AAAA;;;;;;AAOA,YAAA,gBAAA,KAAA,UAAA,YAAA,UAAA,UAAA,aAAA,mBAAA;;;AAIA,aAAA,mBAAA;AAAA;;IAID,uBAAA,SAAA,OAAA;AACC,UAAA,CAAA,KAAA,iBAAA,OAAA,GAAA;AACCA,sBAAAA,MAAA,UAAA;AAAA;;;;MAKD;;;AAOA,UAAA,SAAA,MAAA,WAAA,MAAA,OAAA,MAAA,UAAA,MAAA,OAAA,MAAA,SAAA;;;;;AAOC,iBAAA,MAAA,WAAA,MAAA,SAAA;AACA,iBAAA,MAAA,WAAA,MAAA,SAAA;AAAA,MACD,WAAA,SAAA,MAAA,kBAAA,MAAA,eAAA,SAAA,GAAA;AAEC,cAAA,QAAA,MAAA,eAAA,CAAA;AACA,iBAAA,MAAA,WAAA,MAAA,SAAA;AACA,iBAAA,MAAA,WAAA,MAAA,SAAA;AAAA,MACD;AAGA,YAAA,aAAAA,cAAA,MAAA,kBAAA;AACA,YAAA,cAAA,WAAA,eAAA;AACA,YAAA,eAAA,WAAA,gBAAA;AAGA,YAAA,cAAA;;AAIA,UAAA,SAAA,SAAA,cAAA;AACA,UAAA,SAAA,SAAA,eAAA;;AAIC,iBAAA;AAAA,MACD;AACA,UAAA,SAAA,cAAA,cAAA,IAAA;AACC,iBAAA,cAAA,cAAA;AAAA,MACD;;AAEC,iBAAA,SAAA;AAAA,MACD;AACA,UAAA,SAAA,eAAA,eAAA,IAAA;AACC,iBAAA,eAAA,eAAA;AAAA,MACD;;QAGC,GAAA;AAAA,QACA,GAAA;AAAA,MACD;;;AAMA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAGA,UAAA;AACCA,sBAAAA,MAAA,aAAA;AAAA;MAGD;AAAA;;;;;IASD,iBAAA;AAAA;;IAKA,mBAAA;;;;;IAQA,yBAAA,SAAA;;AAEC,WAAA,iBAAA;AACA,WAAA,MAAA,mBAAA,KAAA;AAAA;;;AAKA,UAAA,CAAA,KAAA;AAAA;AAEA,UAAA,eAAA;AAEA,UAAA;AACCA,sBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AACA,uBAAA;AAEA,cAAA,KAAA,aAAA,cAAA,KAAA,QAAA,KAAA,gBAAA,GAAA;;AAKAA,sBAAAA,MAAA,UAAA;AAAA;;;;AAKAA,sBAAAA,MAAA,MAAA,SAAA,sDAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;;;MAID,UAAA;AACC,YAAA,cAAA;AACCA,wBAAAA,MAAA,YAAA,EAAA,MAAA,MAAA;AAAA,UAAA,CAAA;AAAA,QACD;;AAEA,aAAA,MAAA,mBAAA,MAAA;;MAGD;AAAA;;;;AAMA,WAAA,MAAA,mBAAA,MAAA;AAAA;;;AAKA,UAAA;;;AAMCA,sBAAAA,MAAA,UAAA;AAAA;;;;AAKAA,sBAAAA,MAAA,MAAA,SAAA,sDAAA,WAAA,KAAA;AAAA,MACD;AAAA;;IAID,cAAA,QAAA,UAAA,GAAA;AACC,UAAA,CAAA,UAAA,OAAA,WAAA;AAAA;AAEAA,oBAAAA,MAAA,aAAA;AAAA,QACC;AAAA;;;;IAMF,WAAA,SAAA;AACC,UAAA,CAAA;AAAA,eAAA;AAEA,UAAA;;AAEC,YAAA,MAAA,KAAA,QAAA,CAAA;AAAA,iBAAA;AAEA,cAAA,MAAA,oBAAA,KAAA;AACA,cAAA,OAAA,MAAA;AAGA,YAAA,OAAA,KAAA;AACC,iBAAA;AAAA,QACD;AAEA,YAAA,OAAA,MAAA;AACC,iBAAA,GAAA,KAAA,MAAA,OAAA,GAAA,CAAA;AAAA,QACD;AAEA,YAAA,OAAA,OAAA;AACC,iBAAA,GAAA,KAAA,MAAA,OAAA,IAAA,CAAA;AAAA,QACD;AAEA,YAAA,OAAA,QAAA;AACC,iBAAA,GAAA,KAAA,MAAA,OAAA,KAAA,CAAA;AAAA,QACD;;;AAKAA,sBAAAA,MAAA,MAAA,SAAA,sDAAA,YAAA,KAAA;;MAED;AAAA;;;AAKAA,oBAAAA,MAAA,WAAA;AAAA;;;;IAMD,MAAA,eAAA;AACC,UAAA;AACC,cAAA,KAAA,UAAA,aAAA,KAAA,MAAA;;AAEA,aAAA,KAAA,gBAAA,oBAAA,KAAA,GAAA,YAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;;;;AAKAA,sBAAAA,MAAA,MAAA,SAAA,sDAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;;;MAID;AAAA;;IAID,MAAA,cAAA;AACC,UAAA;AACC,cAAA,KAAA,UAAA,YAAA,KAAA,MAAA;;;AAIAA,sBAAAA,MAAA,UAAA;AAAA;;;;AAKAA,sBAAAA,MAAA,MAAA,SAAA,sDAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;;;MAID;AAAA;;IAID,aAAA,SAAA;;;AAIC,YAAA,OAAA,KAAA,aAAA,OAAA;AACA,aAAA,QAAA;AAAA;;;;;;AAYAA,oBAAAA,MAAA,MAAA,OAAA,sDAAA,wBAAA;AAAA;;;;;;AASA,UAAA,OAAA,oBAAA,YAAA,gBAAA,SAAA,OAAA,GAAA;;MAEA;AAEA,UAAA;AAEC,cAAA,OAAA,IAAA,KAAA,eAAA;;;AAMC,iBAAA,OAAA,oBAAA,WACC,gBAAA,QAAA,kBAAA,EAAA;QAEF;;;AAIAA,sBAAA,MAAA,MAAA,SAAA,sDAAA,YAAA,KAAA;AAEA,eAAA,OAAA,oBAAA,WACC,gBAAA,QAAA,kBAAA,EAAA;MAEF;AAAA;;IAID,kBAAA;;;AAGE,eAAA,KAAA,WAAA,KAAA,KAAA,YAAA;AAAA,MACD;;;MAQA;AAGA,UAAA,GAAA,UAAA,SAAA,mBAAA,kBAAA,UAAA,SAAA,mBAAA,iBAAA,GAAA,UAAA,SAAA,mBAAA,mBAAA;AAEC,eAAA,KAAA,WAAA,KAAA,KAAA,YAAA;AAAA,MACD;AAGA,aAAA;AAAA;;;AAKA,UAAA;AAECA,sBAAAA,MAAA,cAAA;AAAA,UACC,iBAAA;AAAA,UACA,OAAA,CAAA,mBAAA,eAAA;AAAA,UACA,UAAA;AACCA,0BAAAA,MAAA,MAAA,OAAA,sDAAA,UAAA;AAEAA,0BAAAA,MAAA,aAAA;AAAA;UAED,KAAA,KAAA;AACCA,0BAAAA,MAAA,MAAA,OAAA,sDAAA,YAAA,GAAA;AAEAA,0BAAAA,MAAA,gBAAA;AAAA;;AAIEA,8BAAAA,MAAA,UAAA;AAAA;;;cAID;AAAA;UAEF;AAAA;;AAGDA,sBAAAA,MAAA,MAAA,SAAA,sDAAA,WAAA,GAAA;AAEAA,sBAAAA,MAAA,gBAAA;AAAA;;AAIEA,0BAAAA,MAAA,UAAA;AAAA;;;UAID;AAAA;MAEF;AAAA;;IAID,oBAAA;;;;IAKA,qBAAA;;;;IAKA,gBAAA;AACC,UAAA,CAAA,KAAA;AAAA;AAEA,WAAA,UAAA,WAAA,KAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA;;;AAKA,iBAAA,MAAA;AACCA,sBAAAA,MAAA,aAAA;AAAA,MACD,GAAA,IAAA;AAAA;;;AAMA,WAAA,UAAA,gBAAA,OAAA;AAGAA,oBAAAA,MAAA,UAAA;AAAA;;;EAMF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACl+BD,GAAG,WAAW,eAAe;"}
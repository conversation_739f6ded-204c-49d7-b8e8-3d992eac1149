<template>
	<view class="address-container">
		<view class="address-list">
			<!-- 空状态 -->
			<view v-if="addressList.length === 0 && !loading" class="empty-address">
				<text class="empty-text">暂无地址，请添加</text>
				<view class="add-btn" @click="addNewAddress">
					<uni-icons type="plusempty" size="20" color="#fff"></uni-icons>
					<text>新增地址</text>
				</view>
			</view>
			
			<!-- 地址列表 -->
			<view v-else>
				<view class="address-item" v-for="(address, index) in addressList" :key="address._id">
					<view class="address-info">
						<view class="address-top">
							<text class="name">{{ address.name }}</text>
							<text class="phone">{{ address.phone }}</text>
							<view v-if="address.isDefault" class="default-tag">默认</view>
						</view>
						<view class="address-detail">{{ address.province }}{{ address.city }}{{ address.district }}{{ address.address }}</view>
					</view>
					<view class="address-actions">
						<view class="action-buttons">
							<view class="action-btn edit" @click="editAddress(address, index)">
								<uni-icons type="compose" size="16" color="#8a2be2"></uni-icons>
								<text>编辑</text>
							</view>
							<view class="action-btn delete" @click="deleteAddress(address._id, index)">
								<uni-icons type="trash" size="16" color="#f56c6c"></uni-icons>
								<text>删除</text>
							</view>
						</view>
						<view class="default-switch">
							<text class="switch-label">默认地址</text>
							<switch 
								:checked="address.isDefault" 
								@change="toggleDefaultAddress(address._id, index, $event)"
								color="#ff9500"
								style="transform:scale(0.8)"
							/>
						</view>
					</view>
				</view>
				
				<!-- 底部添加按钮 -->
				<view class="bottom-add-btn" @click="addNewAddress">
					<uni-icons type="plusempty" size="20" color="#fff"></uni-icons>
					<text>新增地址</text>
				</view>
			</view>
		</view>
		

	</view>
</template>

<script>
import { useAddressStore } from '../../store/address.js'
import { useUserStore } from '@/store/user.js'

export default {
	data() {
		return {
			addressList: [],
			loading: false,
			addressStore: null,
			userStore: null
		}
	},

	onLoad() {
		this.initStore()

		// 检查登录状态，如果未登录则由导航拦截器处理跳转
		if (!this.userStore.isLogin) {
			console.log('[addressManage] 用户未登录，等待导航拦截器处理')
			return
		}

		this.getAddressList();
	},
	
	onShow() {
		// 页面显示时检查登录状态，如果已登录则检查并同步地址数据
		if (this.userStore && this.userStore.isLogin) {
			this.checkAndSyncAddressData();
		}
	},

	methods: {
		// 初始化Store
		initStore() {
			this.addressStore = useAddressStore()
			this.userStore = useUserStore()
		},
		
		// 检查并同步地址数据
		async checkAndSyncAddressData() {
			if (this.loading) return;

			console.log('[AddressManage] 检查地址数据同步状态...');

			// 检查本地是否有缓存数据
			const hasLocalData = this.addressStore && this.addressStore.addressList.length > 0;
			const lastSyncTime = this.addressStore ? this.addressStore.lastSyncTime : null;
			const now = Date.now();

			// 如果没有本地数据，或者超过5分钟未同步，则从云端获取
			const shouldSync = !hasLocalData || !lastSyncTime || (now - lastSyncTime > 5 * 60 * 1000);

			if (shouldSync) {
				console.log('[AddressManage] 需要同步地址数据，原因:',
					!hasLocalData ? '无本地数据' :
					!lastSyncTime ? '未记录同步时间' : '超过5分钟未同步');
				await this.getAddressList();
			} else {
				console.log('[AddressManage] 使用本地缓存数据');
				this.addressList = this.addressStore.addressList;
			}
		},

		// 获取地址列表
		async getAddressList() {
			if (this.loading) return;

			this.loading = true;
			try {
				const result = await uniCloud.importObject('address-center').getAddressList();

				if (result.code === 0) {
					this.addressList = result.data.list;
					// 同步到Store并记录同步时间
					if (this.addressStore) {
						this.addressStore.setAddressList(result.data.list);
						this.addressStore.lastSyncTime = Date.now();
					}
					console.log('[AddressManage] 地址数据同步完成，共', result.data.list.length, '条');
				} else {
					uni.showToast({
						title: result.message || '获取地址列表失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取地址列表失败:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 添加新地址
		addNewAddress() {
			uni.navigateTo({
				url: '/subpkg-profile/pages/addressEdit/addressEdit'
			});
		},

		// 编辑地址
		editAddress(address, index) {
			uni.navigateTo({
				url: `/subpkg-profile/pages/addressEdit/addressEdit?id=${address._id}`
			});
		},
		
		// 切换默认地址状态
		async toggleDefaultAddress(addressId, index, event) {
			if (!addressId) {
				uni.showToast({
					title: '地址ID无效',
					icon: 'none'
				});
				return;
			}
			
			const isChecked = event.detail.value;
			
			try {
				uni.showLoading({ title: isChecked ? '设置中...' : '取消中...' });
				
				let result;
				if (isChecked) {
					// 使用Store设置默认地址
					if (this.addressStore) {
						result = await this.addressStore.setDefaultAddress(addressId);
					} else {
						// fallback to direct call
					result = await uniCloud.importObject('address-center').setDefaultAddress(addressId);
					}
				} else {
					// 取消默认地址
					result = await uniCloud.importObject('address-center').updateAddress(addressId, {
						is_default: false
					});
					
					// 更新Store中的状态
					if (this.addressStore && result.code === 0) {
						this.addressStore._clearDefaultAddress();
					}
				}
				
				if (result.code === 0) {
					uni.showToast({
						title: isChecked ? '设置成功' : '已取消默认',
						icon: 'success'
					});
					// 刷新地址列表
					setTimeout(() => {
						this.getAddressList();
					}, 800);
				} else {
					uni.showToast({
						title: result.message || '操作失败',
						icon: 'none',
						duration: 3000
					});
					// 如果操作失败，刷新列表恢复状态
					this.getAddressList();
				}
			} catch (error) {
				console.error('操作异常:', error);
				uni.showToast({
					title: '操作失败: ' + (error.message || error),
					icon: 'none',
					duration: 3000
				});
				// 如果操作失败，刷新列表恢复状态
				this.getAddressList();
			} finally {
				uni.hideLoading();
			}
		},
		
		// 删除地址
		async deleteAddress(addressId, index) {
			const confirmResult = await new Promise((resolve) => {
				uni.showModal({
					title: '删除地址',
					content: '确定要删除这个地址吗？',
					success: (res) => resolve(res.confirm)
				});
			});
			
			if (!confirmResult) return;
			
			try {
				uni.showLoading({ title: '删除中...' });
				
				const result = await uniCloud.importObject('address-center').deleteAddress(addressId);
				
				if (result.code === 0) {
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
					this.getAddressList();
				} else {
					uni.showToast({
						title: result.message || '删除失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('删除地址失败:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				uni.hideLoading().catch(() => {})
			}
		}
	}
}
</script>

<style lang="scss">
.address-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 20rpx;
}

.empty-address {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 0;
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 30rpx;
	}
	
	.add-btn {
		display: flex;
		align-items: center;
		background-color: #8a2be2;
		color: #fff;
		padding: 16rpx 30rpx;
		border-radius: 30rpx;
		font-size: 28rpx;
		margin-top: 30rpx;
		
		.uni-icons {
			margin-right: 8rpx;
		}
		
		text {
			color: #fff;
		}
	}
}

.address-list {
	.address-item {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.address-info {
			margin-bottom: 20rpx;
			
			.address-top {
				display: flex;
				align-items: center;
				margin-bottom: 10rpx;
				
				.name {
					font-size: 32rpx;
					font-weight: 500;
					margin-right: 20rpx;
				}
				
				.phone {
					font-size: 28rpx;
					color: #666;
				}
				
				.default-tag {
					margin-left: auto;
					background-color: #8a2be2;
					color: #fff;
					font-size: 22rpx;
					padding: 4rpx 12rpx;
					border-radius: 20rpx;
				}
			}
			
			.address-detail {
				font-size: 28rpx;
				color: #333;
				line-height: 1.5;
			}
		}
		
		.address-actions {
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-top: 1rpx solid #f0f0f0;
			padding-top: 20rpx;
			
			.action-buttons {
				display: flex;
				
				.action-btn {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					margin-left: 30rpx;
					
					.uni-icons {
						margin-right: 4rpx;
					}
					
					&.edit {
						color: #8a2be2;
					}
					
					&.delete {
						color: #f56c6c;
					}
				}
			}
			
			.default-switch {
				display: flex;
				align-items: center;
				
				.switch-label {
					font-size: 24rpx;
					color: #666;
					margin-right: 16rpx;
				}
			}
		}
	}
}

.bottom-add-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #8a2be2;
	color: #fff;
	padding: 24rpx 0;
	border-radius: 12rpx;
	font-size: 30rpx;
	margin-top: 30rpx;
	
	.uni-icons {
		margin-right: 10rpx;
	}
	
	text {
		color: #fff;
	}
}
</style> 
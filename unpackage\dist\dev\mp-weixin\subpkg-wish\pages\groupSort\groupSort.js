"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_group = require("../../../store/group.js");
const utils_gestureManager = require("../../../utils/gestureManager.js");
const _sfc_main = {
  data() {
    return {
      groups: [],
      originalGroups: [],
      activeIndex: -1,
      startY: 0,
      currentY: 0,
      moving: false,
      allGroup: null,
      touchTimer: null,
      catchtouchmove: false,
      // 控制是否阻止页面滚动
      hasMoved: false,
      // 是否已进行排序操作
      saveTimeout: null
      // A用于延迟保存的计时器
    };
  },
  computed: {
    // 获取除"全部"外的可排序标签
    sortableGroups() {
      return this.groups.filter((group) => group.id !== "all").sort((a, b) => a.order - b.order);
    }
  },
  onLoad() {
    this.initGroups();
    common_vendor.index.onNavigationBarButtonTap((e) => {
      if (e.index === 0) {
        this.returnToPrevious();
      }
    });
  },
  onNavigationBarButtonTap(e) {
    if (e.index === 0) {
      this.returnToPrevious();
    }
  },
  onUnload() {
    if (this.touchTimer) {
      clearTimeout(this.touchTimer);
      this.touchTimer = null;
    }
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }
  },
  methods: {
    // 初始化分组数据
    initGroups() {
      const groupStore = store_group.useGroupStore();
      const allGroups = groupStore.getAllGroups;
      this.allGroup = allGroups.find((group) => group.id === "all");
      this.groups = allGroups.filter((group) => group.id !== "all").map((group, index) => ({
        ...group,
        order: group.order || index
      }));
      this.originalGroups = JSON.parse(JSON.stringify(this.groups));
      common_vendor.index.__f__("log", "at subpkg-wish/pages/groupSort/groupSort.vue:122", "初始化分组:", this.groups);
      common_vendor.index.__f__("log", "at subpkg-wish/pages/groupSort/groupSort.vue:123", "全部标签:", this.allGroup);
    },
    // 触摸开始
    onTouchStart(event, index) {
      if (!utils_gestureManager.gestureManager.canStartGesture("drag")) {
        common_vendor.index.__f__("log", "at subpkg-wish/pages/groupSort/groupSort.vue:130", "[GroupSort] 其他手势进行中，忽略拖拽开始");
        return;
      }
      this.activeIndex = index;
      this.startY = event.touches[0].clientY;
      this.currentY = this.startY;
      this.moving = false;
      this.touchTimer = setTimeout(() => {
        if (!utils_gestureManager.gestureManager.startDrag({ groupIndex: index })) {
          return;
        }
        this.moving = true;
        this.catchtouchmove = true;
        common_vendor.index.vibrateShort({
          success: function() {
            common_vendor.index.__f__("log", "at subpkg-wish/pages/groupSort/groupSort.vue:153", "振动成功");
          }
        });
      }, 200);
    },
    // 触摸移动
    onTouchMove(event, index) {
      if (this.touchTimer) {
        clearTimeout(this.touchTimer);
        this.touchTimer = null;
      }
      if (!this.moving)
        return;
      event.preventDefault && event.preventDefault();
      event.stopPropagation && event.stopPropagation();
      const currentY = event.touches[0].clientY;
      this.currentY = currentY;
      const moveDistance = currentY - this.startY;
      const itemHeight = 100;
      const moveItems = Math.round(moveDistance / itemHeight);
      let targetIndex = index + moveItems;
      if (targetIndex >= 0 && targetIndex < this.sortableGroups.length && targetIndex !== index) {
        this.swapItems(index, targetIndex);
        this.hasMoved = true;
        this.startY = currentY;
        this.activeIndex = targetIndex;
      }
    },
    // 触摸结束
    onTouchEnd() {
      if (this.touchTimer) {
        clearTimeout(this.touchTimer);
        this.touchTimer = null;
      }
      const wasMoving = this.moving;
      this.moving = false;
      this.activeIndex = -1;
      this.catchtouchmove = false;
      if (wasMoving) {
        utils_gestureManager.gestureManager.endDrag({ hasMoved: this.hasMoved });
      }
      this.updateSortOrder();
      if (this.hasMoved) {
        if (this.saveTimeout) {
          clearTimeout(this.saveTimeout);
        }
        this.saveTimeout = setTimeout(() => {
          this.autoSave();
        }, 1e3);
      }
    },
    // 交换两个项目的位置
    swapItems(fromIndex, toIndex) {
      const newGroups = [...this.groups];
      const fromGroup = this.sortableGroups[fromIndex];
      const toGroup = this.sortableGroups[toIndex];
      const fromGroupIndex = newGroups.findIndex((g) => g.id === fromGroup.id);
      const toGroupIndex = newGroups.findIndex((g) => g.id === toGroup.id);
      const tempOrder = newGroups[fromGroupIndex].order;
      newGroups[fromGroupIndex].order = newGroups[toGroupIndex].order;
      newGroups[toGroupIndex].order = tempOrder;
      this.groups = newGroups;
      common_vendor.index.__f__("log", "at subpkg-wish/pages/groupSort/groupSort.vue:260", "交换位置:", fromIndex, toIndex);
    },
    // 更新排序顺序
    updateSortOrder() {
      this.sortableGroups.forEach((group, idx) => {
        const groupIndex = this.groups.findIndex((g) => g.id === group.id);
        if (groupIndex !== -1) {
          this.groups[groupIndex].order = idx + 1;
        }
      });
      common_vendor.index.__f__("log", "at subpkg-wish/pages/groupSort/groupSort.vue:273", "更新后的顺序:", this.sortableGroups.map((g) => ({ id: g.id, name: g.name, order: g.order })));
    },
    // 自动保存排序结果
    autoSave() {
      const groupStore = store_group.useGroupStore();
      this.groups.forEach((group) => {
        if (group.id !== "all") {
          groupStore.updateGroupOrder(group.id, group.order);
        }
      });
      common_vendor.index.showToast({
        title: "排序已保存",
        icon: "success",
        duration: 1e3
      });
      this.hasMoved = false;
    },
    // 返回上一页方法
    returnToPrevious() {
      if (this.hasMoved) {
        this.autoSave();
      }
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.groups.length
  }, !$data.groups.length ? {} : common_vendor.e({
    b: $data.allGroup
  }, $data.allGroup ? {
    c: common_vendor.t($data.allGroup.name)
  } : {}, {
    d: common_vendor.f($options.sortableGroups, (group, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(group.name.substr(0, 1)),
        b: common_vendor.n(group.id === "gift" ? "gift-icon" : group.id === "friend-visible" ? "friend-icon" : "custom-icon"),
        c: common_vendor.t(group.name),
        d: group.isDefault
      }, group.isDefault ? {} : {}, {
        e: group.id,
        f: group.isDefault ? 1 : "",
        g: $data.activeIndex === index ? 1 : "",
        h: common_vendor.o(($event) => $options.onTouchStart($event, index), group.id),
        i: common_vendor.o(($event) => $options.onTouchMove($event, index), group.id),
        j: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args), group.id)
      });
    })
  }), {
    e: common_vendor.o((...args) => $data.catchtouchmove && $data.catchtouchmove(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpkg-wish/pages/groupSort/groupSort.js.map

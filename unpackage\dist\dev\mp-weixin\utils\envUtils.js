"use strict";
const common_vendor = require("../common/vendor.js");
const devLog = {
  log: (...args) => {
    {
      common_vendor.index.__f__("log", "at utils/envUtils.js:24", ...args);
    }
  },
  warn: (...args) => {
    {
      common_vendor.index.__f__("warn", "at utils/envUtils.js:29", ...args);
    }
  },
  error: (...args) => {
    {
      common_vendor.index.__f__("error", "at utils/envUtils.js:35", ...args);
    }
  },
  info: (...args) => {
    {
      common_vendor.index.__f__("info", "at utils/envUtils.js:43", ...args);
    }
  }
};
const enablePerformanceMonitoring = () => {
  return true;
};
exports.devLog = devLog;
exports.enablePerformanceMonitoring = enablePerformanceMonitoring;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/envUtils.js.map

<view class="{{['wish-card-container', B && 'is-dragging']}}"><view class="swipe-action-wrapper" ref="swipeRef" bindtouchstart="{{x}}" bindtouchmove="{{y}}" bindtouchend="{{z}}" bindtouchcancel="{{A}}"><view class="swipe-content" style="{{t}}"><view class="wish-card" bindtap="{{s}}"><view class="wish-card-header"><view class="wish-card-order">{{a}}</view><view class="wish-card-title text-ellipsis">{{b}}</view><view class="wish-card-status"><view wx:if="{{c}}" class="private-icon"><uni-icons wx:if="{{d}}" u-i="84ba2e96-0" bind:__l="__l" u-p="{{d}}"></uni-icons></view><view wx:if="{{e}}" class="completed-icon"><uni-icons wx:if="{{f}}" u-i="84ba2e96-1" bind:__l="__l" u-p="{{f}}"></uni-icons></view></view><button open-type="share" class="share-btn" catchtap="{{h}}" data-index="{{i}}"><image src="{{g}}" class="share-icon"></image></button></view><view class="wish-card-content"><view wx:if="{{j}}" class="wish-card-desc">{{k}}</view><view wx:if="{{l}}" class="image-container"><image class="wish-card-image" src="{{m}}" mode="aspectFit" binderror="{{n}}" bindload="{{o}}"></image><view wx:if="{{p}}" class="multi-image-badge">+{{q}}</view></view></view><view class="wish-card-footer"><view class="wish-card-date">{{r}}</view></view></view></view><view class="swipe-buttons"><view class="swipe-button complete" bindtap="{{v}}">完成</view><view class="swipe-button delete" bindtap="{{w}}">删除</view></view></view></view>
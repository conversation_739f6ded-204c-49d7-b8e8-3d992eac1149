{"version": 3, "file": "groupSort.js", "sources": ["subpkg-wish/pages/groupSort/groupSort.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGtnLXdpc2hccGFnZXNcZ3JvdXBTb3J0XGdyb3VwU29ydC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"group-sort-container\" @touchmove.stop.prevent=\"catchtouchmove\">\r\n\t\t<view class=\"group-sort-list\">\r\n\t\t\t<view v-if=\"!groups.length\" class=\"empty-tip\">\r\n\t\t\t\t<text>暂无可排序的标签</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view v-else class=\"sort-list\">\r\n\t\t\t\t<!-- 全部标签固定在第一位 -->\r\n\t\t\t\t<view v-if=\"allGroup\" class=\"group-sort-item is-fixed\">\r\n\t\t\t\t\t<view class=\"group-content\">\r\n\t\t\t\t\t\t<view class=\"group-icon all-icon\">全</view>\r\n\t\t\t\t\t\t<view class=\"group-name\">{{ allGroup.name }}</view>\r\n\t\t\t\t\t\t<view class=\"fixed-tag\">固定</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 可排序标签列表 -->\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-for=\"(group, index) in sortableGroups\" \r\n\t\t\t\t\t:key=\"group.id\"\r\n\t\t\t\t\tclass=\"group-sort-item\"\r\n\t\t\t\t\t:class=\"{ 'is-default': group.isDefault, 'active': activeIndex === index }\"\r\n\t\t\t\t\t@touchstart=\"onTouchStart($event, index)\"\r\n\t\t\t\t\t@touchmove=\"onTouchMove($event, index)\"\r\n\t\t\t\t\t@touchend=\"onTouchEnd\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"group-content\">\r\n\t\t\t\t\t\t<view class=\"group-icon\" :class=\"group.id === 'gift' ? 'gift-icon' : group.id === 'friend-visible' ? 'friend-icon' : 'custom-icon'\">\r\n\t\t\t\t\t\t\t{{ group.name.substr(0,1) }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"group-name\">{{ group.name }}</view>\r\n\t\t\t\t\t\t<view v-if=\"group.isDefault\" class=\"default-tag\">默认</view>\r\n\t\t\t\t\t\t<view class=\"drag-handle\">\r\n\t\t\t\t\t\t\t<text class=\"drag-icon\">≡</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { useGroupStore } from '@/store/group.js'\r\n\timport gestureManager from '@/utils/gestureManager.js'\r\n\t\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tgroups: [],\r\n\t\t\t\toriginalGroups: [],\r\n\t\t\t\tactiveIndex: -1,\r\n\t\t\t\tstartY: 0,\r\n\t\t\t\tcurrentY: 0,\r\n\t\t\t\tmoving: false,\r\n\t\t\t\tallGroup: null,\r\n\t\t\t\ttouchTimer: null,\r\n\t\t\t\tcatchtouchmove: false, // 控制是否阻止页面滚动\r\n\t\t\t\thasMoved: false, // 是否已进行排序操作\r\n\t\t\t\tsaveTimeout: null // A用于延迟保存的计时器\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 获取除\"全部\"外的可排序标签\r\n\t\t\tsortableGroups() {\r\n\t\t\t\treturn this.groups\r\n\t\t\t\t\t.filter(group => group.id !== 'all')\r\n\t\t\t\t\t.sort((a, b) => a.order - b.order);\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.initGroups()\r\n\t\t\t\r\n\t\t\t// 添加返回键监听\r\n\t\t\tuni.onNavigationBarButtonTap(e => {\r\n\t\t\t\tif (e.index === 0) {\r\n\t\t\t\t\tthis.returnToPrevious()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tonNavigationBarButtonTap(e) {\r\n\t\t\tif (e.index === 0) {\r\n\t\t\t\tthis.returnToPrevious()\r\n\t\t\t}\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\t// 清理定时器\r\n\t\t\tif (this.touchTimer) {\r\n\t\t\t\tclearTimeout(this.touchTimer)\r\n\t\t\t\tthis.touchTimer = null\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 清理保存定时器\r\n\t\t\tif (this.saveTimeout) {\r\n\t\t\t\tclearTimeout(this.saveTimeout)\r\n\t\t\t\tthis.saveTimeout = null\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化分组数据\r\n\t\t\tinitGroups() {\r\n\t\t\t\tconst groupStore = useGroupStore()\r\n\t\t\t\t\r\n\t\t\t\t// 获取所有分组\r\n\t\t\t\tconst allGroups = groupStore.getAllGroups\r\n\t\t\t\t\r\n\t\t\t\t// 提取\"全部\"标签\r\n\t\t\t\tthis.allGroup = allGroups.find(group => group.id === 'all');\r\n\t\t\t\t\r\n\t\t\t\t// 映射并添加排序信息\r\n\t\t\t\tthis.groups = allGroups\r\n\t\t\t\t\t.filter(group => group.id !== 'all')\r\n\t\t\t\t\t.map((group, index) => ({\r\n\t\t\t\t\t\t...group,\r\n\t\t\t\t\t\torder: group.order || index\r\n\t\t\t\t\t}));\r\n\t\t\t\t\r\n\t\t\t\t// 保存初始顺序，用于取消操作\r\n\t\t\t\tthis.originalGroups = JSON.parse(JSON.stringify(this.groups))\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('初始化分组:', this.groups);\r\n\t\t\t\tconsole.log('全部标签:', this.allGroup);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 触摸开始\r\n\t\t\tonTouchStart(event, index) {\r\n\t\t\t\t// 检查手势管理器是否允许拖拽\r\n\t\t\t\tif (!gestureManager.canStartGesture('drag')) {\r\n\t\t\t\t\tconsole.log('[GroupSort] 其他手势进行中，忽略拖拽开始');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 记录开始触摸的索引和位置\r\n\t\t\t\tthis.activeIndex = index;\r\n\t\t\t\tthis.startY = event.touches[0].clientY;\r\n\t\t\t\tthis.currentY = this.startY;\r\n\t\t\t\tthis.moving = false;\r\n\t\t\t\t\r\n\t\t\t\t// 设置定时器，如果长按则开始拖动模式\r\n\t\t\t\tthis.touchTimer = setTimeout(() => {\r\n\t\t\t\t\t// 开始拖拽\r\n\t\t\t\t\tif (!gestureManager.startDrag({ groupIndex: index })) {\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.moving = true;\r\n\t\t\t\t\tthis.catchtouchmove = true; // 开始拖动时禁止页面滚动\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 震动反馈\r\n\t\t\t\t\tuni.vibrateShort({\r\n\t\t\t\t\t\tsuccess: function () {\r\n\t\t\t\t\t\t\tconsole.log('振动成功');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 200);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 触摸移动\r\n\t\t\tonTouchMove(event, index) {\r\n\t\t\t\t// 清除长按定时器\r\n\t\t\t\tif (this.touchTimer) {\r\n\t\t\t\t\tclearTimeout(this.touchTimer);\r\n\t\t\t\t\tthis.touchTimer = null;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果不是拖动模式，不处理移动\r\n\t\t\t\tif (!this.moving) return;\r\n\t\t\t\t\r\n\t\t\t\t// 阻止默认行为，防止页面滚动\r\n\t\t\t\tevent.preventDefault && event.preventDefault();\r\n\t\t\t\tevent.stopPropagation && event.stopPropagation();\r\n\t\t\t\t\r\n\t\t\t\t// 获取当前触摸位置\r\n\t\t\t\tconst currentY = event.touches[0].clientY;\r\n\t\t\t\tthis.currentY = currentY;\r\n\t\t\t\t\r\n\t\t\t\t// 计算移动距离\r\n\t\t\t\tconst moveDistance = currentY - this.startY;\r\n\t\t\t\t\r\n\t\t\t\t// 根据移动距离计算目标位置\r\n\t\t\t\t// 项目高度约为100px(按实际情况调整)\r\n\t\t\t\tconst itemHeight = 100;\r\n\t\t\t\tconst moveItems = Math.round(moveDistance / itemHeight);\r\n\t\t\t\t\r\n\t\t\t\t// 计算目标索引\r\n\t\t\t\tlet targetIndex = index + moveItems;\r\n\t\t\t\t\r\n\t\t\t\t// 确保在有效范围内\r\n\t\t\t\tif (targetIndex >= 0 && targetIndex < this.sortableGroups.length && targetIndex !== index) {\r\n\t\t\t\t\t// 交换位置\r\n\t\t\t\t\tthis.swapItems(index, targetIndex);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 标记已进行排序操作\r\n\t\t\t\t\tthis.hasMoved = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 重置开始位置\r\n\t\t\t\t\tthis.startY = currentY;\r\n\t\t\t\t\tthis.activeIndex = targetIndex;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 触摸结束\r\n\t\t\tonTouchEnd() {\r\n\t\t\t\t// 清除长按定时器\r\n\t\t\t\tif (this.touchTimer) {\r\n\t\t\t\t\tclearTimeout(this.touchTimer);\r\n\t\t\t\t\tthis.touchTimer = null;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 结束拖动模式\r\n\t\t\t\tconst wasMoving = this.moving;\r\n\t\t\t\tthis.moving = false;\r\n\t\t\t\tthis.activeIndex = -1;\r\n\t\t\t\tthis.catchtouchmove = false; // 结束拖动时恢复页面滚动\r\n\t\t\t\t\r\n\t\t\t\t// 通知手势管理器拖拽结束\r\n\t\t\t\tif (wasMoving) {\r\n\t\t\t\t\tgestureManager.endDrag({ hasMoved: this.hasMoved });\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 更新排序\r\n\t\t\t\tthis.updateSortOrder();\r\n\t\t\t\t\r\n\t\t\t\t// 如果进行了排序操作，自动保存\r\n\t\t\t\tif (this.hasMoved) {\r\n\t\t\t\t\t// 清除之前的保存计时器\r\n\t\t\t\t\tif (this.saveTimeout) {\r\n\t\t\t\t\t\tclearTimeout(this.saveTimeout);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 设置新的保存计时器，延迟1秒保存，避免频繁保存\r\n\t\t\t\t\tthis.saveTimeout = setTimeout(() => {\r\n\t\t\t\t\t\tthis.autoSave();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 交换两个项目的位置\r\n\t\t\tswapItems(fromIndex, toIndex) {\r\n\t\t\t\t// 获取当前排序\r\n\t\t\t\tconst newGroups = [...this.groups];\r\n\t\t\t\t\r\n\t\t\t\t// 获取实际的组对象\r\n\t\t\t\tconst fromGroup = this.sortableGroups[fromIndex];\r\n\t\t\t\tconst toGroup = this.sortableGroups[toIndex];\r\n\t\t\t\t\r\n\t\t\t\t// 查找这两个组在原数组中的位置\r\n\t\t\t\tconst fromGroupIndex = newGroups.findIndex(g => g.id === fromGroup.id);\r\n\t\t\t\tconst toGroupIndex = newGroups.findIndex(g => g.id === toGroup.id);\r\n\t\t\t\t\r\n\t\t\t\t// 交换这两个组的排序值\r\n\t\t\t\tconst tempOrder = newGroups[fromGroupIndex].order;\r\n\t\t\t\tnewGroups[fromGroupIndex].order = newGroups[toGroupIndex].order;\r\n\t\t\t\tnewGroups[toGroupIndex].order = tempOrder;\r\n\t\t\t\t\r\n\t\t\t\t// 更新组数组\r\n\t\t\t\tthis.groups = newGroups;\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('交换位置:', fromIndex, toIndex);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 更新排序顺序\r\n\t\t\tupdateSortOrder() {\r\n\t\t\t\t// 根据当前展示顺序重新设置order值\r\n\t\t\t\tthis.sortableGroups.forEach((group, idx) => {\r\n\t\t\t\t\tconst groupIndex = this.groups.findIndex(g => g.id === group.id);\r\n\t\t\t\t\tif (groupIndex !== -1) {\r\n\t\t\t\t\t\tthis.groups[groupIndex].order = idx + 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('更新后的顺序:', this.sortableGroups.map(g => ({ id: g.id, name: g.name, order: g.order })));\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 自动保存排序结果\r\n\t\t\tautoSave() {\r\n\t\t\t\tconst groupStore = useGroupStore()\r\n\t\t\t\t\r\n\t\t\t\t// 将所有标签的顺序保存到store\r\n\t\t\t\tthis.groups.forEach(group => {\r\n\t\t\t\t\tif (group.id !== 'all') {\r\n\t\t\t\t\t\tgroupStore.updateGroupOrder(group.id, group.order)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t// 显示成功提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '排序已保存',\r\n\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t// 重置移动标志，允许下次拖拽后再次保存\r\n\t\t\t\tthis.hasMoved = false\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 返回上一页方法\r\n\t\t\treturnToPrevious() {\r\n\t\t\t\t// 如果有未保存的更改，先保存\r\n\t\t\t\tif (this.hasMoved) {\r\n\t\t\t\t\tthis.autoSave()\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.group-sort-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f8f8;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tposition: relative;\r\n\t\r\n\t.group-sort-list {\r\n\t\tflex: 1;\r\n\t\tpadding: 20rpx;\r\n\t\tpadding-top: 30rpx;\r\n\t\t\r\n\t\t.empty-tip {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 100rpx 0;\r\n\t\t\tcolor: #999;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.sort-list {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\t\t}\r\n\t\t\r\n\t\t.group-sort-item {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\t\t\r\n\t\t\t&.active {\r\n\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\r\n\t\t\t\tz-index: 10;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.is-fixed {\r\n\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\t\r\n\t\t\t\t.fixed-tag {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tbackground-color: #eee;\r\n\t\t\t\t\tpadding: 4rpx 16rpx;\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.is-default {\r\n\t\t\t\tbackground-color: #fafafa;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.group-content {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t.group-icon {\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\tbackground-color: #8a2be2;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.all-icon {\r\n\t\t\t\t\t\tbackground-color: #409eff;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.gift-icon {\r\n\t\t\t\t\t\tbackground-color: #ff9800;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.friend-icon {\r\n\t\t\t\t\t\tbackground-color: #67c23a;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.custom-icon {\r\n\t\t\t\t\t\tbackground-color: #8a2be2;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.group-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.default-tag {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tbackground-color: #eee;\r\n\t\t\t\t\tpadding: 4rpx 16rpx;\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.drag-handle {\r\n\t\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.drag-icon {\r\n\t\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\t\tline-height: 40rpx;\r\n\t\t\t\t\t\tcolor: #bbb;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/subpkg-wish/pages/groupSort/groupSort.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "useGroupStore", "gestureManager"], "mappings": ";;;;AA+CC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,QAAQ,CAAE;AAAA,MACV,gBAAgB,CAAE;AAAA,MAClB,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,gBAAgB;AAAA;AAAA,MAChB,UAAU;AAAA;AAAA,MACV,aAAa;AAAA;AAAA,IACd;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,iBAAiB;AAChB,aAAO,KAAK,OACV,OAAO,WAAS,MAAM,OAAO,KAAK,EAClC,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAAA,IACnC;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,WAAW;AAGhBA,kBAAG,MAAC,yBAAyB,OAAK;AACjC,UAAI,EAAE,UAAU,GAAG;AAClB,aAAK,iBAAiB;AAAA,MACvB;AAAA,KACA;AAAA,EACD;AAAA,EACD,yBAAyB,GAAG;AAC3B,QAAI,EAAE,UAAU,GAAG;AAClB,WAAK,iBAAiB;AAAA,IACvB;AAAA,EACA;AAAA,EACD,WAAW;AAEV,QAAI,KAAK,YAAY;AACpB,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAAA,IACnB;AAGA,QAAI,KAAK,aAAa;AACrB,mBAAa,KAAK,WAAW;AAC7B,WAAK,cAAc;AAAA,IACpB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,aAAa;AACZ,YAAM,aAAaC,YAAAA,cAAc;AAGjC,YAAM,YAAY,WAAW;AAG7B,WAAK,WAAW,UAAU,KAAK,WAAS,MAAM,OAAO,KAAK;AAG1D,WAAK,SAAS,UACZ,OAAO,WAAS,MAAM,OAAO,KAAK,EAClC,IAAI,CAAC,OAAO,WAAW;AAAA,QACvB,GAAG;AAAA,QACH,OAAO,MAAM,SAAS;AAAA,MACtB,EAAC;AAGH,WAAK,iBAAiB,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,CAAC;AAE5DD,oBAAY,MAAA,MAAA,OAAA,oDAAA,UAAU,KAAK,MAAM;AACjCA,oBAAA,MAAA,MAAA,OAAA,oDAAY,SAAS,KAAK,QAAQ;AAAA,IAClC;AAAA;AAAA,IAGD,aAAa,OAAO,OAAO;AAE1B,UAAI,CAACE,qBAAc,eAAC,gBAAgB,MAAM,GAAG;AAC5CF,sBAAAA,uEAAY,4BAA4B;AACxC;AAAA,MACD;AAGA,WAAK,cAAc;AACnB,WAAK,SAAS,MAAM,QAAQ,CAAC,EAAE;AAC/B,WAAK,WAAW,KAAK;AACrB,WAAK,SAAS;AAGd,WAAK,aAAa,WAAW,MAAM;AAElC,YAAI,CAACE,qBAAc,eAAC,UAAU,EAAE,YAAY,MAAO,CAAA,GAAG;AACrD;AAAA,QACD;AAEA,aAAK,SAAS;AACd,aAAK,iBAAiB;AAGtBF,sBAAAA,MAAI,aAAa;AAAA,UAChB,SAAS,WAAY;AACpBA,0BAAAA,MAAA,MAAA,OAAA,oDAAY,MAAM;AAAA,UACnB;AAAA,QACD,CAAC;AAAA,MACD,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,YAAY,OAAO,OAAO;AAEzB,UAAI,KAAK,YAAY;AACpB,qBAAa,KAAK,UAAU;AAC5B,aAAK,aAAa;AAAA,MACnB;AAGA,UAAI,CAAC,KAAK;AAAQ;AAGlB,YAAM,kBAAkB,MAAM;AAC9B,YAAM,mBAAmB,MAAM;AAG/B,YAAM,WAAW,MAAM,QAAQ,CAAC,EAAE;AAClC,WAAK,WAAW;AAGhB,YAAM,eAAe,WAAW,KAAK;AAIrC,YAAM,aAAa;AACnB,YAAM,YAAY,KAAK,MAAM,eAAe,UAAU;AAGtD,UAAI,cAAc,QAAQ;AAG1B,UAAI,eAAe,KAAK,cAAc,KAAK,eAAe,UAAU,gBAAgB,OAAO;AAE1F,aAAK,UAAU,OAAO,WAAW;AAGjC,aAAK,WAAW;AAGhB,aAAK,SAAS;AACd,aAAK,cAAc;AAAA,MACpB;AAAA,IACA;AAAA;AAAA,IAGD,aAAa;AAEZ,UAAI,KAAK,YAAY;AACpB,qBAAa,KAAK,UAAU;AAC5B,aAAK,aAAa;AAAA,MACnB;AAGA,YAAM,YAAY,KAAK;AACvB,WAAK,SAAS;AACd,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAGtB,UAAI,WAAW;AACdE,6BAAc,eAAC,QAAQ,EAAE,UAAU,KAAK,SAAU,CAAA;AAAA,MACnD;AAGA,WAAK,gBAAe;AAGpB,UAAI,KAAK,UAAU;AAElB,YAAI,KAAK,aAAa;AACrB,uBAAa,KAAK,WAAW;AAAA,QAC9B;AAGA,aAAK,cAAc,WAAW,MAAM;AACnC,eAAK,SAAQ;AAAA,QACb,GAAE,GAAI;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,UAAU,WAAW,SAAS;AAE7B,YAAM,YAAY,CAAC,GAAG,KAAK,MAAM;AAGjC,YAAM,YAAY,KAAK,eAAe,SAAS;AAC/C,YAAM,UAAU,KAAK,eAAe,OAAO;AAG3C,YAAM,iBAAiB,UAAU,UAAU,OAAK,EAAE,OAAO,UAAU,EAAE;AACrE,YAAM,eAAe,UAAU,UAAU,OAAK,EAAE,OAAO,QAAQ,EAAE;AAGjE,YAAM,YAAY,UAAU,cAAc,EAAE;AAC5C,gBAAU,cAAc,EAAE,QAAQ,UAAU,YAAY,EAAE;AAC1D,gBAAU,YAAY,EAAE,QAAQ;AAGhC,WAAK,SAAS;AAEdF,oBAAA,MAAA,MAAA,OAAA,oDAAY,SAAS,WAAW,OAAO;AAAA,IACvC;AAAA;AAAA,IAGD,kBAAkB;AAEjB,WAAK,eAAe,QAAQ,CAAC,OAAO,QAAQ;AAC3C,cAAM,aAAa,KAAK,OAAO,UAAU,OAAK,EAAE,OAAO,MAAM,EAAE;AAC/D,YAAI,eAAe,IAAI;AACtB,eAAK,OAAO,UAAU,EAAE,QAAQ,MAAM;AAAA,QACvC;AAAA,MACD,CAAC;AAEDA,2FAAY,WAAW,KAAK,eAAe,IAAI,QAAM,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE,MAAM,OAAO,EAAE,QAAQ,CAAC;AAAA,IACjG;AAAA;AAAA,IAGD,WAAW;AACV,YAAM,aAAaC,YAAAA,cAAc;AAGjC,WAAK,OAAO,QAAQ,WAAS;AAC5B,YAAI,MAAM,OAAO,OAAO;AACvB,qBAAW,iBAAiB,MAAM,IAAI,MAAM,KAAK;AAAA,QAClD;AAAA,OACA;AAGDD,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,OACV;AAGD,WAAK,WAAW;AAAA,IAChB;AAAA;AAAA,IAGD,mBAAmB;AAElB,UAAI,KAAK,UAAU;AAClB,aAAK,SAAS;AAAA,MACf;AACAA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjTD,GAAG,WAAW,eAAe;"}
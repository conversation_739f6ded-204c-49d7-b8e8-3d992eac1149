"use strict";
const common_vendor = require("../common/vendor.js");
const utils_envUtils = require("./envUtils.js");
class LoadingManager {
  constructor() {
    this.isLoading = false;
    this.loadingQueue = /* @__PURE__ */ new Set();
    this.currentTitle = "";
    this.timeoutTimer = null;
    this.defaultTimeout = 1e4;
  }
  /**
   * 显示加载弹窗
   * @param {string} title - 加载文本
   * @param {string} id - 操作唯一标识
   * @param {number} timeout - 超时时间（毫秒）
   * @param {boolean} silent - 是否静默模式（不显示弹窗）
   */
  show(title = "加载中...", id = "default", timeout = this.defaultTimeout, silent = false) {
    this.loadingQueue.add(id);
    if (silent) {
      this.loadingQueue.add(`${id}_silent`);
      return;
    }
    if (this.isLoading) {
      if (title !== "加载中..." && this.currentTitle === "加载中...") {
        this.currentTitle = title;
        this._updateLoadingTitle(title);
      }
      return;
    }
    this.isLoading = true;
    this.currentTitle = title;
    try {
      if (typeof common_vendor.index !== "undefined" && typeof common_vendor.index.showLoading === "function") {
        common_vendor.index.showLoading({
          title,
          mask: true
        });
      }
    } catch (error) {
      utils_envUtils.devLog.warn("[LoadingManager] 显示加载弹窗失败:", error);
    }
    this._setTimeoutProtection(timeout);
  }
  /**
   * 隐藏加载弹窗
   * @param {string} id - 操作唯一标识
   */
  hide(id = "default") {
    const isSilentOperation = this.loadingQueue.has(`${id}_silent`);
    this.loadingQueue.delete(id);
    this.loadingQueue.delete(`${id}_silent`);
    if (isSilentOperation) {
      return;
    }
    if (this.loadingQueue.size > 0) {
      return;
    }
    this._clearTimeoutProtection();
    if (this.isLoading) {
      this.isLoading = false;
      this.currentTitle = "";
      if (typeof common_vendor.index !== "undefined" && typeof common_vendor.index.hideLoading === "function") {
        common_vendor.index.hideLoading().catch(() => {
        });
      }
    }
  }
  /**
   * 强制隐藏所有加载状态
   */
  forceHideAll() {
    const wasLoading = this.isLoading;
    this.loadingQueue.size;
    this.loadingQueue.clear();
    this._clearTimeoutProtection();
    this.isLoading = false;
    this.currentTitle = "";
    if (wasLoading) {
      if (typeof common_vendor.index !== "undefined" && typeof common_vendor.index.hideLoading === "function") {
        common_vendor.index.hideLoading().catch(() => {
        });
      }
    }
  }
  /**
   * 包装异步操作，自动管理加载状态
   * @param {Function} asyncFn - 异步函数
   * @param {Object} options - 配置选项
   * @returns {Promise}
   */
  async wrap(asyncFn, options = {}) {
    const {
      title = "加载中...",
      id = `auto_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timeout = this.defaultTimeout,
      silent = false,
      showSuccess = false,
      successTitle = "操作成功",
      showError = true,
      errorTitle = "操作失败"
    } = options;
    this.show(title, id, timeout, silent);
    try {
      const result = await asyncFn();
      if (showSuccess && !silent) {
        this.hide(id);
        setTimeout(() => {
          if (typeof common_vendor.index !== "undefined" && typeof common_vendor.index.showToast === "function") {
            common_vendor.index.showToast({
              title: successTitle,
              icon: "success",
              duration: 1500
            });
          }
        }, 100);
      } else {
        this.hide(id);
      }
      return result;
    } catch (error) {
      this.hide(id);
      if (showError && !silent) {
        setTimeout(() => {
          if (typeof common_vendor.index !== "undefined" && typeof common_vendor.index.showToast === "function") {
            common_vendor.index.showToast({
              title: error.message || errorTitle,
              icon: "none",
              duration: 2e3
            });
          }
        }, 100);
      }
      throw error;
    }
  }
  /**
   * 更新加载标题
   * @private
   */
  _updateLoadingTitle(title) {
    try {
      if (typeof common_vendor.index !== "undefined" && typeof common_vendor.index.showLoading === "function") {
        common_vendor.index.showLoading({
          title,
          mask: true
        });
      }
    } catch (error) {
      utils_envUtils.devLog.warn("[LoadingManager] 更新加载标题失败:", error);
    }
  }
  /**
   * 设置超时保护
   * @private
   */
  _setTimeoutProtection(timeout) {
    this._clearTimeoutProtection();
    this.timeoutTimer = setTimeout(() => {
      utils_envUtils.devLog.warn("[LoadingManager] 加载超时，强制隐藏加载弹窗");
      this.forceHideAll();
    }, timeout);
  }
  /**
   * 清除超时保护
   * @private
   */
  _clearTimeoutProtection() {
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }
  }
  /**
   * 获取当前加载状态
   */
  get loading() {
    return this.isLoading;
  }
  /**
   * 获取当前队列大小
   */
  get queueSize() {
    return this.loadingQueue.size;
  }
}
const loadingManager = new LoadingManager();
exports.loadingManager = loadingManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/loadingManager.js.map

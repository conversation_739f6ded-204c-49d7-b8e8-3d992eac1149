<template>
	<view class="wish-card-container" :class="{'is-dragging': isDragging || isDraggingElement}">
		<view class="swipe-action-wrapper" ref="swipeRef"
			@touchstart="touchStart"
			@touchmove="touchMove"
			@touchend="touchEnd"
			@touchcancel="touchEnd">
			<!-- 主卡片内容 -->
			<view class="swipe-content" :style="contentStyle">
				<view class="wish-card" @click="goToDetail">
					<view class="wish-card-header">
						<view class="wish-card-order">{{ index + 1 }}</view>
						<view class="wish-card-title text-ellipsis">{{ wish?.title || '未知标题' }}</view>
						<view class="wish-card-status">
							<view v-if="wish?.permission === 'private'" class="private-icon">
								<uni-icons type="lock-filled" size="16" color="#999"></uni-icons>
							</view>
							<view v-if="wish?.isCompleted" class="completed-icon">
								<uni-icons type="checkmarkempty" size="16" color="#fff"></uni-icons>
							</view>
						</view>
						<button open-type="share" class="share-btn" @click.stop="shareWish" :data-index="index">
							<image src="/static/tabbar/share.png" class="share-icon"></image>
						</button>
					</view>
					
					<view class="wish-card-content">
						<view class="wish-card-desc" v-if="wish?.description">{{ wish.description }}</view>
						<!-- 🔧 优化图片显示逻辑，支持多种数据格式并添加错误处理 -->
						<view class="image-container" v-if="hasValidImage">
							<image
								class="wish-card-image"
								:src="getFirstImageUrl"
								mode="aspectFit"
								@error="onImageError"
								@load="onImageLoad"
							></image>
							<view v-if="getImageCount > 1" class="multi-image-badge">+{{getImageCount}}</view>
						</view>
					</view>
					
					<view class="wish-card-footer">
						<view class="wish-card-date">{{ formatDate(wish?.createDate) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 滑动操作按钮 -->
			<view class="swipe-buttons">
				<view class="swipe-button complete" @click="handleButtonClick(0)">完成</view>
				<view class="swipe-button delete" @click="handleButtonClick(1)">删除</view>
			</view>
		</view>
	</view>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue';
import { AnimationUtils } from '@/utils/animationPerformance.js';
import { convertCloudUrl } from '@/utils/imageUtils.js';

export default {
	name: 'WishCard',
	props: {
		wish: {
			type: Object,
			required: true
		},
		index: {
			type: Number,
			default: 0
		},
		// 添加一个新属性接收活动卡片的ID，用于判断当前卡片是否需要保持打开状态
		activeCardId: {
			type: String,
			default: ''
		},
		// 添加是否可拖拽的属性
		isDraggable: {
			type: Boolean,
			default: false
		},
		// 添加是否为当前被拖拽的元素
		isDraggingElement: {
			type: Boolean, 
			default: false
		}
	},
	setup(props, { emit }) {
		// 滑动状态
		const swiping = ref(false);
		const startX = ref(0);
		const moveX = ref(0);
		const startY = ref(0);
		const swipeRef = ref(null);
		
		// 添加一个变量记录当前卡片的滑动状态
		const isOpen = ref(false);
		
		// 添加拖拽相关状态
		const longPressTimer = ref(null);
		const isDragging = ref(false);
		const dragStartY = ref(0);
		const touchMoveThrottled = ref(false); // 添加节流控制变量
		// 新增：动画帧ID用于优化性能
		const animationFrameId = ref(null);
		
		// 按钮总宽度 (完成按钮宽度 + 删除按钮宽度)
		const buttonsWidth = 120;  // 2个按钮共120px宽度
		
		// 添加全局滚动监听
		const handleGlobalScroll = () => {
			if (isOpen.value) {
				resetSwipe();
			}
		};
		
		// 在组件挂载时添加滚动监听
		onMounted(() => {
			// 添加uni-app特定的滚动监听
			
			// 订阅自定义页面滚动通知事件
			uni.$on('page-scroll-event', (data) => {
				if (isOpen.value && data && data.shouldClose) {
					// 使用强制样式重置
					resetSwipeWithClass();
				}
			});
			
			// 订阅小程序页面滚动事件
			uni.$on('onPageScroll', () => {
				if (isOpen.value) {
					// 使用强制样式重置
					resetSwipeWithClass();
				}
			});
			
			// 订阅强制关闭卡片事件
			uni.$on('force-close-cards', (data) => {
				if (isOpen.value) {
					resetSwipeWithClass();
				}
			});
			
			// 小程序平台的滚动事件处理
			if (typeof window !== 'undefined') {
				// 浏览器环境
				window.addEventListener('scroll', handleGlobalScroll, true);
				document.addEventListener('scroll', handleGlobalScroll, true);
				// 监听父容器的滚动事件
				const parentElement = document.querySelector('.wish-container');
				if (parentElement) {
					parentElement.addEventListener('scroll', handleGlobalScroll, true);
				}
				// 监听列表的滚动事件
				const listElement = document.querySelector('.wish-list');
				if (listElement) {
					listElement.addEventListener('scroll', handleGlobalScroll, true);
				}
			}
		});
		
		// 在组件卸载时移除滚动监听
		onUnmounted(() => {
			// 移除uni-app特定的事件监听
			uni.$off('onPageScroll');
			uni.$off('page-scroll-event');
			uni.$off('force-close-cards');

			// 清理动画帧
			if (animationFrameId.value) {
				AnimationUtils.cancelRAF(animationFrameId.value);
			}

			// 清理定时器
			if (longPressTimer.value) {
				clearTimeout(longPressTimer.value);
			}

			// 如果在浏览器环境，还需移除原生事件监听
			if (typeof window !== 'undefined') {
				window.removeEventListener('scroll', handleGlobalScroll, true);
				document.removeEventListener('scroll', handleGlobalScroll, true);
			}
		});
		
		// 内容样式
		const contentStyle = computed(() => {
			let x = moveX.value - startX.value;
			
			// 限制只能左滑（负值）
			if (x > 0) {
				x = 0;
			}
			
			// 添加滑动阻尼效果，使滑动感觉更像微信
			if (x < 0) {
				// 当超过滑动阈值时增加阻尼
				if (x < -buttonsWidth) {
					// 计算超出部分，添加0.2的阻尼系数
					x = -buttonsWidth + (x + buttonsWidth) * 0.2;
				}
			}

			// 🔧 图片处理相关的计算属性和方法

			// 检查是否有有效的图片
			const hasValidImage = computed(() => {
				const images = wishData.value.image;
				if (!images) return false;

				if (Array.isArray(images)) {
					return images.length > 0 && images.some(img => {
						if (typeof img === 'string') {
							return img.trim() !== '';
						} else if (img && typeof img === 'object' && img.url) {
							return img.url.trim() !== '';
						}
						return false;
					});
				}

				if (typeof images === 'string') {
					return images.trim() !== '';
				}

				return false;
			});

			// 获取第一张图片的URL（支持云存储转换）
			const rawImageUrl = computed(() => {
				const images = wishData.value.image;
				if (!images) return '';

				if (Array.isArray(images) && images.length > 0) {
					const firstImage = images[0];
					if (typeof firstImage === 'string') {
						return firstImage;
					} else if (firstImage && typeof firstImage === 'object' && firstImage.url) {
						return firstImage.url;
					}
				}

				if (typeof images === 'string') {
					return images;
				}

				return '';
			});

			// 转换后的图片URL（处理云存储）
			const getFirstImageUrl = ref('');

			// 监听原始URL变化，进行云存储转换
			watch(rawImageUrl, async (newUrl) => {
				if (!newUrl) {
					getFirstImageUrl.value = '';
					return;
				}

				try {
					const convertedUrl = await convertCloudUrl(newUrl);
					getFirstImageUrl.value = convertedUrl;
				} catch (error) {
					console.error('[WishCard] 图片URL转换失败:', error);
					getFirstImageUrl.value = newUrl; // 转换失败时使用原始URL
				}
			}, { immediate: true });

			// 获取图片数量
			const getImageCount = computed(() => {
				const images = wishData.value.image;
				if (!images) return 0;

				if (Array.isArray(images)) {
					return images.filter(img => {
						if (typeof img === 'string') {
							return img.trim() !== '';
						} else if (img && typeof img === 'object' && img.url) {
							return img.url.trim() !== '';
						}
						return false;
					}).length;
				}

				if (typeof images === 'string' && images.trim() !== '') {
					return 1;
				}

				return 0;
			});

			// 🔧 图片加载错误处理 - 添加安全检查
			const onImageError = (e) => {
				const src = e?.target?.src || e?.detail?.src || '未知图片源';
				console.error('[WishCard] 图片加载失败:', {
					src: src,
					rawUrl: rawImageUrl.value,
					wishId: wishData.value.id || wishData.value._id,
					imageData: wishData.value.image,
					isCloudUrl: rawImageUrl.value?.startsWith('cloud://'),
					error: e
				});

				// 可以在这里添加默认图片或其他错误处理逻辑
				// e.target.src = '/static/images/default-image.png';
			};

			// 🔧 图片加载成功处理 - 添加安全检查
			const onImageLoad = (e) => {
				const src = e?.target?.src || e?.detail?.src || '未知图片源';
				console.log('[WishCard] 图片加载成功:', src);
			};

			return {
				// 使用 translate3d 启用硬件加速
				transform: `translate3d(${x}px, 0, 0)`,
				transition: swiping.value ? 'none' : 'transform 0.25s cubic-bezier(0.3, 0.9, 0.3, 1)',
				// 性能优化
				willChange: swiping.value ? 'transform' : 'auto',
				backfaceVisibility: 'hidden'
			};
		});
		
		// 创建计算属性以保持对wish的响应式引用
		const wishData = computed(() => props.wish);
		
		// 触摸开始
		const touchStart = (e) => {
			// 如果组件未启用拖拽功能，则只处理滑动
			if (!props.isDraggable) {
				handleSwipeStart(e);
				return;
			}
			
			// 记录触摸开始状态
			swiping.value = true;
			startX.value = e.touches[0].clientX;
			
			// 重要修复：不要直接重置moveX，保持当前滑动状态
			// 如果卡片当前是关闭状态，才设置moveX为startX
			if (!isOpen.value) {
				moveX.value = startX.value;
			}
			// 如果卡片已经打开，保持当前的moveX值不变
			
			// 记录初始触摸点Y坐标，用于检测垂直滚动和拖拽
			startY.value = e.touches[0].clientY;
			dragStartY.value = e.touches[0].clientY;
			
			// 如果存在活动卡片但不是当前卡片，通知父组件当前卡片将开始滑动
			if (props.activeCardId && props.activeCardId !== props.wish.id) {
				emit('card-swipe-start', props.wish.id);
			}
			
			// 使用定时器实现长按检测
			if (longPressTimer.value) {
				clearTimeout(longPressTimer.value);
			}
			
			longPressTimer.value = setTimeout(() => {
				// 长按触发，开始拖拽模式
				isDragging.value = true;
				
				// 阻止页面滚动以获得更好的拖拽体验 - 使用更强的方式防止滚动
				uni.$emit('disable-page-scroll', { cardId: props.wish.id, force: true });
				
				// 立即禁用页面所有触摸滚动行为
				lockPageScroll();
				
				// 震动反馈
				try {
					uni.vibrateShort({
						success: function () {
							// 长按振动触发
						}
					});
				} catch (e) {
					console.error('震动API调用失败:', e);
				}
				
				// 发送开始拖拽事件
				emit('drag-start', {
					wishId: props.wish.id,
					index: props.index,
					clientX: startX.value,
					clientY: startY.value
				});
			}, 500);
		};
		
		// 处理滑动开始，与触摸分离
		const handleSwipeStart = (e) => {
			swiping.value = true;
			startX.value = e.touches[0].clientX;
			
			// 重要修复：不要直接重置moveX，保持当前滑动状态
			// 如果卡片当前是关闭状态，才设置moveX为startX
			if (!isOpen.value) {
				moveX.value = startX.value;
			}
			// 如果卡片已经打开，保持当前的moveX值不变
			
			startY.value = e.touches[0].clientY;
			
			// 如果存在活动卡片但不是当前卡片，通知父组件当前卡片将开始滑动
			if (props.activeCardId && props.activeCardId !== props.wish.id) {
				emit('card-swipe-start', props.wish.id);
			}
		};
		
		// 触摸移动
		const touchMove = (e) => {
			// 获取当前触摸位置与起始位置的差值
			const currentX = e.touches[0].clientX;
			const currentY = e.touches[0].clientY;
			const deltaX = Math.abs(currentX - startX.value);
			const deltaY = Math.abs(currentY - startY.value);
			
			// 如果移动距离超过阈值，取消长按定时器
			// 对于水平滑动（左滑功能），提高阈值减少冲突
			if (longPressTimer.value && (deltaX > 20 || deltaY > 15)) {
				clearTimeout(longPressTimer.value);
				longPressTimer.value = null;
			}
			
			// 如果已经进入拖拽模式
			if (isDragging.value) {
				// 更强力地阻止默认行为和事件冒泡，确保页面不会滚动
				e.stopPropagation();
				if (typeof e.preventDefault === 'function') {
					e.preventDefault();
				}
				
				// 尝试其他方式阻止滚动
				if (e.cancelable) {
					e.cancelable && e.preventDefault();
				}
				
				// 获取直接相对位移并应用到卡片上
				const currentOffsetY = currentY - startY.value;
				
				// 优化后的DOM操作 - 使用 requestAnimationFrame
				if (swipeRef.value && !props.isDraggingElement) {
					try {
						const moveY = currentOffsetY * 0.3;

						// 取消之前的动画帧
						if (animationFrameId.value) {
							AnimationUtils.cancelRAF(animationFrameId.value);
						}

						// 使用 RAF 优化性能
						animationFrameId.value = AnimationUtils.optimizedRAF(() => {
							if (swipeRef.value) {
								// 使用 translate3d 启用硬件加速
								swipeRef.value.style.transform = `translate3d(0, ${moveY}px, 0)`;
								swipeRef.value.style.transition = 'none';
								swipeRef.value.style.willChange = 'transform';
							}
						});
					} catch (e) {
						console.error('应用拖拽变换失败:', e);
					}
				}
				
				// 发送拖拽移动事件
				const moveData = {
					wishId: props.wish.id,
					index: props.index,
					clientY: e.touches[0].clientY,
					clientX: e.touches[0].clientX,
					deltaY: e.touches[0].clientY - dragStartY.value,
					timestamp: Date.now() // 添加时间戳以便于调试和节流
				};
				
				// 添加节流控制，避免快速发送过多事件
				if (!touchMoveThrottled.value) {
					emit('drag-move', moveData);
					
					// 设置节流标志
					touchMoveThrottled.value = true;
					setTimeout(() => {
						touchMoveThrottled.value = false;
					}, 16); // 降低到16ms (约60fps)，提供更流畅的响应
				}
				
				return false; // 返回false进一步阻止事件传播
			}
			
			// 如果不是拖拽模式，则处理滑动
			handleSwipeMove(e);
		};
		
		// 处理滑动移动，与触摸分离
		const handleSwipeMove = (e) => {
			if (!swiping.value) return;
			
			// 获取当前触摸位置
			const currentX = e.touches[0].clientX;
			const currentY = e.touches[0].clientY;
			
			// 检测是否是垂直滚动
			const deltaY = Math.abs(currentY - startY.value);
			const deltaX = Math.abs(currentX - startX.value);
			
			// 仅当垂直移动明显大于水平移动，才判定为滚动
			// 提高阈值到30px，减少误触发，允许用户更自然地保持左滑状态
			if (deltaY > deltaX * 2 && deltaY > 30) {
				swiping.value = false;
				
				// 重要修复：如果卡片未打开，才重置moveX；如果已打开，保持左滑状态
				if (!isOpen.value) {
					moveX.value = startX.value;
				}
				// 如果卡片已经打开，不重置moveX，保持左滑状态
				
				// 通知父组件滚动发生（但不强制关闭卡片）
				emit('card-scroll-detected', {
					cardId: props.wish.id,
					deltaY: deltaY
				});
				
				// 防止继续处理
				return;
			}
			
			// 如果位置未变化，避免不必要的状态更新
			if (currentX === moveX.value) return;
			
			// 更新当前位置
			moveX.value = currentX;
		};
		
		// 触摸结束
		const touchEnd = (e) => {
			// 清除长按定时器
			if (longPressTimer.value) {
				clearTimeout(longPressTimer.value);
				longPressTimer.value = null;
			}
			
			// 如果是拖拽模式
			if (isDragging.value) {
				isDragging.value = false;
				
				// 恢复页面滚动
				uni.$emit('enable-page-scroll', { cardId: props.wish.id });
				
				// 重置卡片位置为原始位置，清除所有变换效果
				if (swipeRef.value) {
					swipeRef.value.style.transform = '';
					swipeRef.value.style.transition = 'transform 0.3s ease'; // 添加过渡效果使恢复更平滑
					
					// 确保在过渡完成后移除过渡属性
					setTimeout(() => {
						if (swipeRef.value) {
							swipeRef.value.style.transition = '';
						}
					}, 300);
				}
				
				// 发送拖拽结束事件
				emit('drag-end', {
					wishId: props.wish.id,
					index: props.index
				});
				return;
			}
			
			// 如果不是拖拽模式，则处理滑动结束
			handleSwipeEnd(e);
		};
		
		// 处理滑动结束，与触摸分离
		const handleSwipeEnd = (e) => {
			swiping.value = false;
			
			// 计算滑动距离
			const distance = moveX.value - startX.value;
			
			// 使用微信风格的滑动判断逻辑
			// 如果滑动距离超过按钮宽度的1/5，则显示按钮
			if (distance < -buttonsWidth / 5) {
				// 如果超过50%，显示全部按钮
				if (distance < -buttonsWidth / 2) {
					moveX.value = startX.value - buttonsWidth;
				} else {
					// 否则仅显示部分按钮
					moveX.value = startX.value - buttonsWidth;
				}
				
				// 更新状态并通知父组件
				isOpen.value = true;
				emit('card-open', props.wish.id);
			} else {
				// 回弹到初始位置
				moveX.value = startX.value;
				isOpen.value = false;
				
				// 如果当前卡片是活动卡片但已关闭，通知父组件
				if (props.activeCardId === props.wish.id) {
					emit('card-close');
				}
			}
		};
		
		// 🔧 按钮点击处理 - 添加安全检查
		const handleButtonClick = (index) => {
			// 检查wish对象是否存在
			if (!props.wish || !props.wish.id) {
				console.warn('[WishCard] 无效的心愿数据，无法执行操作');
				return;
			}

			if (index === 0) {
				// 完成心愿
				emit('complete', props.wish.id);
			} else if (index === 1) {
				// 删除心愿 - 添加删除动画
				handleDeleteWithAnimation();
			}

			// 立即重置滑动状态
			moveX.value = startX.value;
		};
		
		// 删除动画状态
		const isDeleting = ref(false);
		
		// 🔧 带确认的删除处理 - 添加安全检查
		const handleDeleteWithAnimation = () => {
			// 检查wish对象是否存在
			if (!props.wish || !props.wish.id) {
				console.warn('[WishCard] 无效的心愿数据，无法删除');
				return;
			}

			// 先确认删除操作
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个心愿吗？',
				confirmText: '删除',
				confirmColor: '#fa5151',
				success: (res) => {
					if (res.confirm) {
						// 直接触发删除，由父组件的transition-group处理动画
						emit('delete', props.wish.id);
					}
				}
			});
		};
		
		// 🔧 跳转到详情页 - 添加安全检查
		const goToDetail = () => {
			// 检查wish对象是否存在
			if (!props.wish || !props.wish.id) {
				console.warn('[WishCard] 无效的心愿数据，无法跳转到详情页');
				return;
			}

			// 如果正在滑动或滑动菜单已打开，不要跳转
			if (swiping.value || moveX.value !== startX.value) {
				// 如果菜单已打开，则关闭菜单
				if (moveX.value !== startX.value) {
					moveX.value = startX.value;
					isOpen.value = false;
				}
				return;
			}

			uni.navigateTo({
				url: `/subpkg-wish/pages/wishDetail/wishDetail?id=${props.wish.id}`
			});
		};
		
		// 🔧 分享心愿 - 添加安全检查
		const shareWish = (e) => {
			e.stopPropagation()

			// 检查wish对象是否存在
			if (!props.wish || !props.wish.id) {
				console.warn('[WishCard] 无效的心愿数据，无法分享');
				return;
			}

			// 发出共享事件，让父级组件处理登录检查和共享逻辑
			emit('share', props.wish.id)
			
			// 注释掉直接显示分享菜单的代码，改为完全由父组件处理分享逻辑
			// 这样可以确保登录检查在展示分享选项之前进行
			/* 
			// 使用button open-type="share"来触发小程序原生分享
			// 但在这里我们需要模拟点击事件
			try {
				// 触发微信分享菜单
				uni.showShareMenu({
					withShareTicket: true,
					menus: ['shareAppMessage', 'shareTimeline'],
					success() {
						// 触发振动反馈
						uni.vibrateShort({
							success: function() {
								// 振动成功
							}
						})
					},
					fail(e) {
						// 显示分享菜单失败
						// 回退到自定义分享逻辑
						emit('share', props.wish.id)
					}
				})
			} catch (err) {
				console.error('分享操作失败:', err)
				// 回退到自定义分享逻辑
				emit('share', props.wish.id)
			}
			*/
		};
		
		// 🔧 格式化日期 - 添加安全检查
		const formatDate = (dateString) => {
			if (!dateString) return '未知日期';

			try {
				const date = new Date(dateString);
				if (isNaN(date.getTime())) {
					return '无效日期';
				}
				return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
			} catch (error) {
				console.error('[WishCard] 日期格式化错误:', error);
				return '日期错误';
			}
		};
		
		// 重置滑动状态
		const resetSwipe = () => {
			
			// 记录重置前的状态
			const wasOpen = isOpen.value;
			
			// 重置滑动状态数据
			moveX.value = startX.value;
			isOpen.value = false;
			swiping.value = false;
			
			// 只有在卡片真的从打开状态关闭时才触发关闭事件
			// 避免不必要的事件触发导致循环调用
			if (wasOpen && props.activeCardId === props.wish.id) {
				emit('card-close', props.wish.id);
			}
			
			// 注意：不直接操作DOM，让Vue的响应式系统通过contentStyle来处理样式
			// 这样避免了数据状态和DOM样式不一致的问题
		};
		
		// 强制重置滑动状态（用于极端情况）
		const resetSwipeWithClass = () => {
			
			// 记录重置前的状态
			const wasOpen = isOpen.value;
			
			// 重置数据状态
			moveX.value = startX.value;
			isOpen.value = false;
			swiping.value = false;
			
			// 只有在卡片真的从打开状态关闭时才触发关闭事件
			if (wasOpen && props.activeCardId === props.wish.id) {
				emit('card-close', props.wish.id);
			}
			
			// 在极端情况下，可能需要强制同步DOM状态
			// 但优先使用Vue的响应式系统
		};
		
		// 监听activeCardId变化
		watch(
			() => props.activeCardId,
			(newActiveId, oldActiveId) => {
				// 如果卡片已打开，但活动卡片不是当前卡片或为空，则关闭当前卡片
				if (isOpen.value && (newActiveId !== props.wish.id || newActiveId === null)) {
					resetSwipe();
				}
			},
			{ immediate: true } // 立即执行一次，确保组件挂载时状态正确
		);
		
		// 监听 wish 属性变化，确保组件能够实时响应数据更新
		watch(
			() => props.wish, 
			(newWishData, oldWishData) => {
				// 响应数据变化
			},
			{ deep: true } // 深度监听对象变化
		);
		
		// 添加锁定页面滚动函数
		const lockPageScroll = () => {
			// 在触发拖动时直接应用CSS样式阻止滚动
			// 在父组件同步处理之前先做一些紧急处理
			
			try {
				// 使用uni-app事件通知所有页面组件
				uni.$emit('lock-page-scroll', { source: 'drag-start' });
				
				// 针对不同环境采取不同措施
				if (typeof uni.disablePageScroll === 'function') {
					// 如果uni-app提供了原生的禁用滚动方法，使用它
					uni.disablePageScroll();
				} else if (typeof uni.pageScrollTo === 'function') {
					// 尝试使用pageScrollTo固定当前位置
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 0
					});
				}
				
				// 在小程序环境中触发额外的阻止滚动机制
				setTimeout(() => {
					// 再次发送禁止滚动事件，确保页面组件收到
					uni.$emit('disable-page-scroll', { 
						cardId: props.wish.id, 
						force: true, 
						timestamp: Date.now() 
					});
				}, 50);
			} catch (e) {
				console.error('锁定页面滚动失败:', e);
			}
		}

		// 🔧 图片处理相关计算属性和方法
		const hasValidImage = computed(() => {
			const image = props.wish?.image;
			if (!image) return false;

			if (typeof image === 'string') {
				return image.trim() !== '';
			}

			if (Array.isArray(image)) {
				return image.length > 0 && image.some(img => {
					if (typeof img === 'string') {
						return img.trim() !== '';
					} else if (img && typeof img === 'object' && img.url) {
						return img.url.trim() !== '';
					}
					return false;
				});
			}

			if (image && typeof image === 'object' && image.url) {
				return image.url.trim() !== '';
			}

			return false;
		});

		const getFirstImageUrl = computed(() => {
			const image = props.wish?.image;
			if (!image) return '';

			if (typeof image === 'string') {
				return image;
			}

			if (Array.isArray(image) && image.length > 0) {
				const firstImage = image[0];
				if (typeof firstImage === 'string') {
					return firstImage;
				} else if (firstImage && typeof firstImage === 'object' && firstImage.url) {
					return firstImage.url;
				}
			}

			if (image && typeof image === 'object' && image.url) {
				return image.url;
			}

			console.warn('[WishCard] 无法解析图片URL:', image);
			return '';
		});

		const getImageCount = computed(() => {
			const image = props.wish?.image;
			if (!image) return 0;

			if (typeof image === 'string' && image.trim() !== '') {
				return 1;
			}

			if (Array.isArray(image)) {
				return image.filter(img => {
					if (typeof img === 'string') {
						return img.trim() !== '';
					} else if (img && typeof img === 'object' && img.url) {
						return img.url.trim() !== '';
					}
					return false;
				}).length;
			}

			if (image && typeof image === 'object' && image.url && image.url.trim() !== '') {
				return 1;
			}

			return 0;
		});

		// 🔧 图片加载成功处理 - 添加安全检查 (第二个定义)
		const onImageLoad = (e) => {
			const src = e?.target?.src || e?.detail?.src || '未知图片源';
			console.log('[WishCard] 图片加载成功:', src);
		};

		// 🔧 图片加载错误处理 - 添加安全检查 (第二个定义)
		const onImageError = (e) => {
			const src = e?.target?.src || e?.detail?.src || '未知图片源';
			console.error('[WishCard] 图片加载失败:', {
				src: src,
				error: e
			});
		};

		return {
			swipeRef,
			contentStyle,
			touchStart,
			touchMove,
			touchEnd,
			isDeleting,
			handleButtonClick,
			goToDetail,
			shareWish,
			formatDate,
			resetSwipe,
			isOpen,
			// 暴露这些变量给父组件用于紧急状态修复
			moveX,
			startX,
			startY,
			buttonsWidth,
			wish: wishData,
			isDragging,  // 导出拖拽状态
			touchMoveThrottled, // 导出节流控制变量
			// 🔧 图片处理相关
			hasValidImage,
			getFirstImageUrl,
			getImageCount,
			onImageError,
			onImageLoad,
		};
	}
}
</script>

<style lang="scss">
.wish-card-container {
	margin: 10rpx 20rpx; /* 使用固定边距避免百分比计算问题 */
	width: calc(100% - 40rpx); /* 使用calc确保不会超出容器宽度 */
	max-width: calc(100% - 40rpx); /* 防止内容溢出 */
	box-sizing: border-box;
	position: relative; /* 添加相对定位，为拖拽指示器提供基础 */
	
	/* 添加拖拽中的过渡动画，提高流畅度 */
	transition: transform 0.2s cubic-bezier(0.2, 0, 0, 1), 
	            box-shadow 0.2s cubic-bezier(0.2, 0, 0, 1), 
	            opacity 0.2s cubic-bezier(0.2, 0, 0, 1);
	
	/* 拖拽时的状态样式 */
	&.is-dragging {
		transform: scale(1.05);
		box-shadow: 0 10rpx 30rpx rgba(138, 43, 226, 0.4);
		opacity: 0.95;
		z-index: 100;
		background-color: #fff;
		border-radius: 12rpx;
		transition: transform 0.05s linear, box-shadow 0.05s linear; /* 拖拽中使用更快的过渡 */
	}
}

.swipe-action-wrapper {
	position: relative;
	overflow: hidden;
	border-radius: 12rpx;
	display: flex;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	width: 100%; /* 确保宽度充满父容器 */
	max-width: 100%; /* 防止内容溢出 */
	box-sizing: border-box; /* 确保边框和内边距包含在宽度内 */
}

.swipe-content {
	flex: 1;
	width: 100%;
	max-width: 100%; /* 防止内容溢出 */
	z-index: 2;
	background-color: #fff;
	transition: transform 0.3s ease;
	border-radius: 12rpx;
	box-sizing: border-box; /* 确保边框和内边距包含在宽度内 */
	overflow: hidden; /* 防止内容溢出 */
}

.swipe-buttons {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	display: flex;
	height: 100%;
}

.swipe-button {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60px;
	color: white;
	font-size: 26rpx;
	font-weight: 500;
	height: 100%;
	flex-direction: column;
	
	/* 微信风格的触摸反馈 */
	&:active {
		opacity: 0.85;
	}
}

.complete {
	background-color: #19ad19; /* 微信操作按钮绿色 */
}

.delete {
	background-color: #fa5151; /* 微信删除按钮红色 */
}

.wish-card {
	padding: 24rpx;
	box-sizing: border-box;
	background-color: #ffffff;
	border-radius: 12rpx;
	width: 100%; /* 确保宽度充满父容器 */
	max-width: 100%; /* 防止内容溢出 */
	overflow: hidden; /* 防止内容溢出 */
	
	.wish-card-header {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
		position: relative;
		
		.wish-card-order {
			width: 40rpx;
			height: 40rpx;
			border-radius: 20rpx;
			background-color: #8a2be2;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			margin-right: 16rpx;
		}
		
		.wish-card-title {
			flex: 1;
			font-size: 32rpx;
			font-weight: 500;
			margin-right: 60rpx; /* 为分享按钮留出空间 */
		}
		
		.wish-card-status {
			display: flex;
			align-items: center;
			margin-right: 16rpx;
			
			.private-icon, .completed-icon {
				margin-left: 16rpx;
			}
		}
		
		.share-btn {
			position: absolute;
			right: -8rpx;
			top: -8rpx;
			padding: 6rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #f0f0f0;
			border-radius: 50%;
			width: 60rpx;
			height: 60rpx;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
			z-index: 10;
			margin: 0;
			line-height: 1;
			
			&::after {
				border: none;
			}
			
			.share-icon {
				width: 40rpx;
				height: 40rpx;
			}
		}
	}
	
	.wish-card-content {
		margin-bottom: 16rpx;
		position: relative; /* 确保能相对定位 */
		
		.image-container {
			position: relative;
			width: 100%;
			height: 300rpx;
			margin-top: 16rpx;
			border-radius: 8rpx;
			overflow: hidden;
		}
		
		.wish-card-desc {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 16rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3; /* 🔧 修改为最多显示3行 */
			overflow: hidden;
		}
		
		.wish-card-image {
			width: 100%;
			height: 300rpx;
			border-radius: 8rpx;
			/* 🔧 移除 object-fit: cover，使用 mode="aspectFit" 完整显示图片 */
		}
		
		.multi-image-badge {
			position: absolute;
			right: 12rpx;
			bottom: 12rpx;
			background-color: rgba(0, 0, 0, 0.6);
			color: white;
			padding: 4rpx 12rpx;
			border-radius: 16rpx;
			font-size: 20rpx;
			z-index: 5;
		}
	}
	
	.wish-card-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.wish-card-date {
			font-size: 24rpx;
			color: #999;
		}
	}
}

// 优化后的拖拽动画 - 解决重复定义和性能问题
.swipe-action-wrapper.is-dragging {
	// 启用硬件加速
	transform: scale(1.05) translateZ(0);
	opacity: 0.95;
	z-index: 100;
	background-color: #fff;
	border: 4rpx solid #8a2be2;
	border-radius: 12rpx;

	// 性能优化：只对需要动画的属性设置过渡，避免使用 all
	transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1),
	           opacity 0.15s ease-out,
	           box-shadow 0.2s ease-out;

	// 提前告知浏览器需要优化的属性
	will-change: transform, opacity, box-shadow;

	// 避免闪烁
	backface-visibility: hidden;

	// 阴影效果单独处理，避免影响主动画性能
	box-shadow: 0 8rpx 16rpx rgba(138, 43, 226, 0.3);
}

// 拖拽时的微妙脉动效果 - 使用更高性能的实现
@keyframes drag-pulse-optimized {
	0% {
		transform: scale(1.05) translateZ(0);
		box-shadow: 0 8rpx 16rpx rgba(138, 43, 226, 0.3);
	}
	50% {
		transform: scale(1.07) translateZ(0);
		box-shadow: 0 12rpx 24rpx rgba(138, 43, 226, 0.4);
	}
	100% {
		transform: scale(1.05) translateZ(0);
		box-shadow: 0 8rpx 16rpx rgba(138, 43, 226, 0.3);
	}
}

// 应用优化后的动画
.swipe-action-wrapper.is-dragging.with-pulse {
	animation: drag-pulse-optimized 2s infinite ease-in-out;
}


</style> 
"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_wish = require("../../../store/wish.js");
const store_group = require("../../../store/group.js");
const store_comment = require("../../../store/comment.js");
const store_user = require("../../../store/user.js");
const utils_loadingManager = require("../../../utils/loadingManager.js");
const mixins_groupTagOperations = require("../../../mixins/groupTagOperations.js");
const _sfc_main = {
  mixins: [mixins_groupTagOperations.groupTagOperations],
  // 微信小程序分享给朋友
  onShareAppMessage(res) {
    const wish = this.wish || {};
    return {
      title: wish.title || "分享我的心愿",
      path: `/pages/wishDetail/wishDetail?id=${this.wishId}`,
      imageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? wish.image[0] : wish.image || "/static/images/share-image.png"
    };
  },
  // 微信小程序分享到朋友圈
  onShareTimeline() {
    const wish = this.wish || {};
    return {
      title: wish.title || "分享我的心愿",
      query: `id=${this.wishId}`,
      imageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? wish.image[0] : wish.image || "/static/images/share-image.png"
    };
  },
  setup() {
    const wishStore = store_wish.useWishStore();
    const groupStore = store_group.useGroupStore();
    const commentStore = store_comment.useCommentStore();
    const userStore = store_user.useUserStore();
    return {
      wishStore,
      groupStore,
      commentStore,
      userStore
    };
  },
  data() {
    return {
      wishId: "",
      wish: null,
      commentText: "",
      comments: [],
      newComment: "",
      commentImages: [],
      showCommentDialog: false,
      isLoadingComments: false,
      isPageLoading: true,
      // 页面整体加载状态
      commentToDelete: null,
      // 待删除的评论
      showDeleteBtn: false,
      // 是否显示悬浮删除按钮
      selectedComment: null,
      // 当前选中的评论
      deleteButtonPosition: { x: 0, y: 0 },
      // 删除按钮位置
      longPressTimeout: null
      // 长按定时器
    };
  },
  computed: {
    // 获取分组名称映射（计算属性，避免重复调用）
    groupNameMap() {
      if (!this.groupStore || !this.groupStore.getAllGroups) {
        return {};
      }
      const map = {};
      this.groupStore.getAllGroups.forEach((group) => {
        map[group._id || group.id] = group.name;
      });
      return map;
    },
    // 格式化心愿状态
    wishStatus() {
      if (!this.wish)
        return "";
      return this.wish.isCompleted ? "已完成" : "进行中";
    },
    // 格式化权限显示
    permissionText() {
      if (!this.wish)
        return "";
      const permissionMap = {
        "private": "私密",
        "friends": "朋友可见",
        "public": "公开"
      };
      return permissionMap[this.wish.permission] || "未知";
    },
    // 删除按钮样式
    deleteButtonStyle() {
      return {
        left: this.deleteButtonPosition.x + "px",
        top: this.deleteButtonPosition.y + "px"
      };
    }
  },
  async onLoad(options) {
    if (options.id) {
      this.wishId = options.id;
      this.isPageLoading = true;
      this.clearAllLoadingStates();
      try {
        common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:304", "[wishDetail] 开始初始化页面...");
        const pageTimeout = setTimeout(() => {
          common_vendor.index.__f__("warn", "at subpkg-wish/pages/wishDetail/wishDetail.vue:308", "[wishDetail] 页面加载超时，强制结束加载状态");
          this.isPageLoading = false;
          this.isLoadingComments = false;
          this.clearAllLoadingStates();
        }, 12e3);
        await this.initStores();
        this.loadWishDetail();
        await this.loadComments(true);
        clearTimeout(pageTimeout);
        common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:321", "[wishDetail] 页面初始化完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:323", "页面初始化失败:", error);
        this.clearAllLoadingStates();
      } finally {
        this.isPageLoading = false;
        this.clearAllLoadingStates();
      }
    }
  },
  // 页面每次显示时执行，确保数据最新
  onShow() {
    if (this.isPageLoading) {
      return;
    }
    this.clearAllLoadingStates();
    this.wishStore.refreshWishList();
    if (this.wishId) {
      this.loadWishDetail();
      this.loadComments(true);
    }
  },
  // 页面隐藏时清理加载状态
  onHide() {
    this.clearAllLoadingStates();
  },
  methods: {
    // 初始化stores
    async initStores() {
      this.wishStore = store_wish.useWishStore();
      this.groupStore = store_group.useGroupStore();
      this.commentStore = store_comment.useCommentStore();
      this.userStore = store_user.useUserStore();
      try {
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("分组初始化超时")), 8e3);
        });
        await Promise.race([this.groupStore.initGroups(), timeoutPromise]);
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:375", "分组Store初始化失败:", error);
        this.groupStore.ensureDefaultGroups();
      }
      await this.checkAndSyncWishAndComments();
      this._setupSyncListeners();
    },
    // 🚀 检查并同步心愿和评论数据（同步合并：同时调用但不合并内容）
    async checkAndSyncWishAndComments() {
      if (!this.wishId)
        return;
      common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:391", "[wishDetail] 检查心愿和评论数据同步状态...");
      try {
        const wishNeedsSync = await this.checkWishNeedsSync();
        const commentsNeedSync = await this.checkCommentsNeedSync();
        if (wishNeedsSync || commentsNeedSync) {
          common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:399", "[wishDetail] 需要同步数据:", {
            wishNeedsSync,
            commentsNeedSync
          });
          const syncPromises = [];
          if (wishNeedsSync) {
            common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:408", "[wishDetail] 添加心愿数据同步");
            syncPromises.push(this.wishStore.syncFromCloud({ silent: true }));
          }
          if (commentsNeedSync) {
            common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:413", "[wishDetail] 添加评论数据同步");
            syncPromises.push(this.commentStore.syncCommentsForWish(this.wishId, true, true));
          }
          await Promise.allSettled(syncPromises);
          common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:419", "[wishDetail] 同步合并完成");
        } else {
          common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:421", "[wishDetail] 数据无需同步，使用本地缓存");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:424", "[wishDetail] 同步合并失败:", error);
      }
    },
    // 检查心愿数据是否需要同步
    async checkWishNeedsSync() {
      if (!this.wishStore || !this.wishId)
        return true;
      const hasLocalWish = this.wishStore.getWishById(this.wishId);
      const lastSyncTime = this.wishStore.lastSyncTime;
      const now = Date.now();
      return !hasLocalWish || !lastSyncTime || now - lastSyncTime > 5 * 60 * 1e3;
    },
    // 检查评论数据是否需要同步
    async checkCommentsNeedSync() {
      if (!this.commentStore || !this.wishId)
        return true;
      const hasLocalComments = this.commentStore.commentsByWish[this.wishId] && this.commentStore.commentsByWish[this.wishId].length >= 0;
      const lastSyncTime = this.commentStore.syncTimestamps[this.wishId];
      const now = Date.now();
      return !hasLocalComments || !lastSyncTime || now - lastSyncTime > 5 * 60 * 1e3;
    },
    // 设置同步监听器
    _setupSyncListeners() {
      this.commentStore.on && this.commentStore.on("sync_completed", () => {
        common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:459", "评论数据同步完成");
        if (this.wishId) {
          this.loadComments();
        }
      });
    },
    // 加载心愿详情
    loadWishDetail() {
      this.wish = this.wishStore.getWishById(this.wishId);
      if (!this.wish) {
        common_vendor.index.showToast({
          title: "心愿不存在",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }
    },
    // 加载评论列表
    async loadComments(refresh = false) {
      if (!this.wishId)
        return;
      this.isLoadingComments = true;
      try {
        this.comments = await utils_loadingManager.loadingManager.wrap(
          () => this.commentStore.loadComments(this.wishId, refresh),
          {
            title: "加载评论中...",
            id: `load_comments_${this.wishId}`,
            timeout: 8e3,
            silent: true,
            // 静默加载，不显示弹窗
            showError: false
            // 不显示错误提示，由页面处理
          }
        );
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:503", "加载评论失败:", error);
        this.comments = this.commentStore.commentsByWish[this.wishId] || [];
      } finally {
        this.isLoadingComments = false;
      }
    },
    // 显示评论输入框
    showCommentInput() {
      if (!this.userStore.checkLoginAndRedirect()) {
        return;
      }
      this.showCommentDialog = true;
    },
    // 发布评论
    async submitComment() {
      if (!this.commentText.trim()) {
        common_vendor.index.showToast({
          title: "请输入评论内容",
          icon: "none"
        });
        return;
      }
      let loadingShown = false;
      try {
        common_vendor.index.showLoading({ title: "发布中..." });
        loadingShown = true;
        const commentData = {
          content: this.commentText.trim(),
          image: []
        };
        await this.commentStore.addComment(this.wishId, commentData);
        await this.loadComments(true);
        this.commentText = "";
        common_vendor.index.showToast({
          title: "评论成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:555", "发布评论失败:", error);
        common_vendor.index.showToast({
          title: error.message || "评论发布失败",
          icon: "none"
        });
      } finally {
        if (loadingShown) {
          common_vendor.index.hideLoading().catch(() => {
          });
        }
        this.clearAllLoadingStates();
      }
    },
    // 检查是否可以删除评论（评论作者或心愿作者）
    canDeleteComment(comment) {
      var _a, _b;
      if (!this.userStore.isLoggedIn || !comment)
        return false;
      const currentUserId = this.userStore.userId || ((_a = this.userStore.userInfo) == null ? void 0 : _a.uid);
      const isCommentAuthor = comment.userId === currentUserId;
      const isWishAuthor = ((_b = this.wish) == null ? void 0 : _b.userId) === currentUserId;
      return isCommentAuthor || isWishAuthor;
    },
    // 处理评论长按事件
    handleCommentLongPress(comment, event) {
      if (!this.canDeleteComment(comment)) {
        common_vendor.index.showToast({
          title: "无权限删除",
          icon: "none"
        });
        return;
      }
      let touchX = 100;
      let touchY = 200;
      if (event && event.detail && (event.detail.x !== void 0 || event.detail.y !== void 0)) {
        touchX = event.detail.x || touchX;
        touchY = event.detail.y || touchY;
      } else if (event && event.touches && event.touches.length > 0) {
        const touch = event.touches[0];
        touchX = touch.clientX || touch.pageX || touchX;
        touchY = touch.clientY || touch.pageY || touchY;
      } else if (event && event.changedTouches && event.changedTouches.length > 0) {
        const touch = event.changedTouches[0];
        touchX = touch.clientX || touch.pageX || touchX;
        touchY = touch.clientY || touch.pageY || touchY;
      }
      const systemInfo = common_vendor.index.getSystemInfoSync();
      const screenWidth = systemInfo.screenWidth || 375;
      const screenHeight = systemInfo.screenHeight || 667;
      const buttonWidth = 80;
      const buttonHeight = 50;
      let finalX = touchX - buttonWidth / 2;
      let finalY = touchY - buttonHeight - 10;
      if (finalX < 10) {
        finalX = 10;
      }
      if (finalX + buttonWidth > screenWidth - 10) {
        finalX = screenWidth - buttonWidth - 10;
      }
      if (finalY < 10) {
        finalY = touchY + 10;
      }
      if (finalY + buttonHeight > screenHeight - 50) {
        finalY = screenHeight - buttonHeight - 50;
      }
      this.deleteButtonPosition = {
        x: finalX,
        y: finalY
      };
      this.selectedComment = comment;
      this.showDeleteBtn = true;
      setTimeout(() => {
        this.hideDeleteButton();
      }, 3e3);
      try {
        common_vendor.index.vibrateShort();
      } catch (error) {
      }
    },
    // 处理触摸开始
    handleTouchStart(event) {
    },
    // 处理触摸结束
    handleTouchEnd() {
    },
    // 隐藏删除按钮
    hideDeleteButton() {
      this.showDeleteBtn = false;
      this.selectedComment = null;
    },
    // 显示删除评论确认弹窗
    showDeleteCommentConfirm(comment) {
      this.commentToDelete = comment;
      this.hideDeleteButton();
      this.$refs.deleteCommentPopup.open();
    },
    // 确认删除评论
    async confirmDeleteComment() {
      if (!this.commentToDelete)
        return;
      let loadingShown = false;
      try {
        common_vendor.index.showLoading({ title: "删除中..." });
        loadingShown = true;
        await this.commentStore.deleteComment(this.wishId, this.commentToDelete._id);
        await this.loadComments(true);
        common_vendor.index.showToast({
          title: "删除成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:704", "删除评论失败:", error);
        common_vendor.index.showToast({
          title: error.message || "删除失败",
          icon: "none"
        });
      } finally {
        if (loadingShown) {
          common_vendor.index.hideLoading().catch(() => {
          });
        }
        this.commentToDelete = null;
        this.$refs.deleteCommentPopup.close();
        this.clearAllLoadingStates();
      }
    },
    // 关闭删除评论确认弹窗
    closeDeleteCommentConfirm() {
      this.commentToDelete = null;
      this.$refs.deleteCommentPopup.close();
    },
    // 删除评论（原有方法保留，可能其他地方调用）
    async deleteComment(commentId) {
      try {
        await this.commentStore.deleteComment(this.wishId, commentId);
        await this.loadComments(true);
        common_vendor.index.showToast({
          title: "删除成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:739", "删除评论失败:", error);
      }
    },
    // 预览图片
    previewImages(images, current = 0) {
      if (!images || images.length === 0)
        return;
      common_vendor.index.previewImage({
        current,
        urls: images
      });
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime()))
          return dateStr;
        const now = /* @__PURE__ */ new Date();
        const diff = now - date;
        if (diff < 6e4) {
          return "刚刚";
        }
        if (diff < 36e5) {
          return `${Math.floor(diff / 6e4)}分钟前`;
        }
        if (diff < 864e5) {
          return `${Math.floor(diff / 36e5)}小时前`;
        }
        if (diff < 6048e5) {
          return `${Math.floor(diff / 864e5)}天前`;
        }
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:784", "日期格式化错误:", error);
        return dateStr;
      }
    },
    // 编辑心愿
    editWish() {
      common_vendor.index.navigateTo({
        url: `/subpkg-wish/pages/editWish/editWish?id=${this.wishId}`
      });
    },
    // 完成心愿
    async completeWish() {
      try {
        await this.wishStore.completeWish(this.wishId);
        this.wish.isCompleted = true;
        this.wish.completeDate = (/* @__PURE__ */ new Date()).toISOString();
        common_vendor.index.showToast({
          title: "心愿已完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:808", "完成心愿失败:", error);
        common_vendor.index.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },
    // 恢复心愿
    async restoreWish() {
      try {
        await this.wishStore.restoreWish(this.wishId);
        this.wish.isCompleted = false;
        this.wish.completeDate = null;
        common_vendor.index.showToast({
          title: "心愿已恢复",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:828", "恢复心愿失败:", error);
        common_vendor.index.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },
    // 获取分组名称（优化版本，使用缓存）
    getGroupName(groupId) {
      if (!groupId)
        return "未知分组";
      const name = this.groupNameMap[groupId];
      return name || "未知分组";
    },
    // 清理所有加载状态
    clearAllLoadingStates() {
      utils_loadingManager.loadingManager.forceHideAll();
      this.isLoadingComments = false;
      this.isPageLoading = false;
      common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:854", "[wishDetail] 已清理所有加载状态");
    },
    // 格式化时间
    formatTime(timeString) {
      if (!timeString)
        return "无";
      let cleanTimeString = timeString;
      if (typeof cleanTimeString === "string" && cleanTimeString.includes("(已还原)")) {
        cleanTimeString = cleanTimeString.replace("(已还原)", "").trim();
      }
      try {
        const date = new Date(cleanTimeString);
        if (isNaN(date.getTime())) {
          common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:873", "无效的日期字符串:", cleanTimeString);
          return typeof cleanTimeString === "string" ? cleanTimeString.replace(/\s*\(已还原\)\s*/g, "") : "无";
        }
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:882", "日期格式化错误:", error);
        return typeof cleanTimeString === "string" ? cleanTimeString.replace(/\s*\(已还原\)\s*/g, "") : "无";
      }
    },
    // 获取完成时间（显示completeDate或lastCompleteDate）
    getCompleteTime() {
      var _a, _b, _c, _d, _e, _f, _g;
      if (((_a = this.wish) == null ? void 0 : _a.isCompleted) && ((_b = this.wish) == null ? void 0 : _b.completeDate)) {
        return this.formatTime(this.wish.completeDate);
      }
      if (!((_c = this.wish) == null ? void 0 : _c.isCompleted) && ((_d = this.wish) == null ? void 0 : _d.lastCompleteDate)) {
        return this.formatTime(this.wish.lastCompleteDate);
      }
      if (!((_e = this.wish) == null ? void 0 : _e.isCompleted) && ((_f = this.wish) == null ? void 0 : _f.completeDate) && !((_g = this.wish) == null ? void 0 : _g.lastCompleteDate)) {
        return this.formatTime(this.wish.completeDate);
      }
      return "无";
    },
    // 分享心愿
    shareWish() {
      try {
        common_vendor.index.showShareMenu({
          withShareTicket: true,
          menus: ["shareAppMessage", "shareTimeline"],
          success() {
            common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:923", "显示分享菜单成功");
            common_vendor.index.vibrateShort();
          },
          fail(err) {
            common_vendor.index.__f__("log", "at subpkg-wish/pages/wishDetail/wishDetail.vue:928", "显示分享菜单失败", err);
            common_vendor.index.showActionSheet({
              itemList: ["分享给微信好友", "分享到朋友圈"],
              success: (res) => {
                common_vendor.index.showToast({
                  title: "分享成功",
                  icon: "success"
                });
              }
            });
          }
        });
      } catch (err) {
        common_vendor.index.__f__("error", "at subpkg-wish/pages/wishDetail/wishDetail.vue:943", "分享操作失败:", err);
        common_vendor.index.showActionSheet({
          itemList: ["分享给微信好友", "分享到朋友圈"],
          success: (res) => {
            common_vendor.index.showToast({
              title: "分享成功",
              icon: "success"
            });
          }
        });
      }
    },
    // 显示删除确认
    showDeleteConfirm() {
      this.$refs.deletePopup.open();
    },
    // 关闭删除确认
    closeDeleteConfirm() {
      this.$refs.deletePopup.close();
    },
    // 确认删除
    confirmDelete() {
      if (!this.wishId)
        return;
      this.wishStore.deleteWish(this.wishId);
      common_vendor.index.showToast({
        title: "已删除",
        icon: "success"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    },
    // 导航到分组页面
    navigateToGroup(groupId) {
      this.wishStore.setCurrentGroup(groupId);
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_icons2 + _easycom_uni_popup_dialog2 + _easycom_uni_popup2)();
}
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_uni_popup_dialog = () => "../../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_uni_popup_dialog + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z;
  return common_vendor.e({
    a: common_vendor.t(((_a = $data.wish) == null ? void 0 : _a.title) || "心愿详情"),
    b: ((_b = $data.wish) == null ? void 0 : _b.permission) === "private"
  }, ((_c = $data.wish) == null ? void 0 : _c.permission) === "private" ? {
    c: common_vendor.p({
      type: "lock-filled",
      size: "12",
      color: "#fff"
    })
  } : ((_d = $data.wish) == null ? void 0 : _d.permission) === "friends" ? {
    e: common_vendor.p({
      type: "people-filled",
      size: "12",
      color: "#fff"
    })
  } : ((_e = $data.wish) == null ? void 0 : _e.permission) === "public" ? {
    g: common_vendor.p({
      type: "eye-filled",
      size: "12",
      color: "#fff"
    })
  } : {}, {
    d: ((_f = $data.wish) == null ? void 0 : _f.permission) === "friends",
    f: ((_g = $data.wish) == null ? void 0 : _g.permission) === "public",
    h: (_h = $data.wish) == null ? void 0 : _h.isCompleted
  }, ((_i = $data.wish) == null ? void 0 : _i.isCompleted) ? {
    i: common_vendor.p({
      type: "checkmarkempty",
      size: "12",
      color: "#fff"
    })
  } : {}, {
    j: (_j = $data.wish) == null ? void 0 : _j.description
  }, ((_k = $data.wish) == null ? void 0 : _k.description) ? {
    k: common_vendor.t($data.wish.description)
  } : {}, {
    l: ((_l = $data.wish) == null ? void 0 : _l.image) && typeof $data.wish.image === "string"
  }, ((_m = $data.wish) == null ? void 0 : _m.image) && typeof $data.wish.image === "string" ? {
    m: $data.wish.image,
    n: common_vendor.o(($event) => $options.previewImages(0))
  } : ((_n = $data.wish) == null ? void 0 : _n.image) && Array.isArray($data.wish.image) && $data.wish.image.length > 0 ? {
    p: common_vendor.f($data.wish.image, (img, index, i0) => {
      return {
        a: img,
        b: common_vendor.o(($event) => $options.previewImages(index), index),
        c: index
      };
    })
  } : {}, {
    o: ((_o = $data.wish) == null ? void 0 : _o.image) && Array.isArray($data.wish.image) && $data.wish.image.length > 0,
    q: common_vendor.t($options.formatTime((_p = $data.wish) == null ? void 0 : _p.createDate)),
    r: (_q = $data.wish) == null ? void 0 : _q.startDate
  }, ((_r = $data.wish) == null ? void 0 : _r.startDate) ? {
    s: common_vendor.t($options.formatTime((_s = $data.wish) == null ? void 0 : _s.startDate))
  } : {}, {
    t: ((_t = $data.wish) == null ? void 0 : _t.completeDate) || ((_u = $data.wish) == null ? void 0 : _u.lastCompleteDate)
  }, ((_v = $data.wish) == null ? void 0 : _v.completeDate) || ((_w = $data.wish) == null ? void 0 : _w.lastCompleteDate) ? {
    v: common_vendor.t($options.getCompleteTime())
  } : {}, {
    w: common_vendor.p({
      type: "compose",
      size: "18",
      color: "#8a2be2"
    }),
    x: common_vendor.o((...args) => $options.editWish && $options.editWish(...args)),
    y: common_vendor.p({
      type: "redo-filled",
      size: "18",
      color: "#8a2be2"
    }),
    z: common_vendor.o((...args) => $options.shareWish && $options.shareWish(...args)),
    A: !((_x = $data.wish) == null ? void 0 : _x.isCompleted)
  }, !((_y = $data.wish) == null ? void 0 : _y.isCompleted) ? {
    B: common_vendor.p({
      type: "checkbox-filled",
      size: "18",
      color: "#8a2be2"
    }),
    C: common_vendor.o((...args) => $options.completeWish && $options.completeWish(...args))
  } : {}, {
    D: common_vendor.p({
      type: "trash-filled",
      size: "18",
      color: "#f56c6c"
    }),
    E: common_vendor.o((...args) => $options.showDeleteConfirm && $options.showDeleteConfirm(...args)),
    F: common_vendor.f((_z = $data.wish) == null ? void 0 : _z.groupIds, (groupId, k0, i0) => {
      return {
        a: common_vendor.t($options.getGroupName(groupId)),
        b: groupId,
        c: common_vendor.o(($event) => $options.navigateToGroup(groupId), groupId)
      };
    }),
    G: common_vendor.t($data.comments.length || 0),
    H: !$data.comments || $data.comments.length === 0
  }, !$data.comments || $data.comments.length === 0 ? {} : {
    I: common_vendor.f($data.comments, (comment, index, i0) => {
      var _a2, _b2;
      return common_vendor.e({
        a: ((_a2 = comment.user_info) == null ? void 0 : _a2.avatar) || "/static/default-avatar.png",
        b: common_vendor.t(((_b2 = comment.user_info) == null ? void 0 : _b2.nickname) || "匿名用户"),
        c: common_vendor.t($options.formatTime(comment.createDate)),
        d: common_vendor.t(comment.content),
        e: comment.image && comment.image.length > 0
      }, comment.image && comment.image.length > 0 ? {
        f: comment.image[0]
      } : {}, {
        g: comment._id,
        h: common_vendor.o(($event) => $options.handleCommentLongPress(comment, $event), comment._id),
        i: common_vendor.o((...args) => $options.handleTouchStart && $options.handleTouchStart(...args), comment._id),
        j: common_vendor.o((...args) => $options.handleTouchEnd && $options.handleTouchEnd(...args), comment._id),
        k: common_vendor.o(() => {
        }, comment._id)
      });
    })
  }, {
    J: $data.showDeleteBtn && $options.canDeleteComment($data.selectedComment)
  }, $data.showDeleteBtn && $options.canDeleteComment($data.selectedComment) ? {
    K: common_vendor.p({
      type: "trash-filled",
      size: "16",
      color: "#fff"
    }),
    L: common_vendor.s($options.deleteButtonStyle),
    M: common_vendor.o(($event) => $options.showDeleteCommentConfirm($data.selectedComment))
  } : {}, {
    N: common_vendor.o((...args) => $options.submitComment && $options.submitComment(...args)),
    O: $data.commentText,
    P: common_vendor.o(($event) => $data.commentText = $event.detail.value),
    Q: common_vendor.p({
      type: "paperplane-filled",
      size: "20",
      color: "#8a2be2"
    }),
    R: common_vendor.o((...args) => $options.submitComment && $options.submitComment(...args)),
    S: common_vendor.o((...args) => $options.hideDeleteButton && $options.hideDeleteButton(...args)),
    T: common_vendor.o($options.confirmDelete),
    U: common_vendor.o($options.closeDeleteConfirm),
    V: common_vendor.p({
      title: "删除心愿",
      content: "确定要删除这个心愿吗？删除后无法恢复。",
      ["before-close"]: true
    }),
    W: common_vendor.sr("deletePopup", "1b547c0e-10"),
    X: common_vendor.p({
      type: "dialog"
    }),
    Y: common_vendor.o($options.confirmDeleteComment),
    Z: common_vendor.o($options.closeDeleteCommentConfirm),
    aa: common_vendor.p({
      title: "删除评论",
      content: "确定要删除这条评论吗？删除后无法恢复。",
      ["before-close"]: true
    }),
    ab: common_vendor.sr("deleteCommentPopup", "1b547c0e-12"),
    ac: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpkg-wish/pages/wishDetail/wishDetail.js.map

{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "../../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n\t<view class=\"login-container\">\n\t\t<view class=\"login-header\">\n\t\t\t<image class=\"login-logo\" src=\"/static/logo.png\" mode=\"aspectFit\"></image>\n\t\t\t<view class=\"login-title\">心愿清单</view>\n\t\t\t<view class=\"login-subtitle\">记录每一个美好愿望</view>\n\t\t</view>\n\t\t\n\t\t<!-- 主登录/注册表单 -->\n\t\t<view class=\"login-form card light-theme-card\" v-if=\"!showProfileCompletionForm\">\n\t\t\t<!-- Simplified for third-party login -->\n\t\t\t<view class=\"form-content simplified-login\">\n\t\t\t\t<view class=\"simplified-title\">请选择登录方式</view>\n\t\t\t\t<view class=\"third-party-login standalone\">\n\t\t\t\t\t<view class=\"third-party-btns\">\n\t\t\t\t\t\t<view class=\"third-party-btn wechat-btn\" @click=\"thirdPartyLogin('wechat')\">\n\t\t\t\t\t\t\t<uni-icons type=\"weixin\" size=\"26\" color=\"#FFF\"></uni-icons>\n\t\t\t\t\t\t\t<text>微信登录</text>\n\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"third-party-btn alipay-btn\" @click=\"thirdPartyLogin('alipay')\">\n\t\t\t\t\t\t\t<image src=\"/static/icons/alipay_logo_white.png\" class=\"third-party-icon\"></image>\n\t\t\t\t\t\t\t<text>支付宝登录</text>\n\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\t</view>\n\t\t\n\t\t<!-- 用户信息补充表单 -->\n\t\t<view class=\"profile-completion-form card light-theme-card\" v-if=\"showProfileCompletionForm\">\n\t\t\t<view class=\"form-content\">\n\t\t\t\t<view class=\"form-title\">完善您的资料</view>\n\t\t\t\t<view class=\"form-subtitle\">设置您的头像和昵称，让我们更好地认识您</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item avatar-chooser\">\n\t\t\t\t\t<view class=\"label\">选择头像</view>\n\t\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" class=\"avatar-btn\">\n\t\t\t\t\t\t<image :src=\"tempAvatarFile ? tempAvatarFile.path : (userStore.avatarUrl || '/static/default_avatar.png')\" class=\"preview-avatar\"></image>\n\t\t\t\t\t\t<view class=\"choose-text\">点击选择</view>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"label\">昵称</view>\n\t\t\t\t\t<input type=\"nickname\" v-model=\"tempNickname\" class=\"input-nickname\" placeholder=\"请输入或选择昵称\" @blur=\"onNicknameInputBlur\" />\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<button class=\"submit-btn primary-btn\" @click=\"submitUserProfile\">保存并继续</button>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 服务协议 -->\n\t\t<view class=\"agreement\" v-if=\"!showProfileCompletionForm\">\n\t\t\t登录即代表您同意<text class=\"agreement-link\">《用户协议》</text>和<text class=\"agreement-link\">《隐私政策》</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { useUserStore } from '@/store/user.js'\n\timport loadingManager from '@/utils/loadingManager.js'\n\t\n\texport default {\n\t\tsetup() {\n\t\t\tconst userStore = useUserStore()\n\t\t\treturn { userStore }\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\terrors: {},\n\t\t\t\tshowProfileCompletionForm: false, // 控制是否显示补充信息表单\n\t\t\t\ttempAvatarFile: null, // Stores { path: string, name?: string, newUpload?: boolean }\n\t\t\t\ttempNickname: '',\n\t\t\t\tredirectUrl: null, // To store the redirect path from query params\n\t\t\t\tformData: {\n\t\t\t\t\tusername: '',\n\t\t\t\t\tpassword: ''\n\t\t\t\t},\n\t\t\t\trules: {\n\t\t\t\t\tusername: {\n\t\t\t\t\t\trules: [{\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\terrorMessage: '请输入账号',\n\t\t\t\t\t\t}]\n\t\t\t\t\t},\n\t\t\t\t\tpassword: {\n\t\t\t\t\t\trules: [{\n\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\terrorMessage: '请输入密码',\n\t\t\t\t\t\t}]\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\toptions: {} // 用于存储 onLoad 传入的 options\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.userStore = useUserStore();\n\t\t},\n\t\tonLoad(options) {\n\t\t\tconsole.log('[Login Page] onLoad called with options:', options);\n\t\t\tthis.options = options;\n\t\t\t\n\t\t\t// 调试：确认页面加载状态\n\t\t\tconsole.log('[Login Page] userStore state:', {\n\t\t\t\tisLogin: this.userStore.isLogin,\n\t\t\t\ttoken: this.userStore.token ? 'EXISTS' : 'NULL',\n\t\t\t\tuserInfo: this.userStore.userInfo\n\t\t\t});\n\t\t\t\n\t\t\t// 如果直接在登录页，且已登录，根据业务需求决定是否自动跳转\n\t\t\tif (this.userStore.checkLoginStatus()) {\n\t\t\t\tconsole.log('[Login Page] User already logged in. Redirecting...');\n\t\t\t\tconst redirectUrl = decodeURIComponent(this.options.redirect || '') || '/pages/index/index';\n\t\t\t\tthis.handleRedirect(redirectUrl);\n\t\t\t}\n\t\t\tif (options && options.redirect) {\n\t\t\t\tthis.redirectUrl = decodeURIComponent(options.redirect);\n\t\t\t\tconsole.log('[Login Page] Redirect URL from query:', this.redirectUrl);\n\t\t\t}\n\t\t},\n\t\t\n\t\tonShow() {\n\t\t\tconsole.log('[Login Page] onShow called');\n\t\t\tconsole.log('[Login Page] Current showProfileCompletionForm:', this.showProfileCompletionForm);\n\t\t},\n\t\tmethods: {\n\t\t\tasync thirdPartyLogin(provider) {\n\t\t\t\tlet loadingShown = false;\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '登录中...' });\n\t\t\t\t\tloadingShown = true;\n\t\t\t\t\t\n\t\t\t\t\tconst uniIdCo = uniCloud.importObject('uni-id-co', {\n\t\t\t\t\t\tcustomUI: true \n\t\t\t\t\t});\n\n\t\t\t\t\tlet loginResult;\n\t\t\t\t\tif (provider === 'wechat') {\n\t\t\t\t\t\tconst loginRes = await uni.login({ provider: 'weixin' });\n\t\t\t\t\t\tloginResult = await uniIdCo.loginByWeixin({ code: loginRes.code });\n\t\t\t\t\t} else if (provider === 'alipay') {\n\t\t\t\t\t\tconst loginRes = await uni.login({ provider: 'alipay' });\n\t\t\t\t\t\tloginResult = await uniIdCo.loginByAlipay(loginRes); \n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error('不支持的登录方式');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('uni-id loginResult raw:', JSON.stringify(loginResult));\n\n\t\t\t\t\tif (loginResult.errCode === 0 && loginResult.newToken && loginResult.newToken.token) { \n\t\t\t\t\t\t// 1. 构建完整的userInfo，确保包含uid\n\t\t\t\t\t\tconst completeUserInfo = {\n\t\t\t\t\t\t\t...(loginResult.userInfo || {}),\n\t\t\t\t\t\t\tuid: loginResult.uid  // 确保uid被正确设置\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 2. 调用新的 loginSuccess action，并适配参数结构\n\t\t\t\t\t\tawait this.userStore.loginSuccess({\n\t\t\t\t\t\t\ttoken: loginResult.newToken.token,\n\t\t\t\t\t\t\tuserInfo: completeUserInfo,\n\t\t\t\t\t\t\ttokenExpired: loginResult.newToken.tokenExpired\n\t\t\t\t\t\t}, true); // 真正的登录场景，显示登录成功提示\n\n\t\t\t\t\t\tconsole.log('[Login] loginSuccess completed, waiting for data sync...');\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 等待一小段时间确保依赖stores初始化完成\n\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 1000));\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 调试：确认登录后的状态\n\t\t\t\t\t\tconsole.log('[Login] After loginSuccess and data sync:');\n\t\t\t\t\t\tconsole.log('  - userStore.isLogin:', this.userStore.isLogin);\n\t\t\t\t\t\tconsole.log('  - userStore.userId:', this.userStore.userId);\n\t\t\t\t\t\tconsole.log('  - userStore.token:', this.userStore.token ? `EXISTS (${this.userStore.token.length} chars)` : 'NULL');\n\t\t\t\t\t\tconsole.log('  - userInfo with uid:', JSON.stringify(this.userStore.userInfo));\n\n\t\t\t\t\t\t// 3. 主动从数据库获取该 uid 的完整用户信息 (这部分逻辑可以保留，但要确保在 loginSuccess 后执行)\n\t\t\t\t\t\tlet completeUserInfoFromDB = null;\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 确保 userStore.isLogin 和 userStore.userId 在 loginSuccess 后已正确设置\n\t\t\t\t\t\t\tif (this.userStore.isLogin && this.userStore.userId) {\n\t\t\t\t\t\t\t\tconst usersTable = uniCloud.database().collection('uni-id-users');\n\t\t\t\t\t\t\t\tconst userRecord = await usersTable.where({ _id: this.userStore.userId }) // 改为客户端可用的查询方式\n\t\t\t\t\t\t\t\t\t\t\t\t\t .field({ \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t 'nickname': true,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t 'avatar_file': true,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t 'avatar': true,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t 'avatarUrl': true,\n\t\t\t\t\t\t\t\t\t\t\t\t\t })\n\t\t\t\t\t\t\t\t\t\t\t\t\t .limit(1)\n\t\t\t\t\t\t\t\t\t\t\t\t\t .get();\n\t\t\t\t\t\t\t\tconsole.log('Fetched user from DB:', JSON.stringify(userRecord));\n\t\t\t\t\t\t\t\tif (userRecord.result && userRecord.result.data && userRecord.result.data.length > 0) {\n\t\t\t\t\t\t\t\t\tcompleteUserInfoFromDB = userRecord.result.data[0];\n\t\t\t\t\t\t\t\t\tthis.userStore.updateUserInfo(completeUserInfoFromDB); // 使用已有的 updateUserInfo action\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.warn('User not fully logged in after loginSuccess for DB query, UID:', this.userStore.userId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (dbError) {\n\t\t\t\t\t\t\tconsole.error(\"Failed to fetch user info from DB after login:\", dbError);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 4. 基于 userStore 中更新后的 userInfo 进行判断\n\t\t\t\t\t\tconst finalUserInfoToCheck = this.userStore.userInfo; \n\t\t\t\t\t\tconsole.log('Final userInfo for completion check:', JSON.stringify(finalUserInfoToCheck));\n\n\t\t\t\t\t\tconst hasNickname = finalUserInfoToCheck && finalUserInfoToCheck.nickname && finalUserInfoToCheck.nickname !== '微信用户' && finalUserInfoToCheck.nickname.trim() !== '';\n\t\t\t\t\t\tconst hasAvatar = finalUserInfoToCheck && \n\t\t\t\t\t\t\t\t\t\t  ( (finalUserInfoToCheck.avatar_file && finalUserInfoToCheck.avatar_file.url && !finalUserInfoToCheck.avatar_file.url.includes('defaultAvatar.png') && finalUserInfoToCheck.avatar_file.url.trim() !== '') ||\n\t\t\t\t\t\t\t\t\t\t\t(finalUserInfoToCheck.avatarUrl && !finalUserInfoToCheck.avatarUrl.includes('defaultAvatar.png') && finalUserInfoToCheck.avatarUrl.trim() !== '') ||\n\t\t\t\t\t\t\t\t\t\t\t(finalUserInfoToCheck.avatar && !finalUserInfoToCheck.avatar.includes('defaultAvatar.png') && finalUserInfoToCheck.avatar.trim() !== '')\n\t\t\t\t\t\t\t\t\t\t  );\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('hasNickname:', hasNickname, 'hasAvatar:', hasAvatar);\n\t\t\t\t\t\tconst isUserInfoComplete = hasNickname && hasAvatar;\n\n\t\t\t\t\t\t// 在导航前先隐藏loading\n\t\t\t\t\t\tif (loadingShown) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tloadingShown = false;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (isUserInfoComplete) {\n\t\t\t\t\t\t\tuni.showToast({ icon: 'success', title: '登录成功' });\n\t\t\t\t\t\t\tthis.navigateToNextPage();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.tempNickname = (finalUserInfoToCheck && finalUserInfoToCheck.nickname && finalUserInfoToCheck.nickname !== '微信用户') ? finalUserInfoToCheck.nickname : '';\n\t\t\t\t\t\t\t// tempAvatarFile 仍将在 onChooseAvatar 中设置，初始时不需要从 finalUserInfoToCheck 获取头像给 tempAvatarFile\n\t\t\t\t\t\t\t// 因为完善资料表单的头像预览会通过 userStore.avatarUrl (如果已存在)\n\t\t\t\t\t\t\tthis.showProfileCompletionForm = true;\n\t\t\t\t\t\t\tuni.showToast({ icon: 'none', title: '请完善您的头像和昵称' });\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 在错误提示前先隐藏loading\n\t\t\t\t\t\tif (loadingShown) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tloadingShown = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.error('uni-id-co login error:', loginResult);\n\t\t\t\t\t\tuni.showToast({ icon: 'none', title: loginResult.errMsg || '登录失败，请稍后重试' });\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// 在错误提示前先隐藏loading\n\t\t\t\t\tif (loadingShown) {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tloadingShown = false;\n\t\t\t\t\t}\n\t\t\t\t\tconsole.error(\"Client-side third party login error:\", error);\n\t\t\t\t\tlet errMsg = '登录失败，请检查网络或稍后重试';\n\t\t\t\t\tif (error.errMsg) { \n\t\t\t\t\t\terrMsg = error.errMsg;\n\t\t\t\t\t}\n\t\t\t\t\tuni.showToast({ icon: 'none', title: errMsg, duration: 3000 });\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tnavigateToNextPage() {\n\t\t\t\tif (this.redirectUrl) {\n\t\t\t\t\tconsole.log('[Login Page] Navigating to redirectUrl:', this.redirectUrl);\n\t\t\t\t\tthis.handleRedirect(this.redirectUrl);\n\t\t\t\t} else {\n\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\tif (pages.length > 1) {\n\t\t\t\t\t\tconsole.log('[Login Page] Navigating back.');\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('[Login Page] Relaunching to index.');\n\t\t\t\t\tuni.reLaunch({ url: '/pages/index/index' });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonChooseAvatar(e) {\n\t\t\t\tconsole.log('Avatar chosen:', e.detail.avatarUrl);\n\t\t\t\tif (e.detail.avatarUrl) {\n\t\t\t\t\tthis.tempAvatarFile = {\n\t\t\t\t\t\tpath: e.detail.avatarUrl,\n\t\t\t\t\t\tname: `avatar_${Date.now()}.${e.detail.avatarUrl.split('.').pop() || 'png'}`,\n\t\t\t\t\t\tnewUpload: true // Mark that this is a new file to be uploaded\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonNicknameInputBlur(e) {\n\t\t\t\tthis.tempNickname = e.detail.value.trim();\n\t\t\t\tconsole.log('Nickname input:', this.tempNickname);\n\t\t\t\t// Can add basic validation here if needed, e.g., length check\n\t\t\t},\n\n\t\t\tasync submitUserProfile() {\n\t\t\t\tconst nicknameUnchanged = !this.tempNickname || this.tempNickname === this.userStore.nickname;\n\t\t\t\tconst avatarUnchanged = !this.tempAvatarFile || !this.tempAvatarFile.newUpload;\n\n\t\t\t\tif (nicknameUnchanged && avatarUnchanged) {\n\t\t\t\t\tthis.navigateToNextPage();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tlet loadingShown = false;\n\t\t\t\tconst updateData = {};\n\t\t\t\tconst updatedFieldsForStore = {};\n\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '正在保存...' });\n\t\t\t\t\tloadingShown = true;\n\n\t\t\t\t\t// 1. 上传新头像 (如果选择了)\n\t\t\t\t\tif (this.tempAvatarFile && this.tempAvatarFile.newUpload) {\n\t\t\t\t\t\tconst uploadResult = await uniCloud.uploadFile({\n\t\t\t\t\t\t\tfilePath: this.tempAvatarFile.path,\n\t\t\t\t\t\t\tcloudPath: `user_avatars/${this.userStore.userId}/${this.tempAvatarFile.name}`,\n\t\t\t\t\t\t\tonProgressUpdate: (progressEvent) => {}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log('Upload result:', uploadResult);\n\t\t\t\t\t\tif (uploadResult.fileID) {\n\t\t\t\t\t\t\tupdateData.avatar_file = { url: uploadResult.fileID }; // Update avatar_file field\n\t\t\t\t\t\t\tupdatedFieldsForStore.avatarUrl = uploadResult.fileID; // For local store, assuming avatarUrl holds the direct URL\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet errMsg = '头像上传失败';\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tconst parsedError = JSON.parse(uploadResult.data);\n\t\t\t\t\t\t\t\tif(parsedError && parsedError.message) errMsg = parsedError.message;\n\t\t\t\t\t\t\t} catch (e) {}\n\t\t\t\t\t\t\tthrow new Error(errMsg);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// 2. 准备昵称更新数据 (如果提供了且有变动)\n\t\t\t\t\tif (this.tempNickname && this.tempNickname !== this.userStore.nickname) {\n\t\t\t\t\t\tupdateData.nickname = this.tempNickname;\n\t\t\t\t\t\tupdatedFieldsForStore.nickname = this.tempNickname;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 3. 如果有数据需要更新，则执行数据库操作\n\t\t\t\t\tif (Object.keys(updateData).length > 0) {\n\t\t\t\t\t\tconsole.log('Updating user data in DB:', JSON.stringify(updateData));\n\t\t\t\t\t\tconst usersTable = uniCloud.database().collection('uni-id-users');\n\t\t\t\t\t\tconst dbResult = await usersTable.where('_id==$env.uid').update(updateData);\n\t\t\t\t\t\tconsole.log('DB update result:', dbResult);\n\n\t\t\t\t\t\tif (dbResult.result && dbResult.result.updated > 0) {\n\t\t\t\t\t\t\t// 更新成功或部分成功\n\t\t\t\t\t\t} else if (dbResult.result && dbResult.result.updated === 0 && Object.keys(updateData).length > 0) {\n\t\t\t\t\t\t\t// 数据未发生实际改变，但也视为操作完成\n\t\t\t\t\t\t\tconsole.log('Data not changed in DB, but operation considered complete.');\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 更新失败，需要获取更详细的错误信息\n\t\t\t\t\t\t\tlet errMsg = '用户信息更新失败';\n\t\t\t\t\t\t\tif(dbResult.result && dbResult.result.errCode) errMsg = `错误码: ${dbResult.result.errCode}, ${dbResult.result.errMsg}`;\n\t\t\t\t\t\t\telse if (dbResult.errMsg) errMsg = dbResult.errMsg;\n\t\t\t\t\t\t\tthrow new Error(errMsg);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 此分支理论上不应该进入，因为我们在开始时已经检查过是否有变动\n\t\t\t\t\t\tconsole.log('No data to update after processing.');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t// 4. 更新本地 Store (如果数据库操作影响了数据)\n\t\t\t\t\tif (Object.keys(updatedFieldsForStore).length > 0) {\n\t\t\t\t\t\tthis.userStore.updateUserInfo(updatedFieldsForStore);\n\t\t\t\t\t}\n\n\t\t\t\t\t// 先隐藏loading，再显示成功提示\n\t\t\t\t\tif (loadingShown) {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tloadingShown = false;\n\t\t\t\t\t}\n\t\t\t\t\tuni.showToast({ title: '信息保存成功', icon: 'success' });\n\t\t\t\t\tthis.navigateToNextPage();\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// 先隐藏loading，再显示错误提示\n\t\t\t\t\tif (loadingShown) {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tloadingShown = false;\n\t\t\t\t\t}\n\t\t\t\t\tconsole.error('Submit user profile error:', error);\n\t\t\t\t\tuni.showToast({ icon: 'none', title: error.message || '信息保存失败，请重试', duration: 3000 });\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsubmitLogin() {\n\t\t\t\tthis.$refs.form.validate().then(async (res) => {\n\t\t\t\t\tconsole.log('表单数据', res);\n\t\t\t\t\tlet loadingShown = false;\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.showLoading({ title: '登录中...' });\n\t\t\t\t\t\tloadingShown = true;\n\t\t\t\t\t\t\n\t\t\t\t\t\tconst loginRes = await uniCloud.callFunction({\n\t\t\t\t\t\t\tname: 'uni-id-co',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\taction: 'login',\n\t\t\t\t\t\t\t\tusername: this.formData.username,\n\t\t\t\t\t\t\t\tpassword: this.formData.password,\n\t\t\t\t\t\t\t\t// type: 'password' // login方法内部会根据参数判断\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log('[Login Page] uni-id-co login response:', loginRes);\n\n\t\t\t\t\t\tif (loginRes.errCode === 0 && loginRes.token) {\n\t\t\t\t\t\t\t// 先隐藏loading，再显示成功提示\n\t\t\t\t\t\t\tif (loadingShown) {\n\t\t\t\t\t\t\t\tuni.hideLoading().catch(() => {})\n\t\t\t\t\t\t\t\tloadingShown = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tuni.showToast({ title: '登录成功', icon: 'success' });\n\n\t\t\t\t\t\t\t// 调用 userStore.loginSuccess，它会处理状态、存储和 postLoginAction\n\t\t\t\t\t\t\tawait this.userStore.loginSuccess({\n\t\t\t\t\t\t\t\ttoken: loginRes.token,\n\t\t\t\t\t\t\t\tuserInfo: loginRes.userInfo, // login接口通常会返回userInfo\n\t\t\t\t\t\t\t\ttokenExpired: loginRes.tokenExpired\n\t\t\t\t\t\t\t}, true); // 真正的登录场景，显示登录成功提示\n\n\t\t\t\t\t\t\t// 等待 postLoginAction 可能的异步操作（如果存在且是异步的）\n\t\t\t\t\t\t\t// 这是一个简化处理，实际可能需要更复杂的机制来等待postLoginAction完成\n\t\t\t\t\t\t\t// 如果 postLoginAction 自身处理导航，则后续的 handleRedirect 可能不需要执行\n\t\t\t\t\t\t\t// 假设 postLoginAction 执行很快，或者不处理导航\n\t\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100)); \n\n\t\t\t\t\t\t\t// 只有当 postLoginAction 没有主动导航时，才执行这里的默认重定向逻辑\n\t\t\t\t\t\t\t// 可以通过检查一个标志位（例如 userStore.navigatedByPostLoginAction）\n\t\t\t\t\t\t\t// 这里简化为总是尝试重定向，如果postLoginAction已导航，则后续的uni.reLaunch/switchTab可能无效果或被覆盖\n\t\t\t\t\t\t\tconst redirectUrl = decodeURIComponent(this.options.redirect || '') || '/pages/index/index';\n\t\t\t\t\t\t\tthis.handleRedirect(redirectUrl);\n\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 先隐藏loading，再显示错误提示\n\t\t\t\t\t\t\tif (loadingShown) {\n\t\t\t\t\t\t\t\tuni.hideLoading().catch(() => {})\n\t\t\t\t\t\t\t\tloadingShown = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tuni.showToast({ title: loginRes.errMsg || '登录失败，请检查您的账号密码', icon: 'none', duration: 3000 });\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t// 先隐藏loading，再显示错误提示\n\t\t\t\t\t\tif (loadingShown) {\n\t\t\t\t\t\t\tuni.hideLoading().catch(() => {})\n\t\t\t\t\t\t\tloadingShown = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.error('[Login Page] Login error:', error);\n\t\t\t\t\t\tlet errMsg = '登录失败，请稍后再试';\n\t\t\t\t\t\tif(error.errMsg) errMsg = error.errMsg;\n\t\t\t\t\t\tif(error.message) errMsg = error.message;\n\t\t\t\t\t\tuni.showToast({ title: errMsg, icon: 'none', duration: 3000 });\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.log('表单错误', err);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\thandleRedirect(url) {\n\t\t\t\tconst switchTabPages = [\n\t\t\t\t\t'/pages/index/index',\n\t\t\t\t\t'/pages/profile/profile',\n\t\t\t\t\t'/pages/message/message'\n\t\t\t\t];\n\t\t\t\tconst targetPath = url.split('?')[0];\n\n\t\t\t\tif (switchTabPages.includes(targetPath)) {\n\t\t\t\t\tconsole.log(`[Login Page] Redirecting to Tab: ${url}`);\n\t\t\t\t\tuni.switchTab({ url: targetPath }); // switchTab 不支持参数，参数需自行处理\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log(`[Login Page] Redirecting to Page: ${url}`);\n\t\t\t\t\tuni.reLaunch({ url }); // reLaunch 可以关闭登录页，避免返回\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tnavigateToRegister() {\n\t\t\t\tuni.navigateTo({ url: '/pages/login/register' }); // 假设有注册页\n\t\t\t},\n\t\t\tnavigateToForgotPassword() {\n\t\t\t\t// uni.navigateTo({ url: '/pages/login/forgot-password' }); // 假设有忘记密码页\n\t\t\t\tuni.showToast({title:'功能待开发', icon:'none'});\n\t\t\t},\n\t\t\t// loginByWeixin() { ... }\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t// 基础变量 (可以考虑提升到全局 SCSS 变量)\n\t$primary-color: #8A2BE2; // 紫罗兰色，根据项目实际主色调整\n\t$primary-color-light: #9A4FED; // lighten($primary-color, 10%)的静态值\n\t$primary-color-dark: #7A1FCF; // darken($primary-color, 10%)的静态值\n\t$text-color-primary: #333;\n\t$text-color-secondary: #666;\n\t$text-color-light: #999;\n\t$card-bg-color: #FFFFFF;\n\t$page-bg-color: #F8F8F8; // 与现有背景一致\n\t$border-color: #EFEFEF;\n\t$success-color: #4CAF50; // 微信绿\n\t$info-color: #007BFF; // 支付宝蓝\n\t$white-color: #FFFFFF;\n\t$common-border-radius: 16rpx; // 通用圆角\n\t$button-height: 88rpx;\n\n\t.login-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\t// justify-content: center; // 改为 flex-start 以便内容从顶部开始排列，如果内容少则顶部对齐\n\t\tjustify-content: flex-start;\n\t\tmin-height: 100vh;\n\t\tpadding: 40rpx 30rpx;\n\t\tbackground-color: $page-bg-color;\n\t\tbackground-color: #F8F8F8; // fallback\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t.login-header {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmargin-bottom: 60rpx; // 增加与下方卡片的间距\n\t\t\n\t\t.login-logo {\n\t\t\twidth: 160rpx;\n\t\t\theight: 160rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\t\t\n\t\t.login-title {\n\t\t\tfont-size: 48rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: $primary-color;\n\t\t\tmargin-bottom: 10rpx;\n\t\t}\n\t\t\n\t\t.login-subtitle {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: $text-color-secondary;\n\t\t}\n\t}\n\t\n\t// 通用卡片基础样式 (如果全局已定义 card 和 light-theme-card，则这些可酌情删减或作为补充)\n\t.card {\n\t\twidth: 100%;\n\t\tbackground-color: $card-bg-color;\n\t\tbackground-color: #FFFFFF; // fallback\n\t\tborder-radius: $common-border-radius * 1.5; // 卡片圆角稍大\n\t\tborder-radius: 24rpx; // fallback\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\tmargin-bottom: 40rpx;\n\t\tpadding: 40rpx; // 卡片内部统一padding\n\t\tbox-sizing: border-box;\n\t}\n\n\t.light-theme-card { // 确保这个类名在全局有定义，或者在这里补充具体样式\n\t\t// 通常是白色背景，浅色阴影\n\t}\n\t\n\t// 主登录表单卡片 (选择登录方式)\n\t.login-form {\n\t\t.form-content {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t}\n\t\t.simplified-title {\n\t\t\tfont-size: 36rpx;\n\t\t\tcolor: $text-color-primary;\n\t\t\tmargin-bottom: 50rpx; // 标题与按钮组的间距\n\t\t\tfont-weight: 500;\n\t\t}\n\n\t\t.third-party-login { // .standalone 类似乎可以移除，直接作用于 .third-party-login\n\t\t\twidth: 100%;\n\t\t\t.third-party-btns {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column; // 按钮垂直排列\n\t\t\t\tgap: 30rpx; // 按钮之间的间距\n\n\t\t\t\t.third-party-btn {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\theight: $button-height;\n\t\t\t\t\theight: 88rpx; // fallback\n\t\t\t\t\tborder-radius: $common-border-radius;\n\t\t\t\t\tborder-radius: 16rpx; // fallback\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: $white-color;\n\t\t\t\t\tcolor: #FFFFFF; // fallback\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\n\t\t\t\t\ttransition: opacity 0.2s;\n\n\t\t\t\t\tuni-icons, .third-party-icon {\n\t\t\t\t\t\tmargin-right: 16rpx;\n\t\t\t\t}\n\t\t\t\t\t.third-party-icon { // 支付宝图标大小调整\n\t\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.wechat-btn {\n\t\t\t\t\tbackground-color: $success-color; // 使用定义的微信绿\n\t\t\t\t\tbackground-color: #4CAF50; // fallback\n\t\t\t\t}\n\n\t\t\t\t.alipay-btn {\n\t\t\t\t\tbackground-color: $info-color; // 使用定义的支付宝蓝\n\t\t\t\t\tbackground-color: #007BFF; // fallback\n\t\t\t\t\t// 如果支付宝图标是白色背景，可能需要调整图片或按钮背景\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\n\t// 用户信息补充表单卡片\n\t.profile-completion-form {\n\t\t.form-content {\n\t\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t}\n\t\t.form-title {\n\t\t\tfont-size: 40rpx;\n\t\t\tcolor: $text-color-primary;\n\t\t\tfont-weight: bold;\n\t\t\ttext-align: center;\n\t\t\tmargin-bottom: 15rpx;\n\t\t}\n\n\t\t.form-subtitle {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: $text-color-secondary;\n\t\t\ttext-align: center;\n\t\t\tmargin-bottom: 50rpx;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t.form-item {\n\t\t\tmargin-bottom: 40rpx;\n\t\t\t.label {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: $text-color-primary;\n\t\t\t\tmargin-bottom: 15rpx;\n\t\t\t\tdisplay: block;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t.avatar-chooser {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center; // 头像选择整体居中\n\t\t\t.label {\n\t\t\t\ttext-align: center; // \"选择头像\" 文字居中\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t\t.avatar-btn {\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tborder: none;\n\t\t\t\tpadding: 0;\n\t\t\t\tmargin: 0;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\twidth: auto; // 避免按钮宽度撑满\n\n\t\t\t\t&::after { // 移除 button 默认边框\n\t\t\t\tborder: none;\n\t\t\t\t}\n\n\t\t\t\t.preview-avatar {\n\t\t\t\t\twidth: 180rpx;\n\t\t\t\t\theight: 180rpx;\n\t\t\t\t\tborder-radius: 50%; // 圆形头像\n\t\t\t\t\tmargin-bottom: 15rpx;\n\t\t\t\t\tborder: 2rpx solid $border-color;\n\t\t\t\t\tbackground-color: $page-bg-color; // 默认头像背景\n\t\t\t\t}\n\t\t\t\t.choose-text {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.input-nickname {\n\t\t\theight: $button-height;\n\t\t\tbackground-color: $page-bg-color; // 浅色背景\n\t\t\tborder-radius: $common-border-radius;\n\t\t\tpadding: 0 30rpx;\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: $text-color-primary;\n\t\t\tborder: 1rpx solid $border-color;\n\t\t\t&:focus {\n\t\t\t\tborder-color: $primary-color;\n\t\t\t\tbackground-color: $white-color;\n\t\t\t}\n\t\t}\n\n\t\t.submit-btn { // .primary-btn 已有，这里主要调整尺寸和特定样式\n\t\t\theight: $button-height;\n\t\t\tline-height: $button-height;\n\t\t\tborder-radius: $common-border-radius;\n\t\t\tbackground-image: linear-gradient(to right, $primary-color-light, $primary-color); // 紫色渐变\n\t\t\tcolor: $white-color;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tborder: none; // 移除默认边框\n\t\t\tbox-shadow: 0 4rpx 15rpx rgba($primary-color, 0.3);\n\t\t\tmargin-top: 20rpx; // 与上方元素的间距\n\n\t\t\t&::after { // 移除 button 默认边框\n\t\t\t\tborder: none;\n\t\t\t}\n\t\t\t&:active {\n\t\t\t\topacity: 0.8;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.agreement {\n\t\tmargin-top: 60rpx; // 与上方卡片的间距\n\t\tfont-size: 24rpx;\n\t\tcolor: $text-color-light;\n\t\ttext-align: center;\n\t\t.agreement-link {\n\t\t\tcolor: $primary-color;\n\t\t\ttext-decoration: none; // 通常小程序不支持 text-decoration\n\t\t}\n\t}\n\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/wishlist-uniapp/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "uni", "uniCloud"], "mappings": ";;;;AA8DC,MAAK,YAAU;AAAA,EACd,QAAQ;AACP,UAAM,YAAYA,WAAAA,aAAa;AAC/B,WAAO,EAAE,UAAU;AAAA,EACnB;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,QAAQ,CAAE;AAAA,MACV,2BAA2B;AAAA;AAAA,MAC3B,gBAAgB;AAAA;AAAA,MAChB,cAAc;AAAA,MACd,aAAa;AAAA;AAAA,MACb,UAAU;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,MACV;AAAA,MACD,OAAO;AAAA,QACN,UAAU;AAAA,UACT,OAAO,CAAC;AAAA,YACP,UAAU;AAAA,YACV,cAAc;AAAA,WACd;AAAA,QACD;AAAA,QACD,UAAU;AAAA,UACT,OAAO,CAAC;AAAA,YACP,UAAU;AAAA,YACV,cAAc;AAAA,WACd;AAAA,QACF;AAAA,MACA;AAAA,MACD,SAAS,CAAG;AAAA;AAAA,IACb;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,YAAYA,WAAAA;EACjB;AAAA,EACD,OAAO,SAAS;AACfC,kBAAA,MAAA,MAAA,OAAA,gCAAY,4CAA4C,OAAO;AAC/D,SAAK,UAAU;AAGfA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,iCAAiC;AAAA,MAC5C,SAAS,KAAK,UAAU;AAAA,MACxB,OAAO,KAAK,UAAU,QAAQ,WAAW;AAAA,MACzC,UAAU,KAAK,UAAU;AAAA,IAC1B,CAAC;AAGD,QAAI,KAAK,UAAU,oBAAoB;AACtCA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,qDAAqD;AACjE,YAAM,cAAc,mBAAmB,KAAK,QAAQ,YAAY,EAAE,KAAK;AACvE,WAAK,eAAe,WAAW;AAAA,IAChC;AACA,QAAI,WAAW,QAAQ,UAAU;AAChC,WAAK,cAAc,mBAAmB,QAAQ,QAAQ;AACtDA,oBAAA,MAAA,MAAA,OAAA,gCAAY,yCAAyC,KAAK,WAAW;AAAA,IACtE;AAAA,EACA;AAAA,EAED,SAAS;AACRA,kBAAAA,MAAY,MAAA,OAAA,gCAAA,4BAA4B;AACxCA,kBAAA,MAAA,MAAA,OAAA,gCAAY,mDAAmD,KAAK,yBAAyB;AAAA,EAC7F;AAAA,EACD,SAAS;AAAA,IACR,MAAM,gBAAgB,UAAU;AAC/B,UAAI,eAAe;AACnB,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,uBAAe;AAEf,cAAM,UAAUC,cAAAA,GAAS,aAAa,aAAa;AAAA,UAClD,UAAU;AAAA,QACX,CAAC;AAED,YAAI;AACJ,YAAI,aAAa,UAAU;AAC1B,gBAAM,WAAW,MAAMD,oBAAI,MAAM,EAAE,UAAU,SAAS,CAAC;AACvD,wBAAc,MAAM,QAAQ,cAAc,EAAE,MAAM,SAAS,KAAK,CAAC;AAAA,mBACvD,aAAa,UAAU;AACjC,gBAAM,WAAW,MAAMA,oBAAI,MAAM,EAAE,UAAU,SAAS,CAAC;AACvD,wBAAc,MAAM,QAAQ,cAAc,QAAQ;AAAA,eAC5C;AACN,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC3B;AAEAA,4BAAY,MAAA,OAAA,gCAAA,2BAA2B,KAAK,UAAU,WAAW,CAAC;AAElE,YAAI,YAAY,YAAY,KAAK,YAAY,YAAY,YAAY,SAAS,OAAO;AAEpF,gBAAM,mBAAmB;AAAA,YACxB,GAAI,YAAY,YAAY,CAAE;AAAA,YAC9B,KAAK,YAAY;AAAA;AAAA;AAIlB,gBAAM,KAAK,UAAU,aAAa;AAAA,YACjC,OAAO,YAAY,SAAS;AAAA,YAC5B,UAAU;AAAA,YACV,cAAc,YAAY,SAAS;AAAA,UACnC,GAAE,IAAI;AAEPA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,0DAA0D;AAGtE,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAGtDA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,2CAA2C;AACvDA,2EAAY,0BAA0B,KAAK,UAAU,OAAO;AAC5DA,2EAAY,yBAAyB,KAAK,UAAU,MAAM;AAC1DA,2EAAY,wBAAwB,KAAK,UAAU,QAAQ,WAAW,KAAK,UAAU,MAAM,MAAM,YAAY,MAAM;AACnHA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,0BAA0B,KAAK,UAAU,KAAK,UAAU,QAAQ,CAAC;AAG7E,cAAI,yBAAyB;AAC7B,cAAI;AAEH,gBAAI,KAAK,UAAU,WAAW,KAAK,UAAU,QAAQ;AACpD,oBAAM,aAAaC,cAAAA,GAAS,SAAU,EAAC,WAAW,cAAc;AAChE,oBAAM,aAAa,MAAM,WAAW,MAAM,EAAE,KAAK,KAAK,UAAU,QAAQ,EACjE,MAAM;AAAA,gBACN,YAAY;AAAA,gBACZ,eAAe;AAAA,gBACf,UAAU;AAAA,gBACV,aAAa;AAAA,eACb,EACA,MAAM,CAAC,EACP;AACPD,kCAAA,MAAA,OAAA,gCAAY,yBAAyB,KAAK,UAAU,UAAU,CAAC;AAC/D,kBAAI,WAAW,UAAU,WAAW,OAAO,QAAQ,WAAW,OAAO,KAAK,SAAS,GAAG;AACrF,yCAAyB,WAAW,OAAO,KAAK,CAAC;AACjD,qBAAK,UAAU,eAAe,sBAAsB;AAAA,cACrD;AAAA,mBACM;AACNA,gFAAa,kEAAkE,KAAK,UAAU,MAAM;AAAA,YACrG;AAAA,UACD,SAAS,SAAS;AACjBA,0BAAc,MAAA,MAAA,SAAA,gCAAA,kDAAkD,OAAO;AAAA,UACxE;AAGA,gBAAM,uBAAuB,KAAK,UAAU;AAC5CA,2EAAY,wCAAwC,KAAK,UAAU,oBAAoB,CAAC;AAExF,gBAAM,cAAc,wBAAwB,qBAAqB,YAAY,qBAAqB,aAAa,UAAU,qBAAqB,SAAS,KAAI,MAAO;AAClK,gBAAM,YAAY,yBACT,qBAAqB,eAAe,qBAAqB,YAAY,OAAO,CAAC,qBAAqB,YAAY,IAAI,SAAS,mBAAmB,KAAK,qBAAqB,YAAY,IAAI,KAAI,MAAO,MACtM,qBAAqB,aAAa,CAAC,qBAAqB,UAAU,SAAS,mBAAmB,KAAK,qBAAqB,UAAU,KAAI,MAAO,MAC7I,qBAAqB,UAAU,CAAC,qBAAqB,OAAO,SAAS,mBAAmB,KAAK,qBAAqB,OAAO,KAAI,MAAO;AAG1IA,8BAAY,MAAA,OAAA,gCAAA,gBAAgB,aAAa,cAAc,SAAS;AAChE,gBAAM,qBAAqB,eAAe;AAG1C,cAAI,cAAc;AACjBA,0BAAG,MAAC,YAAW;AACf,2BAAe;AAAA,UAChB;AAEA,cAAI,oBAAoB;AACvBA,0BAAG,MAAC,UAAU,EAAE,MAAM,WAAW,OAAO,OAAK,CAAG;AAChD,iBAAK,mBAAkB;AAAA,iBACjB;AACN,iBAAK,eAAgB,wBAAwB,qBAAqB,YAAY,qBAAqB,aAAa,SAAU,qBAAqB,WAAW;AAG1J,iBAAK,4BAA4B;AACjCA,0BAAG,MAAC,UAAU,EAAE,MAAM,QAAQ,OAAO,aAAa,CAAC;AAAA,UACpD;AAAA,eAEM;AAEN,cAAI,cAAc;AACjBA,0BAAG,MAAC,YAAW;AACf,2BAAe;AAAA,UAChB;AACAA,wBAAc,MAAA,MAAA,SAAA,gCAAA,0BAA0B,WAAW;AACnDA,8BAAI,UAAU,EAAE,MAAM,QAAQ,OAAO,YAAY,UAAU,aAAW,CAAG;AAAA,QAC1E;AAAA,MACC,SAAO,OAAO;AAEf,YAAI,cAAc;AACjBA,wBAAG,MAAC,YAAW;AACf,yBAAe;AAAA,QAChB;AACAA,2EAAc,wCAAwC,KAAK;AAC3D,YAAI,SAAS;AACb,YAAI,MAAM,QAAQ;AACjB,mBAAS,MAAM;AAAA,QAChB;AACAA,4BAAI,UAAU,EAAE,MAAM,QAAQ,OAAO,QAAQ,UAAU,IAAK,CAAC;AAAA,MAC9D;AAAA,IACA;AAAA,IAED,qBAAqB;AACpB,UAAI,KAAK,aAAa;AACrBA,sBAAY,MAAA,MAAA,OAAA,gCAAA,2CAA2C,KAAK,WAAW;AACvE,aAAK,eAAe,KAAK,WAAW;AAAA,aAC9B;AACP,cAAM,QAAQ;AACd,YAAI,MAAM,SAAS,GAAG;AACpBA,wBAAAA,mDAAY,+BAA+B;AAC5CA,wBAAG,MAAC,aAAY;AAAA,eACV;AACLA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,oCAAoC;AACjDA,wBAAAA,MAAI,SAAS,EAAE,KAAK,qBAAsB,CAAA;AAAA,QAC1C;AAAA,MACD;AAAA,IACA;AAAA,IAED,eAAe,GAAG;AACjBA,uEAAY,kBAAkB,EAAE,OAAO,SAAS;AAChD,UAAI,EAAE,OAAO,WAAW;AACvB,aAAK,iBAAiB;AAAA,UACrB,MAAM,EAAE,OAAO;AAAA,UACf,MAAM,UAAU,KAAK,IAAK,CAAA,IAAI,EAAE,OAAO,UAAU,MAAM,GAAG,EAAE,IAAI,KAAK,KAAK;AAAA,UAC1E,WAAW;AAAA;AAAA;MAEb;AAAA,IACA;AAAA,IAED,oBAAoB,GAAG;AACtB,WAAK,eAAe,EAAE,OAAO,MAAM,KAAI;AACvCA,uEAAY,mBAAmB,KAAK,YAAY;AAAA,IAEhD;AAAA,IAED,MAAM,oBAAoB;AACzB,YAAM,oBAAoB,CAAC,KAAK,gBAAgB,KAAK,iBAAiB,KAAK,UAAU;AACrF,YAAM,kBAAkB,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe;AAErE,UAAI,qBAAqB,iBAAiB;AACzC,aAAK,mBAAkB;AACvB;AAAA,MACD;AAEA,UAAI,eAAe;AACnB,YAAM,aAAa,CAAA;AACnB,YAAM,wBAAwB,CAAA;AAE9B,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAW,CAAA;AACpC,uBAAe;AAGf,YAAI,KAAK,kBAAkB,KAAK,eAAe,WAAW;AACzD,gBAAM,eAAe,MAAMC,cAAQ,GAAC,WAAW;AAAA,YAC9C,UAAU,KAAK,eAAe;AAAA,YAC9B,WAAW,gBAAgB,KAAK,UAAU,MAAM,IAAI,KAAK,eAAe,IAAI;AAAA,YAC5E,kBAAkB,CAAC,kBAAkB;AAAA,YAAC;AAAA,UACvC,CAAC;AACDD,2EAAY,kBAAkB,YAAY;AAC1C,cAAI,aAAa,QAAQ;AACxB,uBAAW,cAAc,EAAE,KAAK,aAAa,OAAK;AAClD,kCAAsB,YAAY,aAAa;AAAA,iBACzC;AACN,gBAAI,SAAS;AACb,gBAAI;AACH,oBAAM,cAAc,KAAK,MAAM,aAAa,IAAI;AAChD,kBAAG,eAAe,YAAY;AAAS,yBAAS,YAAY;AAAA,qBACpD,GAAG;AAAA,YAAC;AACb,kBAAM,IAAI,MAAM,MAAM;AAAA,UACvB;AAAA,QACD;AAGA,YAAI,KAAK,gBAAgB,KAAK,iBAAiB,KAAK,UAAU,UAAU;AACvE,qBAAW,WAAW,KAAK;AAC3B,gCAAsB,WAAW,KAAK;AAAA,QACvC;AAGA,YAAI,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AACvCA,8BAAA,MAAA,OAAA,gCAAY,6BAA6B,KAAK,UAAU,UAAU,CAAC;AACnE,gBAAM,aAAaC,cAAAA,GAAS,SAAU,EAAC,WAAW,cAAc;AAChE,gBAAM,WAAW,MAAM,WAAW,MAAM,eAAe,EAAE,OAAO,UAAU;AAC1ED,2EAAY,qBAAqB,QAAQ;AAEzC,cAAI,SAAS,UAAU,SAAS,OAAO,UAAU,GAAG;AAAA,UAEpD,WAAW,SAAS,UAAU,SAAS,OAAO,YAAY,KAAK,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AAElGA,0BAAAA,MAAA,MAAA,OAAA,gCAAY,4DAA4D;AAAA,iBAClE;AAEN,gBAAI,SAAS;AACb,gBAAG,SAAS,UAAU,SAAS,OAAO;AAAS,uBAAS,QAAQ,SAAS,OAAO,OAAO,KAAK,SAAS,OAAO,MAAM;AAAA,qBACzG,SAAS;AAAQ,uBAAS,SAAS;AAC5C,kBAAM,IAAI,MAAM,MAAM;AAAA,UACvB;AAAA,eACM;AAENA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,qCAAqC;AAAA,QACjD;AAGD,YAAI,OAAO,KAAK,qBAAqB,EAAE,SAAS,GAAG;AAClD,eAAK,UAAU,eAAe,qBAAqB;AAAA,QACpD;AAGA,YAAI,cAAc;AACjBA,wBAAG,MAAC,YAAW;AACf,yBAAe;AAAA,QAChB;AACAA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,UAAQ,CAAG;AAClD,aAAK,mBAAkB;AAAA,MAEtB,SAAO,OAAO;AAEf,YAAI,cAAc;AACjBA,wBAAG,MAAC,YAAW;AACf,yBAAe;AAAA,QAChB;AACAA,sBAAc,MAAA,MAAA,SAAA,gCAAA,8BAA8B,KAAK;AACjDA,sBAAAA,MAAI,UAAU,EAAE,MAAM,QAAQ,OAAO,MAAM,WAAW,cAAc,UAAU,IAAM,CAAA;AAAA,MACrF;AAAA,IACA;AAAA,IAED,cAAc;AACb,WAAK,MAAM,KAAK,SAAQ,EAAG,KAAK,OAAO,QAAQ;AAC9CA,sBAAA,MAAA,MAAA,OAAA,gCAAY,QAAQ,GAAG;AACvB,YAAI,eAAe;AACnB,YAAI;AACHA,wBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,yBAAe;AAEf,gBAAM,WAAW,MAAMC,cAAQ,GAAC,aAAa;AAAA,YAC5C,MAAM;AAAA,YACN,MAAM;AAAA,cACL,QAAQ;AAAA,cACR,UAAU,KAAK,SAAS;AAAA,cACxB,UAAU,KAAK,SAAS;AAAA;AAAA,YAEzB;AAAA,UACD,CAAC;AACDD,wBAAY,MAAA,MAAA,OAAA,gCAAA,0CAA0C,QAAQ;AAE9D,cAAI,SAAS,YAAY,KAAK,SAAS,OAAO;AAE7C,gBAAI,cAAc;AACjBA,4BAAAA,MAAI,YAAW,EAAG,MAAM,MAAM;AAAA,cAAA,CAAE;AAChC,6BAAe;AAAA,YAChB;AACAA,0BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAGhD,kBAAM,KAAK,UAAU,aAAa;AAAA,cACjC,OAAO,SAAS;AAAA,cAChB,UAAU,SAAS;AAAA;AAAA,cACnB,cAAc,SAAS;AAAA,YACvB,GAAE,IAAI;AAMP,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAKrD,kBAAM,cAAc,mBAAmB,KAAK,QAAQ,YAAY,EAAE,KAAK;AACvE,iBAAK,eAAe,WAAW;AAAA,iBAEzB;AAEN,gBAAI,cAAc;AACjBA,4BAAAA,MAAI,YAAW,EAAG,MAAM,MAAM;AAAA,cAAA,CAAE;AAChC,6BAAe;AAAA,YAChB;AACAA,0BAAAA,MAAI,UAAU,EAAE,OAAO,SAAS,UAAU,kBAAkB,MAAM,QAAQ,UAAU,IAAM,CAAA;AAAA,UAC3F;AAAA,QACC,SAAO,OAAO;AAEf,cAAI,cAAc;AACjBA,0BAAAA,MAAI,YAAW,EAAG,MAAM,MAAM;AAAA,YAAA,CAAE;AAChC,2BAAe;AAAA,UAChB;AACAA,wBAAA,MAAA,MAAA,SAAA,gCAAc,6BAA6B,KAAK;AAChD,cAAI,SAAS;AACb,cAAG,MAAM;AAAQ,qBAAS,MAAM;AAChC,cAAG,MAAM;AAAS,qBAAS,MAAM;AACjCA,8BAAI,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ,UAAU,IAAK,CAAC;AAAA,QAC9D;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,OAAA,gCAAY,QAAQ,GAAG;AAAA,MACxB,CAAC;AAAA,IACD;AAAA,IAED,eAAe,KAAK;AACnB,YAAM,iBAAiB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA;AAED,YAAM,aAAa,IAAI,MAAM,GAAG,EAAE,CAAC;AAEnC,UAAI,eAAe,SAAS,UAAU,GAAG;AACxCA,yEAAY,oCAAoC,GAAG,EAAE;AACrDA,sBAAAA,MAAI,UAAU,EAAE,KAAK,WAAY,CAAA;AAAA,aAC3B;AACNA,yEAAY,qCAAqC,GAAG,EAAE;AACtDA,sBAAAA,MAAI,SAAS,EAAE,IAAE,CAAG;AAAA,MACrB;AAAA,IACA;AAAA,IAED,qBAAqB;AACpBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,wBAAyB,CAAA;AAAA,IAC/C;AAAA,IACD,2BAA2B;AAE1BA,oBAAG,MAAC,UAAU,EAAC,OAAM,SAAS,MAAK,OAAM,CAAC;AAAA,IAC1C;AAAA;AAAA,EAEF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9dD,GAAG,WAAW,eAAe;"}
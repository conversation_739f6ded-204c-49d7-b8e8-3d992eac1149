/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.group-selector {
  padding: 20rpx 0;
  background-color: #fff;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  position: relative;
  /* 为分享按钮绝对定位提供参考 */
  /* 分享按钮样式 - 固定在右侧 */
}
.group-selector .group-scroll-view {
  width: 100%;
  white-space: nowrap;
  padding-right: 54rpx;
  /* 为固定在右侧的分享按钮预留空间 */
}
.group-selector .group-list {
  padding: 0 20rpx;
  display: inline-flex;
  /* 添加占位元素样式 */
}
.group-selector .group-list .group-item {
  height: 56rpx;
  padding: 0 22rpx;
  margin-right: 8rpx;
  background-color: #f0f0f0;
  border-radius: 28rpx;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  line-height: 1;
  /* 确保文字垂直居中 */
}
.group-selector .group-list .group-item .group-text {
  display: inline-block;
  line-height: 1;
}
.group-selector .group-list .group-item.is-default {
  background-color: rgba(138, 43, 226, 0.1);
  border: 1px solid rgba(138, 43, 226, 0.2);
  color: #8a2be2;
}
.group-selector .group-list .group-item.active {
  background-color: #8a2be2;
  color: #fff;
}
.group-selector .group-list .group-item.add-group {
  background-color: #f0e6ff;
  border: 1px dashed #8a2be2;
  color: #8a2be2;
  position: relative;
}
.group-selector .group-list .group-item.add-group .add-icon-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.group-selector .group-list .group-item.add-group .plus-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: #8a2be2;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  padding-bottom: 4rpx;
  /* 微调加号位置 */
}
.group-selector .group-list .spacing-placeholder {
  width: 54rpx;
  /* 与右侧预留空间一致 */
  height: 1rpx;
  display: inline-block;
}
.group-selector .share-group-btn {
  width: 54rpx;
  min-width: 50rpx;
  /* 确保最小宽度 */
  height: 70rpx;
  border-radius: 50% 0 0 50%;
  /* 左侧保持圆形，右侧平直 */
  background-color: #8a2be2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 8rpx;
  /* 减小左边距 */
  padding-right: 0;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.3);
  position: absolute;
  top: 50%;
  right: 0;
  /* 紧贴右边框 */
  transform: translateY(-50%);
  z-index: 101;
  /* 确保在滚动内容之上 */
  transition: all 0.2s ease;
  overflow: hidden;
  /* 用于容纳波纹效果 */
  line-height: 1;
  /* 重置line-height */
  /* 重置button的默认样式 */
  /* 波纹效果 */
  /* 悬浮提示 */
}
.group-selector .share-group-btn::after {
  border: none;
}
.group-selector .share-group-btn .share-icon {
  width: 34rpx;
  /* 缩小图标尺寸 */
  height: 34rpx;
  filter: brightness(5);
  /* 使图标变白 */
  position: relative;
  z-index: 2;
  /* 确保图标在波纹之上 */
}
.group-selector .share-group-btn .share-btn-ripple {
  position: absolute;
  top: 50%;
  left: 30%;
  /* 更加偏左，适应更窄的半圆形 */
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  opacity: 0;
}
.group-selector .share-group-btn .share-tooltip {
  position: absolute;
  right: 65rpx;
  padding: 10rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 6rpx;
  font-size: 24rpx;
  white-space: nowrap;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  opacity: 0;
  transform: translateX(10rpx);
  transition: all 0.3s ease;
  /* 添加小三角形 */
}
.group-selector .share-group-btn .share-tooltip:after {
  content: "";
  position: absolute;
  right: -8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8rpx 0 8rpx 8rpx;
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.7);
}
.group-selector .share-group-btn.show-tooltip .share-tooltip {
  opacity: 1;
  transform: translateX(0);
}
.group-selector .share-group-btn.active .share-btn-ripple {
  animation: ripple 0.6s ease-out;
}
@keyframes ripple {
0% {
    width: 0;
    height: 0;
    opacity: 0.5;
}
100% {
    width: 100rpx;
    height: 100rpx;
    opacity: 0;
}
}
.group-selector .share-group-btn:active {
  transform: translateY(-50%) scale(0.92);
  box-shadow: 0 2rpx 8rpx rgba(138, 43, 226, 0.2);
  opacity: 0.9;
  padding-left: 4rpx;
  /* 点击时左内边距减小，视觉效果更好 */
}
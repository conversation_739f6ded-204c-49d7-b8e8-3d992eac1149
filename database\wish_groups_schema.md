# wish_groups 表设计

## 表结构

```javascript
{
  _id: String,           // 分组的唯一ID
  userId: String,        // 关联的用户ID
  name: String,          // 分组名称 (如：全部、礼物、朋友可见)
  icon: String,          // 分组图标
  color: String,         // 分组颜色
  order: Number,         // 排序序号
  isDefault: Boolean,    // 是否为默认分组
  createDate: Date,      // 创建时间
  updateDate: Date       // 更新时间
}
```

## 建议的默认数据

```javascript
// 用户首次使用时应该自动创建这些默认分组
[
  {
    _id: "group_all_" + userId,
    userId: "683dc597466d41ac68be1977",
    name: "全部",
    icon: "",
    color: "#8a2be2",
    order: 0,
    isDefault: true,
    createDate: new Date(),
    updateDate: new Date()
  },
  {
    _id: "group_gift_" + userId,
    userId: "683dc597466d41ac68be1977", 
    name: "礼物",
    icon: "🎁",
    color: "#ff4757",
    order: 1,
    isDefault: true,
    createDate: new Date(),
    updateDate: new Date()
  },
  {
    _id: "group_friends_" + userId,
    userId: "683dc597466d41ac68be1977",
    name: "朋友可见", 
    icon: "👥",
    color: "#2ed573",
    order: 2,
    isDefault: true,
    createDate: new Date(),
    updateDate: new Date()
  }
]
```

## 数据关系

```
用户表 (uni-id-users)
  └── userId ←→ 分组表 (wish_groups)
                    └── groupId ←→ 心愿表 (wishes.groupIds[])
``` 
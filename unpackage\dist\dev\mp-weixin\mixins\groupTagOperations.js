"use strict";
const common_vendor = require("../common/vendor.js");
const store_group = require("../store/group.js");
const store_wish = require("../store/wish.js");
const store_user = require("../store/user.js");
const groupTagOperations = {
  methods: {
    // 显示分组操作菜单
    showGroupActionSheet(group) {
      try {
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:10", "===== 执行showGroupActionSheet方法 =====");
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:11", "处理的分组:", JSON.stringify(group));
        const userStore = store_user.useUserStore();
        if (!userStore.isLogin) {
          common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:16", "用户未登录，跳转到登录页面");
          common_vendor.index.navigateTo({
            url: "/pages/login/login"
          });
          return;
        }
        let itemList = [];
        if (group.isDefault) {
          itemList = ["排序标签"];
        } else {
          itemList = ["编辑标签", "排序标签", "仅删除该标签", "删除标签及心愿"];
        }
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:35", "显示菜单选项:", itemList);
        common_vendor.index.showActionSheet({
          itemList,
          success: (res) => {
            common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:39", "用户选择:", res.tapIndex);
            if (group.isDefault) {
              if (res.tapIndex === 0) {
                this.enterSortMode();
              }
            } else {
              switch (res.tapIndex) {
                case 0:
                  this.editGroup(group);
                  break;
                case 1:
                  this.enterSortMode();
                  break;
                case 2:
                  common_vendor.index.showModal({
                    title: "确认删除",
                    content: `确定要删除"${group.name}"标签吗？该标签下的心愿卡片将被保留，并转移至"全部"分组。`,
                    cancelText: "取消",
                    confirmText: "确定",
                    success: (modalRes) => {
                      if (modalRes.confirm) {
                        this.handleGroupDelete(group, false);
                      }
                    }
                  });
                  break;
                case 3:
                  try {
                    const wishStore = store_wish.useWishStore();
                    if (!wishStore.wishList) {
                      common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:75", "初始化wishList");
                      wishStore.initWishList();
                    }
                    const relatedWishes = wishStore.wishList.filter(
                      (wish) => wish.groupIds && wish.groupIds.includes(group.id)
                    );
                    common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:84", "关联心愿数量:", relatedWishes.length);
                    if (relatedWishes.length > 0) {
                      common_vendor.index.showModal({
                        title: "确认删除",
                        content: `确定要删除"${group.name}"标签及其下${relatedWishes.length}个心愿卡片吗？此操作不可恢复。`,
                        cancelText: "取消",
                        confirmText: "确定",
                        success: (modalRes) => {
                          if (modalRes.confirm) {
                            this.handleGroupDelete(group, true);
                          }
                        }
                      });
                    } else {
                      common_vendor.index.showModal({
                        title: "提示",
                        content: `该标签下没有关联的心愿卡片，将执行普通删除。`,
                        showCancel: false,
                        success: () => {
                          this.handleGroupDelete(group, false);
                        }
                      });
                    }
                  } catch (error) {
                    common_vendor.index.__f__("error", "at mixins/groupTagOperations.js:111", "检查关联心愿失败:", error);
                    common_vendor.index.showModal({
                      title: "删除标签",
                      content: `确定要删除"${group.name}"标签吗？`,
                      cancelText: "取消",
                      confirmText: "确定",
                      success: (modalRes) => {
                        if (modalRes.confirm) {
                          this.handleGroupDelete(group, false);
                        }
                      }
                    });
                  }
                  break;
              }
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at mixins/groupTagOperations.js:130", "显示菜单失败:", err);
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at mixins/groupTagOperations.js:134", "执行showGroupActionSheet失败:", error);
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "none"
        });
      }
    },
    // 编辑分组
    editGroup(group) {
      const userStore = store_user.useUserStore();
      if (!userStore.isLogin) {
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:147", "用户未登录，跳转到登录页面");
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      group.name;
      common_vendor.index.showModal({
        title: "编辑标签",
        placeholderText: "请输入新标签名称",
        editable: true,
        content: group.name,
        success: (res) => {
          if (res.confirm && res.content && res.content.trim()) {
            this.handleGroupRename(group, res.content.trim());
          }
        }
      });
    },
    // 处理分组重命名
    handleGroupRename(group, newName) {
      const userStore = store_user.useUserStore();
      if (!userStore.isLogin) {
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:192", "用户未登录，跳转到登录页面");
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      const groupStore = store_group.useGroupStore();
      if (groupStore.getAllGroups.some((g) => g.id !== group.id && g.name === newName)) {
        common_vendor.index.showToast({
          title: "标签名称已存在",
          icon: "none"
        });
        return;
      }
      groupStore.updateGroup(group.id, newName);
      common_vendor.index.showToast({
        title: "更新成功",
        icon: "success"
      });
    },
    // 进入标签排序模式
    enterSortMode() {
      common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:222", "进入标签排序模式");
      const userStore = store_user.useUserStore();
      if (!userStore.isLogin) {
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:227", "用户未登录，跳转到登录页面");
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      if (typeof this.sortGroups === "function") {
        this.sortGroups();
      } else {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const currentRoute = currentPage.route;
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:244", "当前页面:", currentRoute);
        common_vendor.index.showToast({
          title: "开始排序标签",
          icon: "none",
          duration: 1500
        });
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/groupSort/groupSort",
            fail: (err) => {
              common_vendor.index.__f__("error", "at mixins/groupTagOperations.js:259", "打开排序页面失败:", err);
              common_vendor.index.showToast({
                title: "无法打开排序页面",
                icon: "none"
              });
            }
          });
        }, 500);
      }
    },
    // 确认删除分组 (仅供内部使用，外部应调用showGroupActionSheet)
    confirmDeleteGroup(group) {
      const userStore = store_user.useUserStore();
      if (!userStore.isLogin) {
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:275", "用户未登录，跳转到登录页面");
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "删除标签",
        content: `确定要删除"${group.name}"标签吗？`,
        cancelText: "取消",
        confirmText: "删除",
        confirmColor: "#FF0000",
        success: (res) => {
          if (res.confirm) {
            this.handleGroupDelete(group, false);
          }
        }
      });
    },
    // 处理分组删除
    async handleGroupDelete(group, deleteWishes = false) {
      common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:299", "执行handleGroupDelete, deleteWishes =", deleteWishes);
      common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:300", "处理的分组对象:", JSON.stringify(group));
      if (!group || !group.id) {
        common_vendor.index.__f__("error", "at mixins/groupTagOperations.js:304", "handleGroupDelete: 无效的分组对象", group);
        common_vendor.index.showToast({
          title: "分组数据无效",
          icon: "none"
        });
        return;
      }
      const userStore = store_user.useUserStore();
      if (!userStore.isLogin) {
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:315", "用户未登录，跳转到登录页面");
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      const groupStore = store_group.useGroupStore();
      const wishStore = store_wish.useWishStore();
      if (deleteWishes) {
        const wishesToDelete = wishStore.wishList.filter(
          (wish) => wish.groupIds && wish.groupIds.includes(group.id)
        );
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:335", `将删除${wishesToDelete.length}个关联心愿`);
        for (const wish of wishesToDelete) {
          wishStore.deleteWish(wish.id);
        }
        const deleteResult = await groupStore.deleteGroup(group.id);
        if (deleteResult) {
          common_vendor.index.showToast({
            title: "已删除标签及相关心愿",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: "删除标签失败",
            icon: "none"
          });
        }
      } else {
        const wishesToUpdate = wishStore.wishList.filter(
          (wish) => wish.groupIds && wish.groupIds.includes(group.id)
        );
        common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:365", `将从${wishesToUpdate.length}个心愿中移除分组标签`);
        for (const wish of wishesToUpdate) {
          if (!Array.isArray(wish.groupIds)) {
            common_vendor.index.__f__("warn", "at mixins/groupTagOperations.js:371", "心愿的groupIds不是数组:", wish.id);
            continue;
          }
          const updatedGroupIds = wish.groupIds.filter((id) => id !== group.id);
          if (updatedGroupIds.length === 0) {
            updatedGroupIds.push("all");
          }
          common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:383", `心愿 ${wish.id} 分组从 [${wish.groupIds.join(",")}] 更新为 [${updatedGroupIds.join(",")}]`);
          wishStore.updateWish({
            id: wish.id,
            groupIds: updatedGroupIds
          });
        }
        if (this.wishForm && this.wishForm.groupIds && this.wishForm.groupIds.includes(group.id)) {
          const index = this.wishForm.groupIds.indexOf(group.id);
          if (index > -1) {
            this.wishForm.groupIds.splice(index, 1);
            if (this.wishForm.groupIds.length === 0) {
              this.wishForm.groupIds.push("all");
            }
            common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:403", "更新当前编辑表单的分组:", this.wishForm.groupIds);
          }
        }
        const deleteResult = await groupStore.deleteGroup(group.id);
        if (deleteResult) {
          common_vendor.index.showToast({
            title: "已删除标签",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: "删除标签失败",
            icon: "none"
          });
        }
        if (wishStore.currentGroupId === group.id) {
          common_vendor.index.__f__("log", "at mixins/groupTagOperations.js:425", '当前正在浏览被删除的分组，切换到"全部"分组');
          wishStore.setCurrentGroup("all");
        }
      }
    }
  }
};
exports.groupTagOperations = groupTagOperations;
//# sourceMappingURL=../../.sourcemap/mp-weixin/mixins/groupTagOperations.js.map

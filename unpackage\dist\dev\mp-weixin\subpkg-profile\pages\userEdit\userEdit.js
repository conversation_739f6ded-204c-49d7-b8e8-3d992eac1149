"use strict";
const common_vendor = require("../../../common/vendor.js");
const store_user = require("../../../store/user.js");
const _sfc_main = {
  setup() {
    const userStore = store_user.useUserStore();
    const avatarDisplayUrl = common_vendor.ref("");
    const newAvatarTempPath = common_vendor.ref(null);
    const nickname = common_vendor.ref("");
    common_vendor.onMounted(() => {
      const currentUserInfo = userStore.getUserInfo;
      nickname.value = (currentUserInfo == null ? void 0 : currentUserInfo.nickname) || "";
      avatarDisplayUrl.value = userStore.avatarUrl;
    });
    return {
      userStore,
      avatarDisplayUrl,
      newAvatarTempPath,
      nickname
    };
  },
  methods: {
    // 选择头像
    chooseAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.avatarDisplayUrl = tempFilePath;
          this.newAvatarTempPath = tempFilePath;
          common_vendor.index.showToast({
            title: "头像已选择",
            icon: "none"
            // 避免与成功图标混淆，仅提示
          });
        }
      });
    },
    // 保存用户信息
    async saveUserInfo() {
      var _a;
      const currentNickname = ((_a = this.userStore.getUserInfo) == null ? void 0 : _a.nickname) || "";
      const nicknameChanged = this.nickname.trim() !== currentNickname;
      const avatarChanged = !!this.newAvatarTempPath;
      if (!this.nickname.trim()) {
        common_vendor.index.showToast({ title: "昵称不能为空", icon: "none" });
        return;
      }
      if (!nicknameChanged && !avatarChanged) {
        common_vendor.index.showToast({ title: "信息未作修改", icon: "none" });
        return;
      }
      common_vendor.index.showLoading({ title: "正在保存..." });
      const updateDataForDB = {};
      const updateDataForStore = {};
      try {
        if (avatarChanged) {
          const uploadResult = await common_vendor.nr.uploadFile({
            filePath: this.newAvatarTempPath,
            cloudPath: `user_avatars/${this.userStore.userId}/avatar_${Date.now()}.${this.newAvatarTempPath.split(".").pop() || "png"}`
          });
          if (uploadResult.fileID) {
            updateDataForDB.avatar_file = { url: uploadResult.fileID };
            updateDataForStore.avatarUrl = uploadResult.fileID;
          } else {
            throw new Error("头像上传失败，未返回fileID");
          }
        }
        if (nicknameChanged) {
          updateDataForDB.nickname = this.nickname.trim();
          updateDataForStore.nickname = this.nickname.trim();
        }
        if (Object.keys(updateDataForDB).length > 0) {
          const usersTable = common_vendor.nr.database().collection("uni-id-users");
          const dbResult = await usersTable.where("_id==$env.uid").update(updateDataForDB);
          if (!dbResult || !(dbResult.result && (dbResult.result.updated > 0 || dbResult.result.updated === 0))) {
            common_vendor.index.__f__("error", "at subpkg-profile/pages/userEdit/userEdit.vue:128", "DB update failed or no docs matched:", dbResult);
            let errMsg = "数据库更新失败";
            if (dbResult && dbResult.result && dbResult.result.errCode)
              errMsg = `错误码: ${dbResult.result.errCode}, ${dbResult.result.errMsg}`;
            else if (dbResult && dbResult.errMsg)
              errMsg = dbResult.errMsg;
            throw new Error(errMsg);
          }
        }
        if (Object.keys(updateDataForStore).length > 0) {
          this.userStore.updateUserInfo(updateDataForStore);
        }
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "保存成功", icon: "success", duration: 1500 });
        setTimeout(() => common_vendor.index.navigateBack(), 1500);
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subpkg-profile/pages/userEdit/userEdit.vue:147", "Save user info error:", error);
        common_vendor.index.showToast({ icon: "none", title: error.message || "保存失败，请重试", duration: 3e3 });
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $setup.avatarDisplayUrl || "/static/default_avatar.png",
    b: common_vendor.p({
      type: "camera-filled",
      size: "20",
      color: "#fff"
    }),
    c: common_vendor.o((...args) => $options.chooseAvatar && $options.chooseAvatar(...args)),
    d: $setup.nickname,
    e: common_vendor.o(($event) => $setup.nickname = $event.detail.value),
    f: common_vendor.o((...args) => $options.saveUserInfo && $options.saveUserInfo(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpkg-profile/pages/userEdit/userEdit.js.map

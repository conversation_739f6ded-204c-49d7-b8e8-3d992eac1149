import { useGroupStore } from '@/store/group.js'
import { useWishStore } from '@/store/wish.js'
import { useUserStore } from '@/store/user.js'

export default {
  methods: {
    // 显示分组操作菜单
    showGroupActionSheet(group) {
      try {
        console.log('===== 执行showGroupActionSheet方法 =====');
        console.log('处理的分组:', JSON.stringify(group));
        
        // 简单检查用户登录状态，避免复杂的验证流程
        const userStore = useUserStore();
        if (!userStore.isLogin) {
          console.log('用户未登录，跳转到登录页面');
          uni.navigateTo({
            url: '/pages/login/login'
          });
          return;
        }
        
        // 根据分组类型设置可用操作
        let itemList = [];
        
        if (group.isDefault) {
          // 默认分组只允许排序
          itemList = ['排序标签'];
        } else {
          // 自定义分组允许所有操作
          itemList = ['编辑标签', '排序标签', '仅删除该标签', '删除标签及心愿'];
        }
        
        // 显示操作菜单
        console.log('显示菜单选项:', itemList);
        uni.showActionSheet({
          itemList: itemList,
          success: (res) => {
            console.log('用户选择:', res.tapIndex);
            
            if (group.isDefault) {
              // 默认分组只有排序选项
              if (res.tapIndex === 0) {
                this.enterSortMode();
              }
            } else {
              // 自定义分组的操作
              switch (res.tapIndex) {
                case 0: // 编辑标签
                  this.editGroup(group);
                  break;
                case 1: // 排序标签
                  this.enterSortMode();
                  break;
                case 2: // 仅删除该标签
                  uni.showModal({
                    title: '确认删除',
                    content: `确定要删除"${group.name}"标签吗？该标签下的心愿卡片将被保留，并转移至"全部"分组。`,
                    cancelText: '取消',
                    confirmText: '确定',
                    success: (modalRes) => {
                      if (modalRes.confirm) {
                        this.handleGroupDelete(group, false);
                      }
                    }
                  });
                  break;
                case 3: // 删除标签及心愿
                  try {
                    // 初始化wishStore
                    const wishStore = useWishStore();
                    
                    // 初始化心愿列表
                    if (!wishStore.wishList) {
                      console.log('初始化wishList');
                      wishStore.initWishList();
                    }
                    
                    // 查找关联心愿
                    const relatedWishes = wishStore.wishList.filter(wish => 
                      wish.groupIds && wish.groupIds.includes(group.id)
                    );
                    
                    console.log('关联心愿数量:', relatedWishes.length);
                    
                    if (relatedWishes.length > 0) {
                      // 有关联心愿
                      uni.showModal({
                        title: '确认删除',
                        content: `确定要删除"${group.name}"标签及其下${relatedWishes.length}个心愿卡片吗？此操作不可恢复。`,
                        cancelText: '取消',
                        confirmText: '确定',
                        success: (modalRes) => {
                          if (modalRes.confirm) {
                            this.handleGroupDelete(group, true);
                          }
                        }
                      });
                    } else {
                      // 无关联心愿，提示用户并执行普通删除
                      uni.showModal({
                        title: '提示',
                        content: `该标签下没有关联的心愿卡片，将执行普通删除。`,
                        showCancel: false,
                        success: () => {
                          this.handleGroupDelete(group, false);
                        }
                      });
                    }
                  } catch (error) {
                    console.error('检查关联心愿失败:', error);
                    // 出错时执行普通删除
                    uni.showModal({
                      title: '删除标签',
                      content: `确定要删除"${group.name}"标签吗？`,
                      cancelText: '取消',
                      confirmText: '确定',
                      success: (modalRes) => {
                        if (modalRes.confirm) {
                          this.handleGroupDelete(group, false);
                        }
                      }
                    });
                  }
                  break;
              }
            }
          },
          fail: (err) => {
            console.error('显示菜单失败:', err);
          }
        });
      } catch (error) {
        console.error('执行showGroupActionSheet失败:', error);
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },
    
    // 编辑分组
    editGroup(group) {
      // 简单检查用户登录状态
      const userStore = useUserStore();
      if (!userStore.isLogin) {
        console.log('用户未登录，跳转到登录页面');
        uni.navigateTo({
          url: '/pages/login/login'
        });
        return;
      }
      
      // 初始名称
      const initialName = group.name
      
      // 显示编辑对话框
      // #ifdef MP-WEIXIN
      uni.showModal({
        title: '编辑标签',
        placeholderText: '请输入新标签名称',
        editable: true,
        content: group.name,
        success: (res) => {
          if (res.confirm && res.content && res.content.trim()) {
            this.handleGroupRename(group, res.content.trim())
          }
        }
      })
      // #endif
      
      // #ifndef MP-WEIXIN
      // 其他平台使用普通输入框
      uni.showModal({
        title: '编辑标签',
        content: group.name,
        editable: true,
        success: (res) => {
          if (res.confirm && res.content && res.content.trim()) {
            this.handleGroupRename(group, res.content.trim())
          }
        }
      })
      // #endif
    },
    
    // 处理分组重命名
    handleGroupRename(group, newName) {
      // 简单检查用户登录状态
      const userStore = useUserStore();
      if (!userStore.isLogin) {
        console.log('用户未登录，跳转到登录页面');
        uni.navigateTo({
          url: '/pages/login/login'
        });
        return;
      }
      
      // 获取分组存储
      const groupStore = useGroupStore()
      
      // 检查是否与已有标签重名
      if (groupStore.getAllGroups.some(g => g.id !== group.id && g.name === newName)) {
        uni.showToast({
          title: '标签名称已存在',
          icon: 'none'
        })
        return
      }
      
      // 更新分组名称
      groupStore.updateGroup(group.id, newName)
      
      uni.showToast({
        title: '更新成功',
        icon: 'success'
      })
    },
    
    // 进入标签排序模式
    enterSortMode() {
      console.log('进入标签排序模式');
      
      // 检查用户是否已登录（避免重复验证）
      const userStore = useUserStore();
      if (!userStore.isLogin) {
        console.log('用户未登录，跳转到登录页面')
        uni.navigateTo({
          url: '/pages/login/login'
        });
        return
      }
      
      // 检查当前页面是否有sortGroups方法（由父组件提供）
      if (typeof this.sortGroups === 'function') {
        // 组件提供了排序方法，直接调用
        this.sortGroups();
      } else {
        // 根据当前页面路径，导航到不同的排序页面
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const currentRoute = currentPage.route;
        
        console.log('当前页面:', currentRoute);
        
        // 通知用户
        uni.showToast({
          title: '开始排序标签',
          icon: 'none',
          duration: 1500
        });
        
        // 在下一个周期执行，确保提示显示
        setTimeout(() => {
          // 导航到排序页面
          uni.navigateTo({
            url: '/pages/groupSort/groupSort',
            fail: (err) => {
              console.error('打开排序页面失败:', err);
              uni.showToast({
                title: '无法打开排序页面',
                icon: 'none'
              });
            }
          });
        }, 500);
      }
    },
    
    // 确认删除分组 (仅供内部使用，外部应调用showGroupActionSheet)
    confirmDeleteGroup(group) {
      // 检查用户是否已登录（避免重复验证）
      const userStore = useUserStore();
      if (!userStore.isLogin) {
        console.log('用户未登录，跳转到登录页面')
        uni.navigateTo({
          url: '/pages/login/login'
        });
        return
      }
      
      // 简化为基本确认
      uni.showModal({
        title: '删除标签',
        content: `确定要删除"${group.name}"标签吗？`,
        cancelText: '取消',
        confirmText: '删除',
        confirmColor: '#FF0000',
        success: (res) => {
          if (res.confirm) {
            this.handleGroupDelete(group, false)
          }
        }
      })
    },
    
    // 处理分组删除
    async handleGroupDelete(group, deleteWishes = false) {
      console.log('执行handleGroupDelete, deleteWishes =', deleteWishes);
      console.log('处理的分组对象:', JSON.stringify(group));
      
      // 验证分组对象
      if (!group || !group.id) {
        console.error('handleGroupDelete: 无效的分组对象', group);
        uni.showToast({
          title: '分组数据无效',
          icon: 'none'
        });
        return;
      }
      
      // 检查用户是否已登录（避免重复验证）
      const userStore = useUserStore();
      if (!userStore.isLogin) {
        console.log('用户未登录，跳转到登录页面')
        uni.navigateTo({
          url: '/pages/login/login'
        });
        return
      }
      
      // 获取分组存储
      const groupStore = useGroupStore()
      
      // 使用导入的wishStore
      const wishStore = useWishStore()
      
      // 如果需要删除心愿卡片
      if (deleteWishes) {
        // 找出属于该分组的所有心愿
        const wishesToDelete = wishStore.wishList.filter(wish => 
          wish.groupIds && wish.groupIds.includes(group.id)
        )
        
        console.log(`将删除${wishesToDelete.length}个关联心愿`);
        
        // 删除这些心愿
        for (const wish of wishesToDelete) {
          wishStore.deleteWish(wish.id)
        }
        
        // 从分组存储中删除分组
        const deleteResult = await groupStore.deleteGroup(group.id)
        
        if (deleteResult) {
          // 显示成功提示
          uni.showToast({
            title: '已删除标签及相关心愿',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '删除标签失败',
            icon: 'none'
          })
        }
      } else {
        // 仅删除分组标签，保留心愿
        
        // 找出属于该分组的所有心愿
        const wishesToUpdate = wishStore.wishList.filter(wish => 
          wish.groupIds && wish.groupIds.includes(group.id)
        )
        
        console.log(`将从${wishesToUpdate.length}个心愿中移除分组标签`);
        
        // 更新这些心愿，移除该分组ID
        for (const wish of wishesToUpdate) {
          // 确保groupIds是一个数组
          if (!Array.isArray(wish.groupIds)) {
            console.warn('心愿的groupIds不是数组:', wish.id);
            continue;
          }
          
          // 创建新的groupIds数组，排除要删除的分组ID
          const updatedGroupIds = wish.groupIds.filter(id => id !== group.id);
          
          // 如果心愿没有任何分组了，添加到"全部"分组
          if (updatedGroupIds.length === 0) {
            updatedGroupIds.push('all');
          }
          
          console.log(`心愿 ${wish.id} 分组从 [${wish.groupIds.join(',')}] 更新为 [${updatedGroupIds.join(',')}]`);
          
          // 更新心愿
          wishStore.updateWish({
            id: wish.id,
            groupIds: updatedGroupIds
          })
        }
        
        // 如果当前页面有wishForm数据（编辑心愿页），移除此分组
        if (this.wishForm && this.wishForm.groupIds && this.wishForm.groupIds.includes(group.id)) {
          const index = this.wishForm.groupIds.indexOf(group.id)
          if (index > -1) {
            this.wishForm.groupIds.splice(index, 1)
            
            // 如果wishForm没有任何分组了，添加到"全部"分组
            if (this.wishForm.groupIds.length === 0) {
              this.wishForm.groupIds.push('all');
            }
            
            console.log('更新当前编辑表单的分组:', this.wishForm.groupIds);
          }
        }
        
        // 从分组存储中删除分组
        const deleteResult = await groupStore.deleteGroup(group.id)
        
        if (deleteResult) {
          // 显示成功提示
          uni.showToast({
            title: '已删除标签',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '删除标签失败',
            icon: 'none'
          })
        }
        
        // 如果在当前组中，切换到"全部"组
        if (wishStore.currentGroupId === group.id) {
          console.log('当前正在浏览被删除的分组，切换到"全部"分组');
          wishStore.setCurrentGroup('all');
        }
      }
    }
  }
} 
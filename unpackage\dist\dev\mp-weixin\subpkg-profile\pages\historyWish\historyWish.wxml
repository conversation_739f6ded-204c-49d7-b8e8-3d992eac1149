<view class="history-container"><view class="history-content"><view wx:if="{{a}}" class="empty-history"><view class="empty-icon"><uni-icons wx:if="{{b}}" u-i="1a9ec0d4-0" bind:__l="__l" u-p="{{b}}"></uni-icons></view><view class="empty-content"><text class="empty-title">暂无完成记录</text><text class="empty-desc">当心愿实现时，会出现在这里</text></view><view class="empty-action"><navigator url="/pages/index/index" class="go-create-btn"><uni-icons wx:if="{{c}}" u-i="1a9ec0d4-1" bind:__l="__l" u-p="{{c}}"></uni-icons><text>去创建心愿</text></navigator></view></view><scroll-view wx:else class="history-list" scroll-y="true" show-scrollbar="{{false}}" enable-flex="{{true}}" bindscroll="{{e}}"><history-wish-card wx:for="{{d}}" wx:for-item="wish" wx:key="a" bindrestore="{{wish.b}}" binddelete="{{wish.c}}" bindcardOpen="{{wish.d}}" bindcardClose="{{wish.e}}" bindcardSwipeStart="{{wish.f}}" bindcardScrollDetected="{{wish.g}}" u-i="{{wish.h}}" bind:__l="__l" u-p="{{wish.i}}"/><view class="list-bottom-space"></view></scroll-view></view></view>
"use strict";
const common_vendor = require("../common/vendor.js");
const utils_envUtils = require("./envUtils.js");
async function convertCloudUrl(cloudUrl) {
  if (!cloudUrl || typeof cloudUrl !== "string") {
    return "";
  }
  if (!cloudUrl.startsWith("cloud://")) {
    return cloudUrl;
  }
  try {
    const result = await common_vendor.nr.getTempFileURL({
      fileList: [cloudUrl]
    });
    if (result && result.fileList && result.fileList.length > 0) {
      return result.fileList[0].tempFileURL || cloudUrl;
    }
    return cloudUrl;
  } catch (error) {
    utils_envUtils.devLog.error("[ImageUtils] 云存储URL转换失败:", error);
    return cloudUrl;
  }
}
class ImageLazyLoader {
  constructor(options = {}) {
    this.loadedImages = /* @__PURE__ */ new Set();
    this.loadingImages = /* @__PURE__ */ new Set();
    this.failedImages = /* @__PURE__ */ new Set();
    this.retryCount = options.retryCount || 3;
    this.retryDelay = options.retryDelay || 1e3;
  }
  /**
   * 检查图片是否已加载
   */
  isLoaded(url) {
    return this.loadedImages.has(url);
  }
  /**
   * 检查图片是否正在加载
   */
  isLoading(url) {
    return this.loadingImages.has(url);
  }
  /**
   * 加载图片
   */
  async loadImage(url, retryCount = 0) {
    if (this.isLoaded(url)) {
      return Promise.resolve();
    }
    if (this.isLoading(url)) {
      return Promise.resolve();
    }
    this.loadingImages.add(url);
    try {
      await new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          this.loadedImages.add(url);
          this.loadingImages.delete(url);
          this.failedImages.delete(url);
          resolve();
        };
        img.onerror = () => {
          reject(new Error(`图片加载失败: ${url}`));
        };
        img.src = url;
      });
    } catch (error) {
      this.loadingImages.delete(url);
      if (retryCount < this.retryCount) {
        utils_envUtils.devLog.warn(`[ImageLazyLoader] 图片加载失败，${this.retryDelay}ms后重试: ${url}`);
        await new Promise((resolve) => setTimeout(resolve, this.retryDelay));
        return this.loadImage(url, retryCount + 1);
      } else {
        this.failedImages.add(url);
        utils_envUtils.devLog.error(`[ImageLazyLoader] 图片加载失败，已达到最大重试次数: ${url}`);
        throw error;
      }
    }
  }
  /**
   * 清除缓存
   */
  clear() {
    this.loadedImages.clear();
    this.loadingImages.clear();
    this.failedImages.clear();
  }
}
new ImageLazyLoader();
exports.convertCloudUrl = convertCloudUrl;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/imageUtils.js.map
